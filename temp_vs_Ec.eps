%!PS-Adobe-3.0 EPSF-3.0
%%Title: temp_vs_Ec.eps
%%Creator: Matplotlib v3.7.1, https://matplotlib.org/
%%CreationDate: Thu Mar 28 12:06:00 2024
%%Orientation: portrait
%%BoundingBox: 39 178 573 614
%%HiResBoundingBox: 39.568750 178.323125 572.431250 613.676875
%%EndComments
%%BeginProlog
/mpldict 11 dict def
mpldict begin
/_d { bind def } bind def
/m { moveto } _d
/l { lineto } _d
/r { rlineto } _d
/c { curveto } _d
/cl { closepath } _d
/ce { closepath eofill } _d
/box {
      m
      1 index 0 r
      0 exch r
      neg 0 r
      cl
    } _d
/clipbox {
      box
      clip
      newpath
    } _d
/sc { setcachedevice } _d
%!PS-TrueTypeFont-1.0-2.0
%%Title: unknown
%%Creator: Converted from TrueType to type 42 by PPR
15 dict begin
/FontName /LiberationSans def
/PaintType 0 def
/FontMatrix[1 0 0 1 0 0]def
/FontBBox[-544 -303 1302 980]def
/FontType 42 def
/Encoding StandardEncoding def
/FontInfo 10 dict dup begin
/FamilyName (unknown) def
/FullName (unknown) def
/Weight (unknown) def
/Version (unknown) def
/ItalicAngle 0.0 def
/isFixedPitch false def
/UnderlinePosition -217 def
/UnderlineThickness 150 def
end readonly def
/sfnts[<000100000009008000030010637674204ADA4BFA0000009C000002886670676D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>]def
/CharStrings 34 dict dup begin
/.notdef 0 def
/space 3 def
/parenleft 4 def
/parenright 5 def
/period 6 def
/slash 7 def
/zero 8 def
/one 9 def
/two 10 def
/three 11 def
/five 12 def
/six 13 def
/nine 14 def
/C 15 def
/E 16 def
/F 17 def
/K 18 def
/M 19 def
/T 20 def
/V 21 def
/a 22 def
/c 23 def
/d 24 def
/e 25 def
/i 26 def
/l 27 def
/m 28 def
/o 29 def
/p 30 def
/r 31 def
/t 32 def
/u 33 def
/v 34 def
/x 35 def
end readonly def

systemdict/resourcestatus known
 {42 /FontType resourcestatus
   {pop pop false}{true}ifelse}
 {true}ifelse
{/TrueDict where{pop}{(%%[ Error: no TrueType rasterizer ]%%)= flush}ifelse
/FontType 3 def
 /TrueState 271 string def
 TrueDict begin sfnts save
 72 0 matrix defaultmatrix dtransform dup
 mul exch dup mul add sqrt cvi 0 72 matrix
 defaultmatrix dtransform dup mul exch dup
 mul add sqrt cvi 3 -1 roll restore
 TrueState initer end
 /BuildGlyph{exch begin
  CharStrings dup 2 index known
    {exch}{exch pop /.notdef}ifelse
  get dup xcheck
    {currentdict systemdict begin begin exec end end}
    {TrueDict begin /bander load cvlit exch TrueState render end}
    ifelse
 end}bind def
 /BuildChar{
  1 index /Encoding get exch get
  1 index /BuildGlyph get exec
 }bind def
}if

FontName currentdict end definefont pop
end
%%EndProlog
mpldict begin
39.569 178.323 translate
532.862 435.354 0 0 clipbox
gsave
0 0 m
532.8625 0 l
532.8625 435.35375 l
0 435.35375 l
cl
1.000 setgray
fill
grestore
gsave
79.2625 67.79375 m
525.6625 67.79375 l
525.6625 428.15375 l
79.2625 428.15375 l
cl
1.000 setgray
fill
grestore
/p0_0 {
newpath
translate
-5.59017 9.682458 m
-11.18034 0 l
-5.59017 -9.682458 l
5.59017 -9.682458 l
11.18034 -0 l
5.59017 9.682458 l
cl

} bind def
1.000 setlinewidth
1 setlinejoin
0 setlinecap
[] 0 setdash
0.031 0.416 0.416 setrgbcolor
gsave
446.4 360.36 79.263 67.794 clipbox
152.988 84.1738 p0_0
stroke
grestore
gsave
446.4 360.36 79.263 67.794 clipbox
206.257 84.5898 p0_0
stroke
grestore
gsave
446.4 360.36 79.263 67.794 clipbox
248.873 86.2538 p0_0
stroke
grestore
gsave
446.4 360.36 79.263 67.794 clipbox
291.489 84.6938 p0_0
stroke
grestore
gsave
446.4 360.36 79.263 67.794 clipbox
207.109755 108.87409 m
204.599613 101.148665 l
196.47662 101.148665 l
203.048259 96.37409 l
200.538116 88.648665 l
207.109755 93.42324 l
213.681394 88.648665 l
211.171251 96.37409 l
217.74289 101.148665 l
209.619898 101.148665 l
207.109755 108.87409 l
cl
stroke
grestore
2.500 setlinewidth
0.000 setgray
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2625 67.7938 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2625 428.154 o
grestore
/LiberationSans 25.000 selectfont
gsave

72.3094 39.6844 translate
0 rotate
0 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
207.11 67.7938 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
207.11 428.154 o
grestore
/LiberationSans 25.000 selectfont
gsave

186.25 39.6844 translate
0 rotate
0 0 m /three glyphshow
13.9038 0 m /zero glyphshow
27.8076 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
334.957 67.7938 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
334.957 428.154 o
grestore
/LiberationSans 25.000 selectfont
gsave

314.098 39.6844 translate
0 rotate
0 0 m /six glyphshow
13.9038 0 m /zero glyphshow
27.8076 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
462.804 67.7938 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
462.804 428.154 o
grestore
/LiberationSans 25.000 selectfont
gsave

441.945 39.6844 translate
0 rotate
0 0 m /nine glyphshow
13.9038 0 m /zero glyphshow
27.8076 0 m /zero glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
111.224 67.7938 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
111.224 428.154 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
143.186 67.7938 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
143.186 428.154 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
175.148 67.7938 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
175.148 428.154 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
239.072 67.7938 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
239.072 428.154 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
271.033 67.7938 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
271.033 428.154 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
302.995 67.7938 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
302.995 428.154 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
366.919 67.7938 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
366.919 428.154 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
398.881 67.7938 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
398.881 428.154 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
430.842 67.7938 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
430.842 428.154 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
494.766 67.7938 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
494.766 428.154 o
grestore
/LiberationSans 25.000 selectfont
gsave

212.127 12.3875 translate
0 rotate
0 0 m /T glyphshow
12.521 0 m /e glyphshow
26.4248 0 m /m glyphshow
47.25 0 m /p glyphshow
61.1538 0 m /e glyphshow
75.0576 0 m /r glyphshow
83.3828 0 m /a glyphshow
97.2866 0 m /t glyphshow
104.232 0 m /u glyphshow
118.136 0 m /r glyphshow
126.461 0 m /e glyphshow
140.365 0 m /space glyphshow
147.311 0 m /parenleft glyphshow
155.636 0 m /K glyphshow
172.311 0 m /parenright glyphshow
grestore
2.500 setlinewidth
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2625 78.9738 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.663 78.9738 o
grestore
/LiberationSans 25.000 selectfont
gsave

34.4969 69.9191 translate
0 rotate
0 0 m /zero glyphshow
13.9038 0 m /period glyphshow
20.8496 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2625 130.974 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.663 130.974 o
grestore
/LiberationSans 25.000 selectfont
gsave

34.4969 121.919 translate
0 rotate
0 0 m /zero glyphshow
13.9038 0 m /period glyphshow
20.8496 0 m /five glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2625 182.974 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.663 182.974 o
grestore
/LiberationSans 25.000 selectfont
gsave

34.4969 173.919 translate
0 rotate
0 0 m /one glyphshow
13.9038 0 m /period glyphshow
20.8496 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2625 234.974 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.663 234.974 o
grestore
/LiberationSans 25.000 selectfont
gsave

34.4969 225.919 translate
0 rotate
0 0 m /one glyphshow
13.9038 0 m /period glyphshow
20.8496 0 m /five glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2625 286.974 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.663 286.974 o
grestore
/LiberationSans 25.000 selectfont
gsave

34.4969 277.919 translate
0 rotate
0 0 m /two glyphshow
13.9038 0 m /period glyphshow
20.8496 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2625 338.974 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.663 338.974 o
grestore
/LiberationSans 25.000 selectfont
gsave

34.4969 329.919 translate
0 rotate
0 0 m /two glyphshow
13.9038 0 m /period glyphshow
20.8496 0 m /five glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2625 390.974 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.663 390.974 o
grestore
/LiberationSans 25.000 selectfont
gsave

34.4969 381.919 translate
0 rotate
0 0 m /three glyphshow
13.9038 0 m /period glyphshow
20.8496 0 m /zero glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2625 68.5738 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.663 68.5738 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2625 89.3738 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.663 89.3738 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2625 99.7738 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.663 99.7738 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2625 110.174 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.663 110.174 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2625 120.574 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.663 120.574 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2625 141.374 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.663 141.374 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2625 151.774 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.663 151.774 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2625 162.174 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.663 162.174 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2625 172.574 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.663 172.574 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2625 193.374 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.663 193.374 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2625 203.774 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.663 203.774 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2625 214.174 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.663 214.174 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2625 224.574 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.663 224.574 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2625 245.374 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.663 245.374 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2625 255.774 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.663 255.774 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2625 266.174 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.663 266.174 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2625 276.574 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.663 276.574 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2625 297.374 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.663 297.374 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2625 307.774 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.663 307.774 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2625 318.174 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.663 318.174 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2625 328.574 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.663 328.574 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2625 349.374 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.663 349.374 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2625 359.774 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.663 359.774 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2625 370.174 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.663 370.174 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2625 380.574 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.663 380.574 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2625 401.374 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.663 401.374 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2625 411.774 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.663 411.774 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2625 422.174 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.663 422.174 o
grestore
/LiberationSans 25.000 selectfont
gsave

25.3094 117.364 translate
90 rotate
0 0 m /C glyphshow
18.0542 0 m /o glyphshow
31.958 0 m /e glyphshow
45.8618 0 m /r glyphshow
54.187 0 m /c glyphshow
66.687 0 m /i glyphshow
72.2412 0 m /v glyphshow
84.7412 0 m /e glyphshow
98.645 0 m /space glyphshow
105.591 0 m /F glyphshow
120.862 0 m /i glyphshow
126.416 0 m /e glyphshow
140.32 0 m /l glyphshow
145.874 0 m /d glyphshow
159.778 0 m /space glyphshow
166.724 0 m /parenleft glyphshow
175.049 0 m /M glyphshow
195.874 0 m /V glyphshow
212.549 0 m /slash glyphshow
219.495 0 m /c glyphshow
231.995 0 m /m glyphshow
252.82 0 m /parenright glyphshow
grestore
2 setlinecap
0.031 0.659 0.255 setrgbcolor
gsave
446.4 360.36 79.263 67.794 clipbox
100.570376 411.77375 m
121.878252 390.97375 l
164.494004 370.17375 l
207.109755 328.57375 l
249.725507 307.77375 l
292.341259 266.17375 l
334.957011 245.37375 l
377.572763 224.57375 l
420.188514 203.77375 l
505.420018 141.37375 l
stroke
grestore
1.000 setlinewidth
0 setlinecap
gsave
446.4 360.36 79.263 67.794 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
100.57 411.774 o
121.878 390.974 o
164.494 370.174 o
207.11 328.574 o
249.726 307.774 o
292.341 266.174 o
334.957 245.374 o
377.573 224.574 o
420.189 203.774 o
505.42 141.374 o
grestore
2.500 setlinewidth
0 setlinejoin
2 setlinecap
0.000 setgray
gsave
79.2625 67.79375 m
79.2625 428.15375 l
stroke
grestore
gsave
525.6625 67.79375 m
525.6625 428.15375 l
stroke
grestore
gsave
79.2625 67.79375 m
525.6625 67.79375 l
stroke
grestore
gsave
79.2625 428.15375 m
525.6625 428.15375 l
stroke
grestore
1.500 setlinewidth
1 setlinejoin
0.031 0.659 0.255 setrgbcolor
gsave
411.63125 402.65375 m
431.63125 402.65375 l
451.63125 402.65375 l
stroke
grestore
1.000 setlinewidth
0 setlinecap
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
431.631 402.654 o
grestore
0.000 setgray
gsave
467.631 395.654 translate
0 rotate
/LiberationSans 20.0 selectfont
0 0.234375 moveto
/E glyphshow
/LiberationSans 14.0 selectfont
13.5247 -2.93437 moveto
/c glyphshow
grestore
0.031 0.416 0.416 setrgbcolor
gsave
426.04108 381.929958 m
420.45091 372.2475 l
426.04108 362.565042 l
437.22142 362.565042 l
442.81159 372.2475 l
437.22142 381.929958 l
426.04108 381.929958 l
cl
stroke
grestore
0.000 setgray
/LiberationSans 20.000 selectfont
gsave

467.631 366.998 translate
0 rotate
0 0 m /E glyphshow
13.3398 0 m /x glyphshow
23.3398 0 m /p glyphshow
34.4629 0 m /period glyphshow
grestore
0.031 0.416 0.416 setrgbcolor
gsave
431.63125 354.77159 m
429.121107 347.046165 l
420.998115 347.046165 l
427.569754 342.27159 l
425.059611 334.546165 l
431.63125 339.32074 l
438.202889 334.546165 l
435.692746 342.27159 l
442.264385 347.046165 l
434.141393 347.046165 l
431.63125 354.77159 l
cl
stroke
grestore
0.000 setgray
/LiberationSans 20.000 selectfont
gsave

467.631 338.341 translate
0 rotate
0 0 m /E glyphshow
13.3398 0 m /x glyphshow
23.3398 0 m /p glyphshow
34.4629 0 m /period glyphshow
grestore

end
showpage
