%!PS-Adobe-3.0 EPSF-3.0
%%Title: temp_vs_afd_111.eps
%%Creator: Mat<PERSON>lotlib v3.7.1, https://matplotlib.org/
%%CreationDate: Wed Apr 24 13:19:32 2024
%%Orientation: portrait
%%BoundingBox: 24 174 588 618
%%HiResBoundingBox: 24.350000 174.577031 587.650000 617.422969
%%EndComments
%%BeginProlog
/mpldict 11 dict def
mpldict begin
/_d { bind def } bind def
/m { moveto } _d
/l { lineto } _d
/r { rlineto } _d
/c { curveto } _d
/cl { closepath } _d
/ce { closepath eofill } _d
/box {
      m
      1 index 0 r
      0 exch r
      neg 0 r
      cl
    } _d
/clipbox {
      box
      clip
      newpath
    } _d
/sc { setcachedevice } _d
%!PS-TrueTypeFont-1.0-2.53740
%%Title: unknown
%%Creator: Converted from TrueType to type 42 by PPR
15 dict begin
/FontName /TimesNewRomanPSMT def
/PaintType 0 def
/FontMatrix[1 0 0 1 0 0]def
/FontBBox[-568 -307 2028 1007]def
/FontType 42 def
/Encoding StandardEncoding def
/FontInfo 10 dict dup begin
/FamilyName (unknown) def
/FullName (unknown) def
/Weight (unknown) def
/Version (unknown) def
/ItalicAngle 0.0 def
/isFixedPitch false def
/UnderlinePosition -223 def
/UnderlineThickness 100 def
end readonly def
/sfnts[<00010000000900800003001063767420F34DDA810000009C000007C46670676D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>]def
/CharStrings 34 dict dup begin
/.notdef 0 def
/minus 34 def
/space 3 def
/parenleft 4 def
/parenright 5 def
/comma 6 def
/period 7 def
/slash 8 def
/zero 9 def
/two 10 def
/four 11 def
/five 12 def
/six 13 def
/seven 14 def
/eight 15 def
/nine 16 def
/less 17 def
/greater 18 def
/E 19 def
/F 20 def
/omega 35 def
/M 21 def
/V 22 def
/c 23 def
/d 24 def
/e 25 def
/i 26 def
/l 27 def
/m 28 def
/r 29 def
/t 30 def
/x 31 def
/y 32 def
/z 33 def
end readonly def

systemdict/resourcestatus known
 {42 /FontType resourcestatus
   {pop pop false}{true}ifelse}
 {true}ifelse
{/TrueDict where{pop}{(%%[ Error: no TrueType rasterizer ]%%)= flush}ifelse
/FontType 3 def
 /TrueState 271 string def
 TrueDict begin sfnts save
 72 0 matrix defaultmatrix dtransform dup
 mul exch dup mul add sqrt cvi 0 72 matrix
 defaultmatrix dtransform dup mul exch dup
 mul add sqrt cvi 3 -1 roll restore
 TrueState initer end
 /BuildGlyph{exch begin
  CharStrings dup 2 index known
    {exch}{exch pop /.notdef}ifelse
  get dup xcheck
    {currentdict systemdict begin begin exec end end}
    {TrueDict begin /bander load cvlit exch TrueState render end}
    ifelse
 end}bind def
 /BuildChar{
  1 index /Encoding get exch get
  1 index /BuildGlyph get exec
 }bind def
}if

FontName currentdict end definefont pop
end
%%EndProlog
mpldict begin
24.35 174.577 translate
563.3 442.846 0 0 clipbox
gsave
0 -0 m
563.3 -0 l
563.3 442.845938 l
0 442.845938 l
cl
1.000 setgray
fill
grestore
gsave
103.45 66.60625 m
549.85 66.60625 l
549.85 426.96625 l
103.45 426.96625 l
cl
1.000 setgray
fill
grestore
2.500 setlinewidth
1 setlinejoin
0 setlinecap
[] 0 setdash
0.000 setgray
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

90.1531 39.2469 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /four glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
215.05 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
215.05 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

201.753 39.2469 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /two glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
326.65 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
326.65 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

320.4 39.2469 translate
0 rotate
0 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
438.25 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
438.25 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

432 39.2469 translate
0 rotate
0 0 m /two glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
549.85 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
549.85 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

543.6 39.2469 translate
0 rotate
0 0 m /four glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
131.35 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
131.35 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
159.25 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
159.25 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
187.15 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
187.15 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
242.95 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
242.95 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
270.85 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
270.85 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
298.75 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
298.75 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
354.55 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
354.55 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
382.45 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
382.45 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
410.35 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
410.35 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
466.15 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
466.15 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
494.05 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
494.05 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
521.95 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
521.95 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

208.603 12.5438 translate
0 rotate
0 0 m /E glyphshow
15.271 0 m /l glyphshow
22.2168 0 m /e glyphshow
33.313 0 m /c glyphshow
44.4092 0 m /t glyphshow
51.355 0 m /r glyphshow
59.6802 0 m /i glyphshow
66.626 0 m /c glyphshow
77.7222 0 m /space glyphshow
83.9722 0 m /F glyphshow
97.876 0 m /i glyphshow
104.822 0 m /e glyphshow
115.918 0 m /l glyphshow
122.864 0 m /d glyphshow
135.364 0 m /space glyphshow
141.614 0 m /parenleft glyphshow
149.939 0 m /M glyphshow
172.168 0 m /V glyphshow
190.222 0 m /slash glyphshow
197.168 0 m /c glyphshow
208.264 0 m /m glyphshow
227.71 0 m /parenright glyphshow
grestore
2.500 setlinewidth
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
549.85 66.6062 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

37.2 57.9266 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /five glyphshow
43.75 0 m /four glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 138.678 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
549.85 138.678 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

37.2 129.999 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /five glyphshow
43.75 0 m /five glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 210.75 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
549.85 210.75 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

37.2 202.071 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /five glyphshow
43.75 0 m /six glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 282.822 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
549.85 282.822 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

37.2 274.143 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /five glyphshow
43.75 0 m /seven glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 354.894 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
549.85 354.894 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

37.2 346.215 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /five glyphshow
43.75 0 m /eight glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
549.85 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

37.2 418.287 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /five glyphshow
43.75 0 m /nine glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 81.0206 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
549.85 81.0206 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 95.435 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
549.85 95.435 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 109.849 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
549.85 109.849 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 124.264 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
549.85 124.264 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 153.093 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
549.85 153.093 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 167.507 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
549.85 167.507 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 181.921 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
549.85 181.921 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 196.336 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
549.85 196.336 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 225.165 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
549.85 225.165 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 239.579 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
549.85 239.579 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 253.993 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
549.85 253.993 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 268.408 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
549.85 268.408 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 297.237 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
549.85 297.237 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 311.651 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
549.85 311.651 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 326.065 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
549.85 326.065 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 340.48 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
549.85 340.48 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 369.309 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
549.85 369.309 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 383.723 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
549.85 383.723 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 398.137 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
549.85 398.137 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 412.552 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
549.85 412.552 o
grestore
gsave
22.2 203.786 translate
90 rotate
/TimesNewRomanPSMT 25.0 selectfont
0 0.6875 moveto
/less glyphshow
14.0991 0.6875 moveto
/omega glyphshow
/TimesNewRomanPSMT 17.5 selectfont
30.9299 -5.75313 moveto
/x glyphshow
39.6799 -5.75313 moveto
/comma glyphshow
46.7009 -5.75313 moveto
/y glyphshow
55.4509 -5.75313 moveto
/comma glyphshow
62.4719 -5.75313 moveto
/z glyphshow
/TimesNewRomanPSMT 25.0 selectfont
71.3127 0.6875 moveto
/greater glyphshow
grestore
0.500 setlinewidth
[1.85 0.8] 0 setdash
0.031 0.235 0.235 setrgbcolor
gsave
446.4 360.36 103.45 66.606 clipbox
538.69 117.909982 m
527.53 124.649435 l
516.37 132.382761 l
505.21 139.590682 l
494.05 147.658421 l
482.89 152.670308 l
471.73 159.045797 l
460.57 167.581284 l
449.41 175.707402 l
438.25 180.751001 l
427.09 188.606128 l
415.93 194.708464 l
404.77 202.779087 l
393.61 208.705567 l
382.45 220.443213 l
371.29 225.870235 l
360.13 234.019416 l
348.97 241.766435 l
337.81 249.917778 l
326.65 260.18011 l
315.49 269.005327 l
304.33 277.363517 l
293.17 284.84459 l
282.01 296.410705 l
270.85 306.544028 l
259.69 317.311585 l
248.53 327.311575 l
237.37 342.706154 l
226.21 351.826866 l
215.05 360.45028 l
203.89 379.96954 l
192.73 399.89889 l
181.57 160.804354 l
170.41 152.695533 l
159.25 145.893378 l
148.09 139.816988 l
136.93 133.628886 l
125.77 127.076099 l
114.61 119.671422 l
103.45 114.061338 l
114.61 121.157547 l
125.77 125.860966 l
136.93 132.586004 l
148.09 140.645816 l
159.25 146.267432 l
170.41 152.858416 l
181.57 159.104176 l
192.73 166.615519 l
203.89 173.77299 l
215.05 181.237487 l
226.21 188.443966 l
237.37 196.357472 l
248.53 204.020167 l
259.69 212.379798 l
270.85 218.499431 l
282.01 227.203567 l
293.17 235.067343 l
304.33 242.034543 l
315.49 250.771832 l
326.65 259.206418 l
337.81 267.249653 l
348.97 277.36712 l
360.13 286.629093 l
371.29 297.182596 l
382.45 303.408896 l
393.61 315.629424 l
404.77 326.440945 l
415.93 338.100033 l
427.09 353.379297 l
438.25 363.996223 l
449.41 376.044499 l
460.57 400.074025 l
471.73 160.072823 l
482.89 152.693371 l
494.05 146.609774 l
505.21 140.174465 l
516.37 133.255553 l
527.53 126.272497 l
538.69 118.340252 l
549.85 115.209445 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
446.4 360.36 103.45 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

0 2 m
-2 -2 l
2 -2 l
cl

gsave
0.031 0.235 0.235 setrgbcolor
fill
grestore
stroke
grestore
} bind def
538.69 117.91 o
527.53 124.649 o
516.37 132.383 o
505.21 139.591 o
494.05 147.658 o
482.89 152.67 o
471.73 159.046 o
460.57 167.581 o
449.41 175.707 o
438.25 180.751 o
427.09 188.606 o
415.93 194.708 o
404.77 202.779 o
393.61 208.706 o
382.45 220.443 o
371.29 225.87 o
360.13 234.019 o
348.97 241.766 o
337.81 249.918 o
326.65 260.18 o
315.49 269.005 o
304.33 277.364 o
293.17 284.845 o
282.01 296.411 o
270.85 306.544 o
259.69 317.312 o
248.53 327.312 o
237.37 342.706 o
226.21 351.827 o
215.05 360.45 o
203.89 379.97 o
192.73 399.899 o
181.57 160.804 o
170.41 152.696 o
159.25 145.893 o
148.09 139.817 o
136.93 133.629 o
125.77 127.076 o
114.61 119.671 o
103.45 114.061 o
114.61 121.158 o
125.77 125.861 o
136.93 132.586 o
148.09 140.646 o
159.25 146.267 o
170.41 152.858 o
181.57 159.104 o
192.73 166.616 o
203.89 173.773 o
215.05 181.237 o
226.21 188.444 o
237.37 196.357 o
248.53 204.02 o
259.69 212.38 o
270.85 218.499 o
282.01 227.204 o
293.17 235.067 o
304.33 242.035 o
315.49 250.772 o
326.65 259.206 o
337.81 267.25 o
348.97 277.367 o
360.13 286.629 o
371.29 297.183 o
382.45 303.409 o
393.61 315.629 o
404.77 326.441 o
415.93 338.1 o
427.09 353.379 o
438.25 363.996 o
449.41 376.044 o
460.57 400.074 o
471.73 160.073 o
482.89 152.693 o
494.05 146.61 o
505.21 140.174 o
516.37 133.256 o
527.53 126.272 o
538.69 118.34 o
549.85 115.209 o
grestore
0.500 setlinewidth
1 setlinejoin
[1.85 0.8] 0 setdash
0.031 0.416 0.416 setrgbcolor
gsave
446.4 360.36 103.45 66.606 clipbox
538.69 119.469621 m
527.53 125.901326 l
516.37 133.558255 l
505.21 138.954286 l
494.05 144.502388 l
482.89 153.717514 l
471.73 161.46093 l
460.57 165.928673 l
449.41 173.514251 l
438.25 181.187757 l
427.09 188.270993 l
415.93 197.905578 l
404.77 203.131519 l
393.61 211.779438 l
382.45 219.697268 l
371.29 226.460505 l
360.13 233.848605 l
348.97 243.81328 l
337.81 251.582642 l
326.65 260.389119 l
315.49 268.370373 l
304.33 278.688921 l
293.17 287.581164 l
282.01 294.871968 l
270.85 305.419705 l
259.69 313.78366 l
248.53 329.18833 l
237.37 337.380754 l
226.21 349.982543 l
215.05 367.269012 l
203.89 383.7735 l
192.73 399.026818 l
181.57 159.224536 l
170.41 152.369047 l
159.25 146.90599 l
148.09 139.902753 l
136.93 132.125464 l
125.77 126.838262 l
114.61 119.425657 l
103.45 114.450526 l
114.61 119.771602 l
125.77 125.796101 l
136.93 131.995734 l
148.09 138.611944 l
159.25 146.965809 l
170.41 152.37193 l
181.57 159.950301 l
192.73 167.671374 l
203.89 175.876771 l
215.05 181.932261 l
226.21 188.129011 l
237.37 195.716031 l
248.53 203.829897 l
259.69 210.507367 l
270.85 218.964296 l
282.01 227.886809 l
293.17 234.255091 l
304.33 243.040668 l
315.49 249.888229 l
326.65 262.201009 l
337.81 270.226947 l
348.97 277.371445 l
360.13 286.667291 l
371.29 295.191247 l
382.45 309.220061 l
393.61 315.511947 l
404.77 327.680584 l
415.93 338.551924 l
427.09 350.456777 l
438.25 368.855317 l
449.41 383.114042 l
460.57 401.16015 l
471.73 160.486516 l
482.89 152.40148 l
494.05 147.434277 l
505.21 139.644736 l
516.37 133.618075 l
527.53 125.606551 l
538.69 118.044036 l
549.85 114.154311 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
446.4 360.36 103.45 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-5 -5 m
5 -5 l
5 5 l
-5 5 l
cl

gsave
grestore
stroke
grestore
} bind def
538.69 119.47 o
527.53 125.901 o
516.37 133.558 o
505.21 138.954 o
494.05 144.502 o
482.89 153.718 o
471.73 161.461 o
460.57 165.929 o
449.41 173.514 o
438.25 181.188 o
427.09 188.271 o
415.93 197.906 o
404.77 203.132 o
393.61 211.779 o
382.45 219.697 o
371.29 226.461 o
360.13 233.849 o
348.97 243.813 o
337.81 251.583 o
326.65 260.389 o
315.49 268.37 o
304.33 278.689 o
293.17 287.581 o
282.01 294.872 o
270.85 305.42 o
259.69 313.784 o
248.53 329.188 o
237.37 337.381 o
226.21 349.983 o
215.05 367.269 o
203.89 383.774 o
192.73 399.027 o
181.57 159.225 o
170.41 152.369 o
159.25 146.906 o
148.09 139.903 o
136.93 132.125 o
125.77 126.838 o
114.61 119.426 o
103.45 114.451 o
114.61 119.772 o
125.77 125.796 o
136.93 131.996 o
148.09 138.612 o
159.25 146.966 o
170.41 152.372 o
181.57 159.95 o
192.73 167.671 o
203.89 175.877 o
215.05 181.932 o
226.21 188.129 o
237.37 195.716 o
248.53 203.83 o
259.69 210.507 o
270.85 218.964 o
282.01 227.887 o
293.17 234.255 o
304.33 243.041 o
315.49 249.888 o
326.65 262.201 o
337.81 270.227 o
348.97 277.371 o
360.13 286.667 o
371.29 295.191 o
382.45 309.22 o
393.61 315.512 o
404.77 327.681 o
415.93 338.552 o
427.09 350.457 o
438.25 368.855 o
449.41 383.114 o
460.57 401.16 o
471.73 160.487 o
482.89 152.401 o
494.05 147.434 o
505.21 139.645 o
516.37 133.618 o
527.53 125.607 o
538.69 118.044 o
549.85 114.154 o
grestore
0.500 setlinewidth
1 setlinejoin
[1.85 0.8] 0 setdash
0.031 0.659 0.659 setrgbcolor
gsave
446.4 360.36 103.45 66.606 clipbox
538.69 119.360071 m
527.53 125.466732 l
516.37 132.022401 l
505.21 139.538069 l
494.05 146.449053 l
482.89 152.665263 l
471.73 158.68832 l
460.57 167.649032 l
449.41 172.579477 l
438.25 182.490819 l
427.09 188.615497 l
415.93 197.112786 l
404.77 203.891878 l
393.61 212.158537 l
382.45 216.807181 l
371.29 226.837441 l
360.13 234.350947 l
348.97 241.782291 l
337.81 252.395614 l
326.65 260.388399 l
315.49 267.996319 l
304.33 277.9473 l
293.17 287.819722 l
282.01 298.500072 l
270.85 305.031237 l
259.69 318.093566 l
248.53 326.110855 l
237.37 337.605619 l
226.21 352.60236 l
215.05 365.373519 l
203.89 379.815306 l
192.73 402.15114 l
181.57 160.934804 l
170.41 153.764361 l
159.25 147.85734 l
148.09 137.484017 l
136.93 132.539878 l
125.77 126.469253 l
114.61 119.67935 l
103.45 112.374132 l
114.61 119.747818 l
125.77 126.247271 l
136.93 132.861319 l
148.09 138.876448 l
159.25 146.213378 l
170.41 152.970848 l
181.57 161.339849 l
192.73 166.983807 l
203.89 173.480377 l
215.05 182.553521 l
226.21 188.446849 l
237.37 195.997112 l
248.53 202.857645 l
259.69 211.974033 l
270.85 217.71745 l
282.01 226.388433 l
293.17 233.712389 l
304.33 240.776887 l
315.49 253.341198 l
326.65 258.597409 l
337.81 267.295779 l
348.97 276.41577 l
360.13 288.676659 l
371.29 296.168543 l
382.45 304.444571 l
393.61 316.112307 l
404.77 327.701484 l
415.93 340.087058 l
427.09 348.412094 l
438.25 363.919106 l
449.41 383.05278 l
460.57 400.831501 l
471.73 161.62093 l
482.89 152.31139 l
494.05 146.27608 l
505.21 138.33807 l
516.37 132.338076 l
527.53 127.312496 l
538.69 119.271423 l
549.85 111.327647 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
446.4 360.36 103.45 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -5 m
1.326016 -5 2.597899 -4.473168 3.535534 -3.535534 c
4.473168 -2.597899 5 -1.326016 5 0 c
5 1.326016 4.473168 2.597899 3.535534 3.535534 c
2.597899 4.473168 1.326016 5 0 5 c
-1.326016 5 -2.597899 4.473168 -3.535534 3.535534 c
-4.473168 2.597899 -5 1.326016 -5 0 c
-5 -1.326016 -4.473168 -2.597899 -3.535534 -3.535534 c
-2.597899 -4.473168 -1.326016 -5 0 -5 c
cl

gsave
grestore
stroke
grestore
} bind def
538.69 119.36 o
527.53 125.467 o
516.37 132.022 o
505.21 139.538 o
494.05 146.449 o
482.89 152.665 o
471.73 158.688 o
460.57 167.649 o
449.41 172.579 o
438.25 182.491 o
427.09 188.615 o
415.93 197.113 o
404.77 203.892 o
393.61 212.159 o
382.45 216.807 o
371.29 226.837 o
360.13 234.351 o
348.97 241.782 o
337.81 252.396 o
326.65 260.388 o
315.49 267.996 o
304.33 277.947 o
293.17 287.82 o
282.01 298.5 o
270.85 305.031 o
259.69 318.094 o
248.53 326.111 o
237.37 337.606 o
226.21 352.602 o
215.05 365.374 o
203.89 379.815 o
192.73 402.151 o
181.57 160.935 o
170.41 153.764 o
159.25 147.857 o
148.09 137.484 o
136.93 132.54 o
125.77 126.469 o
114.61 119.679 o
103.45 112.374 o
114.61 119.748 o
125.77 126.247 o
136.93 132.861 o
148.09 138.876 o
159.25 146.213 o
170.41 152.971 o
181.57 161.34 o
192.73 166.984 o
203.89 173.48 o
215.05 182.554 o
226.21 188.447 o
237.37 195.997 o
248.53 202.858 o
259.69 211.974 o
270.85 217.717 o
282.01 226.388 o
293.17 233.712 o
304.33 240.777 o
315.49 253.341 o
326.65 258.597 o
337.81 267.296 o
348.97 276.416 o
360.13 288.677 o
371.29 296.169 o
382.45 304.445 o
393.61 316.112 o
404.77 327.701 o
415.93 340.087 o
427.09 348.412 o
438.25 363.919 o
449.41 383.053 o
460.57 400.832 o
471.73 161.621 o
482.89 152.311 o
494.05 146.276 o
505.21 138.338 o
516.37 132.338 o
527.53 127.312 o
538.69 119.271 o
549.85 111.328 o
grestore
2.500 setlinewidth
0 setlinejoin
2 setlinecap
0.000 setgray
gsave
103.45 66.60625 m
103.45 426.96625 l
stroke
grestore
gsave
549.85 66.60625 m
549.85 426.96625 l
stroke
grestore
gsave
103.45 66.60625 m
549.85 66.60625 l
stroke
grestore
gsave
103.45 426.96625 m
549.85 426.96625 l
stroke
grestore
0.500 setlinewidth
1 setlinejoin
0 setlinecap
[1.85 0.8] 0 setdash
0.031 0.235 0.235 setrgbcolor
gsave
287.65 160.60625 m
307.65 160.60625 l
327.65 160.60625 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

0 2 m
-2 -2 l
2 -2 l
cl

gsave
0.031 0.235 0.235 setrgbcolor
fill
grestore
stroke
grestore
} bind def
307.65 160.606 o
grestore
0.000 setgray
gsave
343.65 153.606 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.796875 moveto
/omega glyphshow
/TimesNewRomanPSMT 14.0 selectfont
13.4646 -4.35562 moveto
/x glyphshow
grestore
0.500 setlinewidth
1 setlinejoin
[1.85 0.8] 0 setdash
0.031 0.416 0.416 setrgbcolor
gsave
287.65 130.60625 m
307.65 130.60625 l
327.65 130.60625 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-5 -5 m
5 -5 l
5 5 l
-5 5 l
cl

gsave
grestore
stroke
grestore
} bind def
307.65 130.606 o
grestore
0.000 setgray
gsave
343.65 123.606 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.796875 moveto
/omega glyphshow
/TimesNewRomanPSMT 14.0 selectfont
13.4646 -4.35562 moveto
/y glyphshow
grestore
0.500 setlinewidth
1 setlinejoin
[1.85 0.8] 0 setdash
0.031 0.659 0.659 setrgbcolor
gsave
287.65 97.60625 m
307.65 97.60625 l
327.65 97.60625 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -5 m
1.326016 -5 2.597899 -4.473168 3.535534 -3.535534 c
4.473168 -2.597899 5 -1.326016 5 0 c
5 1.326016 4.473168 2.597899 3.535534 3.535534 c
2.597899 4.473168 1.326016 5 0 5 c
-1.326016 5 -2.597899 4.473168 -3.535534 3.535534 c
-4.473168 2.597899 -5 1.326016 -5 0 c
-5 -1.326016 -4.473168 -2.597899 -3.535534 -3.535534 c
-2.597899 -4.473168 -1.326016 -5 0 -5 c
cl

gsave
grestore
stroke
grestore
} bind def
307.65 97.6062 o
grestore
0.000 setgray
gsave
343.65 90.6062 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.796875 moveto
/omega glyphshow
/TimesNewRomanPSMT 14.0 selectfont
13.4646 -4.35562 moveto
/z glyphshow
grestore

end
showpage
