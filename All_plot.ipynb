import yaml
import matplotlib.pyplot as plt



# Load the band structure data from the band.yml file
file_path = '/home/<USER>/Documents/Work/Parameter/BiAlO/results/phonon_dispersion/dfpt/'
with open(f'{file_path}/band.yaml', 'r') as file:
    band_data = yaml.safe_load(file)

# Extract qpoints and frequencies
qpoints = []
frequencies = []

for branch in band_data.get('phonon', []):
    qpoints.extend(branch['q-position'])
    frequencies.extend([mode['frequency'] for mode in branch.get('band', [])])

# Get the number of bands
num_bands = len(band_data['phonon'][0]['band'])

# Plot each band separately
plt.figure(figsize=(10, 6))
for i in range(num_bands):
    band_frequencies = frequencies[i::num_bands]
    plt.plot(band_frequencies, linestyle='-')

# Set labels and title
plt.ylabel('Frequency (THz)')
plt.title('Phonon Band Structure')

# Add legend
plt.legend()

# Add grid lines
plt.grid(True)

# Remove x-axis labels
# plt.xticks([])

# Add labels based on qpoints
# labels = [r'$\Gamma$', 'X', 'M', r'$\Gamma$', 'R', 'X']

# for i, label in enumerate(labels):
#     plt.text(qpoints[i], -2, label, ha='center', va='center', fontsize=12)

# Save the plot as an EPS file
# plt.savefig('phonon_band_structure_with_labels.eps', format='eps', bbox_inches='tight')

# Show the plot
plt.show()



import yaml
import matplotlib.pyplot as plt

# Load the band structure data from the band.yml file
file_path = '/home/<USER>/Documents/Work/Parameter/BiAlO/results/phonon_dispersion/dfpt/'
with open(f'{file_path}/band.yaml', 'r') as file:
    band_data = yaml.safe_load(file)

# Load the qpoint path labels from band.conf
band_conf_path = f'{file_path}/band.conf'
with open(band_conf_path, 'r') as conf_file:
    for line in conf_file:
        if line.startswith("BAND ="):
            qpoint_path = list(map(float, line.split()[2:]))
        elif line.startswith("BAND_LABELS ="):
            labels = line.split()[2:]

# Extract qpoints and frequencies
qpoints = []
frequencies = []

for branch in band_data.get('phonon', []):
    qpoints.extend(branch['q-position'])
    frequencies.extend([mode['frequency'] for mode in branch.get('band', [])])

# Ensure the number of qpoints and frequencies match
num_qpoints = len(qpoints)
num_frequencies = len(frequencies)

# Plot each band separately
plt.figure(figsize=(10, 6))
for i in range(num_frequencies // num_qpoints):
    start_idx = i * num_qpoints
    end_idx = (i + 1) * num_qpoints
    band_frequencies = frequencies[start_idx:end_idx]
    plt.plot(qpoint_path, band_frequencies, linestyle='-', label=f'Band {i + 1}')

# Set labels and title
plt.ylabel('Frequency (THz)')
plt.title('Phonon Band Structure')

# Add legend
plt.legend()

# Add grid lines
plt.grid(True)

# Set x-axis labels based on qpoint path
plt.xticks(qpoint_path, labels)

# Save the plot as an EPS file
# plt.savefig('phonon_band_structure_with_labels.eps', format='eps', bbox_inches='tight')

# Show the plot
plt.show()


import matplotlib.pyplot as plt
import numpy as np

file_path = '/home/<USER>/Documents/Work/Parameter/BiAlO/results/phonon_dispersion/dfpt/'

with open(f'{file_path}/band.dat', 'r') as file:
    # Read lines excluding those starting with '#'
    lines = [line.strip() for line in file.readlines() if not line.startswith('#')]

# Filter out lines with inconsistent column counts
filtered_lines = [line for line in lines if len(line.split()) == 2]

# Split the lines into columns and convert to floats
data = np.array([list(map(float, line.split())) for line in filtered_lines])

# Separate X and Y data
x_data = data[:, 0]
y_data = data[:, 1]

# Plot the data as a scatter plot
plt.scatter(x_data, y_data, marker='+', color='b')
plt.xlabel('Wave Vector')
plt.ylabel('Frequency (THz)')
plt.title('Phonon Dispersion')
plt.show()


import matplotlib.pyplot as plt
import numpy as np

file_path = '/home/<USER>/Documents/Work/Parameter/BiAlO/results/phonon_dispersion/dfpt/'

with open(f'{file_path}/band.dat', 'r') as file:
    # Read lines excluding those starting with '#'
    lines = [line.strip() for line in file.readlines() if not line.startswith('#')]

# Filter out lines with inconsistent column counts
filtered_lines = [line for line in lines if len(line.split()) == 2]

# Split the lines into columns and convert to floats
data = np.array([list(map(float, line.split())) for line in filtered_lines])

# Separate X and Y data
x_data = data[:, 0]
y_data = data[:, 1]

# Identify the positions where there is a change in q point
change_positions = np.where(np.diff(x_data) < 0)[0]

# Add NaN values at the change positions
x_data_with_nan = np.insert(x_data, change_positions + 1, np.nan)
y_data_with_nan = np.insert(y_data, change_positions + 1, np.nan)

# Plot the data as a line plot
plt.plot(x_data_with_nan, y_data_with_nan, linestyle='-',  color='b')
plt.xlabel('Wave Vector')
plt.ylabel('Frequency (THz)')
plt.title('Phonon Dispersion')
plt.show()


import matplotlib.pyplot as plt
import numpy as np

file_path = '/home/<USER>/Documents/Work/Parameter/BiAlO/results/phonon_dispersion/dfpt/'

with open(f'{file_path}/band.dat', 'r') as file:
    # Read all lines
    all_lines = file.readlines()

# Extract the second line containing end points
end_points_line = all_lines[1]

# Extract numerical values from the line
end_points = [float(value) for value in end_points_line.split()[1:]]

# Plot the data as a scatter plot
plt.scatter(x_data, y_data, marker='o', color='b')

# Plot vertical lines at the end points
for pos in end_points:
    plt.axvline(x=pos, color='r', linestyle='--')

plt.xlabel('Wave Vector')
plt.ylabel('Frequency (THz)')
plt.title('Phonon Dispersion')
plt.show()


import matplotlib.pyplot as plt
import numpy as np

file_path = '/home/<USER>/Documents/Work/Parameter/BiAlO/results/phonon_dispersion/dfpt/'

with open(f'{file_path}/band.dat', 'r') as file:
    # Read all lines
    all_lines = file.readlines()

# Extract the second line containing end points
end_points_line = all_lines[1]

# Extract numerical values from the line
end_points = [float(value) for value in end_points_line.split()[1:]]

# Read band.conf file to get alpha xtick labels
with open(f'{file_path}/band.conf', 'r') as band_conf_file:
    # Read lines starting with 'BAND_LABELS'
    band_labels_line = next(line for line in band_conf_file if line.startswith('BAND_LABELS'))

# Extract alpha labels
alpha_labels = band_labels_line.split('=')[-1].strip().split()

# Plot the data as a scatter plot
plt.scatter(x_data, y_data, marker='o', color='b')

# Plot vertical lines at the end points
for pos in end_points:
    plt.axvline(x=pos, color='r', linestyle='--')

# Set alpha xtick labels
plt.xticks(end_points, alpha_labels)

# plt.xlabel('Wave Vector')
plt.ylabel('Frequency (THz)')
# plt.title('Phonon Dispersion')
plt.show()


import tp
import numpy as np
import matplotlib.pyplot as plt

path = '/home/<USER>/Documents/Work/Parameter/BiAlO/results/phonon_dispersion/dfpt'

phile = f'{path}/band.yaml'
dosfile = f'{path}/projected_dos.dat'
poscar = f'{path}/POSCAR'

colour = '#08a8a8'
colours = {'Bi': '#d46ef9',
           'Al': '#1d76db',
           'O': '#919415'}
l_w=2.0
f_s=14

# Axes
fig, ax, add_legend = tp.axes.small.one()

# Load
dispersion = tp.data.load.phonopy_dispersion(phile)
dos = tp.data.load.phonopy_dos(dosfile, poscar=poscar)

# Add
tp.plot.phonons.add_dispersion(ax, dispersion, colour=colour, linewidth=1.5)  # Set line width
# tp.plot.frequency.add_dos(ax[1], dos, colour=colours, invert=True, line=True, linewidth=1.5)

# Set y=0 horizontal line
# ax.axhline(y=0, color='black', linestyle='--', linewidth=1.5)  # Set line style and width
# ax[1].axhline(y=0, color='black', linestyle='--', linewidth=1.5)  # Set line style and width

# Set line width, font style, and legend style
# ax.lines.set_linewidth(2.0)  # Change the line width of the graph for the first plot
# ax[1].lines[0].set_linewidth(2.0)  # Change the line width of the graph for the second plot
# ax[1].legend(loc='center right', frameon=False, fontsize=20)  # Adjust legend style

# Set axis line width
ax.spines['top'].set_linewidth(l_w)
ax.spines['bottom'].set_linewidth(l_w)
ax.spines['left'].set_linewidth(l_w)
ax.spines['right'].set_linewidth(l_w)

# ax[1].spines['top'].set_linewidth(l_w)
# ax[1].spines['bottom'].set_linewidth(l_w)
# ax[1].spines['left'].set_linewidth(l_w)
# ax[1].spines['right'].set_linewidth(l_w)

# Set font of X-axis and Y-axis tick labels
ax.tick_params(axis='x', labelsize=f_s)  # Adjust font size for X-axis tick labels
ax.tick_params(axis='y', labelsize=f_s)  # Adjust font size for Y-axis tick labels

# ax[1].tick_params(axis='x', labelsize=f_s)  # Adjust font size for X-axis tick labels
# ax[1].tick_params(axis='y', labelsize=f_s)  # Adjust font size for Y-axis tick labels

# Set font and size for X-axis and Y-axis labels
ax.xaxis.label.set_fontsize(f_s)  # Adjust font size for X-axis label
ax.yaxis.label.set_fontsize(f_s)  # Adjust font size for Y-axis label
# ax[0].yaxis.label.set_weight('bold')  # Make y-axis label bold
ax.xaxis.label.set_visible(False)  # Make it not visible

# ax[1].xaxis.label.set_fontsize(f_s)  # Adjust font size for X-axis label
# # ax[1].yaxis.label.set_fontsize(16)  # Adjust font size for Y-axis label

# # Adjust y-axis limits
# ax[1].set_ylim(ax[0].get_ylim())


tp.plot.phonons.formatting(ax, dispersion, color='#416269', linewidth=2.0, linestyle='-')


# Show the plot
plt.show()


import matplotlib as mpl
from matplotlib import font_manager

# Fail hard if Times New Roman isn't found (no fallback)
font_path = font_manager.findfont("Times New Roman", fallback_to_default=False)
mpl.rcParams.update({
    "font.family": "Times New Roman",
    "font.serif": ["Times New Roman"],    # only this, no alternatives
    "font.sans-serif": ["Times New Roman"],  # in case any sans gets picked
    "font.cursive": ["Times New Roman"],
    "font.fantasy": ["Times New Roman"],
    "font.monospace": ["Times New Roman"],  # optional, for uniformity
    # Optional: if you use mathtext and want Times for math too:
    "mathtext.fontset": "custom",
    "mathtext.rm": "Times New Roman",
    "mathtext.it": "Times New Roman:italic",
    "mathtext.bf": "Times New Roman:bold",
})

import tp
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.legend_handler import HandlerLine2D
import re
import matplotlib as mpl

# Setting font family to Times New Roman
# # Specify the full path to the Times New Roman font file
times_font_path = '/usr/share/fonts/truetype/msttcorefonts/Times_New_Roman.ttf'
mpl.rcParams['font.family'] = mpl.font_manager.FontProperties(fname=times_font_path).get_name()
#                           

path = '/home/<USER>/Documents/Work/Parameter/BiAlO/results/phonon_dispersion/dfpt'

phile = f'{path}/band.yaml'
dosfile = f'{path}/projected_dos.dat'
poscar = f'{path}/POSCAR'

# colors = [ '#FF8300', '#FF4500','#08a8a8'] #(Burnt Orange #B8390E) (#DC4731 Red Orange) (#A82810 Scarlet) (#F67B50 Coral) (#F89700 Orange) # Define colors as shades of #fb8500    Orange  #FF8300  Red Orange #FF4500
# colour = '#08a8a8'
colour= '#FF8300'

colours = {'Bi': '#d46ef9',
           'Al': '#1d76db',
           'O': '#919415'}
# colours = {'Bi': '#B8390E',
#            'Al': '#FF8300',
#            'O': '#F67B50'}
l_w=2.5
f_s=25

# Axes
fig, ax, add_legend = tp.axes.large.one_dos()

# Load
dispersion = tp.data.load.phonopy_dispersion(phile)
dos = tp.data.load.phonopy_dos(dosfile, poscar=poscar)

# Add
tp.plot.phonons.add_dispersion(ax[0], dispersion, colour=colour, linewidth=1.5)  # Set line width
tp.plot.frequency.add_dos(ax[1], dos, colour=colours, invert=True, line=True, linewidth=1.5)

# Set y=0 horizontal line
ax[0].axhline(y=0, color='black', linestyle='--', linewidth=2)  # Set line style and width
ax[1].axhline(y=0, color='black', linestyle='--', linewidth=2)  # Set line style and width

# Set line width, font style, and legend style
# ax[0].lines[0].set_linewidth(2.0)  # Change the line width of the graph for the first plot
# ax[1].lines[0].set_linewidth(2.0)  # Change the line width of the graph for the second plot
# ax[1].legend(loc='lower right', frameon=False, fontsize=20)  # Adjust legend style



# Get the legend handles and labels
handles, labels = ax[1].get_legend_handles_labels()

# Remove LaTeX formatting from labels
labels = [re.sub(r'\$(.*?)\$', r'\1', label) for label in labels]

# Create a list of legend label colors based on the colors defined in the 'colours' dictionary
legend_colors = [colours.get(label, 'black') for label in labels]

# Create the legend with handles and labels
legend = ax[1].legend(handles, labels, loc='center right', frameon=False, fontsize=f_s, handlelength=0)

# Set the colors of the legend labels
for text, color in zip(legend.get_texts(), legend_colors):
    text.set_color(color)




# Set axis line width
ax[0].spines['top'].set_linewidth(l_w)
ax[0].spines['bottom'].set_linewidth(l_w)
ax[0].spines['left'].set_linewidth(l_w)
ax[0].spines['right'].set_linewidth(l_w)

ax[1].spines['top'].set_linewidth(l_w)
ax[1].spines['bottom'].set_linewidth(l_w)
ax[1].spines['left'].set_linewidth(l_w)
ax[1].spines['right'].set_linewidth(l_w)

# Set font of X-axis and Y-axis tick labels
ax[0].tick_params(axis='x', labelsize=f_s)  # Adjust font size for X-axis tick labels
ax[0].tick_params(axis='y', labelsize=f_s)  # Adjust font size for Y-axis tick labels

# ax[1].tick_params(axis='x', labelsize=f_s)  # Adjust font size for X-axis tick labels
ax[1].tick_params(axis='y', labelsize=f_s)  # Adjust font size for Y-axis tick labels

# Set font and size for X-axis and Y-axis labels
ax[0].xaxis.label.set_fontsize(f_s)  # Adjust font size for X-axis label
ax[0].yaxis.label.set_fontsize(f_s)  # Adjust font size for Y-axis label
# ax[0].yaxis.label.set_weight('bold')  # Make y-axis label bold
ax[0].xaxis.label.set_visible(True)  # Make it not visible
ax[0].xaxis.label.set_color('white')


ax[1].xaxis.label.set_fontsize(f_s)  # Adjust font size for X-axis label
# ax[1].yaxis.label.set_fontsize(16)  # Adjust font size for Y-axis label

# Adjust y-axis limits
ax[1].set_ylim(ax[0].get_ylim())

# Set figure size after creating the figure
plt.gcf().set_size_inches(8, 6.5)

tp.plot.phonons.formatting(ax[0], dispersion, color='black', linewidth=2.0, linestyle='-')


# Save the plot in high-resolution EPS format
# savepath='/home/<USER>/Documents/Work/Parameter/BiAlO/Production_data/orange/phonon_new.png'
# plt.savefig(savepath, format='png', dpi=100)

# Show the plot
plt.show()


import matplotlib.pyplot as plt
import matplotlib as mpl

# Setting font family to Times New Roman
# # Specify the full path to the Times New Roman font file
times_font_path = '/usr/share/fonts/truetype/msttcorefonts/Times_New_Roman.ttf'
mpl.rcParams['font.family'] = mpl.font_manager.FontProperties(fname=times_font_path).get_name()
mpl.rcParams['font.family'] = 'Times New Roman'

l_w=2.5
f_s=25
# Adjusting Matplotlib's default settings
mpl.rcParams['axes.linewidth'] = l_w  # Thickness of the axis lines
mpl.rcParams['xtick.major.width'] = l_w  # Thickness of major ticks on x-axis
mpl.rcParams['ytick.major.width'] = l_w  # Thickness of major ticks on y-axis
mpl.rcParams['xtick.minor.width'] = 0  # Thickness of minor ticks on x-axis
# mpl.rcParams['ytick.minor.width'] = 1.0  # Thickness of minor ticks on y-axis
# mpl.rcParams['xtick.major.size'] = 6  # Length of major ticks on x-axis
# mpl.rcParams['ytick.major.size'] = 6  # Length of major ticks on y-axis
mpl.rcParams['xtick.minor.size'] = 0  # Length of minor ticks on x-axis
# mpl.rcParams['ytick.minor.size'] = 4  # Length of minor ticks on y-axis
mpl.rcParams['xtick.labelsize'] = f_s  # Font size of x-axis tick labels
mpl.rcParams['ytick.labelsize'] = f_s  # Font size of y-axis tick labels

# Define your data
categories = ['$\Gamma$', 'R$_{25}$', 'M', '$\Gamma$+R$_{25}$', '$\Gamma$+M']
# values = [-124.466250000005, -142.146250000003, -100.625000000001, -133.792500000006,-123.980000000003] # obtained directly from the phonopy modulation and the four pair vectors the displacement is considered same for both not as considered in code afd max then u variation
# values = [-159.3, -178.9, -100.625000000001, -225.89,-123.980000000003] # obtained from the eigen vector converssion and energy vs u minima 
values = [-219.516249999998, -201.014999999998, -113.788750000005, -266.318749999996,-227.231250000003] # Obtained from Full Relax structures
exp_values=[-200,-180,np.nan,-250,np.nan]

# Define color gradient from light to dark
# colors = [ '#005151','#047575','#08a8a8', '#003232','#005145']
colors = [ "#BF360C",'#FF8300', '#FF4500','#B8390E','#DC4731', '#F89700','#08a8a8'] #(Burnt Orange #B8390E) (#DC4731 Red Orange) (#A82810 Scarlet) (#F67B50 Coral) (#F89700 Orange) # Define colors as shades of #fb8500    Orange  #FF8300  Red Orange #FF4500

# Create a bar plot
plt.figure(figsize=(8, 6.5))  # Adjust the figure size as needed
bars = plt.bar(categories, values, color=colors[0], width=0.6, label='Energy')  # Adjust bar width here

# Add scatter plot for experimental data
# plt.scatter(categories, exp_values, color='red', marker='*', label='Experiment',s=500)

# Add title and labels
plt.xlabel('Distortion modes', fontsize=f_s)  # Set font size for X-axis label
plt.ylabel('$\Delta$E(meV)', fontsize=f_s)  # Set font size for Y-axis label

# Add ticks on the upper and right sides
plt.tick_params(axis='x', direction='in', which='both', bottom=True, top=True, labelbottom=True)
plt.tick_params(axis='y', direction='in', which='both', left=True, right=True, labelleft=True)

# Add legend
# plt.legend(loc='lower left', fontsize='20')


# Save the plot in high-resolution EPS format
# savepath='/home/<USER>/Documents/Work/Parameter/BiAlO/Production_data/orange/energy_bar_with_relax.eps'
# plt.savefig(savepath, format='eps', dpi=600)

# Show the plot
plt.tight_layout()  # Adjust layout to prevent clipping of labels
plt.show()


import matplotlib.pyplot as plt
import matplotlib as mpl


l_w=2.5
f_s=25
# Adjusting Matplotlib's default settings
mpl.rcParams['axes.linewidth'] = l_w  # Thickness of the axis lines
mpl.rcParams['xtick.major.width'] = l_w  # Thickness of major ticks on x-axis
mpl.rcParams['ytick.major.width'] = l_w  # Thickness of major ticks on y-axis
mpl.rcParams['xtick.minor.width'] = 0  # Thickness of minor ticks on x-axis
# mpl.rcParams['ytick.minor.width'] = 1.0  # Thickness of minor ticks on y-axis
# mpl.rcParams['xtick.major.size'] = 6  # Length of major ticks on x-axis
# mpl.rcParams['ytick.major.size'] = 6  # Length of major ticks on y-axis
mpl.rcParams['xtick.minor.size'] = 0  # Length of minor ticks on x-axis
# mpl.rcParams['ytick.minor.size'] = 4  # Length of minor ticks on y-axis
mpl.rcParams['xtick.labelsize'] = f_s  # Font size of x-axis tick labels
mpl.rcParams['ytick.labelsize'] = f_s  # Font size of y-axis tick labels

# Define your data
categories = ['$\Gamma$', 'R$_{25}$', 'M', '$\Gamma$+R$_{25}$', '$\Gamma$+M']
values = [-219.516249999998, -201.014999999998, -113.788750000005, -266.318749999996,-227.231250000003]
exp_values=[-200,-180,np.nan,-250,np.nan]

# Define color gradient from light to dark
colors = [ '#005151','#047575','#08a8a8', '#003232','#005145']

# Create a bar plot
plt.figure(figsize=(8, 6.5))  # Adjust the figure size as needed
bars = plt.bar(categories, values, color=colors, width=0.6, label='Calculation')  # Adjust bar width here

# Add scatter plot for experimental data
plt.scatter(categories, exp_values, color='red', marker='*', label='Experiment',s=500)

# Add title and labels
plt.xlabel('Distortion modes', fontsize=f_s)  # Set font size for X-axis label
plt.ylabel('$\Delta$E(meV)', fontsize=f_s)  # Set font size for Y-axis label

# Add ticks on the upper and right sides
plt.tick_params(axis='x', direction='in', which='both', bottom=True, top=True, labelbottom=True)
plt.tick_params(axis='y', direction='in', which='both', left=True, right=True, labelleft=True)

# Add legend
plt.legend(loc='lower left', fontsize='20')


# Save the plot in high-resolution EPS format
savepath='/home/<USER>/Documents/Work/Parameter/BiAlO/Production_data/energy_bar.eps'
# plt.savefig(savepath, format='eps', dpi=600)

# Show the plot
plt.tight_layout()  # Adjust layout to prevent clipping of labels
plt.show()


import numpy as np
import matplotlib.pyplot as plt
from matplotlib.patches import Patch

l_w=2.5
f_s=25
# Adjusting Matplotlib's default settings
mpl.rcParams['axes.linewidth'] = l_w  # Thickness of the axis lines
mpl.rcParams['xtick.major.width'] = l_w  # Thickness of major ticks on x-axis
mpl.rcParams['ytick.major.width'] = l_w  # Thickness of major ticks on y-axis
mpl.rcParams['xtick.minor.width'] = 0  # Thickness of minor ticks on x-axis
# mpl.rcParams['ytick.minor.width'] = 1.0  # Thickness of minor ticks on y-axis
# mpl.rcParams['xtick.major.size'] = 6  # Length of major ticks on x-axis
# mpl.rcParams['ytick.major.size'] = 6  # Length of major ticks on y-axis
mpl.rcParams['xtick.minor.size'] = 0  # Length of minor ticks on x-axis
# mpl.rcParams['ytick.minor.size'] = 4  # Length of minor ticks on y-axis
mpl.rcParams['xtick.labelsize'] = f_s  # Font size of x-axis tick labels
mpl.rcParams['ytick.labelsize'] = f_s  # Font size of y-axis tick labels

# Define your data
categories = ['$\Gamma$', 'R$_{25}$', 'M', '$\Gamma$+R$_{25}$', '$\Gamma$+M']
values = [-219.516249999998, -201.014999999998, -113.788750000005, -266.318749999996, -227.231250000003]
exp_values = [-200, -180, np.nan, -250, np.nan]

# Define color gradient from light to dark
colors = ['#005151', '#047575', '#08a8a8', '#003232', '#005145']

# Create figure and axes
fig, ax = plt.subplots(figsize=(8, 6.5))
# Adjust the position for the bars
x = np.arange(len(categories))  # the label locations
width = 0.35  # the width of the bars

# Plot the bars
for i, (calc_val, exp_val) in enumerate(zip(values, exp_values)):
    if np.isnan(exp_val):  # If no experimental value, plot only calculation
        ax.bar(x[i], calc_val, width, color=colors[i], label='Calculation')
    else:  # If both calculation and experimental values, plot double bars
        ax.bar(x[i] - width/2, calc_val, width, color=colors[i], label='Calculation')
        ax.bar(x[i] + width/2, exp_val, width, color='#08a841', hatch='/', label='Experiment')

# Add title and labels
plt.xlabel('Distortion modes', fontsize=f_s)  # Set font size for X-axis label
plt.ylabel('$\Delta$E(meV)', fontsize=f_s)  # Set font size for Y-axis label

# Add ticks on the upper and right sides
plt.tick_params(axis='x', direction='in', which='both', bottom=True, top=True, labelbottom=True)
plt.tick_params(axis='y', direction='in', which='both', left=True, right=True, labelleft=True)

# Set x-axis tick positions and labels
ax.set_xticks(x)
ax.set_xticklabels(categories)

# Customize legend
legend_elements = [Patch(facecolor=color, label=label) for color, label in zip(colors, ['Compu.'])]
legend_elements.append(Patch(facecolor='#08a841', hatch='/', label='Ref.'))
plt.legend(handles=legend_elements, loc='lower left', fontsize='20')


# Save the plot in high-resolution EPS format
savepath='/home/<USER>/Documents/Work/Parameter/BiAlO/Production_data/energy_bar.eps'
# plt.savefig(savepath, format='eps', dpi=600)


plt.show()


import pandas as pd
import matplotlib.pyplot as plt
import matplotlib as mpl

mpl.rcParams['font.family'] = 'Times New Roman'

l_w=2.5
f_s=25
# Adjusting Matplotlib's default settings
mpl.rcParams['axes.linewidth'] = l_w  # Thickness of the axis lines
mpl.rcParams['xtick.major.width'] = l_w  # Thickness of major ticks on x-axis
mpl.rcParams['ytick.major.width'] = l_w  # Thickness of major ticks on y-axis
mpl.rcParams['xtick.minor.width'] = 1.5  # Thickness of minor ticks on x-axis
mpl.rcParams['ytick.minor.width'] = 1.5  # Thickness of minor ticks on y-axis
mpl.rcParams['xtick.major.size'] = 6  # Length of major ticks on x-axis
mpl.rcParams['ytick.major.size'] = 6  # Length of major ticks on y-axis
mpl.rcParams['xtick.minor.size'] = 4  # Length of minor ticks on x-axis
mpl.rcParams['ytick.minor.size'] = 4  # Length of minor ticks on y-axis
mpl.rcParams['xtick.labelsize'] = f_s  # Font size of x-axis tick labels
mpl.rcParams['ytick.labelsize'] = f_s  # Font size of y-axis tick labels

# Read the data
file_path = "~/Documents/Work/Parameter/BiAlO/Production_data/double_well.dat"
data = pd.read_csv(file_path, delim_whitespace=True, skiprows=1)

plt.figure(figsize=(8, 6.5))  # Adjust the figure size as needed

# # Plot the data with symbols based on column index
symbols = ['o', 's', '^']  # Define symbols for each column
# linestyles = ['-', '--', ':']  # Define linestyles for each column
# colors = [  '#086a6a','#083c3c','#08a8a8']  # Define colors as shades of #08a8a8
colors=['#FFB300', '#08a841', '#BF360C','#08a841', '#086a6a','#08a8a8']

for i, column in enumerate(data.columns[1:4]):
    plt.plot(data.iloc[:, 0], data[column], marker=symbols[i], linestyle='-', color=colors[i], markersize=8, label=column)

# Plot all the data with symbols based on column index
# symbols = ['o', 's', '^', 'd', 'x', '+']  # Define symbols for each column
# colors = [  '#08a8a8','#083c3c','#086a6a','#086a6a','#aa1100','#aa5588']  # Define colors as shades of #08a8a8

# for i, column in enumerate(data.columns[1:]):
#     plt.plot(data.iloc[:, 0], data[column], marker=symbols[i%len(symbols)], linestyle='-', color=colors[i%len(colors)], markersize=8, label=column)



# Set limits for x and y axes
plt.xlim(data.iloc[:, 0].min(), data.iloc[:, 0].max())  # Adjust x-axis limits as needed
plt.ylim(-150, 50)  # Set y-axis limits (-150 to 50)

# Add title and labels
plt.xlabel('u (unit of a$_0$)', fontsize=f_s)  # Set font size for X-axis label
plt.ylabel('$\Delta$E(meV)', fontsize=f_s)  # Set font size for Y-axis label

# Add manual legend
# plt.legend(['$\Gamma$', 'R$_{25}$', 'M','$\Gamma$ + R$_{25}$','$\Gamma$ + M'], fontsize=12)  # Add manual legend with labels
plt.legend(['$\Gamma$', 'R$_{25}$', 'M'], fontsize=18,loc='upper center', ncol=3)  # Add manual legend with labels
# plt.xticks(np.arange(-0.15, 0.15, step=0.05))

# plt.grid()
# Add ticks on the upper and right sides
plt.tick_params(axis='x', direction='in', which='both', bottom=True, top=True, labelbottom=True)
plt.tick_params(axis='y', direction='in', which='both', left=True, right=True, labelleft=True)

# Save the plot in high-resolution EPS format
# savepath='/home/<USER>/Documents/Work/Parameter/BiAlO/Production_data/orange/double_well.eps'
# plt.savefig(savepath, format='eps', dpi=600)

# Show the plot
plt.tight_layout()  # Adjust layout to prevent clipping of labels
plt.show()

# Gamma only along 001 110 111 
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib as mpl

mpl.rcParams['font.family'] = 'Times New Roman'

l_w=2.5
f_s=25
# Adjusting Matplotlib's default settings
mpl.rcParams['axes.linewidth'] = l_w  # Thickness of the axis lines
mpl.rcParams['xtick.major.width'] = l_w  # Thickness of major ticks on x-axis
mpl.rcParams['ytick.major.width'] = l_w  # Thickness of major ticks on y-axis
mpl.rcParams['xtick.minor.width'] = 1.5  # Thickness of minor ticks on x-axis
mpl.rcParams['ytick.minor.width'] = 1.5  # Thickness of minor ticks on y-axis
mpl.rcParams['xtick.major.size'] = 6  # Length of major ticks on x-axis
mpl.rcParams['ytick.major.size'] = 6  # Length of major ticks on y-axis
mpl.rcParams['xtick.minor.size'] = 4  # Length of minor ticks on x-axis
mpl.rcParams['ytick.minor.size'] = 4  # Length of minor ticks on y-axis
mpl.rcParams['xtick.labelsize'] = f_s  # Font size of x-axis tick labels
mpl.rcParams['ytick.labelsize'] = f_s  # Font size of y-axis tick labels

# Read the data
file_path = "~/Documents/Work/Parameter/BiAlO/Production_data/Gamma_001_110_111_Energy_vs_u.dat"
data = pd.read_csv(file_path, delim_whitespace=True, skiprows=1)

plt.figure(figsize=(8, 6.5))  # Adjust the figure size as needed

# # Plot the data with symbols based on column index
symbols = ['o', 's', '^']  # Define symbols for each column
# linestyles = ['-', '--', ':']  # Define linestyles for each column
# colors = [  '#086a6a','#083c3c','#08a8a8']  # Define colors as shades of #08a8a8
colors=['#0077BB', '#08a841', '#BF360C','#08a841', '#086a6a','#08a8a8']

for i, column in enumerate(data.columns[1:4]):
    plt.plot(data.iloc[:, 0], data[column], marker=symbols[i], linestyle='-', color=colors[i], markersize=8, label=column)

# Plot all the data with symbols based on column index
# symbols = ['o', 's', '^', 'd', 'x', '+']  # Define symbols for each column
# colors = [  '#08a8a8','#083c3c','#086a6a','#086a6a','#aa1100','#aa5588']  # Define colors as shades of #08a8a8

# for i, column in enumerate(data.columns[1:]):
#     plt.plot(data.iloc[:, 0], data[column], marker=symbols[i%len(symbols)], linestyle='-', color=colors[i%len(colors)], markersize=8, label=column)



# Set limits for x and y axes
plt.xlim(data.iloc[:, 0].min(), data.iloc[:, 0].max())  # Adjust x-axis limits as needed
plt.ylim(-36.93, -36.6)  # Set y-axis limits (-150 to 50)

# Add title and labels
plt.xlabel('u (unit of a$_0$)', fontsize=f_s)  # Set font size for X-axis label
plt.ylabel('Energy(eV)', fontsize=f_s)  # Set font size for Y-axis label

# Add manual legend
# plt.legend(['$\Gamma$', 'R$_{25}$', 'M','$\Gamma$ + R$_{25}$','$\Gamma$ + M'], fontsize=12)  # Add manual legend with labels
plt.legend(['$\Gamma$(001)', '$\Gamma$(110)', '$\Gamma$(111)'], fontsize=f_s,loc='upper center', ncol=1)  # Add manual legend with labels
# plt.xticks(np.arange(-0.15, 0.15, step=0.05))

# plt.grid()
# Add ticks on the upper and right sides
plt.tick_params(axis='x', direction='in', which='both', bottom=True, top=True, labelbottom=True)
plt.tick_params(axis='y', direction='in', which='both', left=True, right=True, labelleft=True)

# Save the plot in high-resolution EPS format
savepath='/home/<USER>/Documents/Work/Parameter/BiAlO/Production_data/orange/Gamma_001_110_111_Energy_vs_u.eps'
plt.savefig(savepath, format='eps', dpi=600)

# Show the plot
plt.tight_layout()  # Adjust layout to prevent clipping of labels
plt.show()

# AFD (R) only along 001 110 111 
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib as mpl

mpl.rcParams['font.family'] = 'Times New Roman'

l_w=2.5
f_s=25
# Adjusting Matplotlib's default settings
mpl.rcParams['axes.linewidth'] = l_w  # Thickness of the axis lines
mpl.rcParams['xtick.major.width'] = l_w  # Thickness of major ticks on x-axis
mpl.rcParams['ytick.major.width'] = l_w  # Thickness of major ticks on y-axis
mpl.rcParams['xtick.minor.width'] = 1.5  # Thickness of minor ticks on x-axis
mpl.rcParams['ytick.minor.width'] = 1.5  # Thickness of minor ticks on y-axis
mpl.rcParams['xtick.major.size'] = 6  # Length of major ticks on x-axis
mpl.rcParams['ytick.major.size'] = 6  # Length of major ticks on y-axis
mpl.rcParams['xtick.minor.size'] = 4  # Length of minor ticks on x-axis
mpl.rcParams['ytick.minor.size'] = 4  # Length of minor ticks on y-axis
mpl.rcParams['xtick.labelsize'] = f_s  # Font size of x-axis tick labels
mpl.rcParams['ytick.labelsize'] = f_s  # Font size of y-axis tick labels

# Read the data
file_path = "~/Documents/Work/Parameter/BiAlO/Production_data/R_001_110_111_Energy_vs_afd.dat"
data = pd.read_csv(file_path, delim_whitespace=True, skiprows=1)

plt.figure(figsize=(8, 6.5))  # Adjust the figure size as needed

# # Plot the data with symbols based on column index
symbols = ['o', 's', '^']  # Define symbols for each column
# linestyles = ['-', '--', ':']  # Define linestyles for each column
# colors = [  '#086a6a','#083c3c','#08a8a8']  # Define colors as shades of #08a8a8
colors=['#0077BB', '#08a841', '#BF360C','#08a841', '#086a6a','#08a8a8']

for i, column in enumerate(data.columns[1:4]):
    plt.plot(data.iloc[:, 0], data[column], marker=symbols[i], linestyle='-', color=colors[i], markersize=8, label=column)

# Plot all the data with symbols based on column index
# symbols = ['o', 's', '^', 'd', 'x', '+']  # Define symbols for each column
# colors = [  '#08a8a8','#083c3c','#086a6a','#086a6a','#aa1100','#aa5588']  # Define colors as shades of #08a8a8

# for i, column in enumerate(data.columns[1:]):
#     plt.plot(data.iloc[:, 0], data[column], marker=symbols[i%len(symbols)], linestyle='-', color=colors[i%len(colors)], markersize=8, label=column)



# Set limits for x and y axes
plt.xlim(data.iloc[:, 0].min(), data.iloc[:, 0].max())  # Adjust x-axis limits as needed
plt.ylim(-36.92, -36.6)  # Set y-axis limits (-150 to 50)

# Add title and labels
plt.xlabel('u (unit of a$_0$)', fontsize=f_s)  # Set font size for X-axis label
plt.ylabel('Energy(eV)', fontsize=f_s)  # Set font size for Y-axis label

# Add manual legend
# plt.legend(['$\Gamma$', 'R$_{25}$', 'M','$\Gamma$ + R$_{25}$','$\Gamma$ + M'], fontsize=12)  # Add manual legend with labels
plt.legend(['AFD(001)', 'AFD(110)', 'AFD(111)'], fontsize=f_s,loc='upper center', ncol=1)  # Add manual legend with labels
# plt.xticks(np.arange(-0.15, 0.15, step=0.05))

# plt.grid()
# Add ticks on the upper and right sides
plt.tick_params(axis='x', direction='in', which='both', bottom=True, top=True, labelbottom=True)
plt.tick_params(axis='y', direction='in', which='both', left=True, right=True, labelleft=True)

# Save the plot in high-resolution EPS format
savepath='/home/<USER>/Documents/Work/Parameter/BiAlO/Production_data/orange/R_001_110_111_Energy_vs_afd.eps'
plt.savefig(savepath, format='eps', dpi=600)

# Show the plot
plt.tight_layout()  # Adjust layout to prevent clipping of labels
plt.show()

# AFD + Gamma along 001 110 111 however the eigen vector is used in this case is Gamma only not G+R, in G+R it is not reaching any minima, 
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib as mpl

mpl.rcParams['font.family'] = 'Times New Roman'

l_w=2.5
f_s=25
# Adjusting Matplotlib's default settings
mpl.rcParams['axes.linewidth'] = l_w  # Thickness of the axis lines
mpl.rcParams['xtick.major.width'] = l_w  # Thickness of major ticks on x-axis
mpl.rcParams['ytick.major.width'] = l_w  # Thickness of major ticks on y-axis
mpl.rcParams['xtick.minor.width'] = 1.5  # Thickness of minor ticks on x-axis
mpl.rcParams['ytick.minor.width'] = 1.5  # Thickness of minor ticks on y-axis
mpl.rcParams['xtick.major.size'] = 6  # Length of major ticks on x-axis
mpl.rcParams['ytick.major.size'] = 6  # Length of major ticks on y-axis
mpl.rcParams['xtick.minor.size'] = 4  # Length of minor ticks on x-axis
mpl.rcParams['ytick.minor.size'] = 4  # Length of minor ticks on y-axis
mpl.rcParams['xtick.labelsize'] = f_s  # Font size of x-axis tick labels
mpl.rcParams['ytick.labelsize'] = f_s  # Font size of y-axis tick labels

# Read the data
file_path = "~/Documents/Work/Parameter/BiAlO/Production_data/Gamma_R_001_110_111_energy_afd_max_vs_u.dat"
data = pd.read_csv(file_path, delim_whitespace=True, skiprows=1)

plt.figure(figsize=(8, 6.5))  # Adjust the figure size as needed

# # Plot the data with symbols based on column index
symbols = ['o', 's', '^']  # Define symbols for each column
# linestyles = ['-', '--', ':']  # Define linestyles for each column
# colors = [  '#086a6a','#083c3c','#08a8a8']  # Define colors as shades of #08a8a8
colors=['#0077BB', '#08a841', '#BF360C','#08a841', '#086a6a','#08a8a8']

for i, column in enumerate(data.columns[1:4]):
    plt.plot(data.iloc[:, 0], data[column], marker=symbols[i], linestyle='-', color=colors[i], markersize=8, label=column)

# Plot all the data with symbols based on column index
# symbols = ['o', 's', '^', 'd', 'x', '+']  # Define symbols for each column
# colors = [  '#08a8a8','#083c3c','#086a6a','#086a6a','#aa1100','#aa5588']  # Define colors as shades of #08a8a8

# for i, column in enumerate(data.columns[1:]):
#     plt.plot(data.iloc[:, 0], data[column], marker=symbols[i%len(symbols)], linestyle='-', color=colors[i%len(colors)], markersize=8, label=column)



# Set limits for x and y axes
plt.xlim(data.iloc[:, 0].min(), data.iloc[:, 0].max())  # Adjust x-axis limits as needed
plt.ylim(-37, -36.6)  # Set y-axis limits (-150 to 50)

# Add title and labels
plt.xlabel('u (unit of a$_0$)', fontsize=f_s)  # Set font size for X-axis label
plt.ylabel('Energy(eV)', fontsize=f_s)  # Set font size for Y-axis label

# Add manual legend
# plt.legend(['$\Gamma$', 'R$_{25}$', 'M','$\Gamma$ + R$_{25}$','$\Gamma$ + M'], fontsize=12)  # Add manual legend with labels
plt.legend(['$\Gamma$+AFD(001)', '$\Gamma$+AFD(110)', '$\Gamma$+AFD(111)'], fontsize=18,loc='upper center', ncol=1)  # Add manual legend with labels
# plt.xticks(np.arange(-0.15, 0.15, step=0.05))

# plt.grid()
# Add ticks on the upper and right sides
plt.tick_params(axis='x', direction='in', which='both', bottom=True, top=True, labelbottom=True)
plt.tick_params(axis='y', direction='in', which='both', left=True, right=True, labelleft=True)

# Save the plot in high-resolution EPS format
savepath='/home/<USER>/Documents/Work/Parameter/BiAlO/Production_data/orange/Gamma_R_001_110_111_energy_afd_max_vs_u.eps'
plt.savefig(savepath, format='eps', dpi=600)

# Show the plot
plt.tight_layout()  # Adjust layout to prevent clipping of labels
plt.show()

# Strained Gamma only along 001 110 111 
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib as mpl
from matplotlib.lines import Line2D


mpl.rcParams['font.family'] = 'Times New Roman'

l_w=2.5
f_s=25
# Adjusting Matplotlib's default settings
mpl.rcParams['axes.linewidth'] = l_w  # Thickness of the axis lines
mpl.rcParams['xtick.major.width'] = l_w  # Thickness of major ticks on x-axis
mpl.rcParams['ytick.major.width'] = l_w  # Thickness of major ticks on y-axis
mpl.rcParams['xtick.minor.width'] = 1.5  # Thickness of minor ticks on x-axis
mpl.rcParams['ytick.minor.width'] = 1.5  # Thickness of minor ticks on y-axis
mpl.rcParams['xtick.major.size'] = 6  # Length of major ticks on x-axis
mpl.rcParams['ytick.major.size'] = 6  # Length of major ticks on y-axis
mpl.rcParams['xtick.minor.size'] = 4  # Length of minor ticks on x-axis
mpl.rcParams['ytick.minor.size'] = 4  # Length of minor ticks on y-axis
mpl.rcParams['xtick.labelsize'] = f_s  # Font size of x-axis tick labels
mpl.rcParams['ytick.labelsize'] = f_s  # Font size of y-axis tick labels

plt.figure(figsize=(8, 6.5))  # Adjust the figure size as needed

# # Plot the data with symbols based on column index
symbols = ['o', 's', '^']  # Define symbols for each column
# linestyles = ['-', '--', ':']  # Define linestyles for each column
# colors = [  '#086a6a','#083c3c','#08a8a8']  # Define colors as shades of #08a8a8
# colors=['#BF360C', '#EF6C00', '#FFB300','#08a841', '#086a6a','#08a8a8']
colors=['#0077BB', '#08a841', '#BF360C','#08a841', '#086a6a','#08a8a8']

#Universal Constant
hatree2eV = 27.2113966413079

#Lattice Parameter in a.u.
a0 = 7.032248652103040

# Parameters to be set (Unstrained Parameters in a.u.)
k = -0.027754784105191
alpha = 0.040324721651564
# gamma = -0.016533547669143
gamma = -0.019488032088727
E0=-36.734462

# Strain and coupling parameters
B11 =  4.379691182059110
B12 = 1.686690163163050
B44 = 1.782374748933030
B1xx = -0.55062
B1yy = -0.1662986
B4yz = -0.1093434  # this value is twice the slope obtained

# Deduced Parameters
B = B11 + (2 * B12)
C = B1xx + (2 * B1yy)
mur = B44
mut = 0.5 * (B11 - B12)
nur = B4yz
nut = 0.5 * (B1xx - B1yy)

# Strained Parameters
alpha_s = alpha - (((C**2 / B) + ((4 * nut**2) / mut)) / 24)
gamma_s = gamma + 0.5 * ((nut**2 / mut) - (nur**2 / mur))

#variation of soft-mode amplitude
xmin = -0.12
xmax = 0.12

# Convertion of both strained and unstrained Parameters from a.u. to eV unit
k = k * hatree2eV * (a0**2)
alpha = alpha * hatree2eV * (a0**4)
gamma = gamma * hatree2eV * (a0**4)
alpha_s = alpha_s * hatree2eV * (a0**4)
gamma_s = gamma_s * hatree2eV * (a0**4)

# Unstrained Energies along three directions
def y1(x):
    return k * x**2 + alpha * x**4 + E0

def y2(x):
    return k * x**2 + alpha * x**4 + (gamma / 4) * x**4 + E0

def y3(x):
    return k * x**2 + alpha * x**4 + (gamma / 3) * x**4 + E0

# Strained Energies along three directions
def y1_s(x):
    return k * x**2 + alpha_s * x**4 + E0

def y2_s(x):
    return k * x**2 + alpha_s * x**4 + (gamma_s / 4) * x**4 + E0

def y3_s(x):
    return k * x**2 + alpha_s * x**4 + (gamma_s / 3) * x**4 + E0

# Generate x-values for plotting
x_vals = np.linspace(xmin, xmax, num=50)

data= [y1,y2,y3]
data_s=[y1_s,y2_s,y3_s]

# Open the file to save the unstrained data
with open("Gamma_001_110_111_from_equation.out", "w") as f1:
    # Write header
    f1.write("U\tU_001\tU_110\tU_111\n")
    # Write data
    for x in x_vals:
        f1.write(f"{x:.6f}\t{y1(x):.6f}\t{y2(x):.6f}\t{y3(x):.6f}\n")

# Open the file to save the strained data
with open("Gamma_+Strain_001_110_111_from_equation.out", "w") as f2:
    # Write header
    f2.write("U\tU_001\tU_110\tU_111\n")
    # Write data
    for x in x_vals:
        f2.write(f"{x:.6f}\t{y1_s(x):.6f}\t{y2_s(x):.6f}\t{y3_s(x):.6f}\n")

# # Plotting
# plt.figure(figsize=(8, 6))
# plt.xlabel("u [in unit of lattice constant]")
# plt.ylabel("E - E0 [eV]")

# plt.plot(x_vals, y1(x_vals), label="E(001) Unstrained", linestyle='-', linewidth=2, color='green',marker='o')
# plt.plot(x_vals, y1_s(x_vals), label="E(001) Strained", linestyle='--', linewidth=2, color='green')

# plt.plot(x_vals, y2(x_vals), label="E(110) Unstrained", linestyle='-', linewidth=2, color='blue',marker='^')
# plt.plot(x_vals, y2_s(x_vals), label="E(110) Strained", linestyle='--', linewidth=2, color='blue')

# plt.plot(x_vals, y3(x_vals), label="E(111) Unstrained", linestyle='-', linewidth=2, color='olive',marker='*')
# plt.plot(x_vals, y3_s(x_vals), label="E(111) Strained", linestyle='--', linewidth=2, color='olive')

# # plt.legend()
# plt.grid(True)
# # plt.savefig("e_vs_u_strain.eps")
# plt.show()




# Plotting unstrained energies (solid lines)
for i, func in enumerate(data):
    plt.plot(x_vals, func(x_vals), marker=symbols[i], linestyle='-', color=colors[i], markersize=8, label=f'Unstrained {i+1}')

# Plotting strained energies (dashed lines)
for i, func_s in enumerate(data_s):
    plt.plot(x_vals, func_s(x_vals), linestyle='--', color=colors[i], markersize=0, label=f'Strained {i+1}')


# Set limits for x and y axes
# plt.xlim(data.iloc[:, 0].min(), data.iloc[:, 0].max())  # Adjust x-axis limits as needed
plt.ylim(-36.93, -36.7)  # Set y-axis limits (-150 to 50)

# Add title and labels
plt.xlabel('u (unit of a$_0$)', fontsize=f_s)  # Set font size for X-axis label
plt.ylabel('Energy(eV)', fontsize=f_s)  # Set font size for Y-axis label

# # Add manual legend
# # plt.legend(['$\Gamma$', 'R$_{25}$', 'M','$\Gamma$ + R$_{25}$','$\Gamma$ + M'], fontsize=12)  # Add manual legend with labels
# plt.legend(['$\Gamma$(001)', '$\Gamma$(110)', '$\Gamma$(111)'], fontsize=18,loc='upper center', ncol=1)  # Add manual legend with labels
# # plt.xticks(np.arange(-0.15, 0.15, step=0.05))
# # Custom legend for line styles (unstrained and strained)
# from matplotlib.lines import Line2D
# custom_lines = [Line2D([0], [0], color='black', linestyle='-', lw=2),
#                 Line2D([0], [0], color='black', linestyle='--', lw=2)]

# plt.legend(custom_lines, ['Unstrained', 'Strained'], fontsize=14, loc='upper right')

# Custom legend for the directions
direction_labels = ['$\Gamma$(001)', '$\Gamma$(110)', '$\Gamma$(111)']
direction_lines = [Line2D([0], [0], color=colors[i], linestyle='-', marker=symbols[i], markersize=8) for i in range(3)]

# Custom legend for the directions
direction_labels_s = ['$\eta+\Gamma$(001)', '$\eta+\Gamma$(110)', '$\eta+\Gamma$(111)']
direction_lines_s = [Line2D([0], [0], color=colors[i], linestyle='--') for i in range(3)]


# Combine both legends into one
handles = direction_lines + direction_lines_s
labels = direction_labels + direction_labels_s

# Now create a single legend
# Manually set legend position using bbox_to_anchor
# bbox_to_anchor=(x, y): x and y are relative to the plot size (0 is left/bottom, 1 is right/top)
# plt.legend(handles, labels, fontsize=14, loc='upper right', bbox_to_anchor=(0.95, 1.02))  # Example of manual control
# plt.legend(handles, labels, fontsize=14, loc='upper right', bbox_to_anchor=(0.95, 1.02),ncol=2, columnspacing=0.9)  # Example of manual control
plt.legend(handles, labels, fontsize=20, loc='lower center',ncol=1, columnspacing=0.5) 


# plt.grid()
# Add ticks on the upper and right sides
plt.tick_params(axis='x', direction='in', which='both', bottom=True, top=True, labelbottom=True)
plt.tick_params(axis='y', direction='in', which='both', left=True, right=True, labelleft=True)

# Save the plot in high-resolution EPS format
savepath='/home/<USER>/Documents/Work/Parameter/BiAlO/Production_data/orange/Strained_Gamma_001_110_111_Energy_vs_u.eps'
plt.savefig(savepath, format='eps', dpi=600)

# Show the plot
plt.tight_layout()  # Adjust layout to prevent clipping of labels
plt.show()



'''Now we can merge any graph to any arrange pattern just we need to keep the common style section first then all the individual
plotings just we need to define first the figure size to accomodate the subplots then add plt.subplot(2, 2, 1) first 2 corresponds 
to column and next refers to row then third refers to plot sequence number  '''

################ Common Style Section #######################################################################################################################################
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib as mpl
from matplotlib import rc
mpl.rcParams['font.family'] = 'Times New Roman'
l_w=2.5
f_s=25
# Adjusting Matplotlib's default settings
mpl.rcParams['axes.linewidth'] = l_w  # Thickness of the axis lines
mpl.rcParams['xtick.major.width'] = l_w  # Thickness of major ticks on x-axis
mpl.rcParams['ytick.major.width'] = l_w  # Thickness of major ticks on y-axis
mpl.rcParams['xtick.minor.width'] = 1.5  # Thickness of minor ticks on x-axis
mpl.rcParams['ytick.minor.width'] = 1.5  # Thickness of minor ticks on y-axis
mpl.rcParams['xtick.major.size'] = 6  # Length of major ticks on x-axis
mpl.rcParams['ytick.major.size'] = 6  # Length of major ticks on y-axis
mpl.rcParams['xtick.minor.size'] = 4  # Length of minor ticks on x-axis
mpl.rcParams['ytick.minor.size'] = 4  # Length of minor ticks on y-axis
mpl.rcParams['xtick.labelsize'] = f_s  # Font size of x-axis tick labels
mpl.rcParams['ytick.labelsize'] = f_s  # Font size of y-axis tick labels

# Create a figure with multiple subplots
plt.figure(figsize=(16,18))  # Adjust the figure size as needed

# Define marker styles and sizes
symbols = ['o', 's','o' ]  # Define symbols for each column
# colors = [ '#08a841', '#086a6a','#08a8a8']  # Define colors as shades of #08a8a8
# colors = [ '#BF360C', '#D84315', '#EF6C00', '#FFB300', '#FFCC80','#FFAA80','#013c3c', '#086a6a','#08a8a8'] #(Burnt Orange #B8390E) (#DC4731 Red Orange) (#A82810 Scarlet) (#F67B50 Coral) (#F89700 Orange) # Define colors as shades of #fb8500    Orange  #FF8300  Red Orange #FF4500
colors=['#BF360C', '#EF6C00', '#FFB300','#08a841', '#086a6a','#08a8a8']
linestyles = ['-.', '--', ':']  # Define linestyles for each column
marker_sizes = [16, 16, 16]  # Define marker sizes for each column



######## Panel 1 #######################################################################################################################################################
plt.subplot(3, 2, 1)

import tp
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.legend_handler import HandlerLine2D
import re
import matplotlib as mpl

# Setting font family to Times New Roman
# # Specify the full path to the Times New Roman font file
times_font_path = '/usr/share/fonts/truetype/msttcorefonts/Times_New_Roman.ttf'
mpl.rcParams['font.family'] = mpl.font_manager.FontProperties(fname=times_font_path).get_name()
#                           

path = '/home/<USER>/Documents/Work/Parameter/BiAlO/results/phonon_dispersion/dfpt'

phile = f'{path}/band.yaml'
dosfile = f'{path}/projected_dos.dat'
poscar = f'{path}/POSCAR'

# colors = [ '#FF8300', '#FF4500','#08a8a8'] #(Burnt Orange #B8390E) (#DC4731 Red Orange) (#A82810 Scarlet) (#F67B50 Coral) (#F89700 Orange) # Define colors as shades of #fb8500    Orange  #FF8300  Red Orange #FF4500
# colour = '#08a8a8'
colour= '#FF8300'

colours = {'Bi': '#d46ef9',
           'Al': '#1d76db',
           'O': '#919415'}
# colours = {'Bi': '#B8390E',
#            'Al': '#FF8300',
#            'O': '#F67B50'}
l_w=2.5
f_s=25

# Axes
fig, ax, add_legend = tp.axes.large.one_dos()

# Load
dispersion = tp.data.load.phonopy_dispersion(phile)
dos = tp.data.load.phonopy_dos(dosfile, poscar=poscar)

# Add
tp.plot.phonons.add_dispersion(ax[0], dispersion, colour=colour, linewidth=1.5)  # Set line width
tp.plot.frequency.add_dos(ax[1], dos, colour=colours, invert=True, line=True, linewidth=1.5)

# Set y=0 horizontal line
ax[0].axhline(y=0, color='black', linestyle='--', linewidth=2)  # Set line style and width
ax[1].axhline(y=0, color='black', linestyle='--', linewidth=2)  # Set line style and width

# Set line width, font style, and legend style
# ax[0].lines[0].set_linewidth(2.0)  # Change the line width of the graph for the first plot
# ax[1].lines[0].set_linewidth(2.0)  # Change the line width of the graph for the second plot
# ax[1].legend(loc='lower right', frameon=False, fontsize=20)  # Adjust legend style



# Get the legend handles and labels
handles, labels = ax[1].get_legend_handles_labels()

# Remove LaTeX formatting from labels
labels = [re.sub(r'\$(.*?)\$', r'\1', label) for label in labels]

# Create a list of legend label colors based on the colors defined in the 'colours' dictionary
legend_colors = [colours.get(label, 'black') for label in labels]

# Create the legend with handles and labels
legend = ax[1].legend(handles, labels, loc='center right', frameon=False, fontsize=f_s, handlelength=0)

# Set the colors of the legend labels
for text, color in zip(legend.get_texts(), legend_colors):
    text.set_color(color)




# Set axis line width
ax[0].spines['top'].set_linewidth(l_w)
ax[0].spines['bottom'].set_linewidth(l_w)
ax[0].spines['left'].set_linewidth(l_w)
ax[0].spines['right'].set_linewidth(l_w)

ax[1].spines['top'].set_linewidth(l_w)
ax[1].spines['bottom'].set_linewidth(l_w)
ax[1].spines['left'].set_linewidth(l_w)
ax[1].spines['right'].set_linewidth(l_w)

# Set font of X-axis and Y-axis tick labels
ax[0].tick_params(axis='x', labelsize=f_s)  # Adjust font size for X-axis tick labels
ax[0].tick_params(axis='y', labelsize=f_s)  # Adjust font size for Y-axis tick labels

# ax[1].tick_params(axis='x', labelsize=f_s)  # Adjust font size for X-axis tick labels
ax[1].tick_params(axis='y', labelsize=f_s)  # Adjust font size for Y-axis tick labels

# Set font and size for X-axis and Y-axis labels
ax[0].xaxis.label.set_fontsize(f_s)  # Adjust font size for X-axis label
ax[0].yaxis.label.set_fontsize(f_s)  # Adjust font size for Y-axis label
# ax[0].yaxis.label.set_weight('bold')  # Make y-axis label bold
ax[0].xaxis.label.set_visible(True)  # Make it not visible
ax[0].xaxis.label.set_color('white')


ax[1].xaxis.label.set_fontsize(f_s)  # Adjust font size for X-axis label
# ax[1].yaxis.label.set_fontsize(16)  # Adjust font size for Y-axis label

# Adjust y-axis limits
ax[1].set_ylim(ax[0].get_ylim())

# Set figure size after creating the figure
# plt.gcf().set_size_inches(8, 6.5)

tp.plot.phonons.formatting(ax[0], dispersion, color='black', linewidth=2.0, linestyle='-')


# Add subplot label (b)
# plt.text(-100, 98, '(a)      Hydrostatic Pressure', fontsize=f_s, va='top', ha='left')

######## Panel 2 #######################################################################################################################################################
plt.subplot(3, 2, 2)

# Define your data
categories = ['$\Gamma$', 'R$_{25}$', 'M', '$\Gamma$+R$_{25}$', '$\Gamma$+M']
# values = [-124.466250000005, -142.146250000003, -100.625000000001, -133.792500000006,-123.980000000003] # obtained directly from the phonopy modulation and the four pair vectors the displacement is considered same for both not as considered in code afd max then u variation
values = [-159.3, -178.9, -100.625000000001, -225.89,-123.980000000003] # obtained from the eigen vector converssion and energy vs u minima 
exp_values=[-200,-180,np.nan,-250,np.nan]

# Define color gradient from light to dark
# colors = [ '#005151','#047575','#08a8a8', '#003232','#005145']
colors = [ '#FF8300', '#FF4500','#B8390E','#DC4731', '#F89700','#08a8a8'] #(Burnt Orange #B8390E) (#DC4731 Red Orange) (#A82810 Scarlet) (#F67B50 Coral) (#F89700 Orange) # Define colors as shades of #fb8500    Orange  #FF8300  Red Orange #FF4500

# Create a bar plot
bars = plt.bar(categories, values, color=colors[0], width=0.6, label='Energy')  # Adjust bar width here

# Add scatter plot for experimental data
# plt.scatter(categories, exp_values, color='red', marker='*', label='Experiment',s=500)

# Add title and labels
plt.xlabel('Distortion modes', fontsize=f_s)  # Set font size for X-axis label
plt.ylabel('$\Delta$E(meV)', fontsize=f_s)  # Set font size for Y-axis label

# Add ticks on the upper and right sides
plt.tick_params(axis='x', direction='in', which='both', bottom=True, top=True, labelbottom=True)
plt.tick_params(axis='y', direction='in', which='both', left=True, right=True, labelleft=True)

# Add subplot label (b)
# plt.text(-90, 98, '(b)        Uniaxial Stress', fontsize=f_s, va='top', ha='left')

######## Panel 3 #######################################################################################################################################################
plt.subplot(3, 2, 3)

# Read the data
file_path = "~/Documents/Work/Parameter/BiAlO/Production_data/Gamma_001_110_111_Energy_vs_u.dat"
data = pd.read_csv(file_path, delim_whitespace=True, skiprows=1)



# # Plot the data with symbols based on column index
symbols = ['o', 's', '^']  # Define symbols for each column
# linestyles = ['-', '--', ':']  # Define linestyles for each column
# colors = [  '#086a6a','#083c3c','#08a8a8']  # Define colors as shades of #08a8a8

for i, column in enumerate(data.columns[1:4]):
    plt.plot(data.iloc[:, 0], data[column], marker=symbols[i], linestyle='-', color=colors[i], markersize=8, label=column)

# Plot all the data with symbols based on column index
# symbols = ['o', 's', '^', 'd', 'x', '+']  # Define symbols for each column
# colors = [  '#08a8a8','#083c3c','#086a6a','#086a6a','#aa1100','#aa5588']  # Define colors as shades of #08a8a8

# for i, column in enumerate(data.columns[1:]):
#     plt.plot(data.iloc[:, 0], data[column], marker=symbols[i%len(symbols)], linestyle='-', color=colors[i%len(colors)], markersize=8, label=column)



# Set limits for x and y axes
plt.xlim(data.iloc[:, 0].min(), data.iloc[:, 0].max())  # Adjust x-axis limits as needed
plt.ylim(-36.93, -36.6)  # Set y-axis limits (-150 to 50)

# Add title and labels
plt.xlabel('u (unit of a$_0$)', fontsize=f_s)  # Set font size for X-axis label
plt.ylabel('Energy(eV)', fontsize=f_s)  # Set font size for Y-axis label

# Add manual legend
# plt.legend(['$\Gamma$', 'R$_{25}$', 'M','$\Gamma$ + R$_{25}$','$\Gamma$ + M'], fontsize=12)  # Add manual legend with labels
plt.legend(['$\Gamma$(001)', '$\Gamma$(110)', '$\Gamma$(111)'], fontsize=18,loc='upper center', ncol=1)  # Add manual legend with labels
# plt.xticks(np.arange(-0.15, 0.15, step=0.05))

# plt.grid()
# Add ticks on the upper and right sides
plt.tick_params(axis='x', direction='in', which='both', bottom=True, top=True, labelbottom=True)
plt.tick_params(axis='y', direction='in', which='both', left=True, right=True, labelleft=True)

# Add subplot label (b)
# plt.text(-90, 98, '(c)        Biaxial Stress', fontsize=f_s, va='top', ha='left')

######## Panel 4 #########################################################################################################################################################
plt.subplot(3, 2, 4)

# Read the data
file_path = "~/Documents/Work/Parameter/BiAlO/Production_data/R_001_110_111_Energy_vs_afd.dat"
data = pd.read_csv(file_path, delim_whitespace=True, skiprows=1)



# # Plot the data with symbols based on column index
symbols = ['o', 's', '^']  # Define symbols for each column
# linestyles = ['-', '--', ':']  # Define linestyles for each column
# colors = [  '#086a6a','#083c3c','#08a8a8']  # Define colors as shades of #08a8a8

for i, column in enumerate(data.columns[1:4]):
    plt.plot(data.iloc[:, 0], data[column], marker=symbols[i], linestyle='-', color=colors[i], markersize=8, label=column)

# Plot all the data with symbols based on column index
# symbols = ['o', 's', '^', 'd', 'x', '+']  # Define symbols for each column
# colors = [  '#08a8a8','#083c3c','#086a6a','#086a6a','#aa1100','#aa5588']  # Define colors as shades of #08a8a8

# for i, column in enumerate(data.columns[1:]):
#     plt.plot(data.iloc[:, 0], data[column], marker=symbols[i%len(symbols)], linestyle='-', color=colors[i%len(colors)], markersize=8, label=column)



# Set limits for x and y axes
plt.xlim(data.iloc[:, 0].min(), data.iloc[:, 0].max())  # Adjust x-axis limits as needed
plt.ylim(-36.92, -36.6)  # Set y-axis limits (-150 to 50)

# Add title and labels
plt.xlabel('u (unit of a$_0$)', fontsize=f_s)  # Set font size for X-axis label
plt.ylabel('Energy(eV)', fontsize=f_s)  # Set font size for Y-axis label

# Add manual legend
# plt.legend(['$\Gamma$', 'R$_{25}$', 'M','$\Gamma$ + R$_{25}$','$\Gamma$ + M'], fontsize=12)  # Add manual legend with labels
plt.legend(['AFD(001)', 'AFD(110)', 'AFD(111)'], fontsize=18,loc='upper center', ncol=1)  # Add manual legend with labels
# plt.xticks(np.arange(-0.15, 0.15, step=0.05))

# plt.grid()
# Add ticks on the upper and right sides
plt.tick_params(axis='x', direction='in', which='both', bottom=True, top=True, labelbottom=True)
plt.tick_params(axis='y', direction='in', which='both', left=True, right=True, labelleft=True)

# Add subplot label (a)
# plt.text(-6.3, 1.24, '(d)', fontsize=f_s, va='top', ha='left')

######## Panel 5 ######################################################################################################################################################
plt.subplot(3, 2, 5)

# Read the data
file_path = "~/Documents/Work/Parameter/BiAlO/Production_data/Gamma_R_001_110_111_energy_afd_max_vs_u.dat"
data = pd.read_csv(file_path, delim_whitespace=True, skiprows=1)



# # Plot the data with symbols based on column index
symbols = ['o', 's', '^']  # Define symbols for each column
# linestyles = ['-', '--', ':']  # Define linestyles for each column
# colors = [  '#086a6a','#083c3c','#08a8a8']  # Define colors as shades of #08a8a8

for i, column in enumerate(data.columns[1:4]):
    plt.plot(data.iloc[:, 0], data[column], marker=symbols[i], linestyle='-', color=colors[i], markersize=8, label=column)

# Plot all the data with symbols based on column index
# symbols = ['o', 's', '^', 'd', 'x', '+']  # Define symbols for each column
# colors = [  '#08a8a8','#083c3c','#086a6a','#086a6a','#aa1100','#aa5588']  # Define colors as shades of #08a8a8

# for i, column in enumerate(data.columns[1:]):
#     plt.plot(data.iloc[:, 0], data[column], marker=symbols[i%len(symbols)], linestyle='-', color=colors[i%len(colors)], markersize=8, label=column)



# Set limits for x and y axes
plt.xlim(data.iloc[:, 0].min(), data.iloc[:, 0].max())  # Adjust x-axis limits as needed
plt.ylim(-37, -36.6)  # Set y-axis limits (-150 to 50)

# Add title and labels
plt.xlabel('u (unit of a$_0$)', fontsize=f_s)  # Set font size for X-axis label
plt.ylabel('Energy(eV)', fontsize=f_s)  # Set font size for Y-axis label

# Add manual legend
# plt.legend(['$\Gamma$', 'R$_{25}$', 'M','$\Gamma$ + R$_{25}$','$\Gamma$ + M'], fontsize=12)  # Add manual legend with labels
plt.legend(['$\Gamma$+AFD(001)', '$\Gamma$+AFD(110)', '$\Gamma$+AFD(111)'], fontsize=18,loc='upper center', ncol=1)  # Add manual legend with labels
# plt.xticks(np.arange(-0.15, 0.15, step=0.05))

# plt.grid()
# Add ticks on the upper and right sides
plt.tick_params(axis='x', direction='in', which='both', bottom=True, top=True, labelbottom=True)
plt.tick_params(axis='y', direction='in', which='both', left=True, right=True, labelleft=True)

# Add subplot label (c)
# plt.text(-6.3, 1.5, '(e)', fontsize=f_s, va='top', ha='left')


######## Panel 6 ######################################################################################################################################################
plt.subplot(3, 2, 6)

# Read the data
file_path = "~/Documents/Work/Parameter/BiAlO/Production_data/double_well.dat"
data = pd.read_csv(file_path, delim_whitespace=True, skiprows=1)



# # Plot the data with symbols based on column index
symbols = ['o', 's', '^']  # Define symbols for each column
# linestyles = ['-', '--', ':']  # Define linestyles for each column
# colors = [  '#086a6a','#083c3c','#08a8a8']  # Define colors as shades of #08a8a8

for i, column in enumerate(data.columns[1:4]):
    plt.plot(data.iloc[:, 0], data[column], marker=symbols[i], linestyle='-', color=colors[i], markersize=8, label=column)

# Plot all the data with symbols based on column index
# symbols = ['o', 's', '^', 'd', 'x', '+']  # Define symbols for each column
# colors = [  '#08a8a8','#083c3c','#086a6a','#086a6a','#aa1100','#aa5588']  # Define colors as shades of #08a8a8

# for i, column in enumerate(data.columns[1:]):
#     plt.plot(data.iloc[:, 0], data[column], marker=symbols[i%len(symbols)], linestyle='-', color=colors[i%len(colors)], markersize=8, label=column)



# Set limits for x and y axes
plt.xlim(data.iloc[:, 0].min(), data.iloc[:, 0].max())  # Adjust x-axis limits as needed
plt.ylim(-150, 50)  # Set y-axis limits (-150 to 50)

# Add title and labels
plt.xlabel('u (unit of a$_0$)', fontsize=f_s)  # Set font size for X-axis label
plt.ylabel('$\Delta$E(meV)', fontsize=f_s)  # Set font size for Y-axis label

# Add manual legend
# plt.legend(['$\Gamma$', 'R$_{25}$', 'M','$\Gamma$ + R$_{25}$','$\Gamma$ + M'], fontsize=12)  # Add manual legend with labels
plt.legend(['$\Gamma$', 'R$_{25}$', 'M'], fontsize=18,loc='upper center', ncol=3)  # Add manual legend with labels
# plt.xticks(np.arange(-0.15, 0.15, step=0.05))

# plt.grid()
# Add ticks on the upper and right sides
plt.tick_params(axis='x', direction='in', which='both', bottom=True, top=True, labelbottom=True)
plt.tick_params(axis='y', direction='in', which='both', left=True, right=True, labelleft=True)

# Add subplot label (d)
# plt.text(-6.3, 1.5, '(f)', fontsize=f_s, va='top', ha='left')  ## the -6.3  and 1.45 is the value of x and y axis data point in actual scale 

# Define subplot labels and positions
subplot_labels = ['(a)', '(b)', '(c)', '(d)', '(e)', '(f)']
positions = [(3, 2, 1), (3, 2, 2), (3, 2, 3), (3, 2, 4), (3, 2, 5), (3, 2, 6)]

# Label Adding Loop
for i, pos in enumerate(positions):
    plt.subplot(*pos)
    # Add subplot label
    plt.text(0.0, 1.03, subplot_labels[i], transform=plt.gca().transAxes, fontsize=f_s)


# Adjust spacing between subplots
plt.subplots_adjust(hspace=0.4, wspace=0.4)

## Save the plot in high-resolution EPS format
# savepath='/home/<USER>/Documents/Work/Parameter/BiAlO/Production_data/orange/Figure_1_3x2_Panel.eps'
# plt.savefig(savepath, format='eps', dpi=600)


# Show the plot
plt.tight_layout()  # Adjust layout to prevent clipping of labels
plt.show()


import pandas as pd
import matplotlib.pyplot as plt
import matplotlib as mpl

mpl.rcParams['font.family'] = 'Times New Roman'

l_w=2.5
f_s=25
# Adjusting Matplotlib's default settings
mpl.rcParams['axes.linewidth'] = l_w  # Thickness of the axis lines
mpl.rcParams['xtick.major.width'] = l_w  # Thickness of major ticks on x-axis
mpl.rcParams['ytick.major.width'] = l_w  # Thickness of major ticks on y-axis
mpl.rcParams['xtick.minor.width'] = 1.5  # Thickness of minor ticks on x-axis
mpl.rcParams['ytick.minor.width'] = 1.5  # Thickness of minor ticks on y-axis
mpl.rcParams['xtick.major.size'] = 6  # Length of major ticks on x-axis
mpl.rcParams['ytick.major.size'] = 6  # Length of major ticks on y-axis
mpl.rcParams['xtick.minor.size'] = 4  # Length of minor ticks on x-axis
mpl.rcParams['ytick.minor.size'] = 4  # Length of minor ticks on y-axis
mpl.rcParams['xtick.labelsize'] = f_s  # Font size of x-axis tick labels
mpl.rcParams['ytick.labelsize'] = f_s  # Font size of y-axis tick labels

# Read the data
file_path = "~/Documents/Work/Parameter/BiAlO/Production_data/temp_vs_u.out"
data = pd.read_csv(file_path, delim_whitespace=True)

plt.figure(figsize=(8, 6.5))  # Adjust the figure size as needed

# Define columns to plot
columns_to_plot = [1, 3, 5]

# Define marker styles and sizes
symbols = ['^', 's','o' ]  # Define symbols for each column
colors = [ '#083c3c', '#086a6a','#08a8a8']  # Define colors as shades of #08a8a8
linestyles = ['-', '--', ':']  # Define linestyles for each column
marker_sizes = [4, 8, 16]  # Define marker sizes for each column

# Plot the data
for i, column_index in enumerate(columns_to_plot):
    plt.plot(data.iloc[:, 0], np.abs(data.iloc[:, column_index]), marker=symbols[i], markerfacecolor='none' if i>0 else colors[i], linestyle='--',linewidth=0.05, color=colors[i], markersize=marker_sizes[i], label=f'Column {column_index}')


# Set limits for x and y axes
plt.xlim(data.iloc[:, 0].min(), data.iloc[:, 0].max())  # Adjust x-axis limits as needed
# plt.ylim(-150, 50)  # Set y-axis limits (-150 to 50)

# Add title and labels
plt.xlabel('Temperature (K)', fontsize=f_s)  # Set font size for X-axis label
plt.ylabel('<u$_{x,y,z} (\Gamma)$>', fontsize=f_s)  # Set font size for Y-axis label

# Add manual legend
# plt.legend(['$\Gamma$', 'R$_{25}$', 'M','$\Gamma$ + R$_{25}$','$\Gamma$ + M'], fontsize=12)  # Add manual legend with labels
plt.legend(['<u$_x$>', '<u$_y$>', '<u$_z$>'], fontsize=20,loc='upper right')  # Add manual legend with labels
plt.xticks(np.arange(0, 1600, 300))  # Set the tick locations from 0 to 250 in steps of 50

# plt.grid()
# Add ticks on the upper and right sides
plt.tick_params(axis='x', direction='in', which='both', bottom=True, top=True, labelbottom=True)
plt.tick_params(axis='y', direction='in', which='both', left=True, right=True, labelleft=True)

# Save the plot in high-resolution EPS format
# savepath='/home/<USER>/Documents/Work/Parameter/BiAlO/Production_data/temp_vs_u.eps'
# plt.savefig(savepath, format='eps', dpi=600)

# Show the plot
plt.tight_layout()  # Adjust layout to prevent clipping of labels
plt.show()


import pandas as pd
import matplotlib.pyplot as plt
import matplotlib as mpl
mpl.rcParams['font.family'] = 'Times New Roman'

l_w=2.5
f_s=25
# Adjusting Matplotlib's default settings
mpl.rcParams['axes.linewidth'] = l_w  # Thickness of the axis lines
mpl.rcParams['xtick.major.width'] = l_w  # Thickness of major ticks on x-axis
mpl.rcParams['ytick.major.width'] = l_w  # Thickness of major ticks on y-axis
mpl.rcParams['xtick.minor.width'] = 1.5  # Thickness of minor ticks on x-axis
mpl.rcParams['ytick.minor.width'] = 1.5  # Thickness of minor ticks on y-axis
mpl.rcParams['xtick.major.size'] = 6  # Length of major ticks on x-axis
mpl.rcParams['ytick.major.size'] = 6  # Length of major ticks on y-axis
mpl.rcParams['xtick.minor.size'] = 4  # Length of minor ticks on x-axis
mpl.rcParams['ytick.minor.size'] = 4  # Length of minor ticks on y-axis
mpl.rcParams['xtick.labelsize'] = f_s  # Font size of x-axis tick labels
mpl.rcParams['ytick.labelsize'] = f_s  # Font size of y-axis tick labels

# Read the data
file_path = "~/Documents/Work/Parameter/BiAlO/Production_data/temp_vs_afd.out"
data = pd.read_csv(file_path, delim_whitespace=True)

plt.figure(figsize=(8, 6.5))  # Adjust the figure size as needed

# Define columns to plot
columns_to_plot = [1, 3, 5]

# Define marker styles and sizes
symbols = ['^', 's','o' ]  # Define symbols for each column
colors = [ '#083c3c', '#086a6a','#08a8a8']  # Define colors as shades of #08a8a8
linestyles = ['-', '--', ':']  # Define linestyles for each column
marker_sizes = [4, 8, 16]  # Define marker sizes for each column

# Plot the data
for i, column_index in enumerate(columns_to_plot):
    plt.plot(data.iloc[:, 0], np.abs(data.iloc[:, column_index]), marker=symbols[i], markerfacecolor='none' if i>0 else colors[i], linestyle='--',linewidth=0.05, color=colors[i], markersize=marker_sizes[i], label=f'Column {column_index}')


# Set limits for x and y axes
plt.xlim(data.iloc[:, 0].min(), data.iloc[:, 0].max())  # Adjust x-axis limits as needed
# plt.ylim(-150, 50)  # Set y-axis limits (-150 to 50)

# Add title and labels
plt.xlabel('Temperature (K)', fontsize=f_s)  # Set font size for X-axis label
plt.ylabel('<$\omega_{x,y,z} $>', fontsize=f_s)  # Set font size for Y-axis label

# Add manual legend
plt.legend(['$<\omega_x>$', '$<\omega_y$>', '$<\omega_z>$'], fontsize=20,loc='upper right')  # Add manual legend with labels
plt.xticks(np.arange(0, 1600, 300))  # Set the tick locations from 0 to 250 in steps of 50

# plt.grid()
# Add ticks on the upper and right sides
plt.tick_params(axis='x', direction='in', which='both', bottom=True, top=True, labelbottom=True)
plt.tick_params(axis='y', direction='in', which='both', left=True, right=True, labelleft=True)

# Save the plot in high-resolution EPS format
# savepath='/home/<USER>/Documents/Work/Parameter/BiAlO/Production_data/temp_vs_afd.eps'
# plt.savefig(savepath, format='eps', dpi=600)

# Show the plot
plt.tight_layout()  # Adjust layout to prevent clipping of labels
plt.show()

import pandas as pd
import matplotlib.pyplot as plt
import matplotlib as mpl
import numpy as np
mpl.rcParams['font.family'] = 'Times New Roman'

# Adjusting Matplotlib's default settings
mpl.rcParams.update({
    'axes.linewidth': 2.5,  # Thickness of the axis lines
    'xtick.major.width': 2.5,  # Thickness of major ticks on x-axis
    'ytick.major.width': 2.5,  # Thickness of major ticks on y-axis
    'xtick.minor.width': 1.5,  # Thickness of minor ticks on x-axis
    'ytick.minor.width': 1.5,  # Thickness of minor ticks on y-axis
    'xtick.major.size': 6,  # Length of major ticks on x-axis
    'ytick.major.size': 6,  # Length of major ticks on y-axis
    'xtick.minor.size': 4,  # Length of minor ticks on x-axis
    'ytick.minor.size': 4,  # Length of minor ticks on y-axis
    'xtick.labelsize': 25,  # Font size of x-axis tick labels
    'ytick.labelsize': 25,  # Font size of y-axis tick labels
})

# Read the data
file_path = "~/Documents/Work/Parameter/BiAlO/Production_data/temp_vs_lattice.out"
data = pd.read_csv(file_path, delim_whitespace=True)

plt.figure(figsize=(8, 6.5))  # Adjust the figure size as needed

# Define columns to plot
columns_to_plot = [1, 2, 3]

# Define marker styles and sizes
symbols = ['^', 's', 'o']  # Define symbols for each column
colors = ['#083c3c', '#086a6a', '#08a8a8']  # Define colors as shades of #08a8a8
linestyles = ['-', '--', ':']  # Define linestyles for each column
marker_sizes = [4, 8, 16]  # Define marker sizes for each column

# Plot the data
for i, column_index in enumerate(columns_to_plot):
    plt.plot(data.iloc[:, 0], np.abs(data.iloc[:, column_index]), 
             marker=symbols[i], markerfacecolor='none' if i>0 else colors[i], linestyle='--',
             linewidth=0.05, color=colors[i], markersize=marker_sizes[i], 
             label=f'Column {column_index}')

# Set limits for x and y axes
plt.xlim(data.iloc[:, 0].min(), data.iloc[:, 0].max())  # Adjust x-axis limits as needed

# Add title and labels
plt.xlabel('Temperature (K)', fontsize=25)  # Set font size for X-axis label
plt.ylabel('Lattice Parameters (a.u.)', fontsize=25)  # Set font size for Y-axis label

# Add manual legend
plt.legend(['$a$', '$b$', '$c$'], fontsize=20, loc='upper right')
plt.xticks(np.arange(0, 1600, 300))  # Set the tick locations from 0 to 250 in steps of 50
plt.yticks(np.arange(7.08, 7.18, 0.02))  # Set the tick locations from 0 to 250 in steps of 50

# Add ticks on the upper and right sides
plt.tick_params(axis='x', direction='in', which='both', bottom=True, top=True, labelbottom=True)
plt.tick_params(axis='y', direction='in', which='both', left=True, right=True, labelleft=True)

# Add shaded regions
plt.axvspan(0, 1180, color='lavender')  # Shaded region 1
plt.axvspan(1180, 1500, color='lightblue')  # Shaded region 2

# Add labels for shaded regions
plt.text(500, 0.5 * (plt.ylim()[1] - plt.ylim()[0]) + plt.ylim()[0],'R3C', fontsize=f_s, horizontalalignment='center')
plt.text(1320, 0.5 * (plt.ylim()[1] - plt.ylim()[0]) + plt.ylim()[0],'Pm$\overline{3}$m', fontsize=f_s, horizontalalignment='center')

# Save the plot in high-resolution EPS format
# savepath = '/home/<USER>/Documents/Work/Parameter/BiAlO/Production_data/temp_vs_lattice.eps'
# plt.savefig(savepath, format='eps', dpi=600)

# Show the plot
plt.tight_layout()  # Adjust layout to prevent clipping of labels
plt.show()


c=42878.62
z=8.164116829222200
a0=7.032248652103040
v0=a0**3


# Open and read the temp_vs_u_001.out file
with open("hysteresis/temp_vs_u_001.out", "r") as u_file:
    u_lines = u_file.readlines()

# Open and read the temp_vs_v_001.out file to get volume
with open("hysteresis/temp_vs_v_001.out", "r") as v_file:
    v_lines = v_file.readlines()

# Extract volume from the 3rd column of the temp_vs_v_001.out file
volume = float(v_lines[0].split()[2])  # Assuming volume is on the first line and third column

# Open a new file to write the calculated polarization values
with open("temp_vs_pol_001.out", "w") as pol_file:
    for line, v_line in zip(u_lines, v_lines):
        # Split the line into columns
        u_columns = line.split()
        v_columns = v_line.split()

        # Get values from the 2nd, 4th, and 6th columns as u
        u_values = [float(u_columns[i]) for i in [1, 3, 5]]

        # Calculate polarization value for each u_value separately
        polarizations = [c * z * u / float(v_columns[2]) for u in u_values]

        # Write the calculated values to the new file
        pol_file.write(f"{u_columns[0]} {polarizations[0]} {polarizations[1]} {polarizations[2]}\n")

c=42878.62
z=8.164116829222200
a0=7.032248652103040
v0=a0**3

def calculate_polarization(u_file_path, v_file_path, output_file_path):
    # Open and read the temp_vs_u_*.out file
    with open(u_file_path, "r") as u_file:
        u_lines = u_file.readlines()

    # Open and read the temp_vs_v_*.out file to get volume
    with open(v_file_path, "r") as v_file:
        v_lines = v_file.readlines()

    # Extract volume from the 3rd column of the temp_vs_v_*.out file
    volume = float(v_lines[0].split()[2])  # Assuming volume is on the first line and third column

    # Open a new file to write the calculated polarization values
    with open(output_file_path, "w") as pol_file:
        for line, v_line in zip(u_lines, v_lines):
            # Split the line into columns
            u_columns = line.split()
            v_columns = v_line.split()

            # Get values from the 2nd, 4th, and 6th columns as u
            u_values = [float(u_columns[i]) for i in [1, 3, 5]]

            # Calculate polarization value for each u_value separately
            polarizations = [c * z * u / float(v_columns[2]) for u in u_values]
            # polarizations = [c * z * u / v0 for u in u_values]

            # Write the calculated values to the new file
            pol_file.write(f"{u_columns[0]} {polarizations[0]} {polarizations[1]} {polarizations[2]}\n")

# # Calculate polarization for 001 direction
# calculate_polarization("hysteresis/temp_vs_u_001.out", "hysteresis/temp_vs_v_001.out", "temp_vs_pol_001.out")

# Calculate polarization for 001 direction
calculate_polarization("hysteresis/temp_vs_u_100.out", "hysteresis/temp_vs_v_100.out", "temp_vs_pol_100.out")

# # Calculate polarization for 110 direction
# calculate_polarization("hysteresis/temp_vs_u_110.out", "hysteresis/temp_vs_v_110.out", "temp_vs_pol_110.out")

# # Calculate polarization for 111 direction
# calculate_polarization("hysteresis/temp_vs_u_111.out", "hysteresis/temp_vs_v_111.out", "temp_vs_pol_111.out")

# Calculate polarization for 001 direction
# calculate_polarization("/home/<USER>/Documents/Work/Parameter/BiAlO/server1_backup_08_Aug_24/20_cell/new_1lac/refined_temp_vs_u.out",
#                         "/home/<USER>/Documents/Work/Parameter/BiAlO/server1_backup_08_Aug_24/20_cell/new_1lac/temp_vs_v.out", 
#                         "temp_vs_pol_final_20x20x20.out")

# awk '{printf "%s\t%.6f\t%.6f\t%.6f\n", $1, ($3)^(1/3), ($3)^(1/3), ($3)^(1/3)}'  temp_vs_v.out> temp_vs_lattice.out

import pandas as pd
import matplotlib.pyplot as plt
import matplotlib as mpl

mpl.rcParams['font.family'] = 'Times New Roman'

l_w=2.5
f_s=25
# Adjusting Matplotlib's default settings
mpl.rcParams['axes.linewidth'] = l_w  # Thickness of the axis lines
mpl.rcParams['xtick.major.width'] = l_w  # Thickness of major ticks on x-axis
mpl.rcParams['ytick.major.width'] = l_w  # Thickness of major ticks on y-axis
mpl.rcParams['xtick.minor.width'] = 1.5  # Thickness of minor ticks on x-axis
mpl.rcParams['ytick.minor.width'] = 1.5  # Thickness of minor ticks on y-axis
mpl.rcParams['xtick.major.size'] = 6  # Length of major ticks on x-axis
mpl.rcParams['ytick.major.size'] = 6  # Length of major ticks on y-axis
mpl.rcParams['xtick.minor.size'] = 4  # Length of minor ticks on x-axis
mpl.rcParams['ytick.minor.size'] = 4  # Length of minor ticks on y-axis
mpl.rcParams['xtick.labelsize'] = f_s  # Font size of x-axis tick labels
mpl.rcParams['ytick.labelsize'] = f_s  # Font size of y-axis tick labels

# Read the data
file_path = "~/Documents/Work/Parameter/BiAlO/Production_data/temp_vs_pol.out"
data = pd.read_csv(file_path, delim_whitespace=True)

plt.figure(figsize=(8, 6.5))  # Adjust the figure size as needed

# Define columns to plot
columns_to_plot = [1, 2, 3]

# Define marker styles and sizes
symbols = ['^', 's','o' ]  # Define symbols for each column
colors = [ '#083c3c', '#086a6a','#08a8a8']  # Define colors as shades of #08a8a8
linestyles = ['-', '--', ':']  # Define linestyles for each column
marker_sizes = [4, 8, 16]  # Define marker sizes for each column

# Plot the data
for i, column_index in enumerate(columns_to_plot):
    plt.plot(data.iloc[:, 0], np.abs(data.iloc[:, column_index]), marker=symbols[i], markerfacecolor='none' if i>0 else colors[i], linestyle='--',linewidth=0.05, color=colors[i], markersize=marker_sizes[i], label=f'Column {column_index}')

## Experimental Data
son_Ps=[33]
son_Pr=[29]
son_Ec=[180]   #'180kV/cm'
son_temp=[300]
Zylberberg_Ps=[3,14,25,30]
Zylberberg_Pr=[1.1,9.5,22,26.7] 
Zylberberg_Ec=[50,54,70,55]
Zylberberg_temp=[173,298,398,498]

DFT_Pr=[41]
DFT_temp=[0]

plt.scatter(Zylberberg_temp,Zylberberg_Ps,marker='s',color='#BF360C',s=500)
plt.scatter(Zylberberg_temp,Zylberberg_Pr,marker='d',color='#FFB300',s=500)
plt.scatter(son_temp,son_Ps,marker='+',color='#BF360C',s=500)
plt.scatter(son_temp,son_Pr,marker='x',color='#FFB300',s=500)
plt.scatter(DFT_temp,DFT_Pr,marker='*',color='#FFB300',s=500)

# Set limits for x and y axes
plt.xlim(data.iloc[:, 0].min(), data.iloc[:, 0].max())  # Adjust x-axis limits as needed
# plt.ylim(-150, 50)  # Set y-axis limits (-150 to 50)

# Add title and labels
plt.xlabel('Temperature (K)', fontsize=f_s)  # Set font size for X-axis label
plt.ylabel('Polarization ($\mu$C/cm$^2$)', fontsize=f_s)  # Set font size for Y-axis label

# Add manual legend
# plt.legend(['$\Gamma$', 'R$_{25}$', 'M','$\Gamma$ + R$_{25}$','$\Gamma$ + M'], fontsize=12)  # Add manual legend with labels
plt.legend(['P$_x$', 'P$_y$', 'P$_z$'], fontsize=20,loc='upper right')  # Add manual legend with labels
plt.xticks(np.arange(0, 1600, 300))  # Set the tick locations from 0 to 250 in steps of 50
plt.yticks(np.arange(0, 52, 10))

# plt.grid()
# Add ticks on the upper and right sides
plt.tick_params(axis='x', direction='in', which='both', bottom=True, top=True, labelbottom=True)
plt.tick_params(axis='y', direction='in', which='both', left=True, right=True, labelleft=True)

# Save the plot in high-resolution EPS format
# savepath='/home/<USER>/Documents/Work/Parameter/BiAlO/Production_data/temp_vs_pol.eps'
# plt.savefig(savepath, format='eps', dpi=600)

# Show the plot
plt.tight_layout()  # Adjust layout to prevent clipping of labels
plt.show()

import pandas as pd
import matplotlib.pyplot as plt
import matplotlib as mpl
import numpy as np

mpl.rcParams['font.family'] = 'Times New Roman'
mpl.rcParams.update({
    'axes.linewidth': 2.5,  # Thickness of the axis lines
    'xtick.major.width': 2.5,  # Thickness of major ticks on x-axis
    'ytick.major.width': 2.5,  # Thickness of major ticks on y-axis
    'xtick.minor.width': 1.5,  # Thickness of minor ticks on x-axis
    'ytick.minor.width': 1.5,  # Thickness of minor ticks on y-axis
    'xtick.major.size': 6,  # Length of major ticks on x-axis
    'ytick.major.size': 6,  # Length of major ticks on y-axis
    'xtick.minor.size': 4,  # Length of minor ticks on x-axis
    'ytick.minor.size': 4,  # Length of minor ticks on y-axis
    'xtick.labelsize': 25,  # Font size of x-axis tick labels
    'ytick.labelsize': 25,  # Font size of y-axis tick labels
})

# Paths to the data files
file_paths = [
    "~/Documents/Work/Parameter/BiAlO/Production_data/temp_vs_u.out",
    "~/Documents/Work/Parameter/BiAlO/Production_data/temp_vs_afd.out",
    "~/Documents/Work/Parameter/BiAlO/Production_data/temp_vs_lattice.out",
    "~/Documents/Work/Parameter/BiAlO/Production_data/temp_vs_pol.out"
]

# Column indices to plot for each dataset
columns_to_plot = [
    [1, 3, 5],
    [1, 3, 5],
    [1, 2, 3],
    [1, 2, 3]
]

# Define marker styles and sizes
symbols = ['^', 's', 'o']
# colors = ['#083c3c', '#086a6a', '#08a8a8']
colors = [ '#0077BB','#08a841','#FF8300', '#DC4731', '#F89700','#08a8a8']
# colors = [ '#BF360C','#D84315','#EF6C00', '#F57C00', '#FFB300','#FFCC80']
linestyles = ['-', '--', ':']
marker_sizes = [4, 8, 16]

# Titles and labels for each subplot
titles = ['(a)', '(b)', '(c)', '(d)']
y_labels = [
    '<u$_{x,y,z} (\Gamma)$>',
    '<$\omega_{x,y,z} $>',
    'Lattice Parameters (a.u.)',
    'Polarization ($\mu$C/cm$^2$)'
]

# Legends for each subplot
legends = [
    ['<u$_x$>', '<u$_y$>', '<u$_z$>'],
    ['$<\omega_x>$', '$<\omega_y$>', '$<\omega_z>$'],
    ['$a$', '$b$', '$c$'],
    ['P$_x$', 'P$_y$', 'P$_z$']
]

# Create a figure and subplots
fig, axs = plt.subplots(2, 2, figsize=(16, 13))  # Adjust the figure size as needed

for idx, (file_path, cols, ax, title, y_label, legend) in enumerate(zip(file_paths, columns_to_plot, axs.flat, titles, y_labels, legends)):
    data = pd.read_csv(file_path, delim_whitespace=True)
    for i, column_index in enumerate(cols):
        ax.plot(data.iloc[:, 0], np.abs(data.iloc[:, column_index]), 
                marker=symbols[i], markerfacecolor='none' if i > 0 else colors[i], linestyle='--',
                linewidth=0.05, color=colors[i], markersize=marker_sizes[i], 
                label=legend[i])
    
    ax.set_xlim(data.iloc[:, 0].min(), data.iloc[:, 0].max())  # Adjust x-axis limits as needed
    ax.set_xlabel('Temperature (K)', fontsize=f_s)
    ax.set_ylabel(y_label, fontsize=f_s)
    ax.legend(fontsize=20, loc='upper right')
    ax.set_xticks(np.arange(0, 1600, 300))
    
    if idx == 2:  # Specific settings for the lattice parameters plot
        ax.set_yticks(np.arange(7.08, 7.18, 0.02))
        ax.axvspan(0, 1180, color='lavender')  # Shaded region 1 shaded_colors = ['#E5E5E5', '#CCCCCC']
        ax.axvspan(1180, 1500, color='#CCCCCC')  # Shaded region 2
        ax.text(500, 0.5 * (ax.get_ylim()[1] - ax.get_ylim()[0]) + ax.get_ylim()[0], 'R3C', fontsize=f_s, horizontalalignment='center')
        ax.text(1320, 0.5 * (ax.get_ylim()[1] - ax.get_ylim()[0]) + ax.get_ylim()[0], 'Pm$\overline{3}$m', fontsize=f_s, horizontalalignment='center')
    elif idx == 3:  # Specific settings for the polarization plot
        ax.set_yticks(np.arange(0, 52, 10))
    
    ax.tick_params(axis='x', direction='in', which='both', bottom=True, top=True, labelbottom=True)
    ax.tick_params(axis='y', direction='in', which='both', left=True, right=True, labelleft=True)
    ax.set_title(title, loc='left', fontsize=f_s)

# Adjust spacing between plots
plt.subplots_adjust(wspace=0.3, hspace=0.3)

# Save the plot in high-resolution EPS format
# savepath='/home/<USER>/Documents/Work/Parameter/BiAlO/Production_data/orange/Fig2.eps'
# plt.savefig(savepath, format='eps', dpi=600)

plt.tight_layout()
plt.show()


## New modification and experimental value addition
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib as mpl
import numpy as np

mpl.rcParams['font.family'] = 'Times New Roman'
mpl.rcParams.update({
    'axes.linewidth': 2.5,  # Thickness of the axis lines
    'xtick.major.width': 2.5,  # Thickness of major ticks on x-axis
    'ytick.major.width': 2.5,  # Thickness of major ticks on y-axis
    'xtick.minor.width': 1.5,  # Thickness of minor ticks on x-axis
    'ytick.minor.width': 1.5,  # Thickness of minor ticks on y-axis
    'xtick.major.size': 6,  # Length of major ticks on x-axis
    'ytick.major.size': 6,  # Length of major ticks on y-axis
    'xtick.minor.size': 4,  # Length of minor ticks on x-axis
    'ytick.minor.size': 4,  # Length of minor ticks on y-axis
    'xtick.labelsize': 25,  # Font size of x-axis tick labels
    'ytick.labelsize': 25,  # Font size of y-axis tick labels
})

# Paths to the data files
file_paths = [
    "/home/<USER>/Documents/Work/Parameter/BiAlO/server1_backup_08_Aug_24/20_cell/new_1lac/refined_temp_vs_u.out",
    "~/Documents/Work/Parameter/BiAlO/Production_data/temp_vs_pol_final_20x20x20.out",
    "/home/<USER>/Documents/Work/Parameter/BiAlO/server1_backup_08_Aug_24/20_cell/new_1lac/temp_vs_afd.out",
    "/home/<USER>/Documents/Work/Parameter/BiAlO/server1_backup_08_Aug_24/20_cell/new_1lac/temp_vs_lattice_20x20x20.out"
]
## Experimental Data
son_Ps = [33]
son_Pr = [29]
son_Ec = [180]   #'180kV/cm'
son_temp = [300]
Zylberberg_Ps = [3,14,25,30]
Zylberberg_Pr = [1.1,9.5,22,26.7] 
Zylberberg_Ec = [50,54,70,55]
Zylberberg_temp = [173,298,398,498]
rao_temp=[300]
rao_Pr=[11.5]
rao_Ps=[16]
rao_Ec=[69 ] #kV/cm

DFT_Pr = [41]
DFT_temp = [0]

# Column indices to plot for each dataset
columns_to_plot = [
    [1, 3, 5],
    [1, 2, 3],
    [1, 3, 5],
    [1, 2, 3]
]

# Define marker styles and sizes
symbols = ['^', 's', 'o']
colors = [ '#B8390E','#08a841','#FF8300', '#DC4731', '#F89700','#08a8a8']
colors_0 = ['#083c3c', '#086a6a', '#08a8a8']
linestyles = ['-', '--', ':']
marker_sizes = [4, 8, 16]
f_s=25
# Titles and labels for each subplot
titles = ['(a)', '(b)', '(c)', '(d)']
y_labels = [
    '<u$_{x,y,z} (\Gamma)$>',
    'Polarization ($\mu$C/cm$^2$)',
    '<$\omega_{x,y,z} $>',
    'Lattice Parameters (a.u.)'
]

# Legends for each subplot
legends = [
    ['<u$_x$>', '<u$_y$>', '<u$_z$>'],
    ['P$_x$', 'P$_y$', 'P$_z$'],
    ['$<\omega_x>$', '$<\omega_y$>', '$<\omega_z>$'],
    ['$a$', '$b$', '$c$']
]

# Create a figure and subplots
fig, axs = plt.subplots(2, 2, figsize=(16, 13))  # Adjust the figure size as needed

for idx, (file_path, cols, ax, title, y_label, legend) in enumerate(zip(file_paths, columns_to_plot, axs.flat, titles, y_labels, legends)):
    data = pd.read_csv(file_path, delim_whitespace=True)
    for i, column_index in enumerate(cols):
        ax.plot(data.iloc[:, 0], np.abs(data.iloc[:, column_index]), 
                marker=symbols[i], markerfacecolor='none' if i > 0 else colors[i], linestyle='--',
                linewidth=0.05, color=colors[i], markersize=marker_sizes[i], 
                label=legend[i])
    if idx == 1:
        # Scatter plots for experimental and DFT data
        ax.scatter(Zylberberg_temp, Zylberberg_Pr, marker='d', color=colors_0[2], s=300, label='Ref. [a]')
        ax.scatter(son_temp, son_Pr, marker='>', color=colors_0[1], s=300, label='Ref. [b]')
        ax.scatter(rao_temp, rao_Pr, marker='*', color=colors_0[0], s=300, label='Ref. [c]')

        # ax.scatter(DFT_temp, DFT_Pr, marker='*', color=colors_0[0], s=300, label='DFT_P$_r$')
        
        # Combine all legends into a single call
        handles, labels = ax.get_legend_handles_labels()
        ax.legend(handles, labels, fontsize=f_s, loc='lower right')

    # ax.set_xlim(data.iloc[:, 0].min(), data.iloc[:, 0].max())  # Adjust x-axis limits as needed
    ax.set_xlim(-50, 1500)  # Extend x-axis to start from -10
    ax.set_xlabel('Temperature (K)', fontsize=f_s)
    ax.set_ylabel(y_label, fontsize=f_s)
    ax.legend(fontsize=f_s, loc='lower left')
    ax.set_xticks(np.arange(0, 1600, 300))
    
    if idx == 1:  # Specific settings for the polarization plot
        
        ax.set_yticks(np.arange(0, 52, 10))  
        # ax.set_xticks(np.arange(0, 1600, 300)) 
        # ax.set_xticklabels(['0', '300', '600', '900', '1200', '1500'])  # Manually set x-tick labels
        ax.legend(fontsize=f_s, loc='lower center',ncol=1, columnspacing=0.1,handletextpad=0.1,bbox_to_anchor=(0.6, 0.0))

    elif idx == 3:  # Specific settings for the lattice parameters plot
        ax.set_yticks(np.arange(7.08, 7.19, 0.02))
        ax.axvspan(-50, 1180, color='lavender')  # Shaded region 1
        ax.axvspan(1180, 1500, color='#CCCCCC')  # Shaded region 2
        ax.text(500, 0.5 * (ax.get_ylim()[1] - ax.get_ylim()[0]) + ax.get_ylim()[0], 'R3C', fontsize=f_s, horizontalalignment='center')
        ax.text(1320, 0.5 * (ax.get_ylim()[1] - ax.get_ylim()[0]) + ax.get_ylim()[0], 'Pm$\overline{3}$m', fontsize=f_s, horizontalalignment='center')
    
    ax.tick_params(axis='x', direction='in', which='both', bottom=True, top=True, labelbottom=True)
    ax.tick_params(axis='y', direction='in', which='both', left=True, right=True, labelleft=True)
    # ax.set_title(title, loc='left', fontsize=25)

    # Adjust title position and alignment
    ax.set_title(title, loc='center', fontsize=f_s, pad=17)  # `pad` moves the title up, and `loc='center'` centers the title
    
# Adjust spacing between plots
plt.subplots_adjust(wspace=0.3, hspace=0.4)

# Save the plot in high-resolution EPS format
savepath='/home/<USER>/Documents/Work/Parameter/BiAlO/Production_data/orange/Fig2_with_exp.eps'
plt.savefig(savepath, format='eps', dpi=600)

plt.tight_layout()
plt.show()



import pandas as pd
import matplotlib.pyplot as plt
import matplotlib as mpl
# mpl.rcParams['font.family'] = 'Times New Roman'

l_w=2.5
f_s=25
# Adjusting Matplotlib's default settings
mpl.rcParams['axes.linewidth'] = l_w  # Thickness of the axis lines
mpl.rcParams['xtick.major.width'] = l_w  # Thickness of major ticks on x-axis
mpl.rcParams['ytick.major.width'] = l_w  # Thickness of major ticks on y-axis
mpl.rcParams['xtick.minor.width'] = 1.5  # Thickness of minor ticks on x-axis
mpl.rcParams['ytick.minor.width'] = 1.5  # Thickness of minor ticks on y-axis
mpl.rcParams['xtick.major.size'] = 6  # Length of major ticks on x-axis
mpl.rcParams['ytick.major.size'] = 6  # Length of major ticks on y-axis
mpl.rcParams['xtick.minor.size'] = 4  # Length of minor ticks on x-axis
mpl.rcParams['ytick.minor.size'] = 4  # Length of minor ticks on y-axis
mpl.rcParams['xtick.labelsize'] = f_s  # Font size of x-axis tick labels
mpl.rcParams['ytick.labelsize'] = f_s  # Font size of y-axis tick labels

# Read the data
file_path = "/home/<USER>/Documents/IITD/bao/Production_data/temp_vs_pol_100.out"
data = pd.read_csv(file_path, delim_whitespace=True)

plt.figure(figsize=(8, 6.5))  # Adjust the figure size as needed

# Define columns to plot
columns_to_plot = [1,2,3]

# Define marker styles and sizes
symbols = ['^', 's','o' ]  # Define symbols for each column
colors = [ '#083c3c', '#086a6a','#08a8a8']  # Define colors as shades of #08a8a8
linestyles = ['-', '--', ':']  # Define linestyles for each column
marker_sizes = [5, 8, 16]  # Define marker sizes for each column

# for i, column in enumerate(data.columns[1:4]):
#     plt.plot(data.iloc[:, 0], data[column],linestyle='--', linewidth=1.5, color=colors[i], marker=symbols[i], markerfacecolor='none' if i> 0 else colors[i],  markersize=marker_sizes[i], label=f'Column {column_index}')

# Plot the data
for i, column_index in enumerate(columns_to_plot):
    plt.plot(data.iloc[:, 0], (data.iloc[:, column_index]), marker=symbols[i], markerfacecolor='none' if i>0 else colors[i], linestyle='--',linewidth=0.5, color=colors[i], markersize=marker_sizes[i], label=f'Column {column_index}')


# Set limits for x and y axes
plt.xlim(data.iloc[:, 0].min(), data.iloc[:, 0].max())  # Adjust x-axis limits as needed
# plt.ylim(-150, 50)  # Set y-axis limits (-150 to 50)

# Add title and labels
plt.xlabel('Electric Field (MV/cm)', fontsize=f_s)  # Set font size for X-axis label
plt.ylabel('Polarization ($\mu$C/cm$^2$)', fontsize=f_s)  # Set font size for Y-axis label

# Add manual legend
plt.legend(['$P_x$', '$P_y$', '$P_z$'], fontsize=20,loc='center')  # Add manual legend with labels
# plt.xticks(np.arange(-4, 4, 1))  # Set the tick locations from 0 to 250 in steps of 50

# Add ticks on the upper and right sides
plt.tick_params(axis='x', direction='in', which='both', bottom=True, top=True, labelbottom=True)
plt.tick_params(axis='y', direction='in', which='both', left=True, right=True, labelleft=True)


# plt.grid()


# Save the plot in high-resolution EPS format
# savepath='/home/<USER>/Documents/Work/Parameter/BiAlO/Production_data/temp_vs_pol_111.eps'
# plt.savefig(savepath, format='eps', dpi=600)

# Show the plot
plt.tight_layout()  # Adjust layout to prevent clipping of labels
plt.show()

import pandas as pd
import matplotlib.pyplot as plt
import matplotlib as mpl
mpl.rcParams['font.family'] = 'Times New Roman'

l_w=2.5
f_s=25
# Adjusting Matplotlib's default settings
mpl.rcParams['axes.linewidth'] = l_w  # Thickness of the axis lines
mpl.rcParams['xtick.major.width'] = l_w  # Thickness of major ticks on x-axis
mpl.rcParams['ytick.major.width'] = l_w  # Thickness of major ticks on y-axis
mpl.rcParams['xtick.minor.width'] = 1.5  # Thickness of minor ticks on x-axis
mpl.rcParams['ytick.minor.width'] = 1.5  # Thickness of minor ticks on y-axis
mpl.rcParams['xtick.major.size'] = 6  # Length of major ticks on x-axis
mpl.rcParams['ytick.major.size'] = 6  # Length of major ticks on y-axis
mpl.rcParams['xtick.minor.size'] = 4  # Length of minor ticks on x-axis
mpl.rcParams['ytick.minor.size'] = 4  # Length of minor ticks on y-axis
mpl.rcParams['xtick.labelsize'] = f_s  # Font size of x-axis tick labels
mpl.rcParams['ytick.labelsize'] = f_s  # Font size of y-axis tick labels

# Read the data
file_path = "./hysteresis/temp_vs_afd_100.out"
data = pd.read_csv(file_path, delim_whitespace=True)

plt.figure(figsize=(8, 6.5))  # Adjust the figure size as needed

# Define columns to plot
columns_to_plot = [1, 3, 5]

# Define marker styles and sizes
symbols = ['^', 's','o' ]  # Define symbols for each column
colors = [ '#083c3c', '#086a6a','#08a8a8']  # Define colors as shades of #08a8a8
linestyles = ['-', '--', ':']  # Define linestyles for each column
marker_sizes = [4, 10, 10]  # Define marker sizes for each column

# Plot the data
for i, column_index in enumerate(columns_to_plot):
    plt.plot(data.iloc[:, 0], (data.iloc[:, column_index]), marker=symbols[i],  markerfacecolor='none' if i> 0 else colors[i], linestyle='--',linewidth=0.5, color=colors[i], markersize=marker_sizes[i], label=f'Column {column_index}')


# Set limits for x and y axes
plt.xlim(data.iloc[:, 0].min(), data.iloc[:, 0].max())  # Adjust x-axis limits as needed
# plt.ylim(-150, 50)  # Set y-axis limits (-150 to 50)

# Add title and labels
plt.xlabel('Electric Field (MV/cm)', fontsize=f_s)  # Set font size for X-axis label
plt.ylabel('<$\omega_{x,y,z} $>', fontsize=f_s)  # Set font size for Y-axis label

# Add manual legend
plt.legend(['$\omega_x$', '$\omega_y$', '$\omega_z$'], fontsize=20,loc='lower center')  # Add manual legend with labels
# plt.xticks(np.arange(0, 1600, 300))  # Set the tick locations from 0 to 250 in steps of 50
# plt.yticks(np.arange(0.054, 0.06, 0.001))  # Set the tick locations from 0 to 250 in steps of 50
# plt.yticks(np.arange(0.05, 0.064, 0.002))  # Set the tick locations from 0 to 250 in steps of 50
# plt.grid()
# Add ticks on the upper and right sides
plt.tick_params(axis='x', direction='in', which='both', bottom=True, top=True, labelbottom=True)
plt.tick_params(axis='y', direction='in', which='both', left=True, right=True, labelleft=True)


# Save the plot in high-resolution EPS format
# savepath='/home/<USER>/Documents/Work/Parameter/BiAlO/Production_data/temp_vs_afd_001.eps'
# plt.savefig(savepath, format='eps', dpi=600)

# Show the plot
plt.tight_layout()  # Adjust layout to prevent clipping of labels
plt.show()

import pandas as pd
import matplotlib.pyplot as plt
import matplotlib as mpl
mpl.rcParams['font.family'] = 'Times New Roman'

l_w=2.5
f_s=25
# Adjusting Matplotlib's default settings
mpl.rcParams['axes.linewidth'] = l_w  # Thickness of the axis lines
mpl.rcParams['xtick.major.width'] = l_w  # Thickness of major ticks on x-axis
mpl.rcParams['ytick.major.width'] = l_w  # Thickness of major ticks on y-axis
mpl.rcParams['xtick.minor.width'] = 1.5  # Thickness of minor ticks on x-axis
mpl.rcParams['ytick.minor.width'] = 1.5  # Thickness of minor ticks on y-axis
mpl.rcParams['xtick.major.size'] = 6  # Length of major ticks on x-axis
mpl.rcParams['ytick.major.size'] = 6  # Length of major ticks on y-axis
mpl.rcParams['xtick.minor.size'] = 4  # Length of minor ticks on x-axis
mpl.rcParams['ytick.minor.size'] = 4  # Length of minor ticks on y-axis
mpl.rcParams['xtick.labelsize'] = f_s  # Font size of x-axis tick labels
mpl.rcParams['ytick.labelsize'] = f_s  # Font size of y-axis tick labels

# Read the data
file_path = "./hysteresis/temp_vs_strain_100.out"
data = pd.read_csv(file_path, delim_whitespace=True)

plt.figure(figsize=(8, 6.5))  # Adjust the figure size as needed

# Define columns to plot
columns_to_plot = [1, 2, 3]

# Define marker styles and sizes
symbols = ['^', 's','o' ]  # Define symbols for each column
colors = [ '#083c3c', '#086a6a','#08a8a8']  # Define colors as shades of #08a8a8
linestyles = ['-', '--', ':']  # Define linestyles for each column
marker_sizes = [4, 10, 10]  # Define marker sizes for each column

# Plot the data
for i, column_index in enumerate(columns_to_plot):
    plt.plot(data.iloc[:, 0], (data.iloc[:, column_index]), marker=symbols[i],  markerfacecolor='none' if i> 0 else colors[i], linestyle='--',linewidth=0.5, color=colors[i], markersize=marker_sizes[i], label=f'Column {column_index}')


# Set limits for x and y axes
plt.xlim(data.iloc[:, 0].min(), data.iloc[:, 0].max())  # Adjust x-axis limits as needed
# plt.ylim(-150, 50)  # Set y-axis limits (-150 to 50)

# Add title and labels
plt.xlabel('Electric Field (MV/cm)', fontsize=f_s)  # Set font size for X-axis label
plt.ylabel('Strain', fontsize=f_s)  # Set font size for Y-axis label

# Add manual legend
plt.legend(['$\eta_1$', '$\eta_2$', '$\eta_3$'], fontsize=20,loc='lower center')  # Add manual legend with labels
# plt.xticks(np.arange(0, 1600, 300))  # Set the tick locations from 0 to 250 in steps of 50
# plt.yticks(np.arange(0.015, 0.021, 0.001))  # Set the tick locations from 0 to 250 in steps of 50
# plt.yticks(np.arange(0.015, 0.021, 0.001))  # Set the tick locations from 0 to 250 in steps of 50
# plt.grid()

# Add ticks on the upper and right sides
plt.tick_params(axis='x', direction='in', which='both', bottom=True, top=True, labelbottom=True)
plt.tick_params(axis='y', direction='in', which='both', left=True, right=True, labelleft=True)


# Save the plot in high-resolution EPS format
# savepath='/home/<USER>/Documents/Work/Parameter/BiAlO/Production_data/temp_vs_strain_001.eps'
# plt.savefig(savepath, format='eps', dpi=600)

# Show the plot
plt.tight_layout()  # Adjust layout to prevent clipping of labels
plt.show()

import pandas as pd
import matplotlib.pyplot as plt
import matplotlib as mpl
import numpy as np

mpl.rcParams['font.family'] = 'Times New Roman'

l_w = 2.5
f_s = 25
# Adjusting Matplotlib's default settings
mpl.rcParams['axes.linewidth'] = l_w  # Thickness of the axis lines
mpl.rcParams['xtick.major.width'] = l_w  # Thickness of major ticks on x-axis
mpl.rcParams['ytick.major.width'] = l_w  # Thickness of major ticks on y-axis
mpl.rcParams['xtick.minor.width'] = 1.5  # Thickness of minor ticks on x-axis
mpl.rcParams['ytick.minor.width'] = 1.5  # Thickness of minor ticks on y-axis
mpl.rcParams['xtick.major.size'] = 6  # Length of major ticks on x-axis
mpl.rcParams['ytick.major.size'] = 6  # Length of major ticks on y-axis
mpl.rcParams['xtick.minor.size'] = 4  # Length of minor ticks on x-axis
mpl.rcParams['ytick.minor.size'] = 4  # Length of minor ticks on y-axis
mpl.rcParams['xtick.labelsize'] = f_s  # Font size of x-axis tick labels
mpl.rcParams['ytick.labelsize'] = f_s  # Font size of y-axis tick labels

# Define file paths
file_paths = [
    "~/Documents/Work/Parameter/BiAlO/Production_data/temp_vs_pol_111.out",
    "~/Documents/Work/Parameter/BiAlO/Production_data/hysteresis/temp_vs_afd_111.out",
    "~/Documents/Work/Parameter/BiAlO/Production_data/hysteresis/temp_vs_strain_111.out",
    "~/Documents/Work/Parameter/BiAlO/Production_data/temp_vs_pol_110.out",
    "~/Documents/Work/Parameter/BiAlO/Production_data/hysteresis/temp_vs_afd_110.out",
    "~/Documents/Work/Parameter/BiAlO/Production_data/hysteresis/temp_vs_strain_110.out",
    "~/Documents/Work/Parameter/BiAlO/Production_data/temp_vs_pol_001.out",
    "~/Documents/Work/Parameter/BiAlO/Production_data/hysteresis/temp_vs_afd_001.out",
    "~/Documents/Work/Parameter/BiAlO/Production_data/hysteresis/temp_vs_strain_001.out"
]

# Define titles for columns
column_titles = ['E [111]', 'E [110]', 'E [001]']

# Define colors and markers
colors = ['#BF360C', '#08a841', '#EF6C00', '#F57C00', '#FFB300', '#FFCC80']
symbols = ['^', 's', 'o']
linestyles = ['-', '--', ':']
marker_sizes = [4, 8, 16]  # Define marker sizes for each column

# Create a figure
fig, axs = plt.subplots(3, 3, figsize=(20, 18))

# Plotting loop
for col in range(3):
    for row in range(3):
        file_path = file_paths[row + col * 3]
        data = pd.read_csv(file_path, delim_whitespace=True)

        # Determine columns to plot based on file path pattern
        if '_pol' in file_path:
            columns_to_plot = [1, 2, 3]
            labels = ['$P_x$', '$P_y$', '$P_z$']
            ylabel = 'Polarization ($\mu$C/cm$^2$)'
        elif '_afd' in file_path:
            columns_to_plot = [1, 3, 5]
            labels = ['$\omega_x$', '$\omega_y$', '$\omega_z$']
            ylabel = '<$\omega_{x,y,z}$>'
        elif '_strain' in file_path:
            columns_to_plot = [1, 2, 3]
            labels = ['$\eta_1$', '$\eta_2$', '$\eta_3$']
            ylabel = 'Strain'

        ax = axs[row, col]
        
        for i, column_index in enumerate(columns_to_plot):
            ax.plot(data.iloc[:, 0], data.iloc[:, column_index], 
                    linestyle=linestyles[i], linewidth=1.5 if i == 0 else 0.01, color=colors[i], 
                    marker=symbols[i], markerfacecolor='none' if i > 0 else colors[i], 
                    markersize=marker_sizes[i], label=labels[i])

        # Set limits for x and y axes
        # ax.set_xlim(data.iloc[:, 0].min(), data.iloc[:, 0].max())
        ax.set_xlim(-4.5,4.5)
        ax.set_xticks(np.arange(-4, 4.3))
       
        # Add labels
        ax.set_ylabel(ylabel, fontsize=f_s)
        ax.set_xlabel('Electric Field (MV/cm)', fontsize=f_s)

        # Add legend
        if row == 0: 
            ax.legend(fontsize=f_s, loc='center')
            if col == 2:
               ax.legend(fontsize=f_s, loc='center',bbox_to_anchor=(0.5, 0.6)) 
            ax.set_yticks(np.arange(-60, 65, 20))
        elif row == 1:
            if col == 2:
             ax.legend(fontsize=f_s, loc='lower center')
            elif col == 0: 
               ax.legend(fontsize=f_s, loc='lower center',bbox_to_anchor=(0.5, -0.07),handletextpad=0.2 )
                #  ax.legend(fontsize=f_s, loc='upper center')
            else:
                ax.legend(fontsize=f_s, loc='upper center',bbox_to_anchor=(0.5, 1.08),handletextpad=0.2) 
            if col == 1:
                ax.set_yticks(np.arange(0.05, 0.065, 0.002))
        else:
            ax.legend(fontsize=f_s, loc='lower center')
            ax.set_yticks(np.arange(0.015, 0.021, 0.001))

        # Add ticks on the upper and right sides
        ax.tick_params(axis='x', direction='in', which='both', bottom=True, top=True, labelbottom=True)
        ax.tick_params(axis='y', direction='in', which='both', left=True, right=True, labelleft=True)
        
        # Add subplot labels (a), (b), etc.
        label = f"({chr(97 + col + row * 3)})"
        if row == 0:
            ax.text(0.37, 1.13, label, transform=ax.transAxes, fontsize=f_s, va='top', ha='right')
        else:
            ax.text(0.54, 1.13, label, transform=ax.transAxes, fontsize=f_s, va='top', ha='right')    

# Add column titles
# for col, title in enumerate(column_titles):
#     fig.text(0.20 + col * 0.33, 0.91, title, ha='center', va='center', fontsize=f_s, fontweight='bold')            # 
# Custom positions for the column titles
title_positions = [0.21, 0.535, 0.86]  # Adjust these values to your specific needs

for col, title in enumerate(column_titles):
    fig.text(title_positions[col], 0.91, title, ha='center', va='center', fontsize=f_s, fontweight='bold')

# Adjust spacing between plots
plt.subplots_adjust(wspace=0.1, hspace=0.1)

# Ensure tight layout
plt.tight_layout(rect=[0, 0, 1, 0.96])

# Save the plot in high-resolution EPS format
savepath='/home/<USER>/Documents/Work/Parameter/BiAlO/Production_data/orange/Fig3.eps'
plt.savefig(savepath, format='eps', dpi=600)

plt.show()


c = 42878.62
z = 8.164116829222200
a0 = 7.032248652103040
v0 = a0 ** 3

# Open and read the temp_vs_hyst.out file
with open("hysteresis/temp_vs_hyst.out", "r") as u_file:
    u_lines = u_file.readlines()

# Open a new file to write the calculated polarization values
with open("temp_vs_pol_hyst.out", "w") as pol_file:
    # Write the header from the input file
    header = u_lines[0]  # Assuming the header is the first line
    pol_file.write(header)

    for line in u_lines[1:]:  # Skip the header line
        # Split the line into columns
        u_columns = line.split()

        # Ensure there are enough columns in the line
        if len(u_columns) >= 2:
            # Get E_Field value (first column)
            e_field = float(u_columns[0])

            # Get temperature values (other columns)
            temperatures = [float(value) for value in u_columns[1:]]

            # Calculate polarization value for each temperature separately
            polarizations = [c * z * t / v0 for t in temperatures]

            # Write the calculated values to the new file
            pol_file.write("{}\t{}\n".format(e_field, '\t'.join(map(str, polarizations))))


import pandas as pd
import matplotlib.pyplot as plt
import matplotlib as mpl
mpl.rcParams['font.family'] = 'Times New Roman'
l_w=2.5
f_s=25
# Adjusting Matplotlib's default settings
mpl.rcParams['axes.linewidth'] = l_w  # Thickness of the axis lines
mpl.rcParams['xtick.major.width'] = l_w  # Thickness of major ticks on x-axis
mpl.rcParams['ytick.major.width'] = l_w  # Thickness of major ticks on y-axis
mpl.rcParams['xtick.minor.width'] = 1.5  # Thickness of minor ticks on x-axis
mpl.rcParams['ytick.minor.width'] = 1.5  # Thickness of minor ticks on y-axis
mpl.rcParams['xtick.major.size'] = 6  # Length of major ticks on x-axis
mpl.rcParams['ytick.major.size'] = 6  # Length of major ticks on y-axis
mpl.rcParams['xtick.minor.size'] = 4  # Length of minor ticks on x-axis
mpl.rcParams['ytick.minor.size'] = 4  # Length of minor ticks on y-axis
mpl.rcParams['xtick.labelsize'] = f_s  # Font size of x-axis tick labels
mpl.rcParams['ytick.labelsize'] = f_s  # Font size of y-axis tick labels

# Read the data
file_path = "~/Documents/Work/Parameter/BiAlO/Production_data/temp_vs_pol_hyst.out"
data = pd.read_csv(file_path, delim_whitespace=True)

plt.figure(figsize=(8, 6.5))  # Adjust the figure size as needed

# Define columns to plot
columns_to_plot = [3, 6, 8, 10]

# Define marker styles and sizes
symbols = ['^', 's','o', '>']  # Define symbols for each column
# colors = [ '#083c3c', '#086a6a','#08a8a8','#08a841']  # Define colors as shades of #08a8a8
colors = ['#BF360C', '#D84315', '#EF6C00', '#FFB300', '#FFCC80']
linestyles = ['-', '--', ':', '-.']  # Define linestyles for each column
marker_sizes = [5, 16, 16, 16]  # Define marker sizes for each column

# Plot the data
for i, column_index in enumerate(columns_to_plot):
    plt.plot(data.iloc[:, 0], (data.iloc[:, column_index]), marker=symbols[i], markersize='10', markerfacecolor='none' if i>0 else colors[i],
             linestyle=linestyles[i],linewidth=1.5, color=colors[i], label=f'Column {column_index}') 


# Set limits for x and y axes
# plt.xlim(data.iloc[:, 0].min(), data.iloc[:, 0].max())  # Adjust x-axis limits as needed
# plt.ylim(-150, 50)  # Set y-axis limits (-150 to 50)

# Add title and labels
plt.xlabel('Electric Field (MV/cm)', fontsize=f_s)  # Set font size for X-axis label
plt.ylabel('Polarization ($\mu$C/cm$^2$)', fontsize=f_s)  # Set font size for Y-axis label

# Add manual legend
plt.legend(['100 K', '400 K', '600 K', '800 K'], fontsize=20,loc='center')  # Add manual legend with labels
# plt.xticks(np.arange(0, 1600, 300))  # Set the tick locations from 0 to 250 in steps of 50
plt.yticks(np.arange(-60, 62, 20))  # Set the tick locations from 0 to 250 in steps of 50
# plt.grid()
# Add ticks on the upper and right sides
plt.tick_params(axis='x', direction='in', which='both', bottom=True, top=True, labelbottom=True)
plt.tick_params(axis='y', direction='in', which='both', left=True, right=True, labelleft=True)
# Plot the lines x=0 and y=0
plt.axvline(x=0, color='b', linestyle='--', linewidth=0.5)  # Plot vertical line at x=0
plt.axhline(y=0, color='b', linestyle='--', linewidth=0.5)  # Plot horizontal line at y=0

# Save the plot in high-resolution EPS format
# savepath='/home/<USER>/Documents/Work/Parameter/BiAlO/Production_data/orange/temp_vs_pol_hyst.eps'
# plt.savefig(savepath, format='eps', dpi=600)

# Show the plot
plt.tight_layout()  # Adjust layout to prevent clipping of labels
plt.show()

import pandas as pd
import matplotlib.pyplot as plt
import matplotlib as mpl

l_w=2.5
f_s=25
# Adjusting Matplotlib's default settings
mpl.rcParams['axes.linewidth'] = l_w  # Thickness of the axis lines
mpl.rcParams['xtick.major.width'] = l_w  # Thickness of major ticks on x-axis
mpl.rcParams['ytick.major.width'] = l_w  # Thickness of major ticks on y-axis
mpl.rcParams['xtick.minor.width'] = 1.5  # Thickness of minor ticks on x-axis
mpl.rcParams['ytick.minor.width'] = 1.5  # Thickness of minor ticks on y-axis
mpl.rcParams['xtick.major.size'] = 6  # Length of major ticks on x-axis
mpl.rcParams['ytick.major.size'] = 6  # Length of major ticks on y-axis
mpl.rcParams['xtick.minor.size'] = 4  # Length of minor ticks on x-axis
mpl.rcParams['ytick.minor.size'] = 4  # Length of minor ticks on y-axis
mpl.rcParams['xtick.labelsize'] = f_s  # Font size of x-axis tick labels
mpl.rcParams['ytick.labelsize'] = f_s  # Font size of y-axis tick labels

# Read the data
file_path = "~/Documents/Work/Parameter/BiAlO/Production_data/temp_vs_pol_hyst.out"
data = pd.read_csv(file_path, delim_whitespace=True)

plt.figure(figsize=(8, 6.5))  # Adjust the figure size as needed

# Define columns to plot
columns_to_plot = [3, 6, 8, 10]

# Define marker styles and sizes
symbols = ['^', 's','o', '>']  # Define symbols for each column
colors = [ '#083c3c', '#086a6a','#08a8a8','#08a841']  # Define colors as shades of #08a8a8
linestyles = ['-', '--', ':', '-.']  # Define linestyles for each column
marker_sizes = [5, 16, 16, 16]  # Define marker sizes for each column

# Plot the data
for i, column_index in enumerate(columns_to_plot):
    plt.plot(data.iloc[:, 0], (data.iloc[:, column_index]), marker=symbols[i], markersize='10', markerfacecolor='none' if i>0 else colors[i],
             linestyle=linestyles[i],linewidth=1.5, color=colors[i], label=f'Column {column_index}') 


# Set limits for x and y axes
# plt.xlim(data.iloc[:, 0].min(), data.iloc[:, 0].max())  # Adjust x-axis limits as needed
# plt.ylim(-150, 50)  # Set y-axis limits (-150 to 50)

# Add title and labels
plt.xlabel('Electric Field (MV/cm)', fontsize=f_s)  # Set font size for X-axis label
plt.ylabel('Polarization ($\mu$C/cm$^2$)', fontsize=f_s)  # Set font size for Y-axis label

# Add manual legend
plt.legend(['100 K', '400 K', '600 K', '800 K'], fontsize=20,loc='center')  # Add manual legend with labels
# plt.xticks(np.arange(0, 1600, 300))  # Set the tick locations from 0 to 250 in steps of 50
plt.yticks(np.arange(-60, 62, 20))  # Set the tick locations from 0 to 250 in steps of 50
# plt.grid()
# Add ticks on the upper and right sides
plt.tick_params(axis='x', direction='in', which='both', bottom=True, top=True, labelbottom=True)
plt.tick_params(axis='y', direction='in', which='both', left=True, right=True, labelleft=True)
# Plot the lines x=0 and y=0
plt.axvline(x=0, color='b', linestyle='--', linewidth=0.5)  # Plot vertical line at x=0
plt.axhline(y=0, color='b', linestyle='--', linewidth=0.5)  # Plot horizontal line at y=0
# plt.axvline(x=3.1, color='b', linestyle='--', linewidth=0.5)  # Plot horizontal line at y=0
# plt.axvline(x=-3.1, color='b', linestyle='--', linewidth=0.5)  # Plot horizontal line at y=0

# # Add marker arrows with text for E_c = +3.1 and -3.1
# plt.annotate('E_c = +3.1', xy=(3.1, 0), xytext=(3.1 + 200, 20),
#              arrowprops=dict(facecolor='black', shrink=0.05),
#              fontsize=15, color='black')

# plt.annotate('E_c = -3.1', xy=(-3.1, 0), xytext=(-3.1 + 200, 20),
#              arrowprops=dict(facecolor='black', shrink=0.05),
#              fontsize=15, color='black')


# Save the plot in high-resolution EPS format
# savepath='/home/<USER>/Documents/Work/Parameter/BiAlO/Production_data/temp_vs_pol_hyst.eps'
# plt.savefig(savepath, format='eps', dpi=600)

# Show the plot
plt.tight_layout()  # Adjust layout to prevent clipping of labels
plt.show()


import pandas as pd
import matplotlib.pyplot as plt
import matplotlib as mpl
from matplotlib import rc
mpl.rcParams['font.family'] = 'Times New Roman'

l_w=2.5
f_s=25
# Adjusting Matplotlib's default settings
mpl.rcParams['axes.linewidth'] = l_w  # Thickness of the axis lines
mpl.rcParams['xtick.major.width'] = l_w  # Thickness of major ticks on x-axis
mpl.rcParams['ytick.major.width'] = l_w  # Thickness of major ticks on y-axis
mpl.rcParams['xtick.minor.width'] = 1.5  # Thickness of minor ticks on x-axis
mpl.rcParams['ytick.minor.width'] = 1.5  # Thickness of minor ticks on y-axis
mpl.rcParams['xtick.major.size'] = 6  # Length of major ticks on x-axis
mpl.rcParams['ytick.major.size'] = 6  # Length of major ticks on y-axis
mpl.rcParams['xtick.minor.size'] = 4  # Length of minor ticks on x-axis
mpl.rcParams['ytick.minor.size'] = 4  # Length of minor ticks on y-axis
mpl.rcParams['xtick.labelsize'] = f_s  # Font size of x-axis tick labels
mpl.rcParams['ytick.labelsize'] = f_s  # Font size of y-axis tick labels

# Read the data
file_path = "~/Documents/Work/Parameter/BiAlO/Production_data/temp_vs_Ps_Pr.out"
data = pd.read_csv(file_path, delim_whitespace=True)

plt.figure(figsize=(8, 6.5))  # Adjust the figure size as needed

# Define columns to plot
# columns_to_plot = [1, 2]
columns_to_plot = [1]

# Define marker styles and sizes
symbols = ['o', 's','o' ]  # Define symbols for each column
colors1 = [ '#08a841', '#086a6a','#08a8a8']  # Define colors as shades of #08a8a8
colors = ['#BF360C', '#FFB300', '#EF6C00', '#D84315',  '#FFCC80']
linestyles = ['-.', '--', ':']  # Define linestyles for each column
marker_sizes = [16, 16, 16]  # Define marker sizes for each column

# Plot the data
for i, column_index in enumerate(columns_to_plot):
    plt.plot(data.iloc[:, 0], np.abs(data.iloc[:, column_index]), marker=symbols[i], markerfacecolor='none' , linestyle=linestyles[i],linewidth=1.5
             , color=colors[2], markersize=marker_sizes[i], label=f'Column {column_index}')

## Experimental Data
son_Ps=[33]
son_Pr=[29]
son_Ec=[180]   #'180kV/cm'
son_temp=[300]
Zylberberg_Ps=[3,14,25,30]
Zylberberg_Pr=[1.1,9.5,22,26.7] 
Zylberberg_Ec=[50,54,70,55]
Zylberberg_temp=[173,298,398,498]
rao_temp=[300]
rao_Pr=[11.5]
rao_Ps=[16]
rao_Ec=[69 ] #kV/cm


DFT_Pr=[41]
DFT_temp=[0]

plt.scatter(Zylberberg_temp,Zylberberg_Ps,marker='s',color=colors1[0],s=300)
# plt.scatter(Zylberberg_temp,Zylberberg_Pr,marker='d',color='#FFB300',s=500)
plt.scatter(son_temp,son_Ps,marker='+',color=colors1[1],s=500)
# plt.scatter(son_temp,son_Pr,marker='x',color='#FFB300',s=500)
# plt.scatter(DFT_temp,DFT_Pr,marker='*',color='#FFB300',s=500)
plt.scatter(rao_temp,rao_Ps,marker='*',color=colors1[2],s=700)


# Set limits for x and y axes
# plt.xlim(data.iloc[:, 0].min(), data.iloc[:, 0].max())  # Adjust x-axis limits as needed
# plt.ylim(38, 60)  # Set y-axis limits (-150 to 50)

# Add title and labels
plt.xlabel('Temperature (K)', fontsize=f_s)  # Set font size for X-axis label
plt.ylabel('Polarization ($\mu$C/cm$^2$)', fontsize=f_s)  # Set font size for Y-axis label

# Add manual legend
# plt.legend(['P$_s$', 'P$_r$', 'Zylberberg  $\mathit{et \ al.}$', 'Zylberberg  $\mathit{et \ al.}$','Son $\mathit{et \ al.}$','Son $\mathit{et \ al.}$', 'DFT' ], fontsize=20,loc='lower right')  # Add manual legend with labels
plt.legend(['P$_s$', 'Ref.[a]', 'Ref.[b]','Ref.[c]' ], fontsize=20,loc='lower right')  # Add manual legend with labels
plt.xticks(np.arange(0, 1100, 300))  # Set the tick locations from 0 to 250 in steps of 50


# plt.grid()
# Add ticks on the upper and right sides
plt.tick_params(axis='x', direction='in', which='both', bottom=True, top=True, labelbottom=True)
plt.tick_params(axis='y', direction='in', which='both', left=True, right=True, labelleft=True)

# Save the plot in high-resolution EPS format
savepath='/home/<USER>/Documents/Work/Parameter/BiAlO/Production_data/orange/temp_vs_Ps_Pr.eps'
plt.savefig(savepath, format='eps', dpi=600)

# Show the plot
plt.tight_layout()  # Adjust layout to prevent clipping of labels
plt.show()


import pandas as pd
import matplotlib.pyplot as plt
import matplotlib as mpl

l_w=2.5
f_s=25
# Adjusting Matplotlib's default settings
mpl.rcParams['axes.linewidth'] = l_w  # Thickness of the axis lines
mpl.rcParams['xtick.major.width'] = l_w  # Thickness of major ticks on x-axis
mpl.rcParams['ytick.major.width'] = l_w  # Thickness of major ticks on y-axis
mpl.rcParams['xtick.minor.width'] = 1.5  # Thickness of minor ticks on x-axis
mpl.rcParams['ytick.minor.width'] = 1.5  # Thickness of minor ticks on y-axis
mpl.rcParams['xtick.major.size'] = 6  # Length of major ticks on x-axis
mpl.rcParams['ytick.major.size'] = 6  # Length of major ticks on y-axis
mpl.rcParams['xtick.minor.size'] = 4  # Length of minor ticks on x-axis
mpl.rcParams['ytick.minor.size'] = 4  # Length of minor ticks on y-axis
mpl.rcParams['xtick.labelsize'] = f_s  # Font size of x-axis tick labels
mpl.rcParams['ytick.labelsize'] = f_s  # Font size of y-axis tick labels

# Read the data
file_path = "~/Documents/Work/Parameter/BiAlO/Production_data/temp_vs_Pr.out"
data = pd.read_csv(file_path, delim_whitespace=True)

plt.figure(figsize=(8, 6.5))  # Adjust the figure size as needed

# Define columns to plot
columns_to_plot = [1]

# Define marker styles and sizes
symbols = ['^', 's','o' ]  # Define symbols for each column
colors = [ '#08a841', '#086a6a','#08a8a8']  # Define colors as shades of #08a8a8
linestyles = ['-.', '--', ':']  # Define linestyles for each column
marker_sizes = [16, 16, 16]  # Define marker sizes for each column

# Plot the data
for i, column_index in enumerate(columns_to_plot):
    plt.plot(data.iloc[:, 0], np.abs(data.iloc[:, column_index]), marker=symbols[i], markerfacecolor='none' , linestyle=linestyles[i],linewidth=1.5
             , color=colors[i], markersize=marker_sizes[i], label=f'Column {column_index}')

## Experimental Data
son_Ps=[300,33]
son_Pr=[300,29]
son_Ec=[180]   #'180kV/cm'

Zylberberg_Ps=[3,14,25,30]
Zylberberg_Pr=[1.1,9.5,22,26.7] 
Zylberberg_Ec=[50,54,70,55]
Zylberberg_temp=[173,298,398,498]

plt.plot(Zylberberg_temp,Zylberberg_Pr,marker='^',markersize='20',color='orange', linewidth=0.0001)

# Set limits for x and y axes
# plt.xlim(data.iloc[:, 0].min(), data.iloc[:, 0].max())  # Adjust x-axis limits as needed
# plt.ylim(38, 60)  # Set y-axis limits (-150 to 50)

# Add title and labels
plt.xlabel('Temperature (K)', fontsize=f_s)  # Set font size for X-axis label
plt.ylabel('Polarization ($\mu$C/cm$^2$)', fontsize=f_s)  # Set font size for Y-axis label

# Add manual legend
# plt.legend(['$\Gamma$', 'R$_{25}$', 'M','$\Gamma$ + R$_{25}$','$\Gamma$ + M'], fontsize=12)  # Add manual legend with labels
plt.legend(['P$_s$', 'P$_r$'], fontsize=20,loc='upper right')  # Add manual legend with labels
plt.xticks(np.arange(0, 1100, 300))  # Set the tick locations from 0 to 250 in steps of 50

# plt.grid()
# Add ticks on the upper and right sides
plt.tick_params(axis='x', direction='in', which='both', bottom=True, top=True, labelbottom=True)
plt.tick_params(axis='y', direction='in', which='both', left=True, right=True, labelleft=True)

# Save the plot in high-resolution EPS format
savepath='/home/<USER>/Documents/Work/Parameter/BiAlO/Production_data/temp_vs_Pr.eps'
# plt.savefig(savepath, format='eps', dpi=600)

# Show the plot
plt.tight_layout()  # Adjust layout to prevent clipping of labels
plt.show()


import pandas as pd
import matplotlib.pyplot as plt
import matplotlib as mpl
from scipy.interpolate import interp1d
mpl.rcParams['font.family'] = 'Times New Roman'

l_w=2.5
f_s=25
# Adjusting Matplotlib's default settings
mpl.rcParams['axes.linewidth'] = l_w  # Thickness of the axis lines
mpl.rcParams['xtick.major.width'] = l_w  # Thickness of major ticks on x-axis
mpl.rcParams['ytick.major.width'] = l_w  # Thickness of major ticks on y-axis
mpl.rcParams['xtick.minor.width'] = 1.5  # Thickness of minor ticks on x-axis
mpl.rcParams['ytick.minor.width'] = 1.5  # Thickness of minor ticks on y-axis
mpl.rcParams['xtick.major.size'] = 6  # Length of major ticks on x-axis
mpl.rcParams['ytick.major.size'] = 6  # Length of major ticks on y-axis
mpl.rcParams['xtick.minor.size'] = 4  # Length of minor ticks on x-axis
mpl.rcParams['ytick.minor.size'] = 4  # Length of minor ticks on y-axis
mpl.rcParams['xtick.labelsize'] = f_s  # Font size of x-axis tick labels
mpl.rcParams['ytick.labelsize'] = f_s  # Font size of y-axis tick labels

# Read the data
file_path = "~/Documents/Work/Parameter/BiAlO/Production_data/temp_vs_Ec.out"
data = pd.read_csv(file_path, delim_whitespace=True)

plt.figure(figsize=(8, 6.5))  # Adjust the figure size as needed

# Define columns to plot
columns_to_plot = [1,2,3]

# Define marker styles and sizes
symbols = ['o', 's','o' ]  # Define symbols for each column
colors = [ '#08a841', '#086a6a','#08a8a8']  # Define colors as shades of #08a8a8
linestyles = ['-', '--', ':']  # Define linestyles for each column
marker_sizes = [16, 8, 16]  # Define marker sizes for each column

# # Plot the data
for i, column_index in enumerate(columns_to_plot):
    plt.plot(data.iloc[:, 0], np.abs(data.iloc[:, column_index]), marker=symbols[i], markerfacecolor='none' , linestyle='-',linewidth=1.5, color=colors[i], markersize=marker_sizes[i], label=f'Column {column_index}')

# Plot the data in smooth line and scatter plot
# for i, column_index in enumerate(columns_to_plot):
#     x = data.iloc[:, 0]
#     y = np.abs(data.iloc[:, column_index])
    
#     plt.scatter(x, y, marker=symbols[i], color=colors[i], s=marker_sizes[i]*30, label=f'Column {column_index}', facecolor='none')

#     # Interpolate to get smoother lines
#     f = interp1d(x, y, kind='cubic')
#     x_interp = np.linspace(min(x), max(x), 1000)  # 1000 points for smoother lines
#     y_interp = f(x_interp)
    
#     plt.plot(x_interp, y_interp, linestyle='-', linewidth=l_w, color=colors[i])


## Experimental Data
son_Ps=[300,33]
son_Pr=[300,29]
son_Ec=[180*1e-3]   #'180kV/cm'
son_temp=[300]
Zylberberg_Ps=[3,14,25,30]
Zylberberg_Pr=[1.1,9.5,22,26.7] 
Zylberberg_Ec = [x * 1e-3 for x in [50, 54, 70, 55]]
Zylberberg_temp=[173,298,398,498]


plt.scatter(Zylberberg_temp,Zylberberg_Ec,marker='H',s=500,color='#086a6a',facecolor='none')
plt.scatter(son_temp,son_Ec,marker='*',s=500,color='#086a6a',facecolor='none')


# Add title and labels
plt.xlabel('Temperature (K)', fontsize=f_s)  # Set font size for X-axis label
plt.ylabel('Coercive Field (MV/cm)', fontsize=f_s)  # Set font size for Y-axis label

# Add manual legend
plt.legend(['E$_c$', 'Exp.', 'Exp.'], fontsize=20,loc='upper right')  # Add manual legend with labels
plt.xticks(np.arange(0, 1100, 300))  # Set the tick locations from 0 to 250 in steps of 50

# plt.grid()
# Add ticks on the upper and right sides
plt.tick_params(axis='x', direction='in', which='both', bottom=True, top=True, labelbottom=True)
plt.tick_params(axis='y', direction='in', which='both', left=True, right=True, labelleft=True)

# Save the plot in high-resolution EPS format
savepath='/home/<USER>/Documents/Work/Parameter/BiAlO/Production_data/temp_vs_Ec.eps'
# plt.savefig(savepath, format='eps', dpi=600)

# Show the plot
plt.tight_layout()  # Adjust layout to prevent clipping of labels
plt.show()

import pandas as pd
import matplotlib.pyplot as plt
import matplotlib as mpl
import numpy as np
from mpl_toolkits.axes_grid1.inset_locator import inset_axes
mpl.rcParams['font.family'] = 'Times New Roman'

l_w = 2.5
f_s = 25

# Adjusting Matplotlib's default settings
mpl.rcParams['axes.linewidth'] = l_w
mpl.rcParams['xtick.major.width'] = l_w
mpl.rcParams['ytick.major.width'] = l_w
mpl.rcParams['xtick.minor.width'] = 1.5
mpl.rcParams['ytick.minor.width'] = 1.5
mpl.rcParams['xtick.major.size'] = 6
mpl.rcParams['ytick.major.size'] = 6
mpl.rcParams['xtick.minor.size'] = 4
mpl.rcParams['ytick.minor.size'] = 4
mpl.rcParams['xtick.labelsize'] = f_s
mpl.rcParams['ytick.labelsize'] = f_s

# Read the data
file_path = "~/Documents/Work/Parameter/BiAlO/Production_data/temp_vs_Ec.out"
data = pd.read_csv(file_path, delim_whitespace=True)

plt.figure(figsize=(8, 6.5))

# Define columns to plot
columns_to_plot = [1,2,3]

# Define marker styles and sizes
symbols = ['o', 's', 'o']
colors = ['#FF8300', '#086a6a', '#08a8a8']
marker_sizes = [16, 8, 16]

# Plot the data
for i, column_index in enumerate(columns_to_plot):
    plt.plot(data.iloc[:, 0], np.abs(data.iloc[:, column_index]), marker=symbols[i], markerfacecolor='none',
             linestyle='-.', linewidth=1.5, color=colors[i], markersize=marker_sizes[i], label=f'Theoretical {column_index}')

# Add title and labels
plt.xlabel('Temperature (K)', fontsize=f_s)
plt.ylabel('Coercive Field (MV/cm)', fontsize=f_s)

# Add manual legend
plt.legend(['E$_c$', 'Exp.', 'Exp.'], fontsize=20, loc='upper right')
plt.xticks(np.arange(0, 1100, 300))

# Add ticks on the upper and right sides
plt.tick_params(axis='x', direction='in', which='both', bottom=True, top=True, labelbottom=True)
plt.tick_params(axis='y', direction='in', which='both', left=True, right=True, labelleft=True)


son_Ec=[180]   #'180kV/cm'
son_temp=[300]
Zylberberg_Ec = [50, 54, 70, 55]
Zylberberg_temp=[173,298,398,498]

# # Inset plot for experimental data
# # left, bottom, width, height = [0.62, 0.59, 0.3, 0.3]  # Define inset position and size
# ax_inset = inset_axes(plt.gca(), width="40%", height="40%", loc='upper right')
# # ax_inset = plt.axes([left, bottom, width, height])
# ax_inset.scatter(Zylberberg_temp, Zylberberg_Ec, marker='H', s=400, color='#C20078')
# ax_inset.scatter(son_temp, son_Ec, marker='*', s=400, color='#FE420F')
# ax_inset.set_xlabel('Temperature (K)', fontsize='20')
# ax_inset.set_ylabel('E$_c$ (kV/cm)', fontsize='20')
# ax_inset.tick_params(axis='both', direction='in', labelsize='20')
# # ax_inset.set_xticks(np.arange(100, 650, 200))  # Setting yticks
# ax_inset.set_yticks(np.arange(30, 230, 30))  # Setting yticks
# ax_inset.legend(['Exp.', 'Exp.'], fontsize=15, loc='center left')

# Adjust layout manually
# plt.subplots_adjust(left=0.1, bottom=0.1, right=0.9, top=0.9, wspace=0.2, hspace=0.2)

# Save the plot
# savepath = '/home/<USER>/Documents/Work/Parameter/BiAlO/Production_data/orange/temp_vs_Ec_without_inset.eps'
# plt.savefig(savepath, format='eps', dpi=600)

# Show the plot
plt.tight_layout()
plt.show()


import pandas as pd
import matplotlib.pyplot as plt
import matplotlib as mpl
mpl.rcParams['font.family'] = 'Times New Roman'
l_w=2.5
f_s=25
# Adjusting Matplotlib's default settings
mpl.rcParams['axes.linewidth'] = l_w  # Thickness of the axis lines
mpl.rcParams['xtick.major.width'] = l_w  # Thickness of major ticks on x-axis
mpl.rcParams['ytick.major.width'] = l_w  # Thickness of major ticks on y-axis
mpl.rcParams['xtick.minor.width'] = 1.5  # Thickness of minor ticks on x-axis
mpl.rcParams['ytick.minor.width'] = 1.5  # Thickness of minor ticks on y-axis
mpl.rcParams['xtick.major.size'] = 6  # Length of major ticks on x-axis
mpl.rcParams['ytick.major.size'] = 6  # Length of major ticks on y-axis
mpl.rcParams['xtick.minor.size'] = 4  # Length of minor ticks on x-axis
mpl.rcParams['ytick.minor.size'] = 4  # Length of minor ticks on y-axis
mpl.rcParams['xtick.labelsize'] = f_s  # Font size of x-axis tick labels
mpl.rcParams['ytick.labelsize'] = f_s  # Font size of y-axis tick labels

# Read the data
file_path = "~/Documents/Work/Parameter/BiAlO/Production_data/temp_vs_strain.out"
data = pd.read_csv(file_path, delim_whitespace=True)

plt.figure(figsize=(8, 6.5))  # Adjust the figure size as needed

# Define columns to plot
columns_to_plot = [1, 2, 3]

# Define marker styles and sizes
symbols = ['^', 's','o' ]  # Define symbols for each column
# colors = [ '#083c3c', '#086a6a','#08a8a8']  # Define colors as shades of #08a8a8
colors = ['#BF360C', '#FFB300', '#EF6C00', '#D84315',  '#FFCC80']
linestyles = ['-', '--', ':']  # Define linestyles for each column
marker_sizes = [4, 8, 16]  # Define marker sizes for each column

# Plot the data
for i, column_index in enumerate(columns_to_plot):
    plt.plot(data.iloc[:, 0], np.abs(data.iloc[:, column_index]), marker=symbols[i], markerfacecolor='none' if i>0 else colors[i], linestyle='--',linewidth=0.02, color=colors[i], markersize=marker_sizes[i], label=f'Column {column_index}')


# Set limits for x and y axes
plt.xlim(data.iloc[:, 0].min(), data.iloc[:, 0].max())  # Adjust x-axis limits as needed
# plt.ylim(-150, 50)  # Set y-axis limits (-150 to 50)

# Add title and labels
plt.xlabel('Temperature (K)', fontsize=f_s)  # Set font size for X-axis label
plt.ylabel('Strain', fontsize=f_s)  # Set font size for Y-axis label

# Add manual legend
plt.legend(['$\eta_1$', '$\eta_2$', '$\eta_3$'], fontsize=20,loc='upper right')  # Add manual legend with labels
plt.xticks(np.arange(0, 1600, 300))  # Set the tick locations from 0 to 250 in steps of 50

# plt.grid()
# Add ticks on the upper and right sides
plt.tick_params(axis='x', direction='in', which='both', bottom=True, top=True, labelbottom=True)
plt.tick_params(axis='y', direction='in', which='both', left=True, right=True, labelleft=True)

# Save the plot in high-resolution EPS format
# savepath='/home/<USER>/Documents/Work/Parameter/BiAlO/Production_data/temp_vs_u.eps'
# plt.savefig(savepath, format='eps', dpi=600)

# Show the plot
plt.tight_layout()  # Adjust layout to prevent clipping of labels
plt.show()

import pandas as pd
import matplotlib.pyplot as plt
import matplotlib as mpl
mpl.rcParams['font.family'] = 'Times New Roman'
l_w=2.5
f_s=25
# Adjusting Matplotlib's default settings
mpl.rcParams['axes.linewidth'] = l_w  # Thickness of the axis lines
mpl.rcParams['xtick.major.width'] = l_w  # Thickness of major ticks on x-axis
mpl.rcParams['ytick.major.width'] = l_w  # Thickness of major ticks on y-axis
mpl.rcParams['xtick.minor.width'] = 1.5  # Thickness of minor ticks on x-axis
mpl.rcParams['ytick.minor.width'] = 1.5  # Thickness of minor ticks on y-axis
mpl.rcParams['xtick.major.size'] = 6  # Length of major ticks on x-axis
mpl.rcParams['ytick.major.size'] = 6  # Length of major ticks on y-axis
mpl.rcParams['xtick.minor.size'] = 4  # Length of minor ticks on x-axis
mpl.rcParams['ytick.minor.size'] = 4  # Length of minor ticks on y-axis
mpl.rcParams['xtick.labelsize'] = f_s  # Font size of x-axis tick labels
mpl.rcParams['ytick.labelsize'] = f_s  # Font size of y-axis tick labels

# Read the data
file_path = "~/Documents/Work/Parameter/BiAlO/Production_data/temp_vs_piezo.out"
data = pd.read_csv(file_path, delim_whitespace=True)

plt.figure(figsize=(8, 6.5))  # Adjust the figure size as needed

# Define columns to plot
columns_to_plot = [1, 2, 3]

# Define marker styles and sizes
symbols = ['^', 's','o' ]  # Define symbols for each column
colors = [ '#083c3c', '#086a6a','#08a8a8']  # Define colors as shades of #08a8a8
linestyles = ['-', '--', ':']  # Define linestyles for each column
marker_sizes = [4, 8, 16]  # Define marker sizes for each column

# Plot the data
for i, column_index in enumerate(columns_to_plot):
    plt.plot(data.iloc[:, 0], np.abs(data.iloc[:, column_index]), marker=symbols[i], markerfacecolor='none' if i>0 else colors[i], linestyle='--',linewidth=2.02, color=colors[i], markersize=marker_sizes[i], label=f'Column {column_index}')


# Set limits for x and y axes
# plt.xlim(data.iloc[:, 0].min(), data.iloc[:, 0].max())  # Adjust x-axis limits as needed
# plt.ylim(-150, 50)  # Set y-axis limits (-150 to 50)
plt.xlim(1100, 1400)

# Add title and labels
plt.xlabel('Temperature (K)', fontsize=f_s)  # Set font size for X-axis label
plt.ylabel('Piezo Electric Coefficient ', fontsize=f_s)  # Set font size for Y-axis label

# Add manual legend
plt.legend(['$\eta_1$', '$\eta_2$', '$\eta_3$'], fontsize=20,loc='upper right')  # Add manual legend with labels
# plt.xticks(np.arange(0, 1600, 300))  # Set the tick locations from 0 to 250 in steps of 50

# plt.grid()
# Add ticks on the upper and right sides
plt.tick_params(axis='x', direction='in', which='both', bottom=True, top=True, labelbottom=True)
plt.tick_params(axis='y', direction='in', which='both', left=True, right=True, labelleft=True)

# Save the plot in high-resolution EPS format
# savepath='/home/<USER>/Documents/Work/Parameter/BiAlO/Production_data/temp_vs_u.eps'
# plt.savefig(savepath, format='eps', dpi=600)

# Show the plot
plt.tight_layout()  # Adjust layout to prevent clipping of labels
plt.show()


import pandas as pd
import matplotlib.pyplot as plt
import matplotlib as mpl
from matplotlib import rc
mpl.rcParams['font.family'] = 'Times New Roman'
l_w=2.5
f_s=25
# Adjusting Matplotlib's default settings
mpl.rcParams['axes.linewidth'] = l_w  # Thickness of the axis lines
mpl.rcParams['xtick.major.width'] = l_w  # Thickness of major ticks on x-axis
mpl.rcParams['ytick.major.width'] = l_w  # Thickness of major ticks on y-axis
mpl.rcParams['xtick.minor.width'] = 1.5  # Thickness of minor ticks on x-axis
mpl.rcParams['ytick.minor.width'] = 1.5  # Thickness of minor ticks on y-axis
mpl.rcParams['xtick.major.size'] = 6  # Length of major ticks on x-axis
mpl.rcParams['ytick.major.size'] = 6  # Length of major ticks on y-axis
mpl.rcParams['xtick.minor.size'] = 4  # Length of minor ticks on x-axis
mpl.rcParams['ytick.minor.size'] = 4  # Length of minor ticks on y-axis
mpl.rcParams['xtick.labelsize'] = f_s  # Font size of x-axis tick labels
mpl.rcParams['ytick.labelsize'] = f_s  # Font size of y-axis tick labels

# Read the data
file_path = "/home/<USER>/Documents/Work/Parameter/BiAlO/Production_data/temp_vs_Smax_Srem.out"
data = pd.read_csv(file_path, delim_whitespace=True)

plt.figure(figsize=(8, 6.5))  # Adjust the figure size as needed

# Define columns to plot
columns_to_plot = [1, 2]

# Define marker styles and sizes
symbols = ['o', 's','o' ]  # Define symbols for each column
# colors = [ '#08a841', '#086a6a','#08a8a8']  # Define colors as shades of #08a8a8
colors = ['#BF360C', '#FFB300', '#EF6C00', '#D84315',  '#FFCC80']
linestyles = ['-.', '--', ':']  # Define linestyles for each column
marker_sizes = [16, 16, 16]  # Define marker sizes for each column

# Plot the data
for i, column_index in enumerate(columns_to_plot):
    plt.plot(data.iloc[:, 0], np.abs(data.iloc[:, column_index]), marker=symbols[i], markerfacecolor='none' , linestyle=linestyles[i],linewidth=1.5
             , color=colors[i], markersize=marker_sizes[i], label=f'Column {column_index}')


# Set limits for x and y axes
# plt.xlim(data.iloc[:, 0].min(), data.iloc[:, 0].max())  # Adjust x-axis limits as needed
# plt.ylim(38, 60)  # Set y-axis limits (-150 to 50)

# Add title and labels
plt.xlabel('Temperature (K)', fontsize=f_s)  # Set font size for X-axis label
plt.ylabel('Strain', fontsize=f_s)  # Set font size for Y-axis label

# Add manual legend
# plt.legend(['P$_s$', 'P$_r$', 'Zylberberg  $\mathit{et \ al.}$', 'Zylberberg  $\mathit{et \ al.}$','Son $\mathit{et \ al.}$','Son $\mathit{et \ al.}$', 'DFT' ], fontsize=20,loc='lower right')  # Add manual legend with labels
plt.legend(['S$_{rem}$', 'S$_{max}$'], fontsize=20,loc='upper right')  # Add manual legend with labels
plt.xticks(np.arange(0, 1100, 300))  # Set the tick locations from 0 to 250 in steps of 50


# plt.grid()
# Add ticks on the upper and right sides
plt.tick_params(axis='x', direction='in', which='both', bottom=True, top=True, labelbottom=True)
plt.tick_params(axis='y', direction='in', which='both', left=True, right=True, labelleft=True)

# Save the plot in high-resolution EPS format
# savepath='/home/<USER>/Documents/Work/Parameter/BiAlO/Production_data/orange/temp_vs_Smax_Srem.eps'
# plt.savefig(savepath, format='eps', dpi=600)

# Show the plot
plt.tight_layout()  # Adjust layout to prevent clipping of labels
plt.show()

import matplotlib.pyplot as plt
import matplotlib as mpl
from matplotlib.transforms import Bbox

mpl.rcParams['font.family'] = 'Times New Roman'
f_s = 25

# Create a figure
fig = plt.figure(figsize=(16, 13))

# Define the paths to the EPS files
eps_files = [
    '/home/<USER>/Documents/Work/Parameter/BiAlO/Production_data/orange/temp_vs_pol_hyst.eps',
    '/home/<USER>/Documents/Work/Parameter/BiAlO/Production_data/orange/temp_vs_Ps_Pr.eps',
    '/home/<USER>/Documents/Work/Parameter/BiAlO/Production_data/orange/temp_vs_Ec_without_inset.eps',
    '/home/<USER>/Documents/Work/Parameter/BiAlO/Production_data/orange/temp_vs_Smax_Srem.eps'
]

# Define labels for the subplots
labels = ['(a)', '(b)', '(c)', '(d)']

# Positions for the subplots
positions = [
    [0.05, 0.55, 0.4, 0.4],  # (left, bottom, width, height)
    [0.55, 0.55, 0.4, 0.4],
    [0.05, 0.05, 0.4, 0.4],
    [0.55, 0.05, 0.4, 0.4]
]

for i, pos in enumerate(positions):
    ax = fig.add_axes(pos)
    ax.set_xticks([])
    ax.set_yticks([])
    ax.axis('off')
    
    # Read and plot the EPS image
    with open(eps_files[i], 'rb') as f:
        img = plt.imread(f, format='eps')
        ax.imshow(img)
    
    # Add subplot label
    label = labels[i]
    ax.text(0.05, 0.95, label, transform=ax.transAxes, fontsize=f_s, va='top', ha='left')

# Adjust spacing between plots
plt.subplots_adjust(wspace=0.05, hspace=0.05)

# Save the combined figure in high-resolution EPS format
combined_savepath = '/home/<USER>/Documents/Work/Parameter/BiAlO/Production_data/orange/Fig4_matplot_trial.eps'
plt.savefig(combined_savepath, format='eps', dpi=600)

plt.show()


pip install reportlab


import fitz  # PyMuPDF

import fitz  # PyMuPDF
from PIL import Image

# Define the paths to the EPS files
eps_files = [
    '/home/<USER>/Documents/Work/Parameter/BiAlO/Production_data/orange/temp_vs_pol_hyst.eps',
    '/home/<USER>/Documents/Work/Parameter/BiAlO/Production_data/orange/temp_vs_Ps_Pr.eps',
    '/home/<USER>/Documents/Work/Parameter/BiAlO/Production_data/orange/temp_vs_Ec_without_inset.eps',
    '/home/<USER>/Documents/Work/Parameter/BiAlO/Production_data/orange/temp_vs_Smax_Srem.eps'
]

# Define labels for the subplots
labels = ['(a)', '(b)', '(c)', '(d)']

# Path to the Times New Roman font file
# font_path = '/usr/share/fonts/truetype/msttcorefonts/Times_New_Roman.ttf'  # Update this path based on your system

# Create a new PDF to hold the combined EPS files
pdf = fitz.open()

# Set the layout for the combined figure
width, height = 595, 842  # A4 size in points
rect = fitz.Rect(0, 0, width, height)

# Function to convert EPS to PNG for handling in PyMuPDF
def convert_eps_to_png(eps_file):
    img = Image.open(eps_file)
    png_file = eps_file.replace('.eps', '.png')
    img.save(png_file, 'PNG')
    return png_file

# Create a new page for the combined figure
page = pdf.new_page(width=width, height=height)

# Define positions for the 2x2 layout
positions = [
    fitz.Rect(0, height/2, width/2, height),
    fitz.Rect(width/2, height/2, width, height),
    fitz.Rect(0, 0, width/2, height/2),
    fitz.Rect(width/2, 0, width, height/2)
]

# Insert each EPS file into the PDF
for i, eps_file in enumerate(eps_files):
    png_file = convert_eps_to_png(eps_file)
    page.insert_image(positions[i], filename=png_file)
    page.insert_text((positions[i].x0 + 20, positions[i].y0 + 20), labels[i], fontsize=25)

# Save the combined PDF
combined_savepath = '/home/<USER>/Documents/Work/Parameter/BiAlO/Production_data/orange/Fig4_trial.pdf'
pdf.save(combined_savepath)

print(f"Combined PDF saved at: {combined_savepath}")


from reportlab.lib.pagesizes import letter
from reportlab.pdfgen import canvas
from reportlab.lib.units import inch

# Define the paths to the EPS files
eps_files = [
    '/home/<USER>/Documents/Work/Parameter/BiAlO/Production_data/orange/temp_vs_pol_hyst.eps',
    '/home/<USER>/Documents/Work/Parameter/BiAlO/Production_data/orange/temp_vs_Ps_Pr.eps',
    '/home/<USER>/Documents/Work/Parameter/BiAlO/Production_data/orange/temp_vs_Ec_without_inset.eps',
    '/home/<USER>/Documents/Work/Parameter/BiAlO/Production_data/orange/temp_vs_Smax_Srem.eps'
]

# Define labels for the subplots
labels = ['(a)', '(b)', '(c)', '(d)']

# Create a new PDF to hold the combined EPS files
combined_savepath = '/home/<USER>/Documents/Work/Parameter/BiAlO/Production_data/orange/Fig4_reportlab.pdf'
c = canvas.Canvas(combined_savepath, pagesize=letter)

# Set the layout for the combined figure
width, height = letter
margin = 0.5 * inch

# Define positions for the 2x2 layout
positions = [
    (margin, height/2 + margin),
    (width/2 + margin, height/2 + margin),
    (margin, margin),
    (width/2 + margin, margin)
]

# Insert each EPS file into the PDF
for i, eps_file in enumerate(eps_files):
    x, y = positions[i]
    c.drawImage(eps_file, x, y, width/2 - 2 * margin, height/2 - 2 * margin)
    c.setFont("Times-Roman", 25)
    c.drawString(x + 20, y + (height/2 - 2 * margin) - 20, labels[i])

# Save the combined PDF
c.save()

print(f"Combined PDF saved at: {combined_savepath}")


from reportlab.lib.pagesizes import letter
from reportlab.pdfgen import canvas
from reportlab.lib.units import inch
from PIL import Image

# Define the paths to the EPS files
eps_files = [
    '/home/<USER>/Documents/Work/Parameter/BiAlO/Production_data/orange/temp_vs_pol_hyst.eps',
    '/home/<USER>/Documents/Work/Parameter/BiAlO/Production_data/orange/temp_vs_Ps_Pr.eps',
    '/home/<USER>/Documents/Work/Parameter/BiAlO/Production_data/orange/temp_vs_Ec_without_inset.eps',
    '/home/<USER>/Documents/Work/Parameter/BiAlO/Production_data/orange/temp_vs_Smax_Srem.eps'
]

# Define labels for the subplots
labels = ['(a)', '(b)', '(c)', '(d)']

# Create a new PDF to hold the combined EPS files
combined_savepath = '/home/<USER>/Documents/Work/Parameter/BiAlO/Production_data/orange/Fig4_reportlab_aspect_ratio.pdf'
c = canvas.Canvas(combined_savepath, pagesize=letter)

# Set the layout for the combined figure
width, height = letter
margin = 0.5 * inch

# Define positions for the 2x2 layout
positions = [
    (margin, height/2 + margin),
    (width/2 + margin, height/2 + margin),
    (margin, margin),
    (width/2 + margin, margin)
]

# Function to maintain the aspect ratio
def draw_image_with_aspect_ratio(c, path, x, y, max_width, max_height):
    img = Image.open(path)
    aspect = img.width / img.height
    if img.width > img.height:
        draw_width = max_width
        draw_height = max_width / aspect
    else:
        draw_height = max_height
        draw_width = max_height * aspect

    if draw_width > max_width:
        draw_width = max_width
        draw_height = max_width / aspect
    if draw_height > max_height:
        draw_height = max_height
        draw_width = max_height * aspect

    c.drawImage(path, x, y + (max_height - draw_height), draw_width, draw_height)

# Insert each EPS file into the PDF
for i, eps_file in enumerate(eps_files):
    x, y = positions[i]
    max_width = width/2 - 2 * margin
    max_height = height/2 - 2 * margin
    draw_image_with_aspect_ratio(c, eps_file, x, y, max_width, max_height)
    c.setFont("Times-Roman", 25)
    c.drawString(x + 20, y + max_height - 20, labels[i])

# Save the combined PDF
c.save()

print(f"Combined PDF saved at: {combined_savepath}")


# Tedious method to mwerge copy all code section and subplot 


'''Now we can merge any graph to any arrange pattern just we need to keep the common style section first then all the individual
plotings just we need to define first the figure size to accomodate the subplots then add plt.subplot(2, 2, 1) first 2 corresponds 
to column and next refers to row then third refers to plot sequence number  '''

################ Common Style Section #######################################################################################################################################
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib as mpl
from matplotlib import rc
mpl.rcParams['font.family'] = 'Times New Roman'
l_w=2.5
f_s=25
# Adjusting Matplotlib's default settings
mpl.rcParams['axes.linewidth'] = l_w  # Thickness of the axis lines
mpl.rcParams['xtick.major.width'] = l_w  # Thickness of major ticks on x-axis
mpl.rcParams['ytick.major.width'] = l_w  # Thickness of major ticks on y-axis
mpl.rcParams['xtick.minor.width'] = 1.5  # Thickness of minor ticks on x-axis
mpl.rcParams['ytick.minor.width'] = 1.5  # Thickness of minor ticks on y-axis
mpl.rcParams['xtick.major.size'] = 6  # Length of major ticks on x-axis
mpl.rcParams['ytick.major.size'] = 6  # Length of major ticks on y-axis
mpl.rcParams['xtick.minor.size'] = 4  # Length of minor ticks on x-axis
mpl.rcParams['ytick.minor.size'] = 4  # Length of minor ticks on y-axis
mpl.rcParams['xtick.labelsize'] = f_s  # Font size of x-axis tick labels
mpl.rcParams['ytick.labelsize'] = f_s  # Font size of y-axis tick labels

# Create a figure with multiple subplots
# fig, axs = plt.subplots(2, 2, figsize=(16, 13))  # 2x2 grid of subplots
plt.figure(figsize=(16,13))  # Adjust the figure size as needed

# # Define marker styles and sizes
# symbols = ['o', 's','o' ]  # Define symbols for each column
# # colors = [ '#08a841', '#086a6a','#08a8a8']  # Define colors as shades of #08a8a8
# colors = [ '#FF8300', '#FF4500','#08a8a8'] #(Burnt Orange #B8390E) (#DC4731 Red Orange) (#A82810 Scarlet) (#F67B50 Coral) (#F89700 Orange) # Define colors as shades of #fb8500    Orange  #FF8300  Red Orange #FF4500
# linestyles = ['-.', '--', ':']  # Define linestyles for each column
# marker_sizes = [16, 16, 16]  # Define marker sizes for each column


########################################################### Panel 1 ######################################################################################################
plt.subplot(2, 2, 1)

# Read the data
file_path = "~/Documents/Work/Parameter/BiAlO/Production_data/temp_vs_pol_hyst.out"
data = pd.read_csv(file_path, delim_whitespace=True)

# Define columns to plot
columns_to_plot = [3, 6, 8, 10]

# Define marker styles and sizes
symbols = ['^', 's','o', '>']  # Define symbols for each column
# colors = [ '#083c3c', '#086a6a','#08a8a8','#08a841']  # Define colors as shades of #08a8a8
# colors = ['#BF360C', '#D84315', '#EF6C00', '#FFB300', '#FFCC80'] # orange shades
colors = ['#BF360C', '#FFB300', '#08a841', '#083c3c', '#FFCC80']
linestyles = ['-', '--', ':', '-.']  # Define linestyles for each column
marker_sizes = [5, 16, 16, 16]  # Define marker sizes for each column

# Plot the data
for i, column_index in enumerate(columns_to_plot):
    plt.plot(data.iloc[:, 0], (data.iloc[:, column_index]), marker=symbols[i], markersize='10', markerfacecolor='none' if i>0 else colors[i],
             linestyle=linestyles[i],linewidth=1.5, color=colors[i], label=f'Column {column_index}') 


# Set limits for x and y axes
# plt.xlim(data.iloc[:, 0].min(), data.iloc[:, 0].max())  # Adjust x-axis limits as needed
# plt.ylim(-150, 50)  # Set y-axis limits (-150 to 50)

# Add title and labels
plt.xlabel('Electric Field (MV/cm)', fontsize=f_s)  # Set font size for X-axis label
plt.ylabel('Polarization ($\mu$C/cm$^2$)', fontsize=f_s)  # Set font size for Y-axis label

# Add manual legend
plt.legend(['100 K', '400 K', '600 K', '800 K'], fontsize=f_s,loc='center',handletextpad=0.05 )  # Add manual legend with labels
plt.yticks(np.arange(-4, 4.3))
plt.yticks(np.arange(-60, 62, 20))  # Set the tick locations from 0 to 250 in steps of 50
# plt.grid()
# Add ticks on the upper and right sides
plt.tick_params(axis='x', direction='in', which='both', bottom=True, top=True, labelbottom=True)
plt.tick_params(axis='y', direction='in', which='both', left=True, right=True, labelleft=True)
# Plot the lines x=0 and y=0
plt.axvline(x=0, color='b', linestyle='--', linewidth=0.5)  # Plot vertical line at x=0
plt.axhline(y=0, color='b', linestyle='--', linewidth=0.5)  # Plot horizontal line at y=0

########################################################### Panel 2 ######################################################################################################
plt.subplot(2, 2, 2)

# Read the data
file_path = "~/Documents/Work/Parameter/BiAlO/Production_data/temp_vs_Ps_Pr.out"
data = pd.read_csv(file_path, delim_whitespace=True)

# Define columns to plot
# columns_to_plot = [1, 2]
columns_to_plot = [1]

# Define marker styles and sizes
symbols = ['o', 's','o' ]  # Define symbols for each column
colors1 = [ '#08a841', '#086a6a','#08a8a8']  # Define colors as shades of #08a8a8
colors = ['#BF360C', '#FFB300', '#EF6C00', '#D84315',  '#FFCC80']
linestyles = ['-.', '--', ':']  # Define linestyles for each column
marker_sizes = [16, 16, 16]  # Define marker sizes for each column

# Plot the data
for i, column_index in enumerate(columns_to_plot):
    plt.plot(data.iloc[:, 0], np.abs(data.iloc[:, column_index]), marker=symbols[i], markerfacecolor='none' , linestyle=linestyles[i],linewidth=1.5
             , color=colors[2], markersize=marker_sizes[i], label=f'Column {column_index}')

## Experimental Data
son_Ps=[33]
son_Pr=[29]
son_Ec=[180]   #'180kV/cm'
son_temp=[300]
Zylberberg_Ps=[3,14,25,30]
Zylberberg_Pr=[1.1,9.5,22,26.7] 
Zylberberg_Ec=[50,54,70,55]
Zylberberg_temp=[173,298,398,498]
rao_temp=[300]
rao_Pr=[11.5]
rao_Ps=[16]
rao_Ec=[69 ] #kV/cm


DFT_Pr=[41]
DFT_temp=[0]

plt.scatter(Zylberberg_temp,Zylberberg_Ps,marker='s',color=colors1[0],s=300)
# plt.scatter(Zylberberg_temp,Zylberberg_Pr,marker='d',color='#FFB300',s=500)
plt.scatter(son_temp,son_Ps,marker='+',color=colors1[1],s=500)
# plt.scatter(son_temp,son_Pr,marker='x',color='#FFB300',s=500)
# plt.scatter(DFT_temp,DFT_Pr,marker='*',color='#FFB300',s=500)
plt.scatter(rao_temp,rao_Ps,marker='*',color=colors1[2],s=700)


# Set limits for x and y axes
# plt.xlim(data.iloc[:, 0].min(), data.iloc[:, 0].max())  # Adjust x-axis limits as needed
# plt.ylim(38, 60)  # Set y-axis limits (-150 to 50)

# Add title and labels
plt.xlabel('Temperature (K)', fontsize=f_s)  # Set font size for X-axis label
plt.ylabel('P$_{Sat}$ ($\mu$C/cm$^2$)', fontsize=f_s)  # Set font size for Y-axis label

# Add manual legend
# plt.legend(['P$_s$', 'P$_r$', 'Zylberberg  $\mathit{et \ al.}$', 'Zylberberg  $\mathit{et \ al.}$','Son $\mathit{et \ al.}$','Son $\mathit{et \ al.}$', 'DFT' ], fontsize=20,loc='lower right')  # Add manual legend with labels
plt.legend(['H$_{eff.}$', 'Ref. [a]', 'Ref. [b]','Ref. [c]' ], fontsize=f_s,loc='lower right')  # Add manual legend with labels
plt.xticks(np.arange(0, 1100, 200))  # Set the tick locations from 0 to 250 in steps of 50
plt.yticks(np.arange(0, 62, 10))  # Set the tick locations from 0 to 250 in steps of 50

# plt.grid()
# Add ticks on the upper and right sides
plt.tick_params(axis='x', direction='in', which='both', bottom=True, top=True, labelbottom=True)
plt.tick_params(axis='y', direction='in', which='both', left=True, right=True, labelleft=True)


########################################################### Panel 3 ######################################################################################################
plt.subplot(2, 2, 3)

# Read the data
file_path = "~/Documents/Work/Parameter/BiAlO/Production_data/temp_vs_Ec.out"
data = pd.read_csv(file_path, delim_whitespace=True)


# Define columns to plot
columns_to_plot = [1,2,3]

# Define marker styles and sizes
symbols = ['o', 's','<', '>']  # Define symbols for each column
# colors = [ '#083c3c', '#086a6a','#08a8a8','#08a841']  # Define colors as shades of #08a8a8
colors = ['#BF360C', '#0077BB',  '#08a841', '#FFCC80']
marker_sizes = [16, 16, 16]

# Plot the data
for i, column_index in enumerate(columns_to_plot):
    plt.plot(data.iloc[:, 0], np.abs(data.iloc[:, column_index]), marker=symbols[i], markerfacecolor='none',
             linestyle='-.', linewidth=1.5, color=colors[i], markersize=marker_sizes[i], label=f'Theoretical {column_index}')

# Add title and labels
plt.xlabel('Temperature (K)', fontsize=f_s)
plt.ylabel('E$_c$ (MV/cm)', fontsize=f_s)

# Add manual legend
plt.legend(['E$_c$(111)', 'E$_c$(110)', 'E$_c$(001)'], fontsize=f_s, loc='upper right')
plt.xticks(np.arange(0, 1100, 200))

# Add ticks on the upper and right sides
plt.tick_params(axis='x', direction='in', which='both', bottom=True, top=True, labelbottom=True)
plt.tick_params(axis='y', direction='in', which='both', left=True, right=True, labelleft=True)


# son_Ec=[180]   #'180kV/cm'
# son_temp=[300]
# Zylberberg_Ec = [50, 54, 70, 55]
# Zylberberg_temp=[173,298,398,498]

########################################################### Panel 4 ######################################################################################################
plt.subplot(2, 2, 4)
# Read the data
file_path = "/home/<USER>/Documents/Work/Parameter/BiAlO/Production_data/temp_vs_Smax_Srem.out"
data = pd.read_csv(file_path, delim_whitespace=True)

# Define columns to plot
columns_to_plot = [1, 2]

# Define marker styles and sizes
symbols = ['o', 's','o' ]  # Define symbols for each column
# colors = [ '#08a841', '#086a6a','#08a8a8']  # Define colors as shades of #08a8a8
colors = ['#BF360C',  '#08a841', '#EF6C00', '#D84315',  '#FFCC80']
linestyles = ['-.', '--', ':']  # Define linestyles for each column
marker_sizes = [16, 16, 16]  # Define marker sizes for each column

# Plot the data
for i, column_index in enumerate(columns_to_plot):
    plt.plot(data.iloc[:, 0], np.abs(data.iloc[:, column_index]), marker=symbols[i], markerfacecolor='none' , linestyle=linestyles[i],linewidth=1.5
             , color=colors[i], markersize=marker_sizes[i], label=f'Column {column_index}')


# Set limits for x and y axes
# plt.xlim(data.iloc[:, 0].min(), data.iloc[:, 0].max())  # Adjust x-axis limits as needed
# plt.ylim(38, 60)  # Set y-axis limits (-150 to 50)

# Add title and labels
plt.xlabel('Temperature (K)', fontsize=f_s)  # Set font size for X-axis label
plt.ylabel('Strain', fontsize=f_s)  # Set font size for Y-axis label

# Add manual legend
# plt.legend(['P$_s$', 'P$_r$', 'Zylberberg  $\mathit{et \ al.}$', 'Zylberberg  $\mathit{et \ al.}$','Son $\mathit{et \ al.}$','Son $\mathit{et \ al.}$', 'DFT' ], fontsize=20,loc='lower right')  # Add manual legend with labels
plt.legend(['S$_{rem}$', 'S$_{max}$'], fontsize=f_s,loc='upper right')  # Add manual legend with labels
plt.xticks(np.arange(0, 1100, 200))  # Set the tick locations from 0 to 250 in steps of 50


# plt.grid()
# Add ticks on the upper and right sides
plt.tick_params(axis='x', direction='in', which='both', bottom=True, top=True, labelbottom=True)
plt.tick_params(axis='y', direction='in', which='both', left=True, right=True, labelleft=True)


# Define subplot labels and positions
subplot_labels = ['(a)', '(b)', '(c)', '(d)']
positions = [(2, 2, 1), (2, 2, 2), (2, 2, 3), (2, 2, 4)]

# Label Adding Loop
for i, pos in enumerate(positions):
    plt.subplot(*pos)
    # Add subplot label
    plt.text(0.465, 1.08, subplot_labels[i], transform=plt.gca().transAxes, fontsize=f_s)

# Adjust spacing between subplots
plt.subplots_adjust(hspace=0.45, wspace=0.4)

## Save the plot in high-resolution EPS format
savepath='/home/<USER>/Documents/Work/Parameter/BiAlO/Production_data/orange/Figure_4.eps'
plt.savefig(savepath, format='eps', dpi=600)


# Show the plot
plt.tight_layout()  # Adjust layout to prevent clipping of labels
plt.show()



import pandas as pd
import matplotlib.pyplot as plt
import matplotlib as mpl
from matplotlib import rc
mpl.rcParams['font.family'] = 'Times New Roman'
l_w=2.5
f_s=25
# Adjusting Matplotlib's default settings
mpl.rcParams['axes.linewidth'] = l_w  # Thickness of the axis lines
mpl.rcParams['xtick.major.width'] = l_w  # Thickness of major ticks on x-axis
mpl.rcParams['ytick.major.width'] = l_w  # Thickness of major ticks on y-axis
mpl.rcParams['xtick.minor.width'] = 1.5  # Thickness of minor ticks on x-axis
mpl.rcParams['ytick.minor.width'] = 1.5  # Thickness of minor ticks on y-axis
mpl.rcParams['xtick.major.size'] = 6  # Length of major ticks on x-axis
mpl.rcParams['ytick.major.size'] = 6  # Length of major ticks on y-axis
mpl.rcParams['xtick.minor.size'] = 4  # Length of minor ticks on x-axis
mpl.rcParams['ytick.minor.size'] = 4  # Length of minor ticks on y-axis
mpl.rcParams['xtick.labelsize'] = f_s  # Font size of x-axis tick labels
mpl.rcParams['ytick.labelsize'] = f_s  # Font size of y-axis tick labels

# Read the data
file_path = "/home/<USER>/Documents/Work/Parameter/BiAlO/Production_data/hysteresis/pressure_vs_Tc.out"
data = pd.read_csv(file_path, delim_whitespace=True)

plt.figure(figsize=(8, 6.5))  # Adjust the figure size as needed

# Define columns to plot
columns_to_plot = [ 2]

# Define marker styles and sizes
symbols = ['o', 's','o' ]  # Define symbols for each column
colors = [ '#08a841', '#086a6a','#08a8a8']  # Define colors as shades of #08a8a8
linestyles = ['-.', '--', ':']  # Define linestyles for each column
marker_sizes = [16, 16, 16]  # Define marker sizes for each column

# Plot the data
for i, column_index in enumerate(columns_to_plot):
    plt.plot(data.iloc[:, 0], (data.iloc[:, column_index]), marker=symbols[i], markerfacecolor='none' , linestyle=linestyles[i],linewidth=1.5
             , color=colors[i], markersize=marker_sizes[i], label=f'Column {column_index}')


# Set limits for x and y axes
# plt.xlim(data.iloc[:, 0].min(), data.iloc[:, 0].max())  # Adjust x-axis limits as needed
# plt.ylim(data.iloc[:, column_index].min(), data.iloc[:, column_index].max())  # Adjust x-axis limits as needed
plt.ylim(0.8, 1.2)  # Set y-axis limits (-150 to 50)
plt.xlim(-5.0, 5.0)

# Add title and labels
plt.xlabel('Pressure (GPa)', fontsize=f_s)  # Set font size for X-axis label
plt.ylabel('$T_C/T_C(0)$', fontsize=f_s)  # Set font size for Y-axis label

# Add manual legend
# plt.legend(['P$_s$', 'P$_r$', 'Zylberberg  $\mathit{et \ al.}$', 'Zylberberg  $\mathit{et \ al.}$','Son $\mathit{et \ al.}$','Son $\mathit{et \ al.}$', 'DFT' ], fontsize=20,loc='lower right')  # Add manual legend with labels
# plt.legend(['S$_{rem}$', 'S$_{max}$'], fontsize=20,loc='upper right')  # Add manual legend with labels
plt.xticks(np.arange(-6, 7, 1))  # Set the tick locations from 0 to 250 in steps of 50


# plt.grid()
# Add ticks on the upper and right sides
plt.tick_params(axis='x', direction='in', which='both', bottom=True, top=True, labelbottom=True)
plt.tick_params(axis='y', direction='in', which='both', left=True, right=True, labelleft=True)

## Save the plot in high-resolution EPS format
# savepath='/home/<USER>/Documents/Work/Parameter/BiAlO/Production_data/pressure_vs_Tc_Tc0.eps'
# plt.savefig(savepath, format='eps', dpi=600)

# Show the plot
plt.tight_layout()  # Adjust layout to prevent clipping of labels
plt.show()


import pandas as pd
import matplotlib.pyplot as plt
import matplotlib as mpl
from matplotlib import rc
mpl.rcParams['font.family'] = 'Times New Roman'
l_w=2.5
f_s=25
# Adjusting Matplotlib's default settings
mpl.rcParams['axes.linewidth'] = l_w  # Thickness of the axis lines
mpl.rcParams['xtick.major.width'] = l_w  # Thickness of major ticks on x-axis
mpl.rcParams['ytick.major.width'] = l_w  # Thickness of major ticks on y-axis
mpl.rcParams['xtick.minor.width'] = 1.5  # Thickness of minor ticks on x-axis
mpl.rcParams['ytick.minor.width'] = 1.5  # Thickness of minor ticks on y-axis
mpl.rcParams['xtick.major.size'] = 6  # Length of major ticks on x-axis
mpl.rcParams['ytick.major.size'] = 6  # Length of major ticks on y-axis
mpl.rcParams['xtick.minor.size'] = 4  # Length of minor ticks on x-axis
mpl.rcParams['ytick.minor.size'] = 4  # Length of minor ticks on y-axis
mpl.rcParams['xtick.labelsize'] = f_s  # Font size of x-axis tick labels
mpl.rcParams['ytick.labelsize'] = f_s  # Font size of y-axis tick labels

# Read the data
file_path = "/home/<USER>/Documents/Work/Parameter/BiAlO/Production_data/hysteresis/pressure_vs_pol.out"
data = pd.read_csv(file_path, delim_whitespace=True)

plt.figure(figsize=(8, 6.5))  # Adjust the figure size as needed

# Define columns to plot
columns_to_plot = [ 1]

# Define marker styles and sizes
symbols = ['o', 's','o' ]  # Define symbols for each column
colors = [ '#08a841', '#086a6a','#08a8a8']  # Define colors as shades of #08a8a8
linestyles = ['-.', '--', ':']  # Define linestyles for each column
marker_sizes = [16, 16, 16]  # Define marker sizes for each column

# Plot the data
for i, column_index in enumerate(columns_to_plot):
    plt.plot(data.iloc[:, 0], (data.iloc[:, column_index]), marker=symbols[i], markerfacecolor='none' , linestyle=linestyles[i],linewidth=1.5
             , color=colors[i], markersize=marker_sizes[i], label=f'Column {column_index}')


# Set limits for x and y axes
# plt.xlim(data.iloc[:, 0].min(), data.iloc[:, 0].max())  # Adjust x-axis limits as needed
# plt.ylim(data.iloc[:, column_index].min(), data.iloc[:, column_index].max())  # Adjust x-axis limits as needed
plt.ylim(44, 52)  # Set y-axis limits (-150 to 50)
plt.xlim(-5.0, 5.0)

# Add title and labels
plt.xlabel('Pressure (GPa)', fontsize=f_s)  # Set font size for X-axis label
plt.ylabel('Polarization ($\mu$C/cm$^2$)', fontsize=f_s)  # Set font size for Y-axis label

# Add manual legend
# plt.legend(['P$_s$', 'P$_r$', 'Zylberberg  $\mathit{et \ al.}$', 'Zylberberg  $\mathit{et \ al.}$','Son $\mathit{et \ al.}$','Son $\mathit{et \ al.}$', 'DFT' ], fontsize=20,loc='lower right')  # Add manual legend with labels
# plt.legend(['S$_{rem}$', 'S$_{max}$'], fontsize=20,loc='upper right')  # Add manual legend with labels
plt.xticks(np.arange(-6, 7, 1))  # Set the tick locations from 0 to 250 in steps of 50


# plt.grid()
# Add ticks on the upper and right sides
plt.tick_params(axis='x', direction='in', which='both', bottom=True, top=True, labelbottom=True)
plt.tick_params(axis='y', direction='in', which='both', left=True, right=True, labelleft=True)

## Save the plot in high-resolution EPS format
# savepath='/home/<USER>/Documents/Work/Parameter/BiAlO/Production_data/pressure_vs_Pol.eps'
# plt.savefig(savepath, format='eps', dpi=600)

# Show the plot
plt.tight_layout()  # Adjust layout to prevent clipping of labels
plt.show()

# Hi Collect the data and plot 
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib as mpl

mpl.rcParams['font.family'] = 'Times New Roman'

l_w=2.5
f_s=25
# Adjusting Matplotlib's default settings
mpl.rcParams['axes.linewidth'] = l_w  # Thickness of the axis lines
mpl.rcParams['xtick.major.width'] = l_w  # Thickness of major ticks on x-axis
mpl.rcParams['ytick.major.width'] = l_w  # Thickness of major ticks on y-axis
mpl.rcParams['xtick.minor.width'] = 1.5  # Thickness of minor ticks on x-axis
mpl.rcParams['ytick.minor.width'] = 1.5  # Thickness of minor ticks on y-axis
mpl.rcParams['xtick.major.size'] = 6  # Length of major ticks on x-axis
mpl.rcParams['ytick.major.size'] = 6  # Length of major ticks on y-axis
mpl.rcParams['xtick.minor.size'] = 4  # Length of minor ticks on x-axis
mpl.rcParams['ytick.minor.size'] = 4  # Length of minor ticks on y-axis
mpl.rcParams['xtick.labelsize'] = f_s  # Font size of x-axis tick labels
mpl.rcParams['ytick.labelsize'] = f_s  # Font size of y-axis tick labels

# Read the data
file_path = "/home/<USER>/Documents/Work/Parameter/BiAlO/Production_data/pressure_temp_vs_u.out"
data = pd.read_csv(file_path, delim_whitespace=True,skiprows=2)

plt.figure(figsize=(8, 6.5))  # Adjust the figure size as needed

# Define columns to plot
columns_to_plot = [1, 2, 3,4,5,6,7,8,9]

# Define marker styles and sizes
symbols = ['^', 's','o','^', 's','o','^', 's','o' ]  # Define symbols for each column
# colors = [ '#013c3c', '#086a6a','#08a8a8','#083c3c', '#086a6a','#08a8a8','#083c3c', '#086a6a','#08a8a8']  # Define colors as shades of #08a8a8
colors = [ '#BF360C', '#D84315', '#EF6C00', '#FFB300', '#FFCC80','#FFAA80','#013c3c', '#086a6a','#08a8a8'] #(Burnt Orange #B8390E) (#DC4731 Red Orange) (#A82810 Scarlet) (#F67B50 Coral) (#F89700 Orange) # Define colors as shades of #fb8500    Orange  #FF8300  Red Orange #FF4500
linestyles = ['-', '--', ':','-', '--', ':','-', '--', ':']  # Define linestyles for each column
marker_sizes = [4, 8, 16,4, 8, 16,4, 8, 16]  # Define marker sizes for each column

# Plot the data
# for i, column_index in enumerate(columns_to_plot):
#     plt.plot(data.iloc[:, 0], np.abs(data.iloc[:, column_index]), marker=symbols[i], markerfacecolor='none' if i>0 else colors[i], linestyle='--',linewidth=0.05, color=colors[i], markersize=marker_sizes[i], label=f'Column {column_index}')
skip_value = 5  # Change this value to skip every nth data point

for i, column_index in enumerate(columns_to_plot):
    plt.plot(data.iloc[::skip_value, 0],
             np.abs(data.iloc[::skip_value, column_index]),
             marker=symbols[i],
             markerfacecolor='none' if i > 0 else colors[i],
             linestyle='--',
             linewidth=1.05,
             color=colors[i],
            #  markersize='0',
             markersize=marker_sizes[i],
             label=f'Column {column_index}')


# Set limits for x and y axes
# plt.xlim(data.iloc[:, 0].min(), data.iloc[:, 0].max())  # Adjust x-axis limits as needed
# plt.ylim(-150, 50)  # Set y-axis limits (-150 to 50)

# Add title and labels
plt.xlabel('Temperature (K)', fontsize=f_s)  # Set font size for X-axis label
plt.ylabel('<u$_{x,y,z} (\Gamma)$>', fontsize=f_s)  # Set font size for Y-axis label

# Add manual legend
# plt.legend(['$\Gamma$', 'R$_{25}$', 'M','$\Gamma$ + R$_{25}$','$\Gamma$ + M'], fontsize=12)  # Add manual legend with labels
plt.legend(['<u$_x$>', '<u$_y$>', '<u$_z$>'], fontsize=20,loc='lower left')  # Add manual legend with labels
plt.xticks(np.arange(0, 1600, 200))  # Set the tick locations from 0 to 250 in steps of 50

# plt.grid()
# Add ticks on the upper and right sides
plt.tick_params(axis='x', direction='in', which='both', bottom=True, top=True, labelbottom=True)
plt.tick_params(axis='y', direction='in', which='both', left=True, right=True, labelleft=True)

# Save the plot in high-resolution EPS format
# savepath='/home/<USER>/Documents/Work/Parameter/BiAlO/Production_data/orange/pressure_temp_vs_u.eps'
# plt.savefig(savepath, format='eps', dpi=600)

# Show the plot
plt.tight_layout()  # Adjust layout to prevent clipping of labels
plt.show()

import math
c=42878.62
z=8.164116829222200
a0=7.032248652103040
v0=a0**3

def calculate_polarization(u_file_path, v_file_path, output_file_path):
    # Open and read the temp_vs_u_*.out file
    with open(u_file_path, "r") as u_file:
        u_lines = u_file.readlines()

    # Open and read the temp_vs_v_*.out file to get volume
    with open(v_file_path, "r") as v_file:
        v_lines = v_file.readlines()

    # Extract volume from the 3rd column of the temp_vs_v_*.out file
    volume = float(v_lines[0].split()[2])  # Assuming volume is on the first line and third column

    # Open a new file to write the calculated polarization values
    with open(output_file_path, "w") as pol_file:
        for line, v_line in zip(u_lines, v_lines):
            # Split the line into columns
            u_columns = line.split()
            v_columns = v_line.split()

            # Get values from the 2nd, 4th, and 6th columns as u
            u_values = [float(u_columns[i]) for i in [1, 3, 5]]

            # Calculate polarization value for each u_value separately
            polarizations = [c * z * u / float(v_columns[2]) for u in u_values]
            # polarizations = [c * z * u / v0 for u in u_values]

            # Write the calculated values to the new file also it write the total amplituted  
            pol_file.write(f"{u_columns[0]} {polarizations[0]} {polarizations[1]} {polarizations[2]} {math.sqrt(polarizations[0]**2 + polarizations[1]**2 + polarizations[2]**2)}\n")


# # Calculate polarization
# calculate_polarization("temp_vs_u_pressure_-5.00GPa.out", "temp_vs_v_pressure_-5.00GPa.out", "temp_vs_pol_pressure_-5.00GPa.out")

# # Calculate polarization
# calculate_polarization("temp_vs_u_uniStress_-5.00GPa.out", "temp_vs_v_uniStress_-5.00GPa.out", "temp_vs_pol_uniStress_-5.00GPa.out")

# # Calculate polarization
# calculate_polarization("temp_vs_u_biStress_-5.00GPa.out", "temp_vs_v_biStress_-5.00GPa.out", "temp_vs_pol_biStress_-5.00GPa.out")

for i in range(-5, 6):  # Loop from -5 to 5 inclusive
    # Format the pressure value: include '-' for negative values, but no '+' for positive values
    pressure = f"{i:.2f}GPa"  # Format the pressure value, e.g., "-5.00GPa", "5.00GPa"
    
    # new_path="/home/<USER>/Documents/Work/Parameter/BiAlO/Production_data/pressure/"
    new_path1="/home/<USER>/Documents/Work/Parameter/BiAlO/Production_data/uniaxial/"
    new_path2="/home/<USER>/Documents/Work/Parameter/BiAlO/Production_data/biaxial/"
    # Calculate polarization for different stress types
    # calculate_polarization(f"{new_path}temp_vs_u_pressure_{pressure}.out", f"{new_path}temp_vs_v_pressure_{pressure}.out", f"{new_path}temp_vs_pol_pressure_{pressure}.out")
    calculate_polarization(f"{new_path1}temp_vs_u_uniStress_{pressure}.out", f"{new_path1}temp_vs_v_uniStress_{pressure}.out", f"{new_path1}temp_vs_pol_uniStress_{pressure}.out")
    calculate_polarization(f"{new_path2}temp_vs_u_biStress_{pressure}.out", f"{new_path2}temp_vs_v_biStress_{pressure}.out", f"{new_path2}temp_vs_pol_biStress_{pressure}.out")




import pandas as pd
import matplotlib.pyplot as plt
import matplotlib as mpl
from matplotlib import rc
mpl.rcParams['font.family'] = 'Times New Roman'
l_w=2.5
f_s=25
# Adjusting Matplotlib's default settings
mpl.rcParams['axes.linewidth'] = l_w  # Thickness of the axis lines
mpl.rcParams['xtick.major.width'] = l_w  # Thickness of major ticks on x-axis
mpl.rcParams['ytick.major.width'] = l_w  # Thickness of major ticks on y-axis
mpl.rcParams['xtick.minor.width'] = 1.5  # Thickness of minor ticks on x-axis
mpl.rcParams['ytick.minor.width'] = 1.5  # Thickness of minor ticks on y-axis
mpl.rcParams['xtick.major.size'] = 6  # Length of major ticks on x-axis
mpl.rcParams['ytick.major.size'] = 6  # Length of major ticks on y-axis
mpl.rcParams['xtick.minor.size'] = 4  # Length of minor ticks on x-axis
mpl.rcParams['ytick.minor.size'] = 4  # Length of minor ticks on y-axis
mpl.rcParams['xtick.labelsize'] = f_s  # Font size of x-axis tick labels
mpl.rcParams['ytick.labelsize'] = f_s  # Font size of y-axis tick labels

# Read the data
file_path = "/home/<USER>/Documents/Work/Parameter/BiAlO/Production_data/hysteresis/uni_stress_vs_Tc.out"
data = pd.read_csv(file_path, delim_whitespace=True)

plt.figure(figsize=(8, 6.5))  # Adjust the figure size as needed

# Define columns to plot
columns_to_plot = [ 1,2]

# Define marker styles and sizes
symbols = ['o', 's','o' ]  # Define symbols for each column
# colors = [ '#08a841', '#086a6a','#08a8a8']  # Define colors as shades of #08a8a8
# colors = [ '#FF8300', '#FF4500','#08a8a8'] #(Burnt Orange #B8390E) (#DC4731 Red Orange) (#A82810 Scarlet) (#F67B50 Coral) (#F89700 Orange) # Define colors as shades of #fb8500    Orange  #FF8300  Red Orange #FF4500
linestyles = ['-.', '--', ':']  # Define linestyles for each column
marker_sizes = [16, 16, 16]  # Define marker sizes for each column

# Plot the data
for i, column_index in enumerate(columns_to_plot):
    plt.plot(data.iloc[:, 0], (data.iloc[:, column_index]), marker=symbols[i], markerfacecolor='none' , linestyle=linestyles[i],linewidth=1.5
             , color=colors[i], markersize=marker_sizes[i], label=f'Column {column_index}')


# Set limits for x and y axes
# plt.xlim(data.iloc[:, 0].min(), data.iloc[:, 0].max())  # Adjust x-axis limits as needed
# plt.ylim(data.iloc[:, column_index].min(), data.iloc[:, column_index].max())  # Adjust x-axis limits as needed
# plt.ylim(0.6, 1.4)  # Set y-axis limits (-150 to 50)
plt.xlim(-5.0, 5.0)

# Add title and labels
plt.xlabel('Uniaxial stress (GPa)', fontsize=f_s)  # Set font size for X-axis label
plt.ylabel('$T_C/T_C(0)$', fontsize=f_s)  # Set font size for Y-axis label

# Add manual legend
# plt.legend(['P$_s$', 'P$_r$', 'Zylberberg  $\mathit{et \ al.}$', 'Zylberberg  $\mathit{et \ al.}$','Son $\mathit{et \ al.}$','Son $\mathit{et \ al.}$', 'DFT' ], fontsize=20,loc='lower right')  # Add manual legend with labels
plt.legend(['T$_{C_2}$', 'T$_{C_1}$'], fontsize=20,loc='upper left')  # Add manual legend with labels
plt.xticks(np.arange(-6, 7, 1))  # Set the tick locations from 0 to 250 in steps of 50


# plt.grid()
# Add ticks on the upper and right sides
plt.tick_params(axis='x', direction='in', which='both', bottom=True, top=True, labelbottom=True)
plt.tick_params(axis='y', direction='in', which='both', left=True, right=True, labelleft=True)

## Save the plot in high-resolution EPS format
# savepath='/home/<USER>/Documents/Work/Parameter/BiAlO/Production_data/orange/uni_stress_vs_Tc_Tc0.eps'
# plt.savefig(savepath, format='eps', dpi=600)

# Show the plot
plt.tight_layout()  # Adjust layout to prevent clipping of labels
plt.show()

# Hi Collect the data and plot 
'''This is for the data extraction from a specific sheet of a an excel file to a data file '''
import pandas as pd

# Path to the Excel file
excel_file = '/home/<USER>/Documents/Work/Parameter/BiAlO/BAO_Parameterization_Results.ods'

# Name or index of the sheet you want to export
sheet_name = 'Order_Parameter_Stress'  # Replace with the name of your sheet or its index

# Load the sheet into a DataFrame
df = pd.read_excel(excel_file, sheet_name=sheet_name)

# Path to the output text file
output_file = 'stress_temp_vs_u.out'

# Export the DataFrame to a text file
df.to_csv(output_file, sep='\t', index=False)


# Hi Collect the data and plot 
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib as mpl
from matplotlib.lines import Line2D

mpl.rcParams['font.family'] = 'Times New Roman'

l_w=2.5
f_s=25
# Adjusting Matplotlib's default settings
mpl.rcParams['axes.linewidth'] = l_w  # Thickness of the axis lines
mpl.rcParams['xtick.major.width'] = l_w  # Thickness of major ticks on x-axis
mpl.rcParams['ytick.major.width'] = l_w  # Thickness of major ticks on y-axis
mpl.rcParams['xtick.minor.width'] = 1.5  # Thickness of minor ticks on x-axis
mpl.rcParams['ytick.minor.width'] = 1.5  # Thickness of minor ticks on y-axis
mpl.rcParams['xtick.major.size'] = 6  # Length of major ticks on x-axis
mpl.rcParams['ytick.major.size'] = 6  # Length of major ticks on y-axis
mpl.rcParams['xtick.minor.size'] = 4  # Length of minor ticks on x-axis
mpl.rcParams['ytick.minor.size'] = 4  # Length of minor ticks on y-axis
mpl.rcParams['xtick.labelsize'] = f_s  # Font size of x-axis tick labels
mpl.rcParams['ytick.labelsize'] = f_s  # Font size of y-axis tick labels

# Read the data
file_path = "/home/<USER>/Documents/Work/Parameter/BiAlO/Production_data/stress_temp_vs_u.out"
data = pd.read_csv(file_path, delim_whitespace=True,skiprows=2)

plt.figure(figsize=(8, 6.5))  # Adjust the figure size as needed

# Define columns to plot
columns_to_plot = [1, 2, 3,4,5,6,7,8,9]  # 0GPa -5GPa 5GPa 

# Define marker styles and sizes
symbols = ['^', 's','o','^', 's','o','^', 's','o' ]  # Define symbols for each column
# colors = [ '#013c3c', '#086a6a','#08a8a8','#083c3c', '#086a6a','#08a8a8','#083c3c', '#086a6a','#08a8a8']  # Define colors as shades of #08a8a8
# colors = [ '#BF360C', '#D84315', '#EF6C00', '#FFB300', '#FFCC80','#FFAA80','#013c3c', '#086a6a','#08a8a8'] #(Burnt Orange #B8390E) (#DC4731 Red Orange) (#A82810 Scarlet) (#F67B50 Coral) (#F89700 Orange) # Define colors as shades of #fb8500    Orange  #FF8300  Red Orange #FF4500
colors = [ '#013c3c','#013c3c','#013c3c', '#BF360C',  '#BF360C', '#BF360C','#EF6C00', '#EF6C00','#EF6C00','#EF6C00','#FFB300', '#FFCC80','#FFAA80','#013c3c', '#086a6a','#08a8a8']
linestyles = ['-', '--', ':','-', '--', ':','-', '--', ':']  # Define linestyles for each column
marker_sizes = [2, 4, 8,4, 8, 16,4, 12, 18]  # Define marker sizes for each column

# Plot the data
# for i, column_index in enumerate(columns_to_plot):
#     plt.plot(data.iloc[:, 0], np.abs(data.iloc[:, column_index]), marker=symbols[i], markerfacecolor='none' if i>0 else colors[i], linestyle='--',linewidth=0.05, color=colors[i], markersize=marker_sizes[i], label=f'Column {column_index}')
skip_value = 10  # Change this value to skip every nth data point

for i, column_index in enumerate(columns_to_plot):
    plt.plot(data.iloc[::skip_value, 0],
             np.abs(data.iloc[::skip_value, column_index]),
             marker=symbols[i],
             markerfacecolor=colors[i] if i % 3 == 0 else 'none',
             linestyle='--',
             linewidth=1.05,
             color=colors[i],
            #  markersize='0',
             markersize=marker_sizes[i],
             label=f'Column {column_index}')


# Set limits for x and y axes
# plt.xlim(data.iloc[:, 0].min(), data.iloc[:, 0].max())  # Adjust x-axis limits as needed
# plt.ylim(-150, 50)  # Set y-axis limits (-150 to 50)

# Add title and labels
plt.xlabel('Temperature (K)', fontsize=f_s)  # Set font size for X-axis label
plt.ylabel('<u$_{x,y,z} (\Gamma)$>', fontsize=f_s)  # Set font size for Y-axis label

# Add manual legend
# plt.legend(['$\Gamma$', 'R$_{25}$', 'M','$\Gamma$ + R$_{25}$','$\Gamma$ + M'], fontsize=12)  # Add manual legend with labels
# plt.legend(['<u$_x$>', '<u$_y$>', '<u$_z$>'], fontsize=20,loc='lower left')  # Add manual legend with labels
# plt.xticks(np.arange(0, 1600, 200))  # Set the tick locations from 0 to 250 in steps of 50

# Custom legends for sets (colors)
set_legend = [Line2D([0], [0], color=colors[i*3], lw=4) for i in range(3)]
set_labels = ['0GPa', '-5GPa', '5GPa']

# Custom legends for subsets (symbols) with same style, size, and facecolor
subset_legend = [Line2D([0], [0], color=colors[i], marker=symbols[i],linewidth=1.05, linestyle=linestyles[i], 
                        markersize=10, markerfacecolor=colors[i] if i % 3 == 0 else 'none') 
                 for i in range(3)]
subset_labels = ['<u$_x$>', '<u$_y$>', '<u$_z$>']

# Adding both legends
plt.legend(set_legend + subset_legend, set_labels + subset_labels, fontsize=18, loc='upper right')


# plt.grid()
# Add ticks on the upper and right sides
plt.tick_params(axis='x', direction='in', which='both', bottom=True, top=True, labelbottom=True)
plt.tick_params(axis='y', direction='in', which='both', left=True, right=True, labelleft=True)

# Save the plot in high-resolution EPS format
# savepath='/home/<USER>/Documents/Work/Parameter/BiAlO/Production_data/orange/uniaxial_stress_temp_vs_u.eps'
# plt.savefig(savepath, format='eps', dpi=600)

# Show the plot
plt.tight_layout()  # Adjust layout to prevent clipping of labels
plt.show()


import pandas as pd
import matplotlib.pyplot as plt
import matplotlib as mpl
from matplotlib import rc
mpl.rcParams['font.family'] = 'Times New Roman'
l_w=2.5
f_s=25
# Adjusting Matplotlib's default settings
mpl.rcParams['axes.linewidth'] = l_w  # Thickness of the axis lines
mpl.rcParams['xtick.major.width'] = l_w  # Thickness of major ticks on x-axis
mpl.rcParams['ytick.major.width'] = l_w  # Thickness of major ticks on y-axis
mpl.rcParams['xtick.minor.width'] = 1.5  # Thickness of minor ticks on x-axis
mpl.rcParams['ytick.minor.width'] = 1.5  # Thickness of minor ticks on y-axis
mpl.rcParams['xtick.major.size'] = 6  # Length of major ticks on x-axis
mpl.rcParams['ytick.major.size'] = 6  # Length of major ticks on y-axis
mpl.rcParams['xtick.minor.size'] = 4  # Length of minor ticks on x-axis
mpl.rcParams['ytick.minor.size'] = 4  # Length of minor ticks on y-axis
mpl.rcParams['xtick.labelsize'] = f_s  # Font size of x-axis tick labels
mpl.rcParams['ytick.labelsize'] = f_s  # Font size of y-axis tick labels

# Read the data
file_path = "/home/<USER>/Documents/Work/Parameter/BiAlO/Production_data/hysteresis/bi_stress_vs_Tc.out"
data = pd.read_csv(file_path, delim_whitespace=True)

plt.figure(figsize=(8, 6.5))  # Adjust the figure size as needed

# Define columns to plot
columns_to_plot = [ 1,2]

# Define marker styles and sizes
symbols = ['o', 's','o' ]  # Define symbols for each column
colors = [ '#08a841', '#086a6a','#08a8a8']  # Define colors as shades of #08a8a8
linestyles = ['-.', '--', ':']  # Define linestyles for each column
marker_sizes = [16, 16, 16]  # Define marker sizes for each column

# Plot the data
for i, column_index in enumerate(columns_to_plot):
    plt.plot(data.iloc[:, 0], (data.iloc[:, column_index]), marker=symbols[i], markerfacecolor='none' , linestyle=linestyles[i],linewidth=1.5
             , color=colors[i], markersize=marker_sizes[i], label=f'Column {column_index}')


# Set limits for x and y axes
# plt.xlim(data.iloc[:, 0].min(), data.iloc[:, 0].max())  # Adjust x-axis limits as needed
# plt.ylim(data.iloc[:, column_index].min(), data.iloc[:, column_index].max())  # Adjust x-axis limits as needed
# plt.ylim(0.6, 1.4)  # Set y-axis limits (-150 to 50)
plt.xlim(-5.0, 5.0)

# Add title and labels
plt.xlabel('Biaxial stress (GPa)', fontsize=f_s)  # Set font size for X-axis label
plt.ylabel('$T_C/T_C(0)$', fontsize=f_s)  # Set font size for Y-axis label

# Add manual legend
# plt.legend(['P$_s$', 'P$_r$', 'Zylberberg  $\mathit{et \ al.}$', 'Zylberberg  $\mathit{et \ al.}$','Son $\mathit{et \ al.}$','Son $\mathit{et \ al.}$', 'DFT' ], fontsize=20,loc='lower right')  # Add manual legend with labels
plt.legend(['T$_{C_2}$', 'T$_{C_1}$'], fontsize=20,loc='upper left')  # Add manual legend with labels
plt.xticks(np.arange(-6, 7, 1))  # Set the tick locations from 0 to 250 in steps of 50


# plt.grid()
# Add ticks on the upper and right sides
plt.tick_params(axis='x', direction='in', which='both', bottom=True, top=True, labelbottom=True)
plt.tick_params(axis='y', direction='in', which='both', left=True, right=True, labelleft=True)

## Save the plot in high-resolution EPS format
# savepath='/home/<USER>/Documents/Work/Parameter/BiAlO/Production_data/bi_stress_vs_Tc_Tc0.eps'
# plt.savefig(savepath, format='eps', dpi=600)

# Show the plot
plt.tight_layout()  # Adjust layout to prevent clipping of labels
plt.show()

# Hi Collect the data and plot 
# Hi Collect the data and plot 
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib as mpl
from matplotlib.lines import Line2D

mpl.rcParams['font.family'] = 'Times New Roman'

l_w=2.5
f_s=25
# Adjusting Matplotlib's default settings
mpl.rcParams['axes.linewidth'] = l_w  # Thickness of the axis lines
mpl.rcParams['xtick.major.width'] = l_w  # Thickness of major ticks on x-axis
mpl.rcParams['ytick.major.width'] = l_w  # Thickness of major ticks on y-axis
mpl.rcParams['xtick.minor.width'] = 1.5  # Thickness of minor ticks on x-axis
mpl.rcParams['ytick.minor.width'] = 1.5  # Thickness of minor ticks on y-axis
mpl.rcParams['xtick.major.size'] = 6  # Length of major ticks on x-axis
mpl.rcParams['ytick.major.size'] = 6  # Length of major ticks on y-axis
mpl.rcParams['xtick.minor.size'] = 4  # Length of minor ticks on x-axis
mpl.rcParams['ytick.minor.size'] = 4  # Length of minor ticks on y-axis
mpl.rcParams['xtick.labelsize'] = f_s  # Font size of x-axis tick labels
mpl.rcParams['ytick.labelsize'] = f_s  # Font size of y-axis tick labels

# Read the data
file_path = "/home/<USER>/Documents/Work/Parameter/BiAlO/Production_data/stress_temp_vs_u.out"
data = pd.read_csv(file_path, delim_whitespace=True,skiprows=2)

plt.figure(figsize=(8, 6.5))  # Adjust the figure size as needed

# Define columns to plot
# columns_to_plot = [1,2,3,10,11,12,13,14,15]
columns_to_plot = [1,2,3,10,11,12,16,17,18]

# Define marker styles and sizes
symbols = ['^', 's','o','^', 's','o','^', 's','o' ]  # Define symbols for each column
# colors = [ '#013c3c', '#086a6a','#08a8a8','#083c3c', '#086a6a','#08a8a8','#083c3c', '#086a6a','#08a8a8']  # Define colors as shades of #08a8a8
colors = [ '#013c3c','#013c3c','#013c3c', '#BF360C',  '#BF360C', '#BF360C','#EF6C00', '#EF6C00','#EF6C00','#EF6C00','#FFB300', '#FFCC80','#FFAA80','#013c3c', '#086a6a','#08a8a8']
linestyles = ['-', '--', ':','-', '--', ':','-', '--', ':']  # Define linestyles for each column
marker_sizes = [2, 4, 8,4, 8, 16,4, 12, 18]  # Define marker sizes for each column

# Plot the data
# for i, column_index in enumerate(columns_to_plot):
#     plt.plot(data.iloc[:, 0], np.abs(data.iloc[:, column_index]), marker=symbols[i], markerfacecolor='none' if i>0 else colors[i], linestyle='--',linewidth=0.05, color=colors[i], markersize=marker_sizes[i], label=f'Column {column_index}')
skip_value = 8  # Change this value to skip every nth data point

for i, column_index in enumerate(columns_to_plot):
    plt.plot(data.iloc[::skip_value, 0],
             np.abs(data.iloc[::skip_value, column_index]),
             marker=symbols[i],
             markerfacecolor=colors[i] if i % 3 == 0 else 'none',
             linestyle='--',
             linewidth=1.05,
             color=colors[i],
            #  markersize='0',
             markersize=marker_sizes[i],
             label=f'Column {column_index}')


# Set limits for x and y axes
# plt.xlim(data.iloc[:, 0].min(), data.iloc[:, 0].max())  # Adjust x-axis limits as needed
# plt.ylim(-150, 50)  # Set y-axis limits (-150 to 50)

# Add title and labels
plt.xlabel('Temperature (K)', fontsize=f_s)  # Set font size for X-axis label
plt.ylabel('<u$_{x,y,z} (\Gamma)$>', fontsize=f_s)  # Set font size for Y-axis label

# Add manual legend
# plt.legend(['$\Gamma$', 'R$_{25}$', 'M','$\Gamma$ + R$_{25}$','$\Gamma$ + M'], fontsize=12)  # Add manual legend with labels
# plt.legend(['<u$_x$>', '<u$_y$>', '<u$_z$>'], fontsize=20,loc='lower left')  # Add manual legend with labels
# plt.xticks(np.arange(0, 1600, 200))  # Set the tick locations from 0 to 250 in steps of 50
# Custom legends for sets (colors)

set_legend = [Line2D([0], [0], color=colors[i*3], lw=4) for i in range(3)]
set_labels = ['0GPa', '-5GPa', '4GPa']

# Custom legends for subsets (symbols) with same style, size, and facecolor
subset_legend = [Line2D([0], [0], color=colors[i], marker=symbols[i],linewidth=1.05, linestyle=linestyles[i], 
                        markersize=10, markerfacecolor=colors[i] if i % 3 == 0 else 'none') 
                 for i in range(3)]
subset_labels = ['<u$_x$>', '<u$_y$>', '<u$_z$>']

# Adding both legends
plt.legend(set_legend + subset_legend, set_labels + subset_labels, fontsize=18, loc='upper right')



# plt.grid()
# Add ticks on the upper and right sides
plt.tick_params(axis='x', direction='in', which='both', bottom=True, top=True, labelbottom=True)
plt.tick_params(axis='y', direction='in', which='both', left=True, right=True, labelleft=True)

# Save the plot in high-resolution EPS format
# savepath='/home/<USER>/Documents/Work/Parameter/BiAlO/Production_data/orange/biaxial_stress_temp_vs_u.eps'
# plt.savefig(savepath, format='eps', dpi=600)

# Show the plot
plt.tight_layout()  # Adjust layout to prevent clipping of labels
plt.show()

import matplotlib.pyplot as plt
import pandas as pd
import matplotlib as mpl

# Adjusting Matplotlib's default settings
mpl.rcParams['font.family'] = 'Times New Roman'
l_w = 2.5
f_s = 25
mpl.rcParams['axes.linewidth'] = l_w
mpl.rcParams['xtick.major.width'] = l_w
mpl.rcParams['ytick.major.width'] = l_w
mpl.rcParams['xtick.minor.width'] = 1.5
mpl.rcParams['ytick.minor.width'] = 1.5
mpl.rcParams['xtick.major.size'] = 6
mpl.rcParams['ytick.major.size'] = 6
mpl.rcParams['xtick.minor.size'] = 4
mpl.rcParams['ytick.minor.size'] = 4
mpl.rcParams['xtick.labelsize'] = f_s
mpl.rcParams['ytick.labelsize'] = f_s

# Create a figure with multiple subplots
fig, axs = plt.subplots(2, 2, figsize=(16, 13))  # 2x2 grid of subplots

# Plot 1: Pressure vs Tc
data1 = pd.read_csv("/home/<USER>/Documents/Work/Parameter/BiAlO/Production_data/hysteresis/pressure_vs_Tc.out", delim_whitespace=True)
axs[0, 0].plot(data1.iloc[:, 0], data1.iloc[:, 2], marker='o', linestyle='-.', linewidth=1.5, color='#08a841', markersize=16)
axs[0, 0].set_xlim(-5.0, 5.0)
axs[0, 0].set_ylim(0.8, 1.2)
axs[0, 0].set_xlabel('Pressure (GPa)', fontsize=f_s)
axs[0, 0].set_ylabel('$T_C/T_C(0)$', fontsize=f_s)

# Plot 2: Pressure vs Polarization
data2 = pd.read_csv("/home/<USER>/Documents/Work/Parameter/BiAlO/Production_data/hysteresis/pressure_vs_pol.out", delim_whitespace=True)
axs[0, 1].plot(data2.iloc[:, 0], data2.iloc[:, 1], marker='o', linestyle='-.', linewidth=1.5, color='#086a6a', markersize=16)
axs[0, 1].set_xlim(-5.0, 5.0)
axs[0, 1].set_ylim(44, 52)
axs[0, 1].set_xlabel('Pressure (GPa)', fontsize=f_s)
axs[0, 1].set_ylabel('Polarization ($\mu$C/cm$^2$)', fontsize=f_s)

# Plot 3: Uniaxial Stress vs Tc
data3 = pd.read_csv("/home/<USER>/Documents/Work/Parameter/BiAlO/Production_data/hysteresis/uni_stress_vs_Tc.out", delim_whitespace=True)
axs[1, 0].plot(data3.iloc[:, 0], data3.iloc[:, 1], marker='o', linestyle='-.', linewidth=1.5, color='#08a8a8', markersize=16)
axs[1, 0].plot(data3.iloc[:, 0], data3.iloc[:, 2], marker='s', linestyle='--', linewidth=1.5, color='#FF8300', markersize=16)
axs[1, 0].set_xlim(-5.0, 5.0)
axs[1, 0].set_xlabel('Uniaxial stress (GPa)', fontsize=f_s)
axs[1, 0].set_ylabel('$T_C/T_C(0)$', fontsize=f_s)
axs[1, 0].legend(['T$_{C_2}$', 'T$_{C_1}$'], fontsize=20, loc='upper left')

# Plot 4: Biaxial Stress vs Tc
data4 = pd.read_csv("/home/<USER>/Documents/Work/Parameter/BiAlO/Production_data/hysteresis/uni_stress_vs_Tc.out", delim_whitespace=True)
axs[1, 1].plot(data4.iloc[:, 0], data4.iloc[:, 1], marker='o', linestyle='-.', linewidth=1.5, color='#08a841', markersize=16)
axs[1, 1].plot(data4.iloc[:, 0], data4.iloc[:, 2], marker='s', linestyle='--', linewidth=1.5, color='#086a6a', markersize=16)
axs[1, 1].set_xlim(-5.0, 5.0)
axs[1, 1].set_xlabel('Biaxial stress (GPa)', fontsize=f_s)
axs[1, 1].set_ylabel('$T_C/T_C(0)$', fontsize=f_s)
axs[1, 1].legend(['T$_{C_2}$', 'T$_{C_1}$'], fontsize=20, loc='upper left')

plt.tight_layout()  # Adjust layout to prevent clipping of labels

# Save the combined figure in high-resolution EPS format
# savepath = '/home/<USER>/Documents/Work/Parameter/BiAlO/Production_data/orange/pressure_stress.eps'
# plt.savefig(savepath, format='eps', dpi=600)

# Show the combined figure
plt.show()


'''Now we can merge any graph to any arrange pattern just we need to keep the common style section first then all the individual
plotings just we need to define first the figure size to accomodate the subplots then add plt.subplot(2, 2, 1) first 2 corresponds 
to column and next refers to row then third refers to plot sequence number  '''

################ Common Style Section #######################################################################################################################################
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib as mpl
from matplotlib import rc
mpl.rcParams['font.family'] = 'Times New Roman'
l_w=2.5
f_s=25
# Adjusting Matplotlib's default settings
mpl.rcParams['axes.linewidth'] = l_w  # Thickness of the axis lines
mpl.rcParams['xtick.major.width'] = l_w  # Thickness of major ticks on x-axis
mpl.rcParams['ytick.major.width'] = l_w  # Thickness of major ticks on y-axis
mpl.rcParams['xtick.minor.width'] = 1.5  # Thickness of minor ticks on x-axis
mpl.rcParams['ytick.minor.width'] = 1.5  # Thickness of minor ticks on y-axis
mpl.rcParams['xtick.major.size'] = 6  # Length of major ticks on x-axis
mpl.rcParams['ytick.major.size'] = 6  # Length of major ticks on y-axis
mpl.rcParams['xtick.minor.size'] = 4  # Length of minor ticks on x-axis
mpl.rcParams['ytick.minor.size'] = 4  # Length of minor ticks on y-axis
mpl.rcParams['xtick.labelsize'] = f_s  # Font size of x-axis tick labels
mpl.rcParams['ytick.labelsize'] = f_s  # Font size of y-axis tick labels

# Create a figure with multiple subplots
# fig, axs = plt.subplots(2, 2, figsize=(16, 13))  # 2x2 grid of subplots
plt.figure(figsize=(16,13))  # Adjust the figure size as needed

# Define marker styles and sizes
symbols = ['o', 's','o' ]  # Define symbols for each column
# colors = [ '#08a841', '#086a6a','#08a8a8']  # Define colors as shades of #08a8a8
colors = [ '#FF8300', '#FF4500','#08a8a8'] #(Burnt Orange #B8390E) (#DC4731 Red Orange) (#A82810 Scarlet) (#F67B50 Coral) (#F89700 Orange) # Define colors as shades of #fb8500    Orange  #FF8300  Red Orange #FF4500
linestyles = ['-.', '--', ':']  # Define linestyles for each column
marker_sizes = [16, 16, 16]  # Define marker sizes for each column


######## Panel 1 #########################################################################################################################################################
plt.subplot(2, 2, 1)

# Read the data
file_path = "/home/<USER>/Documents/Work/Parameter/BiAlO/Production_data/hysteresis/pressure_vs_Tc.out"
data = pd.read_csv(file_path, delim_whitespace=True)

# Define columns to plot
columns_to_plot = [ 2]

# Plot the data
for i, column_index in enumerate(columns_to_plot):
    plt.plot(data.iloc[:, 0], (data.iloc[:, column_index]), marker=symbols[i], markerfacecolor='none' , linestyle=linestyles[i],linewidth=1.5
             , color=colors[i], markersize=marker_sizes[i], label=f'Column {column_index}')


plt.ylim(0.8, 1.2)  # Set y-axis limits (-150 to 50)
plt.xlim(-5.0, 5.0)

# Add title and labels
plt.xlabel('Pressure (GPa)', fontsize=f_s)  # Set font size for X-axis label
plt.ylabel('$T_C/T_C(0)$', fontsize=f_s)  # Set font size for Y-axis label

# Add manual legend
plt.xticks(np.arange(-6, 7, 1))  # Set the tick locations from 0 to 250 in steps of 50

# Add ticks on the upper and right sides
plt.tick_params(axis='x', direction='in', which='both', bottom=True, top=True, labelbottom=True)
plt.tick_params(axis='y', direction='in', which='both', left=True, right=True, labelleft=True)

# Add subplot label (a)
plt.text(-6.3, 1.25, '(a)', fontsize=f_s, va='top', ha='left')


######## Panel 2 #######################################################################################################################################################
plt.subplot(2, 2, 2)

# Read the data
file_path = "/home/<USER>/Documents/Work/Parameter/BiAlO/Production_data/hysteresis/pressure_vs_pol.out"
data = pd.read_csv(file_path, delim_whitespace=True)

# Define columns to plot
columns_to_plot = [ 1]

# Plot the data
for i, column_index in enumerate(columns_to_plot):
    plt.plot(data.iloc[:, 0], (data.iloc[:, column_index]), marker=symbols[i], markerfacecolor='none' , linestyle=linestyles[i],linewidth=1.5
             , color=colors[i], markersize=marker_sizes[i], label=f'Column {column_index}')


# Set limits for x and y axes
plt.ylim(44, 52)  # Set y-axis limits (-150 to 50)
plt.xlim(-5.0, 5.0)

# Add title and labels
plt.xlabel('Pressure (GPa)', fontsize=f_s)  # Set font size for X-axis label
plt.ylabel('Polarization ($\mu$C/cm$^2$)', fontsize=f_s)  # Set font size for Y-axis label

# Add manual legend
plt.xticks(np.arange(-6, 7, 1))  # Set the tick locations from 0 to 250 in steps of 50

# Add ticks on the upper and right sides
plt.tick_params(axis='x', direction='in', which='both', bottom=True, top=True, labelbottom=True)
plt.tick_params(axis='y', direction='in', which='both', left=True, right=True, labelleft=True)

# Add subplot label (b)
plt.text(-6.3, 53, '(b)', fontsize=f_s, va='top', ha='left')

######## Panel 3 ######################################################################################################################################################
plt.subplot(2, 2, 3)

# Read the data
file_path = "/home/<USER>/Documents/Work/Parameter/BiAlO/Production_data/hysteresis/uni_stress_vs_Tc.out"
data = pd.read_csv(file_path, delim_whitespace=True)

# Define columns to plot
columns_to_plot = [ 1,2]

# Plot the data
for i, column_index in enumerate(columns_to_plot):
    plt.plot(data.iloc[:, 0], (data.iloc[:, column_index]), marker=symbols[i], markerfacecolor='none' , linestyle=linestyles[i],linewidth=1.5
             , color=colors[i], markersize=marker_sizes[i], label=f'Column {column_index}')


# Set limits for x and y axes
plt.xlim(-5.0, 5.0)

# Add title and labels
plt.xlabel('Uniaxial stress (GPa)', fontsize=f_s)  # Set font size for X-axis label
plt.ylabel('$T_C/T_C(0)$', fontsize=f_s)  # Set font size for Y-axis label

# Add manual legend
# plt.legend(['P$_s$', 'P$_r$', 'Zylberberg  $\mathit{et \ al.}$', 'Zylberberg  $\mathit{et \ al.}$','Son $\mathit{et \ al.}$','Son $\mathit{et \ al.}$', 'DFT' ], fontsize=20,loc='lower right')  # Add manual legend with labels
plt.legend(['T$_{C_2}$', 'T$_{C_1}$'], fontsize=20,loc='upper left')  # Add manual legend with labels
plt.xticks(np.arange(-6, 7, 1))  # Set the tick locations from 0 to 250 in steps of 50


# Add ticks on the upper and right sides
plt.tick_params(axis='x', direction='in', which='both', bottom=True, top=True, labelbottom=True)
plt.tick_params(axis='y', direction='in', which='both', left=True, right=True, labelleft=True)

# Add subplot label (c)
plt.text(-6.3, 1.45, '(c)', fontsize=f_s, va='top', ha='left')


######## Panel 4 ######################################################################################################################################################
plt.subplot(2, 2, 4)

# Read the data
file_path = "/home/<USER>/Documents/Work/Parameter/BiAlO/Production_data/hysteresis/uni_stress_vs_Tc.out"
data = pd.read_csv(file_path, delim_whitespace=True)

# Define columns to plot
columns_to_plot = [ 1,2]

# Plot the data
for i, column_index in enumerate(columns_to_plot):
    plt.plot(data.iloc[:, 0], (data.iloc[:, column_index]), marker=symbols[i], markerfacecolor='none' , linestyle=linestyles[i],linewidth=1.5
             , color=colors[i], markersize=marker_sizes[i], label=f'Column {column_index}')

# Set limits for x and y axes
plt.xlim(-5.0, 5.0)

# Add title and labels
plt.xlabel('Biaxial stress (GPa)', fontsize=f_s)  # Set font size for X-axis label
plt.ylabel('$T_C/T_C(0)$', fontsize=f_s)  # Set font size for Y-axis label

# Add manual legend
plt.legend(['T$_{C_2}$', 'T$_{C_1}$'], fontsize=20,loc='upper left')  # Add manual legend with labels
plt.xticks(np.arange(-6, 7, 1))  # Set the tick locations from 0 to 250 in steps of 50

# Add ticks on the upper and right sides
plt.tick_params(axis='x', direction='in', which='both', bottom=True, top=True, labelbottom=True)
plt.tick_params(axis='y', direction='in', which='both', left=True, right=True, labelleft=True)

# Add subplot label (d)
plt.text(-6.3, 1.45, '(d)', fontsize=f_s, va='top', ha='left')  ## the -6.3  and 1.45 is the value of x and y axis data point in actual scale 


# # Define subplot labels and positions
# subplot_labels = ['(a)', '(b)', '(c)', '(d)']
# positions = [(2, 2, 1), (2, 2, 2), (2, 2, 3), (2, 2, 4)]

# # Label Adding Loop
# for i, pos in enumerate(positions):
#     plt.subplot(*pos)
#     # Add subplot label
#     plt.text(-0.1, 1.05, subplot_labels[i], transform=plt.gca().transAxes, fontsize=f_s, fontweight='bold')


# Adjust spacing between subplots
plt.subplots_adjust(hspace=0.3, wspace=0.3)

## Save the plot in high-resolution EPS format
# savepath='/home/<USER>/Documents/Work/Parameter/BiAlO/Production_data/orange/pressure_stress.eps'
# plt.savefig(savepath, format='eps', dpi=600)


# Show the plot
plt.tight_layout()  # Adjust layout to prevent clipping of labels
plt.show()

'''Now we can merge any graph to any arrange pattern just we need to keep the common style section first then all the individual
plotings just we need to define first the figure size to accomodate the subplots then add plt.subplot(2, 2, 1) first 2 corresponds 
to column and next refers to row then third refers to plot sequence number  '''

################ Common Style Section #######################################################################################################################################
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib as mpl
from matplotlib import rc
mpl.rcParams['font.family'] = 'Times New Roman'
l_w=2.5
f_s=25
# Adjusting Matplotlib's default settings
mpl.rcParams['axes.linewidth'] = l_w  # Thickness of the axis lines
mpl.rcParams['xtick.major.width'] = l_w  # Thickness of major ticks on x-axis
mpl.rcParams['ytick.major.width'] = l_w  # Thickness of major ticks on y-axis
mpl.rcParams['xtick.minor.width'] = 1.5  # Thickness of minor ticks on x-axis
mpl.rcParams['ytick.minor.width'] = 1.5  # Thickness of minor ticks on y-axis
mpl.rcParams['xtick.major.size'] = 6  # Length of major ticks on x-axis
mpl.rcParams['ytick.major.size'] = 6  # Length of major ticks on y-axis
mpl.rcParams['xtick.minor.size'] = 4  # Length of minor ticks on x-axis
mpl.rcParams['ytick.minor.size'] = 4  # Length of minor ticks on y-axis
mpl.rcParams['xtick.labelsize'] = f_s  # Font size of x-axis tick labels
mpl.rcParams['ytick.labelsize'] = f_s  # Font size of y-axis tick labels

# Create a figure with multiple subplots
plt.figure(figsize=(22,12))  # Adjust the figure size as needed

# Define marker styles and sizes
symbols = ['o', 's','o' ]  # Define symbols for each column
# colors = [ '#08a841', '#086a6a','#08a8a8']  # Define colors as shades of #08a8a8
# colors = [ '#BF360C', '#D84315', '#EF6C00', '#FFB300', '#FFCC80','#FFAA80','#013c3c', '#086a6a','#08a8a8'] #(Burnt Orange #B8390E) (#DC4731 Red Orange) (#A82810 Scarlet) (#F67B50 Coral) (#F89700 Orange) # Define colors as shades of #fb8500    Orange  #FF8300  Red Orange #FF4500
colors=['#BF360C', '#EF6C00', '#FFB300','#08a841', '#086a6a','#08a8a8']
linestyles = ['-.', '--', ':']  # Define linestyles for each column
marker_sizes = [16, 16, 16]  # Define marker sizes for each column



######## Panel 1 #######################################################################################################################################################
plt.subplot(2, 3, 1)

# for k, i in enumerate (range(-3,4,1)):  # Loop from -5 to 5 inclusive
for k, i in enumerate([x for x in range(-3, 4, 1) if x != 0]):  # Exclude 0 from the range
    # Format the pressure value: include '-' for negative values, but no '+' for positive values
    pressure = f"{i:.2f}GPa"  # Format the pressure value, e.g., "-5.00GPa", "5.00GPa"

    # calculate_polarization(f"temp_vs_u_uniStress_{pressure}.out", f"temp_vs_v_uniStress_{pressure}.out", f"temp_vs_pol_uniStress_{pressure}.out")
    # calculate_polarization(f"temp_vs_u_biStress_{pressure}.out", f"temp_vs_v_biStress_{pressure}.out", f"temp_vs_pol_biStress_{pressure}.out")

    # Read the data
    file_path = f"/home/<USER>/Documents/Work/Parameter/BiAlO/Production_data/pressure/temp_vs_pol_pressure_{pressure}.out"
    data = pd.read_csv(file_path, delim_whitespace=True)

    # Define columns to plot
    columns_to_plot = [ 4]
    # skip_value=2
    
    # Plot the data
    for j, column_index in enumerate(columns_to_plot):
        plt.plot(data.iloc[::, 0], (data.iloc[::, column_index]),  markerfacecolor='none' , linestyle='-',linewidth=1.5
             , color=colors[k], markersize=0, label=f' {i}GPa')
    
    plt.legend(fontsize=f_s, loc='lower left', ncol=1, bbox_to_anchor=(0.0, -0.05))

# Set limits for x and y axes
plt.xlim(-100,1700)  # Adjust x-axis limits as needed
plt.ylim(0,90)  # Adjust x-axis limits as needed

plt.xticks(np.arange(0, 1800, 400))  # Set the tick locations from 0 to 250 in steps of 50
plt.yticks(np.arange(0, 95, 15))

# Add title and labels
plt.xlabel('Temperature (K)', fontsize=f_s)  # Set font size for X-axis label
plt.ylabel('Polarization ($\mu$C/cm$^2$)', fontsize=f_s)  # Set font size for Y-axis label


# Add ticks on the upper and right sides
plt.tick_params(axis='x', direction='in', which='both', bottom=True, top=True, labelbottom=True)
plt.tick_params(axis='y', direction='in', which='both', left=True, right=True, labelleft=True)

# Add subplot label (b)
plt.text(200, 102, '(a) Hydrostatic Pressure', fontsize=f_s, va='top', ha='left')

######## Panel 2 #######################################################################################################################################################
plt.subplot(2, 3, 2)

# for k, i in enumerate (range(-3,4,1)):  # Loop from -5 to 5 inclusive
for k, i in enumerate([x for x in range(-3, 4, 1) if x != 0]):  # Exclude 0 from the range
    # Format the pressure value: include '-' for negative values, but no '+' for positive values
    pressure = f"{i:.2f}GPa"  # Format the pressure value, e.g., "-5.00GPa", "5.00GPa"

    # Read the data
    file_path = f"/home/<USER>/Documents/Work/Parameter/BiAlO/Production_data/uniaxial/temp_vs_pol_uniStress_{pressure}.out"
    data = pd.read_csv(file_path, delim_whitespace=True)

    # Define columns to plot
    columns_to_plot = [ 4]
    # skip_value=2
    
    # Plot the data
    for j, column_index in enumerate(columns_to_plot):
        plt.plot(data.iloc[::, 0], (data.iloc[::, column_index]),  markerfacecolor='none' , linestyle='-',linewidth=1.5
             , color=colors[k], markersize=0, label=f' {i}GPa')
    
    plt.legend(fontsize=f_s, loc='lower left', ncol=1, bbox_to_anchor=(0.0, -0.05))

# Set limits for x and y axes
plt.xlim(-100,1700)  # Adjust x-axis limits as needed
plt.ylim(0,90)  # Adjust x-axis limits as needed

plt.xticks(np.arange(0, 1800, 400))  # Set the tick locations from 0 to 250 in steps of 50
plt.yticks(np.arange(0, 95, 15))

# Add title and labels
plt.xlabel('Temperature (K)', fontsize=f_s)  # Set font size for X-axis label
plt.ylabel('Polarization ($\mu$C/cm$^2$)', fontsize=f_s)  # Set font size for Y-axis label


# Add ticks on the upper and right sides
plt.tick_params(axis='x', direction='in', which='both', bottom=True, top=True, labelbottom=True)
plt.tick_params(axis='y', direction='in', which='both', left=True, right=True, labelleft=True)

# Add subplot label (b)
plt.text(300, 102, '(b) Uniaxial Stress', fontsize=f_s, va='top', ha='left')

######## Panel 3 #######################################################################################################################################################
plt.subplot(2, 3, 3)

# for k, i in enumerate (range(-3,4,1)):  # Loop from -5 to 5 inclusive
for k, i in enumerate([x for x in range(-3, 4, 1) if x != 0]):  # Exclude 0 from the range
    # Format the pressure value: include '-' for negative values, but no '+' for positive values
    pressure = f"{i:.2f}GPa"  # Format the pressure value, e.g., "-5.00GPa", "5.00GPa"

    # Read the data
    file_path = f"/home/<USER>/Documents/Work/Parameter/BiAlO/Production_data/biaxial/temp_vs_pol_biStress_{pressure}.out"
    data = pd.read_csv(file_path, delim_whitespace=True)

    # Define columns to plot
    columns_to_plot = [ 4]
    # skip_value=2
    
    # Plot the data
    for j, column_index in enumerate(columns_to_plot):
        plt.plot(data.iloc[::, 0], (data.iloc[::, column_index]),  markerfacecolor='none' , linestyle='-',linewidth=1.5
             , color=colors[k], markersize=0, label=f' {i}GPa')
    
    plt.legend(fontsize=f_s, loc='lower left', ncol=1, bbox_to_anchor=(0.0, -0.05))

# Set limits for x and y axes
plt.xlim(-100,1600)  # Adjust x-axis limits as needed
plt.ylim(0,90)  # Adjust x-axis limits as needed

plt.xticks(np.arange(0, 1700, 400))  # Set the tick locations from 0 to 250 in steps of 50
plt.yticks(np.arange(0, 95, 15))

# Add title and labels
plt.xlabel('Temperature (K)', fontsize=f_s)  # Set font size for X-axis label
plt.ylabel('Polarization ($\mu$C/cm$^2$)', fontsize=f_s)  # Set font size for Y-axis label


# Add ticks on the upper and right sides
plt.tick_params(axis='x', direction='in', which='both', bottom=True, top=True, labelbottom=True)
plt.tick_params(axis='y', direction='in', which='both', left=True, right=True, labelleft=True)

# Add subplot label (b)
plt.text(350, 102, '(c) Biaxial Stress', fontsize=f_s, va='top', ha='left')

######## Panel 4 #########################################################################################################################################################
plt.subplot(2, 3, 4)

# Read the data
file_path = "/home/<USER>/Documents/Work/Parameter/BiAlO/Production_data/pressure/pressure_vs_Tc.out"
data = pd.read_csv(file_path, delim_whitespace=True)

# Define columns to plot
columns_to_plot = [ 2]

# Plot the data
for i, column_index in enumerate(columns_to_plot):
    plt.plot(data.iloc[:, 0], (data.iloc[:, column_index]), marker=symbols[i], markerfacecolor='none' , linestyle=linestyles[i],linewidth=1.5
             , color=colors[i], markersize=marker_sizes[i], label=f'Column {column_index}')


# Define the x-axis and y-axis data
x = data.iloc[:, 0]
y1 = data.iloc[:, columns_to_plot[0]]
# y2 = data.iloc[:, columns_to_plot[1]]

# Shade the regions
# s_color=["#0077BB","#EE7733","#999933"]
s_color=['#80BDDD','#F7BB99','#CCCC99']
plt.fill_between(x, y1.min(), y1, color=s_color[0],  label='Region 1')  # R1: y < y1
plt.fill_between(x, y1.max(), y1, color=s_color[2],  label='Region 1')  # R1: y < y1
# plt.fill_between(x, y1, y2, where=((y1 == y2) | (y1 < y2)), color='red', alpha=0.5, label='Region 3') # R3: y1 < y < y2
# # plt.fill_between(x, y2.max(), y1, where=(x <= 0), color='orange', alpha=0.5, label='Region 2: y > y1 (x<0)')
# # plt.fill_between(x, y2.max(), y2,where=(x >= 0) , color='orange', alpha=0.5, label='Region 2: y > y2 (x>=0)')
# plt.fill_between(x, y2.max(), np.where(x <= 0, y1, y2), color='orange', alpha=0.5, label='Region 2: y > y1 (x<0) or y > y2 (x>=0)')
plt.text(-2, 0.95, 'R', ha='center', va='top', fontsize=f_s)
plt.text(3, 1.1, 'C', ha='center', va='top', fontsize=f_s)
# plt.text(3, 1.1, 'T', ha='center', va='top', fontsize=f_s)


plt.ylim(0.785, 1.22)  # Set y-axis limits (-150 to 50)
plt.xlim(-6.0, 6.0)

# Add title and labels
plt.xlabel('Pressure (GPa)', fontsize=f_s)  # Set font size for X-axis label
plt.ylabel('$T_C/T_C(0)$', fontsize=f_s)  # Set font size for Y-axis label

# Add manual legend
plt.xticks(np.arange(-5,6, 2))  # Set the tick locations from 0 to 250 in steps of 50

# Add ticks on the upper and right sides
plt.tick_params(axis='x', direction='in', which='both', bottom=True, top=True, labelbottom=True)
plt.tick_params(axis='y', direction='in', which='both', left=True, right=True, labelleft=True)

# Add subplot label (a)
plt.text(-0.5, 1.27, '(d)', fontsize=f_s, va='top', ha='left')

######## Panel 5 ######################################################################################################################################################
plt.subplot(2, 3, 5)

# Read the data
file_path = "/home/<USER>/Documents/Work/Parameter/BiAlO/Production_data/uniaxial/uni_stress_vs_Tc.out"
data = pd.read_csv(file_path, delim_whitespace=True)

# # Define columns to plot
columns_to_plot = [ 1,2]

# # Plot the data
for i, column_index in enumerate(columns_to_plot):
    plt.plot(data.iloc[:, 0], (data.iloc[:, column_index]), marker=symbols[i], markerfacecolor='none' , linestyle=linestyles[i],linewidth=1.5
             , color=colors[i], markersize=marker_sizes[i], label=f'Column {column_index}')


# Define the x-axis and y-axis data
x = data.iloc[:, 0]
y1 = data.iloc[:, columns_to_plot[0]]
y2 = data.iloc[:, columns_to_plot[1]]

# Shade the regions
# s_color=["#0077BB","#EE7733","#999933"]
s_color=['#80BDDD','#F7BB99','#CCCC99']
plt.fill_between(x, y1.min(), y1, color=s_color[0],  label='Region 1')  # R1: y < y1
plt.fill_between(x, y1, y2, where=((y1 == y2) | (y1 < y2)), color=s_color[1],  label='Region 3') # R3: y1 < y < y2
# plt.fill_between(x, y2.max(), y1, where=(x <= 0), color='orange', alpha=0.5, label='Region 2: y > y1 (x<0)')
# plt.fill_between(x, y2.max(), y2,where=(x >= 0) , color='orange', alpha=0.5, label='Region 2: y > y2 (x>=0)')
plt.fill_between(x, y2.max(), np.where(x <= 0, y1, y2), color=s_color[2], alpha=0.5, label='Region 2: y > y1 (x<0) or y > y2 (x>=0)')
plt.text(0, 0.8, 'R', ha='center', va='top', fontsize=f_s)
plt.text(0, 1.2, 'C', ha='center', va='top', fontsize=f_s)
plt.text(3, 1.1, 'T', ha='center', va='top', fontsize=f_s)




# Set limits for x and y axes
plt.xlim(-5.0, 6.0)
plt.ylim(0.55, 1.42) 

# Add title and labels
plt.xlabel('Uniaxial Stress (GPa)', fontsize=f_s)  # Set font size for X-axis label
plt.ylabel('$T_C/T_C(0)$', fontsize=f_s)  # Set font size for Y-axis label

# Add manual legend
# plt.legend(['P$_s$', 'P$_r$', 'Zylberberg  $\mathit{et \ al.}$', 'Zylberberg  $\mathit{et \ al.}$','Son $\mathit{et \ al.}$','Son $\mathit{et \ al.}$', 'DFT' ], fontsize=20,loc='lower right')  # Add manual legend with labels
plt.legend(['T$_{C_2}$', 'T$_{C_1}$'], fontsize=f_s,loc='upper left', ncol=2, bbox_to_anchor=(0.0, 1.05))  # Add manual legend with labels
plt.xticks(np.arange(-5, 6, 2))  # Set the tick locations from 0 to 250 in steps of 50
# plt.yticks(np.arange(0.4, 1.42, 0.2))  # Set the tick locations from 0 to 250 in steps of 50

# Add ticks on the upper and right sides
plt.tick_params(axis='x', direction='in', which='both', bottom=True, top=True, labelbottom=True)
plt.tick_params(axis='y', direction='in', which='both', left=True, right=True, labelleft=True)

# Add subplot label (c)
plt.text(-0.5, 1.53, '(e)', fontsize=f_s, va='top', ha='left')


######## Panel 6 ######################################################################################################################################################
plt.subplot(2, 3, 6)

# Read the data
file_path = "/home/<USER>/Documents/Work/Parameter/BiAlO/Production_data/biaxial/bi_stress_vs_Tc.out"
data = pd.read_csv(file_path, delim_whitespace=True)

# Define columns to plot
columns_to_plot = [ 1,2]

# Plot the data
for i, column_index in enumerate(columns_to_plot):
    plt.plot(data.iloc[:, 0], (data.iloc[:, column_index]), marker=symbols[i], markerfacecolor='none' , linestyle=linestyles[i],linewidth=1.5
             , color=colors[i], markersize=marker_sizes[i], label=f'Column {column_index}')


# Define the x-axis and y-axis data
x = data.iloc[:, 0]
y1 = data.iloc[:, columns_to_plot[0]]
y2 = data.iloc[:, columns_to_plot[1]]

# Shade the regions
# s_color=["#0077BB","#EE7733","#999933"]
s_color=['#80BDDD','#F7BB99','#CCCC99']
# s_color=["#882255", "#999933", "#DDCC77"]
# s_color=["#AA4499", "#44AA99","#CC3311"]
plt.fill_between(x, y1.min(), y1, color=s_color[0], label='Region 1')  # R1: y < y1
plt.fill_between(x, y1, y2, where=((y1 == y2) | (y1 < y2)), color=s_color[1], label='Region 3') # R3: y1 < y < y2
plt.fill_between(x, y2.max(), np.where(x <= 0, y2, y1), color=s_color[2], label='Region 2: y > y1 (x<0) or y > y2 (x>=0)')
plt.text(0, 0.7, 'R', ha='center', va='top', fontsize=f_s)
plt.text(0, 1.13, 'C', ha='center', va='top', fontsize=f_s)
plt.text(-4, 1.0, 'T', ha='center', va='top', fontsize=f_s)


# Set limits for x and y axes
plt.xlim(-6, 5.0)
plt.ylim(0.3, 1.3) 

# Add title and labels
plt.xlabel('Biaxial Stress (GPa)', fontsize=f_s)  # Set font size for X-axis label
plt.ylabel('$T_C/T_C(0)$', fontsize=f_s)  # Set font size for Y-axis label

# Add manual legend
plt.legend(['T$_{C_2}$', 'T$_{C_1}$'], fontsize=f_s,loc='upper left', ncol=2, bbox_to_anchor=(0.0, 1.05))  # Add manual legend with labels
plt.xticks(np.arange(-5, 6, 2))  # Set the tick locations from 0 to 250 in steps of 50
plt.yticks(np.arange(0.4, 1.35, 0.2))  # Set the tick locations from 0 to 250 in steps of 50

# Add ticks on the upper and right sides
plt.tick_params(axis='x', direction='in', which='both', bottom=True, top=True, labelbottom=True)
plt.tick_params(axis='y', direction='in', which='both', left=True, right=True, labelleft=True)

# Add subplot label (d)
plt.text(-0.5, 1.42, '(f)', fontsize=f_s, va='top', ha='left')  ## the -6.3  and 1.45 is the value of x and y axis data point in actual scale 


# Adjust spacing between subplots
plt.subplots_adjust(hspace=0.4, wspace=0.45)

## Save the plot in high-resolution EPS format
savepath='/home/<USER>/Documents/Work/Parameter/BiAlO/Production_data/orange/Fig_5_new.eps'
plt.savefig(savepath, format='eps', dpi=600)


# Show the plot
plt.tight_layout()  # Adjust layout to prevent clipping of labels
plt.show()

################ Common Style Section #######################################################################################################################################
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib as mpl
from matplotlib import rc
mpl.rcParams['font.family'] = 'Times New Roman'
l_w=2.5
f_s=25
# Adjusting Matplotlib's default settings
mpl.rcParams['axes.linewidth'] = l_w  # Thickness of the axis lines
mpl.rcParams['xtick.major.width'] = l_w  # Thickness of major ticks on x-axis
mpl.rcParams['ytick.major.width'] = l_w  # Thickness of major ticks on y-axis
mpl.rcParams['xtick.minor.width'] = 1.5  # Thickness of minor ticks on x-axis
mpl.rcParams['ytick.minor.width'] = 1.5  # Thickness of minor ticks on y-axis
mpl.rcParams['xtick.major.size'] = 6  # Length of major ticks on x-axis
mpl.rcParams['ytick.major.size'] = 6  # Length of major ticks on y-axis
mpl.rcParams['xtick.minor.size'] = 4  # Length of minor ticks on x-axis
mpl.rcParams['ytick.minor.size'] = 4  # Length of minor ticks on y-axis
mpl.rcParams['xtick.labelsize'] = f_s  # Font size of x-axis tick labels
mpl.rcParams['ytick.labelsize'] = f_s  # Font size of y-axis tick labels

# Create a figure with multiple subplots
plt.figure(figsize=(8,6.5))  # Adjust the figure size as needed

# Define marker styles and sizes
symbols = ['o', 's','o' ]  # Define symbols for each column
# colors = [ '#08a841', '#086a6a','#08a8a8']  # Define colors as shades of #08a8a8
# colors = [ '#BF360C', '#D84315', '#EF6C00', '#FFB300', '#FFCC80','#FFAA80','#013c3c', '#086a6a','#08a8a8'] #(Burnt Orange #B8390E) (#DC4731 Red Orange) (#A82810 Scarlet) (#F67B50 Coral) (#F89700 Orange) # Define colors as shades of #fb8500    Orange  #FF8300  Red Orange #FF4500
colors=['#BF360C', '#EF6C00', '#FFB300','#FFAA80','#08a841', '#086a6a','#08a8a8']
linestyles = ['-.', '--', ':']  # Define linestyles for each column
marker_sizes = [16, 16, 16]  # Define marker sizes for each column



######## Panel 1 #######################################################################################################################################################
# plt.subplot(2, 3, 1)

# for k, i in enumerate (range(-3,1,1)):  # Loop from -5 to 5 inclusive
for k, i in enumerate([x for x in range(-3, 4, 1) if x != 0]):  # Exclude 0 from the range
    # Format the pressure value: include '-' for negative values, but no '+' for positive values
    pressure = f"{i:.2f}GPa"  # Format the pressure value, e.g., "-5.00GPa", "5.00GPa"

    # calculate_polarization(f"temp_vs_u_uniStress_{pressure}.out", f"temp_vs_v_uniStress_{pressure}.out", f"temp_vs_pol_uniStress_{pressure}.out")
    # calculate_polarization(f"temp_vs_u_biStress_{pressure}.out", f"temp_vs_v_biStress_{pressure}.out", f"temp_vs_pol_biStress_{pressure}.out")

    # Read the data
    # file_path = f"/home/<USER>/Documents/Work/Parameter/BiAlO/Production_data/pressure/temp_vs_pol_pressure_{pressure}.out"
    file_path = f"/home/<USER>/Documents/Work/Parameter/BiAlO/Production_data/uniaxial/temp_vs_pol_uniStress_{pressure}.out"
    
    data = pd.read_csv(file_path, delim_whitespace=True)

    # Define columns to plot
    columns_to_plot = [ 4]
    
    
    # Plot the data
    for j, column_index in enumerate(columns_to_plot):
        plt.plot(data.iloc[::, 0], (data.iloc[::, column_index]),  markerfacecolor='none' , linestyle='-',linewidth=1.5
             , color=colors[k], markersize=0, label=f' {i}GPa')
    
    plt.legend(fontsize=20,loc='lower left',ncol=1)

# Set limits for x and y axes
plt.ylim(0,90)  # Adjust x-axis limits as needed

plt.xticks(np.arange(0, 1600, 300))  # Set the tick locations from 0 to 250 in steps of 50
plt.yticks(np.arange(0, 95, 15))

# Add title and labels
plt.xlabel('Temperature (K)', fontsize=f_s)  # Set font size for X-axis label
plt.ylabel('Polarization ($\mu$C/cm$^2$)', fontsize=f_s)  # Set font size for Y-axis label


# Add ticks on the upper and right sides
plt.tick_params(axis='x', direction='in', which='both', bottom=True, top=True, labelbottom=True)
plt.tick_params(axis='y', direction='in', which='both', left=True, right=True, labelleft=True)

# Add subplot label (b)
plt.text(200, 102, '           Pressure', fontsize=f_s, va='top', ha='left')

plt.tight_layout()  # Adjust layout to prevent clipping of labels
plt.show()

