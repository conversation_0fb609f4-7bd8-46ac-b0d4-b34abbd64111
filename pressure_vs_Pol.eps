%!PS-Adobe-3.0 EPSF-3.0
%%Title: pressure_vs_Pol.eps
%%Creator: Matplotlib v3.7.1, https://matplotlib.org/
%%CreationDate: Thu Jul 25 15:44:07 2024
%%Orientation: portrait
%%BoundingBox: 36 174 576 618
%%HiResBoundingBox: 36.475000 174.577031 575.525000 617.422969
%%EndComments
%%BeginProlog
/mpldict 11 dict def
mpldict begin
/_d { bind def } bind def
/m { moveto } _d
/l { lineto } _d
/r { rlineto } _d
/c { curveto } _d
/cl { closepath } _d
/ce { closepath eofill } _d
/box {
      m
      1 index 0 r
      0 exch r
      neg 0 r
      cl
    } _d
/clipbox {
      box
      clip
      newpath
    } _d
/sc { setcachedevice } _d
%!PS-TrueTypeFont-1.0-2.53740
%%Title: unknown
%%Creator: Converted from TrueType to type 42 by PPR
15 dict begin
/FontName /TimesNewRomanPSMT def
/PaintType 0 def
/FontMatrix[1 0 0 1 0 0]def
/FontBBox[-568 -307 2028 1007]def
/FontType 42 def
/Encoding StandardEncoding def
/FontInfo 10 dict dup begin
/FamilyName (unknown) def
/FullName (unknown) def
/Weight (unknown) def
/Version (unknown) def
/ItalicAngle 0.0 def
/isFixedPitch false def
/UnderlinePosition -223 def
/UnderlineThickness 100 def
end readonly def
/sfnts[<00010000000900800003001063767420F34DDA810000009C000007C46670676D
3775A72F0000086000000573676C79668575A62A00000DD400002BD868656164CAFCED46
000039AC000000366868656113290CB9000039E400000024686D74787702086300003A08
000000846C6F636194139F4200003A8C000000446D617870068410AC00003AD000000020
70726570D2662BF400003AF000000F18058E0000054C001F054C001C0394001B0000FFE1
0000FFE40000FFE8FE4AFFFC056B0023FE6AFFE00313000000AD000000AD000000000025
0096009F002400F0013100C200C0004A00A6004100500094004700CF00AF000E007901CB
00040023004400A80025011F0002004600170105009900D9005C007200E500E00028004B
00DE011200240045007000160039FFE90016004B0088FFB900D9000A004300AE00BA016C
0153002F00430048022C012B0025008FFFC000170028FFCDFFD80025009D00E50124FFB1
0048009D00E600110027007F00910012006A00CAFFFC00000024006200A7017C01E90021
0060008B0434048AFF6B003B00B500D5014BFF6B004D007905D809B5006C009100A30117
01C0FFDFFFE700BE04010065007F00820088009900B200C0022E034305A000200026003D
004E00610065007B00D9011301310340FF27FF42FF99004E00A700F2022B02C603070011
002B0049005F008D00A100AF00D600E400F5010B0135019D01AB01AB01D101EE05D80000
004B0075007A0080009D00A600A700AC00B9013101310217021700020017002900550080
008F00A500B200B300D0014B015901C001C103A50530FE3FFF14FF15FFE7FFFF002A0058
0099009F00C100E400F40130015901AB01AB03220374041E04740532FD81004D0064009C
00D000D100D600DE00E500F500F8012A012A01E1027E027FFF57FFA8FFE500000008001F
00380051005A006F0076007700A200C000C200C400F101FB0209027E02CF04C5057A05F0
FF92001200260042004B004F005100530064008B00AE00B200B800B800D600F501110120
01310138014E01520167018F019601B801D901D902060221027102EA03B003CB03DC0436
0505FF3A00120016001E001F002300570068006C007E0088009200A500A800C500C90115
0126012D013001D601D901F6023B0244024402A302CF02DE0385038F04FC0586FEE0FEEB
FEFBFF8A0007004400470058007500AA00E400EF011601200129016A017301E3027E0290
02B4030E0310032303350341035403590388039403C803CE047204AB04DA0549056105AB
0761FE6EFED1FF4BFF84000000010006001E0027002C0034003700620066006A006B006C
007000700072007C0081008A008E0091009200A000AB00B800BF00C900D500DD00EC00F4
0100012101300169016A016D017C0185018E018E019901AC01C101C501C901E101F601F6
01F60222022202280236023F02430246026702850285029402D002D602E8031C0363037F
03800380039E03B603D90400040404FF053205320548058B05A706CB07280748076208CC
FCEDFD2AFD59FDDEFE00FE1AFE5BFE96FEC1FEE7FF56FF7900010025002D002E007C0087
0091009900A100A500A500AA00AF00B600C600CC00D700DD00EC00F20102010501170118
0123012A012C0131013F014701490149014D01510151015501550157015A015A01610162
01680168017F0180018201830184018D0195019501950198019901A501A901B601B601B7
01BA01BA01D501DF01E601EA01F2020002000203021702250227022F0239024302430247
024F025202520267026F026F027002720276027E02A702B302B902D603130325032D0361
0371039903AE03C203D403F90402042C042F043C04560467048304CF04D104D804FB051F
05450568059E05C2061B06340655066A069806AF06E806FC070607500762077C07D407FF
082500AD00C700AA00B5000000000000000000000000002F06CF01730514047802DF009C
0018037005870155002500060254036C038E03D2056601F0032001DA018A0369036BFFA3
034602F8036F015602BF0122031F053A0366008C00FF01AB02E102F402E70415015402E9
0128049101B7026F034302060000000005D30415048305E8000002D7003A027D01C002C5
03830383FFBD003A059E01DF059E02D1002004E0021300DF01C001870297000000CE0269
028B0058043405FB0069015A01A905780182013E0288012A03D4049E00E5032302F301F0
0196007A00CD014A0424025E023901AB00CF00FD011E00ED017100700195004001BB01DD
01B8000101A803A7014C020C018D01B0020D0137010000CD032101D4030A005900000000
01260215015002F0025503BC06D00335010100D000D2007A01030130007C000000000000
000000FE006E006600940227002B0045004D00D3013200180097004100F4FEBCFFE90016
05D8058B009100A1032C00520030005D02CB003A009200E500E500580086003200BA0099
008800300298007CFF8001640028004D0065000200B8016A002F010B001100170100007F
00040016022200A6005F000000F8000A00CA0043004B01EE0077012000F401C00028045F
0000008C044500C20060007B008B008B0064005D00C2009C009206B505D3004F01170000
0420FE9E00CC00DC005E004600E30032001A003C0091005A00A1042C0041002000490071
009C009CFE4800400040008600CB0102007D003A003E006A0050044800290096FF6A0097
006900E0004C001B00C90069FF970043FFBD0052FF83FF8B005FFFA1FF5C00670053FFA8
002A0076FFB20036008705590256052B043400DE00C901C4004800DB018B00B3004800DA
01160125011800EA00EA00AE0046003E05BB008A04D70053003FFF8CFFD5001500280022
00990062004A00E4006D00EE00E5004803C00033FE4E02B1FF460370007905DF0051FFA7
FF1F010A0068FF6C004F00BC00A5070500AB0080001E05A54040403F3E3D3C3B3A393837
363534333231302F2E2D2C2B2A292827262524232221201F1E1D1C1B1A19181716141312
11100F0E0D0C0B0A090807060504030201002C4523466020B02660B004262348482D2C45
2346236120B02661B004262348482D2C45234660B0206120B04660B004262348482D2C45
23462361B0206020B02661B02061B004262348482D2C45234660B0406120B06660B00426
2348482D2C4523462361B0406020B02661B04061B004262348482D2C0110203C003C2D2C
20452320B0CD442320B8015A51582320B08D44235920B0ED51582320B04D44235920B090
51582320B00D44235921212D2C20204518684420B001602045B04676688A4560442D2C01
B10B0A432343650A2D2C00B10A0B4323430B2D2C00B0172370B101173E01B0172370B102
17453AB10200080D2D2C45B01A234445B01923442D2C2045B00325456164B05051584544
1B2121592D2CB00143632362B0002342B00F2B2D2C2045B0004360442D2C01B00643B007
43650A2D2C2069B04061B0008B20B12CC08A8CB8100062602B0C642364615C58B0036159
2D2C45B0112BB0172344B0177AE4182D2C45B0112BB01723442D2CB01243588745B0112B
B0172344B0177AE41B038A45186920B01723448A8A8720B0A05158B0112BB0172344B017
7AE41B21B0177AE45959182D2CB0022546608A46B040618C482D2C4B53205C58B0028559
58B00185592D2C20B0032545B019234445B01A23444565234520B00325606A20B0092342
23688A6A606120B01A8AB000527921B21A1A40B9FFE0001A45208A54582321B03F1B2359
61441CB114008A5279B31940201945208A54582321B03F1B235961442D2CB11011432343
0B2D2CB10E0F4323430B2D2CB10C0D4323430B2D2CB10C0D432343650B2D2CB10E0F4323
43650B2D2CB11011432343650B2D2C4B525845441B2121592D2C0120B003252349B04060
B0206320B000525823B002253823B002256538008A63381B212121212159012D2C4BB064
51584569B00943608A103A1B212110592D2C01B005251023208AF500B0016023EDEC2D2C
01B005251023208AF500B0016123EDEC2D2C01B0062510F500EDEC2D2C20B00160011020
3C003C2D2C20B001610110203C003C2D2CB02B2BB02A2A2D2C00B00743B006430B2D2C3E
B02A2A2D2C352D2C76B802B023701020B802B04520B0005058B00161593A2F182D2C2121
0C6423648BB84000622D2C21B08051580C6423648BB82000621BB200402F2B59B002602D
2C21B0C051580C6423648BB81555621BB200802F2B59B002602D2C0C6423648BB8400062
6023212D2CB4000100000015B00826B00826B00826B008260F10161345683AB001162D2C
B4000100000015B00826B00826B00826B008260F1016134568653AB001162D2C4B53234B
515A5820458A60441B2121592D2C4B545820458A60441B2121592D2C4B53234B515A5838
1B2121592D2C4B5458381B2121592D2C014B53234B515AB00225B00425B0062549234518
69525A58B00225B00225B00525462345696048592121212D2CB0134358031B02592D2CB0
134358021B03592D2C4B54B012435C5A58381B2121592D2CB012435C580CB00425B00425
060C6423646164B807085158B00425B00425012046B01060482046B0106048590A21211B
2121592D2CB012435C580CB00425B00425060C6423646164B807085158B00425B0042501
2046B8FFF060482046B8FFF06048590A21211B2121592D2C4B53234B515A58B03A2B1B21
21592D2C4B53234B515A58B03B2B1B2121592D2C4B53234B515AB012435C5A58381B2121
592D2C0C8A034B54B00426024B545A8A8A0AB012435C5A58381B2121592D2C462346608A
8A462320468A608A61B8FF8062232010238AB9035803588A70456020B0005058B00161B8
FFBA8B1BB0468C59B0106068013A2D0000010054FE4A027C058E0013003A40239611A711
02860C8911020A980911009801130100000A09A80E22500601068014545E182B10F65DED
FD3C3C103C003FED3FED3130005D015D01152627260235100037150606021514171E0202
7C9765909C0132F67B9E4E211A4A7DFE6F254C6691018AD4013601FF6E2A44ECFE96C5D6
AF8AA79A0001002EFE4A0256058E00130039402429042A08480503009801110A98091300
0101090AA80E222006300640060306801558A4182B10F65DEDFD3C3C103C003FED3FED31
30015D13351617161215100007353636123534272E022E98658F9CFECFF77B9F4D21194B
7C05642A4B6692FE77D5FECAFE016E2545EB016BC5D5B08AA69A00010003FFE4023E058E
0003005240090005CB1F6736000101B80327400D02031402020303000002010B00B8011F
40142003300360037003A003E0030603BB01AD02CB04B8016FB1DF182B10F6EDF45DED00
3F3C3F3C87052E2B7D10C431302B01012301023EFE155001EB058EFA5605AA000002004A
FFE803B705680010002400BAB2610820B80106B2050515B80106B20D0D1AB8010F401209
1A002640260240266026A026E026042611B8010F400E5F006F007F008F00A00005001925
BA011E010100182B4E10F45D4DED4E105D71F64DED003FED3FED31304379404C01242324
2224020602010301020607251C1B1D1B1E1B030613260F250B2618191719020621041162
001F061A6201140E116200160C1A620124012062011B082062011210156200190A156200
2B2B2B2B012B2B2B2B2A2B2B2B2A2B2A2A81133412373633321716111402062322272637
101716333236373611342726272623220706024A8C745A609C7C9B88D362C2816DC44539
7136741E2E302439293A44354834029EE8014F52419FC5FEAFECFEB695E5C1F7FEE8B195
6172AC0139E89B7330213D53FE9C000100F000000306056800160097401440186018A018
E01804001840180276008600020E411301690141000901A00022000301690141000801A0
0023000001F8000F016900160141000001F2400E010F0F020902010509080C020300BA01
F70003014940120E0E0F401135300F7F0F900FA00F040F1917BA022401E400182B4E10F4
5D2B3C4D10EDE4103C003F3C3F3C11123901113900F5EDFC01F52B2B3130005D01715D13
253311141616171521353E02351134272626232207F0014A21133C5CFE026038160A0725
1A254204C7A1FB8772381E022525021D317A02DC942A201E1F000001002C000003AB0568
001E0146408207180B3917181C3D3418401C3D3419401C3D340F1E161629073C074907A9
070640205B045A085B175A186B08741174129C0B9D0E9911AC0BAC0EC905C917C818D917
D918E020F904F9171515011D0419051B15191619171D180709170B180B1D341947198917
8F200718190202171A190C19060D031902050618171615140713040DB801684009094014
0C3F80090109B80333400C10051A8F19019F19AF190219BA03330003018DB301020C1EB8
018D400D0006E24F135F136F137F130413B801074013400001001A002040208020036020
A020022019BB01F90003000D014040145F026F027F028F02BF02CF02DF02EF020802191F
BA018E010100182B4E10F45D4DE43CED4E105D71F65D4DF45DED10ED003F3CEDFD5D713C
3FFD712BE411121739111239011112393902100E3C8710057DC40EC431300171725D005D
012B2B2B002B010321350000353426232206072336363332161514070607020721323636
3703AB5FFCE0016101209E6E649F262519CF9BA5DD304AA6F93E01626C57461A0105FEFB
2501420198A981A67571B9C6D4906767A2B5FEF03810312D00010053FFE8035605680032
014CB9000AFFE0B20C3909B8FFC0403C0C394109450A460B4B2204CF0901292938294034
6034CF34E034F70A0700340141097F237A2EAA24B924BA2EC92EDF23DF25DB2EEA22E925
0C490829B8018CB328281000B802E4B3D0300130B80334B5030510160116B8019FB51D40
2B2F341DB80143B3100D2928BB01680014000902E3400B50208020029020A0200220B803
33B3B00C010CB80190400B502D802D02902DA02D022DB80333B55F077F070207B802E540
0A403401A034C034023400B8013EB74013BF1302131933BA011E01E800182B4E10F45D4D
E4105D71F65DED5D71F45DED5D71E410F43C003FED2BED723FED5DED12392FED31304379
40362E2F1E260A0F04062221232124212521040605250E26260A2062012F042D62011E0F
206201210B2762010A092E063062011F0D1D62002B2B103C2B012B2B2B2B2B2A81818181
005D01715D7200712B2B1336363332171615140716161514070621222635343633321716
161716333236353427262726262323353E02353426232207683AB184A35742BA7D807092
FEEB89632F21191A117817252A6697231A1F2B964E204F9F4881609B68044A89956A4F5A
949E31B67BB081A844271D2C08053F060B9E6C4F4B381D28411E0A5E844F677FA6000002
0020000003B90568000A000D00D74028160D010F00450D400F03400F012A0C0115070102
06010309050601040C0B0D00040C0D04060C0D0DB8011C40120708140707080803040706
0C08001F0D010DBB019C0006000B019F400D050106090806060805040C0C04BB01490003
00080166400B09090F03019F03AF030203B801F74018013F0001001A000F01600FA00FE0
0F030F0640070107190EBA018E010100182B4E10F4713C105D71F65D3C4DF45D713C10E6
10FD3C003F3F392F103C103C10ED10FD723C1139113901111239872E2B047D10C4011112
390F0F0F313000725D015D7172011523112311213501331123110103B9B6A5FDC202756E
A5FE2401F48EFE9A0166800382FC8C02A1FD5F0000010062FFE80379054C002101064043
A4040112591E691E7F057F067F1D8C048D1D07350235215503552057216B1C7605791C87
028A1A8A1CA303A809A023E0230F0023010F1011121315161708140D020303B8011C4011
20211420030420211A1B1C030718040503B8019FB320202113BA01F900180313400C0D0D
01E20002E22121000400BA013E001B0147B58007A0070207B801F5400A40230140236023
022316B801F9B61002A021012103B8019F4009204010A01002101922BA019501E900182B
4E10E45D4D10ED105D3C10ED105D71F65DEDE4003F3C10ED10ED3FEDED12392FED011139
1112173987082E2B057D10C40011121739313001715D005D435C58400B6B046F12641E70
05701D055D59015D01072107041716151406060706232226353436333216171633323635
34262726270103794EFE685901099B8557845173797A6F2E231A272F4B4D75B19E8B6DBC
0104054CAAB6279E88B86BB680273753321C2B102134B17F7BD53A2D07020F0000020058
FFE803B105680018002800ED402A7509760A770E8209D925E925060603017D037A047A16
8517043C0828060503231928190603205F080108B80143B620260126260F01B8018DB318
000520B80106B30F0D0100BA01400023010F40120B1A002A402A02402A602AA02AE02A04
2A19BA013E001B010F4012001310132013301340139013A01307131929BA011E01010018
2B4E10FC5D4DFDE44E105D71F64DEDF43C003FED3F3CED12392F5DED7212173901111217
3931304379402C1C2509121D1C1E1C020611250D2625092362011F101B6200210E236201
240A2662011C12206200220C206200002B2B2B012B2B2B2B2B2A8181005D01715D01150E
030736333216151407062322272611341224363301061514161716333236353426232206
039684A7A36B2490918BCC677CCC8B61BE92010FF86BFDCC12474633495789887D265705
68250D4FA2C78963E0B0AA8CAA5CB3011DB60148FE58FD44875360E1422FA498ABFA2000
0003007CFFE8038A0568001900260033017A40BA59010109331F332A276F337A22800B80
0C801A801B822580268A33A918A51AA725B30CB41AB726BB27C50AC50BD70D1607000A01
060D021A0927160D171A250D250E4B018C01830D850EA9000E3B003A014B004B0149285F
015B275C336A006A01690267266A2768337B017C27762C7C338F048F0580078008821182
128F148F1698049608941196129B16A626AD27AD33B804B608E90BEA0CE90EE927E93229
070D09273A0039013832054408000C1A2704000C1A2704101720B80106B206052DB80106
B2130D1D4109010F00090140000901400030010F001001664010003540350240356035A0
35E035043523BC010F0003013E002A010F400A30174017901703171934BA01EE01E90018
2B4E10F45D4DEDF4ED105D71F6EDE4F4ED003FED3FED111217390117393130437940322B
2F1E2211160408152521052362001F071D62012C142A62002E1230620122042062011E08
2062012B162D62002F112D6200002B2B2B2B012B2B2B2B2B8181818101715D00715D0072
012626353436333216151406071617161514062322272635343625363635342623220615
141617130606151416333236353427260189A15DCCA9A4C86CABB0394CDAB1C16C567901
3178407666668035313653508D6D6C82264702AB84A05684BFB2724C9E6B884E66718FCB
7961735AB1D66C7D4F6977764F34682FFEE746A560819B7A5748396A0001004AFFE1050F
056B002400FB4042091E2F012F022F032F1F960F991EA30FA312B60FB7120B081E011617
017D037B1578168D038A169D03961AAC03BB03090C030E04021D48035903052F08101124
1B00B80105B5021B0101BA00B8034BB6209A05281C0301B802DFB5112BB0100110B80341
B58F0D9F0D020DB8032F4029140902AC000101013210ACAF11011F113F1102111A402601
26093C2018010F181F18021849256463182B4E10F45D724DED4E105DF672714DEDF471ED
003FFD71F45DF4E63FEDECF4ED0110EDF4ED10C9313043794020151B060C07251A260B26
1625061B092D000C15092D000819052D010A170D2D00002B2B012B2B2B2B2B2B81810171
5D0072715D01132326262322060215141216333236371706042320272635341224333217
16333237363704D11F1F3EE6A187DA7D76ED9884CA791F66FEF0BBFEAFB98AB6013FBD93
8F2A121B141A0B056BFE33CFB689FED4DFB8FEF29071A814B5A8FABAFCCB0154BB481613
1B3000010048FFE105AA056B0034012540540A04462E0219271A28021018101902203640
366036780870187019782A90189019B018B0190B2D2F760B870B0318362E1A503670368C
04AD04E036040C03860BC0360348081E1F1B18F322121F1B172123341B00B80105B3021B
0101BA01B30000034B402B319A2C1718182206282C030E282209012B1F1E221111501290
12020F124F1202001210127F12FF12041212B802F840100A3C5026010F261F2602264935
648A182B4E10F45D724DFDF62F5D71723C10FD3CE4003FED3FED12392F3C10ECF4ED0110
EDF4ED2B2B313043794034202B07100825282729272A2703060C2624250F21113B01201F
1011072B0A2D000D230A2D0010200E3B000927062D010B250E2D002B2B2B012B2B103C10
3C2B2B2B2A2B818101715D2B005D015D0172007271011323262726232007061514121633
323637113426262335211523220706151106062320272635343736373633321617163332
363704E92323355479BEFEFD877196F3804B8C411F4152020D194E1D1473E089FE77CC99
5666B295CB4A796F3813131B03056BFE54A05175CDADEFC2FEC09526250188663F212626
34256DFE613E3AFCBDF7B3A4C369571829152333000200220000042B054C001F002C014E
B9002EFFC040933A352F2E7518741B741C7C28702E951C077918B724BA28DB1BDB1CDA24
061927102E2826392539273B28302E5A276927702E802E0BC600011A1C291C4B1CD719DB
1B05A82801CA24D917DA24D927D828EB24061C40231D28080E1F1B082122011F1B072123
0F1F1B152123001D202C2A1D283F234F2302232307152A281616150208070812001A101A
301A401A701A051A492EB8FFC0401A3F35002E01402EB02E029F2EC02ED02E032E2C0122
0F0E9E2D2EB80177B3216163182B2B4EF43C4DFD3C4D105D71722BF65D4D4358B9002603
2DE91BB90026032DED59003F3C3F3C10ED1112392F5DED12393912392B2B2B3130437940
1C2429171C182528262917263301241C26330127192A3301251B2333002B2B012B2B2B2B
81810049547940101E22211F233B04221E203B0021201F0001103C103C2B002B8101715D
0071720172005D015D2B0111141716333315213533323736351134272623233521321616
1514062322262716163332363534262623220701A41C264D34FDBB335625141B274D3301
F1B6D290DBC831724135521D68974884543350027BFE75801F2C2525381F74036C801F2C
254BB27AA6D00E470A0AA18058974B1300020049FFED038903AF0032003D0255406F0B1C
8A33021253360112201F39803FA809B60A03122B127D007D3386008B0B8B35061D12163A
103F803F04091C4C054C06452045224C3A403F891D080A0E0720002249014B0A490B4935
4B37433A493D570B670B8509840A840B0F54168316021F3F5F3F0260083300343C2E292D
34BB011B000C000CFFC0B609390C280B390CB8FFC0401A3A35100C500C02400C500C600C
03200C500C600C760C040C3C18B8FFD840290B394F185F186F18032F187F1802187E1F10
0110251E07303C403C023C2C04702D802D022D35292C30B803464011040B2EC02D012D60
2500330D0C0C343433BB016700240025FFC040140E3900251F2580259025044025F02502
80250125BB024300070015FFC0B21F3915B80167401E1B2F393107400E39200750078007
03100701F0070150070107433E436E182B4E10F45D7172722B4DEDF4ED2B10FD5D71722B
3CFD3C103C103C103C10F65D3C003FF4FDE45D10ED713FED72FD5D712B11395D71722B2B
2B2FED11123911123939313043794047353B1C23051337383638020609080A0802062122
20220206350B392000111D131C0012130F1F0D1C0122233B05391C00380834200135340B
121C101C010E22101C013A063C1C002B2B2B3C103C2B012B103C2B103C2B2B2A2A2A8181
81017201710071017172005D4358B23F12015D59015D2B0072435C58B431400E392EB8FF
E0B210392EB8FFE0B60E3937200E3920B8FFE8B20C3920B8FFE0400B0B3918200B391720
0B391CB8FFF0401A0B390A280B3937280B390A280A3937280A390A28093937280939002B
2B2B2B2B2B2B2B2B2B2B2B2B2B2B59005D25060706232226353437363637353426232207
061517140623222635343633321716171615111416163332373637150623222627110607
0606151416333202478D24363D5F7B1E29CBEC57533F2526022F26252FB09F7A4E3B1C12
0A170F100C153C7066313A01972C4F4456384C846D1119826A433144785624896622222C
3A2E32342D5690291F422B85FEC9833B14070D3C38964493015D3C192C6039485F000001
0046FFE4034A03AF00210182B40804011223B8FFC040732A2D340023430D5D36170D5705
021C135404530553065407581B581C076705760580008021B41BC520D020E000E5050937
0147015618551C5F236018601C7618721C8A128E1390119018A601A402AF23B301C101C7
07C719E908E41CEA20F4011806024A1257128B1F8B208023F02307112001BCFFE00020FF
E0001FFFE0B2001D00B80346403010210160218021020021102120215021602170219021
A021B021C021D021E021F0210D21661DDF0F010FC7162509071DB8FFD8B214391DB8FFD8
403812391D31030B21CC1F0C014F0C8F0C020C2F100030004000600070009000B000C000
E00009300040000200AA731A831A02501A019F1A011AB8010C4012F00601000610062006
300640060506432243B9029100182B4E10F472714DED5D7271FD715DE47172ED003FED2B
2B3FEDED7110F45D7172E412393130383838013801715D005D017200722B2B435C58B400
1018391BB8FFF0B613390510103901B8FFC0B2103920B8FFC0B11039002B2B2B2B012B59
017101060623220235340033321615140623222726262726232207061514163332373637
034A25D8839CE80101B487AE312C3B1E110B23233E643D51A189624E3734015CB5C30106
DFD8010E8F4D262F2615761F1E4A62A1A4FB432E79000002004CFFE4035303B00014001D
02434019125F185D1960006014D6030519201C3917201C3916401C391FB8FFC0400A434A
34081F430D5D361FB8FFC0B32828341FB8FFC0405B2A2E341B06190958135E165F175A18
5B1A07010309060709080C0515490689028C06870C8A10851DA302AB18B513D402D90FDA
10F402F3031312000700081007100860076008700780078909C107C80FF0070C0401070D
84020309BAFFE00006FFE040433609460247094F1F5402540962027202891389199913A4
09A40AB808B509B00AC702E702E00CF40A1408D007010007D0070220079007A007B00704
077D041400301615B8FFC040131239125F157F159F15DF150415151BD3040104B8FFD0B2
143904B8FFE8B2133904B8FFD84048123904310B0B5C1B011B25110707CC0816281B390F
16016F167F168F160316F41414800801300890080230088F08DF08031008400860087008
B008E0080608AA0E15040089000200B8032C4012300E400E500E03000E100E200E03F00E
010EB8FFC04009434A340E431E434B182B4E10F42B7172724DFD713C10FD5D715D713C10
ED5D712B10ED003FED723FED2B2B2B7211392F5D4358B26F15015D592B3CFD3C10F45D71
72393130015D00383800715D014358B40600010202715971722B2B2B2B2B2B2B0072435C
58B90007FFC0400B23390C402D390D402D3908B8FFC0B2283907B8FFC0B2283906B8FFC0
B21B3907B8FFC0B21B3908B8FFC0B21B3907B8FFC0B20A3908B8FFC0B20A3907B8FFC0B2
093908B8FFC0400E093915101939192011390D201139002B2B012B002B2B2B2B2B2B2B2B
2B2B2B2B59130617163332363717060623220235341233321615252126272626232206DA
016464875A852D1F15CA98A5EBF1B69AC6FD8701A805101963365383023BCC7474637814
89E10101D9EB0107CBAA3A58243840810002003C00000207058E000B002200DB40199024
01602470249024A024F024052024502402402450240224B8FFC0B332323424B8FFC0B338
3A3424B8FFC0B32D303424B8FFC0B323253424B8FFC0402E191A3418291E134A220D291E
124A2321271941201E21440C190C1390060106390000220C0713120A900901093903B8FF
C0B2433503B8FFC0400F3F35036B0C0C0D190D2418402B3918B8FFC0401A363A34901801
501801601870189018A018F0180518B223B2A3182B10F65D71722B2BED3C103C10F42B2B
ED72003F3C3F3C3FED7211123910F5EDFC01F52B2B3130012B2B2B2B2B015D715D017201
321615140623222635343613111416163315213532363635113427262623220727250129
2A3B3B2A2A3C3B7E193141FE43432E1B09071E1A1C280E0114058E3B2A2A3C3C2A2A3BFE
21FD2056391C24241A3C550161952C20190F24700001003D0000020F058E0015009BB790
17C017F0170317B8FFC0B33F463417B8FFC0403A393B340117B20D643650170140175017
601770179017A017F017070C291E074A2201291E06272314270D41131E14441500000706
0A0001240D0CB8FFC0B33F46340CB8FFC0401A353B34900C01500C01600C700C900CA00C
F00C050CB216B2A3182B10F65D71722B2B3CFD3C003F3C3F3CF5EDFC01F52B2B3130015D
0171012B012B2B017201111416163315213532363635113426262322072725017B193447
FE3F3F2E1A0E1F181A28110111058EFB4156381D24241A3C5503409B471A102370000001
00110000063003AF00570144401C3407D059EF16038059011159600D5D362B0D01905901
200820291E18B802FCB42237291E31B802FC400B224C291E474A2211291E17B8030EB423
2B291E30B8030DB42341291E46B8030D403E2355274D41541E55440829374D4039292808
000725475657073C2C05252C0C0A0B07060405074746313018170A5917171A1011242130
20017020B0200220B80135400F37292E2B24383037017037B0370237B8013540164D5741
244C4C1F4D504D02804D904D02004D104D024DB8FFC0B61416344D605859B8025AB321A6
7F18B80164852B2B4EF42B5D71723C4D10FD3C10F471723CFDE410F471723CFD3C4E4565
44E6003F3C3C3C3C3C3F3C3C3F3C3C4DED10ED3F3C111217390111123900F5EDFC01F52B
2B2B2B2B2B31304379401422240C0F0D252326240C211C010E0F220E251C012B01103C2B
2B2B81810172005D2B01715D013637363633321617363633321617161511141716163315
213533323736373635113427262322060707171114161633152135323637363511342726
23220706071114161633152135323636351134272626232207272533015064122D683356
7C15678E4B497121160D0A363DFE3C133B21170A041B2756356B4C0202153A46FE314C39
0B05212C4F3635532D19314BFE3B3F321A09071E1A1C270F01142B02EC640F262A645F78
4B4B553A7CFE765620161F24241710231150018A702E4035480B2BFE4B5E2E1F24242424
1152018A7031401D2C37FE155A361B24241B3B55015E972C21190F2470000001000C0000
03F703AF00330108403A35402A350835600D5D3630355035603570359035052D04014035
6035703580359035B03506B035D03502B0350160358035C035031D0816291E0FB8030F40
112229291E244A2208291E0E4A231D291E23B8030E402E2331272A41301E31441C002332
33071A2C02072423230F0F0E0A17162407900801B008010F0870089F08CF080408B802BD
401B29331D242A1F295029602970290480299029B02903002910290229B8FFC040091416
34296034A67F182B10F62B5D71723CFD3C10FD5D71723CFD3C003F3C103C103C3FED3F3C
113939F5EDFC01F52B2B2B2B3130437940121819030604251903171C01050618051A1C01
2B01103C2B2B8181015D71015D71005D01722B2B01363332161716151114171616331521
353332363736351134262322071114171616331521353332363511342626232207272533
014BA1924B6C20160E0B3142FE3B1340330A04414D77760B0E314BFE3B1446310F1F1A1C
270F01142B02EDC24B563C7CFE79571F191C242427260F4F01777D7182FE1D5D161D1B24
2447640154A5481A0F24700000020045FFE403B903AF000F001D0158404512801501A716
B616C501C909C41DD9090612E70A0148094516571585018C09890FD91B071F403235041F
430D5D369F1F01C615C91A02401F014908102500071725080B1204B8FFC0402B120B3F4F
0401400401D0040140045004600470049004B0040604EC0C40120B3F400C9F0C020C431E
434B182B4E10F4722B4DED5D5D71722B4BB028534BB050515AB10C1E49B11F0449525A58
BD000CFFC00004FFC0001FFFC0383838594358BC001A0332000400140332E910E91BBC00
1A0332000400140332ED10ED59003FED3FED313043794036011D12250E2602251C260A25
0626110F1420001D011A2001160914200018071A2001130D1020011B03102001150B1720
0019051720002B2B2B2B012B2B2B2B2B2B2B2B2B2B8101720171722B2B71015D01435840
11750275067A0A7A0E7A1278167518751C085D595D005D435C5840091C1011391B101139
15B8FFF0B10B39002B2B2B59013217161514060623222726353436361722060615141233
3236353427260200D07E6B76CF7FCF7A677DCC53356B429F82617E694703AF9E87AF7BFC
80A58BAD7EF977413F9E7CC8FEDEA0C3F48C60000001000D000002B703AF002800A4406F
2002200F3202320F4002400F820407402A015F2A011F291E18272211291E178623272720
41261E2744000A0B2A111420100104188009010939100C010C590303000718170A5F0601
400601062E1F2A012A0011241F1F1F2001802090200200201020B020C020D02005206029
A66E182B4E10F45D71723C4D10FD3C1072E47172003F3C3F3C10ED72ED5D111739011112
39390010F5EDFC01F52B2B3130017271005D011536333216151406232226232207060711
1417161633152135323736373635113426262322072725014C7379374834242357151215
2D30130D423EFE2B4622190A050D231A1F270A011503AFCECE432C27364514295EFE494C
271B24242416102311500163A03D1C0F247000010064FFE402D503AF003103094029092C
192C0212122E400B392C280B391814590C5A269B109424050A070A280A29603370338033
060F33B8012040870D5D36CB0DCB0EC424C425D723D624D92CE604E623E624E92C0B123F
2C5D2C6E2D7D2C8F018F028F038015801A801B801C892D8F2E0D0F010F020A03090C061C
0A2CC822C923081C031614121C161D19291B2C9909990A9B20932393240B122B0D282C4A
2C4F335F337829782C860EA823AF33E803E61C0C2908311E00BD021E011F012F010201B8
012BB200BD2EB8011A40112A181E19BD1B1E1A1F1A01101A201A021AB8012BB219BD16B8
011A4022121224230D0B040F2724230D0B04051E01C7002E2F2A31000005252A071A7E19
1918B803464014162F1E25120B02CC12010112CF21DF21EF210321B80314400B700F0160
0F700F900F030FB801364019271A192E1F0801082C9F2701602770278027032027302702
27BA012000320120B14B182B4E10F45D71724DED72F43C10FD5D71FD5D4358B2FF21015D
59392F435C58B2FF01015D59ED003FEDE4F43C10ED3FED3C103C10E410ED111217390111
1217394358400A242323240D0B140D0D0B870E2E2B0E7D10C459180010ECF4ED5D720110
EDF4ED0010ECF4ED5D0110EDF4ED31304379401C28291F20101106071F11211C01062908
1C0020101E1C000728051C01002B2B012B2B81818181015D43584009FB07F610F611FB28
045D5901720071005D435840199F019F029F039F0B9A0D9015901A901B901C992297239F
2D0C5D595D2B017100722B2B4358400B2F232F248B2C9B249B2C055D59435C5840112820
19390928193901400A3902400A391BB8FFC0B20A391AB8FFC040190A392E400A392C400A
392C4009390C101E123F0E201E123F0EB8FFF0B21B390EB8FFF0B2193924B8FFE8B21339
23B8FFE8B213390CB8FFE8B613392C1813391BB8FFC0B212391AB8FFC0400F1239014012
39024012392C20123924B8FFF0400F0F392C180F3903100D392E400D3923B8FFF040120D
390C100D392C200D392C1811392C181139002B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B
2B2B2B2B2B2B2B2B2B2B2B012B2B59005D01112326262322061514171617171615140623
222726232207231133161633323635342624272635343633321716333236370290212677
5C4656201F5F92CBBD75546C2115170D21211C9E62455761FEDE2D2D9B7B364D33111012
0C03AFFEC8936A4A2D3828292E4763A27D991E0A1A01478C8E5139455E903A3957719817
0F0E180000010014FFF1023C04C1001B00D6B9000DFFE840410C395F015F02023F1D9911
9919BF15BF16B819E819079F1D01891A014F0C4F0D5F0C5F0DF518050618151827180316
1518191A030118191A03141BA00103153004B8011B401C010330020201060C35082C0F0B
16CF15DF15EF15031565141BCC000BB801EC402C200C010C2E2F1DB01D021D0001010404
05241450130180130100131013B013C013D013E0130613601CAB89182B10F65D71723CFD
3C103C103C105DF45DED10ED10F45D3C003FFDE43F3C10ED10EDFD3C10E4011117390011
17391239313000715D0171725D00722B0111331523111416333236373306062322262635
1123353636373637014AD6D63328213E11272380442E582A9137732D172904C1FED346FD
AE593E29286263335F63026821166948266500010002FFE403FD0394002500DC40350127
600D5D36202760277027B02704340B371F3A20481F4820051A08134F1E182723214F1E25
272308270141071E0844200B251D08B8014540130A002525191918061D2C0E0E0A0B0A0B
0B2120B80167400E00B001010F0170019F01CF010401B802BD402512191A241212501390
130280139013B01303001310132013B013C013D01306136026C27F182B4E10F45D71723C
4D10FD3C10FD5D713CFD3C3C103C003F3C10ED3F3C103C103C10ED11123939F5EDFC01F5
2B2B3130437940101B1C0F111C0F1A1C0010111B101D1C002B01103C2B8181005D015D2B
011114161633323717052335060623222626351134262607352111141633323637113426
273503630F21161F270EFEEE2D767C454D712C1C37480141593F2B6D4B395A0394FDD59F
471C112371C28042598C80019941321B0125FD9B8050364C02074E370225000100290000
036C03940015015F403812080418049F049F0D9F0EA904B804079F17010D17750D313650
00580F5015031B04170E130F5E04520FDF04D00F0700F6101041151E000BB80124403205
05B40A1E0B040E0F0F24030414030304030B02040F010C0E170D030F020E045005010530
0D0C06105F0F010F3001020A0FBB023E000E0004023E402C03012E9F0001002E500D0130
0D400D700D030D1A2F173F174F1703170C2E0B3500020102191617A121CD89182B2B4EF4
714DF4E44E105DF65D724DF45DE410E410E4003F3CFD723C3F3CFD723C39111239011112
3911123939111239872E2B877DC4180110EDEC0010F50110EDEC0010F53130017200722B
5D015D435C58B9000EFFD040091E123F03301E123F04B8FFC0400D1E123F0F401E123F04
2416390FB8FFDCB61639042814390FB8FFD8B61439047012390FB8FF90B6123904181539
0FB8FFE8B6153904180F390FB8FFE8B10F39012B2B2B2B2B2B2B2B2B2B2B2B002B2B5901
032135012122060706072337211501213236373637035C0BFCD80260FED4613C131B0428
060300FD9A014E694B17100B0119FEE724032A1923324AFE25FCD4232C20670000010025
0280045B02D20003002BB900020327401601300040000200001000200003001A05015C04
585E182B10E64E10E65D5D002F4DED313001213521045BFBCA043602805200010074FE46
04490394002900B640138108011C001404020C0F1E1D1D151514062524B8FFC040120B0D
3424242222270B1A2C020B001C252F24B8FFC0400D1F21347F24A024B024C0240424B8FF
C04037090D3424241F0405161309090F0F131E1F1D1C24201F010F1F701F9F1FCF1F041F
1F1516141624501301A013C0130200131013901303132F5D5D71ED3C103C332F5D71FD3C
103C11332F332F1112393911332F2B5D2BED103C003FED3F332F332F2B3C3F3C103C103C
3F11391139393130015D2506232227151417161514062322263534373635113311141616
3332371133111416333237330623222602D8B586564A1C203A2B2C341A1EA51F4F32598E
A6242B44142413AD4B63A6C242355C5D67212E3C4631245E6862038BFDAF5A54357A02BA
FD49584385E36500000100000002D1EC7E042C9B5F0F3CF50819080000000000A2E31DC2
00000000B53DB300FB74FD8C103A080E000000090001000000000000000100000721FE45
00571000FB74FE52103A0001000000000000000000000000000000210639011C00000000
020000000200000002AA005402AA002E023900030400004A040000F00400002C04000053
0400002004000062040000580400007C0556004A05C7004804730022038D0049038D0046
038D004C0239003C0239003D063900110400000C0400004502AA000D031D006402390014
04000002038D002904830025044A0074000000000000000000000042008300BC015501C7
029E038D041604D0058606900749082A09120A950B8B0CDE0D820DF50F130FE210BE114D
131A13B114591532155515EC00010000002100F2003C006F000500020010002F00410000
05CD0F1800030002B9FFC003E1B345453240B803E1B32B2E3240B803E1B2282932B9FFC0
03E1B21A1C32BD03E102AC0027001FFFC003DFB2161B32B9FFC003DEB2424232B9FFC003
DEB2363832B9FFC003DEB32A2D32DF410A03DE00EF03DE000203DE03DF0028001FFFC003
DFB3282E32F0410D03DF0001037E000F0101001F00A003DD00B003DD0002004003DAB324
26329FBF03CC000103CA03C90064001FFFC003C9B20D1132410A03C703B70012001F03B6
03B50064001FFFC003B5B30E1132004173038D000100C0038D00D0038D00E0038D00F003
8D0004006F03A7007F03A7008F03A700AF03A70004000F03A7001F03A7002F03A7004F03
A7000403AB03AB00EF03A50001000F03A5002F03A5006F03A5008F03A50004005403AA00
01006B03AA000103A8036A0022001F038C03940015001F038B03930015001F03A4039300
1A001F03A20394001E001F03A10393001E001F039F0394001E001F039B0394001A001F03
9A0393001E001F039903940016001F039803940016001F03970393001B001F0396039400
1B001F03950393001B001F03760375001A001F03740375001A001F03A00373B21E1F1041
1E0393002003930030039300030020039400300394004003940003000003940001038303
6C001E001F03B1036C0032001F036D036C0032001FFFC0037DB2212332B9FFC0037DB317
1932A0410A037D00B0037D00C0037D00D0037D0004FFC0037CB2212332B9FFC0037CB317
1932A0412D037C00B0037C00C0037C00D0037C0004003003730040037300020000037300
10037300200373000300E0037300F00373000200B0037300C0037300D003730003008403
730090037300A0037300030377036A0029001F0389036AB2281F40B80367B33940323FBB
0366000100400366B3191D328FBB0366000100400366B3090A3240B80366B3090E3240B8
0366B3090F323FBB0365000100400365B3090C3240B80365B31A1D3240B80365B3090E32
6B410E0363007B036300020014036300240363003403630044036300040363B2242F1FBA
034E006D0800400E1F7F027F037F047F050430440112BF033200500800001F0012032D00
3C080040291F5F3C01376009700980090310092009300940095009056F037F038F03031F
032F033F034F035F0305B8FFC0B2073A33B8FFC04047063A33900BA00BB00BC00BD00B05
B006C006D006E006F0060520063006400650066006700680069006A00609900690070260
0B700B800B03100B200B300B400B500B051F0701A0418503620001000003620010036200
70036200900362000400F0035F00010020035E0020035F0030035F0040035E0004000003
5E0000035F0010035F00D0035E00E0035F00050010030F0020030F0030030F00D0030F00
E0030F00050000030F0010030F0050030F0060030F0070030F00D0030F00060000030F00
10030F0020030F0030030F00E0030F00F0030F0006030F00270000030E0030030E000200
E0030E00F0030E0002030E004A00E0030D00F0030D0002030D002700D002FC0001001002
FC002002FC005002FC000300D002FC00E002FC0002000002FC001002FC002002FC003002
FC005002FC006002FC000600E002FC00F002FC0002002002FC003002FC004002FC000302
FC406127C02901B02901A02901902901403C3F413240223F41321212125F235F255F285F
A5046F236F256F286FA5044F234F254F284FA5043F233F253F283FA5042F232F252F282F
A5041F231F251F281FA5048F4CAF4CBF4CCF4C045F4C6F4C7F4C0337B8FFC0B3B22B3032
B8FFC0B3B2222532B8FFC0B5B2191A32370F413D02AF0001005F02AF009F02AF00DF02AF
0003000F02AF001F02AF002F02AF003F02AF006F02AF000502AF02AF001F02AD002F02AD
003F02AD004F02AD005F02AD000500DF02AD0001000F02AD001F02AD003F02AD005F02AD
009F02AD0005005F02AD00DF02AD0002000F02AD001F02AD003F02AD0003004002ACB23A
334F414C02AC005F02AC009F02AC0003002F02AC003F02AC0002000F02AC003F02AC00AF
02AC000300B002AC00E002AC0002004F02AC005F02AC00A002AC0003000F02AC001F02AC
002F02AC003F02AC0004000F035A0001000F035A001F035A003F035A005F035A0070035A
000500CF035700DF03570002000F0357001F03570070035700AF03570004035A035A0357
035702AD02AD02AC02AC032C400D31151F001616000000121108104110020C004A000D01
A8004A000D0198004A000D0189004A000D013F004A000D0124400E4A0DF64A0DBE4A0D86
4A0D274A0DBE02280041000D01940041000D0121400B410DB4410D4F410D29410D411002
170021000D02150021000D02060021000D01EB0021000D014E0021000D012C4014210DF9
210DF3210DF1210D9D210D71210D3D210D4110021C001F000D0214001F000D020B001F00
0D0196001F000D014A001F000D0126400B1F0DC61F0D571F0D371F0D410D019E0141000D
00420141000D001E0141000D001B0141000D01F2B40F440F0009BB01F20044000D0201B2
3C291FB80200B23C291FB801FFB23C411FB801FEB23C471FB801FDB23C9E1FB801FAB23C
931FBC01F9010F0101001F01F6B224E41F411501F401490401001F01F301490401001F01
F1014900AB001F01F001490067001F01A6003C0125001F01A4B23C811F411501A3010F01
9A001F01A200220801001F01A100500401001F019F0149019A001F019D01490067001F01
9CB22C621FB8019BB22C791FBC019A002C0101001F0197B22CE41FB80193B22C891FB801
92B22C6C1FB8018FB2259E1FB8016AB23C2A1F4111016700240201001F0163002502AB00
1F014C010F019A001F01480149006C001F0147B22C891FB80145B22C9E1FB80144B22C79
1FB80143B223311FB80127B23C811FBC012300500101001F011FB223E41F4115011D0023
019A001F011C00230801001F011B00250801001F010E010F0401001F010D00220401001F
0108B223811FB80106B425E41FF73CBB0125001F00F5010FB29E1FE3BC01490156001F00
E20149B2AB1FD1B901490401B21FCF2CB80125B61FCE23BB1FC524B80156B21FC02CB808
01B21FBF2CB80201B51FB124E41FB0B901490201B61FAF2C671FAD23B80801B21FA523B8
0201400B1F9F3C2D1F9B235A1F9925B80201B21F812CBC0401001F006D010F0156400B1F
592C3E1F4C3CAB1F4625B80101B21F403CB80125400A1F3A23721F393CAB1F38B80149B3
AB1F3124B80401B21F3025B802ABB61F2A24E41F2623B80156B21F5537BA023500070175
402C0774076207560751073B0733072D0720071D071C071408120810080E080C080A0808
08060804080208000814B8FFE0402B000001001406100000010006040000010004100000
01001002000001000200000001000002010802004A00B013034B024B534201B0124B004B
5442B0372B4BB807FF52B0382B4BB008505B58B101018E59B0382BB00288B801005458B8
01FFB101018E851BB0124358B90001012F858D1BB90001017C858D5959014BB0C063004B
6220B0F65323B8010A515AB0052342180016763F183F123E113946443E113946443E1139
46443E113946443E11394660443E11394660442B2B2B2B2B2B2B2B2B2B2B182B2B2B2B2B
2B2B2B2B2B2B2B2B181DB0964B5358B0AA1D59B0324B5358B0FF1D594BB04753205C58B9
0271026F4544B90270026F45445958B9017A0271455258B90271017A4459594BB0475320
5C58B9002202704544B9003C027045445958B901B30022455258B9002201B34459594BB0
4C53205C58B9014900224544B1222245445958B901C20149455258B9014901C24459594B
B06753205C58B9002402714544B90050027145445958B9021E0024455258B90024021E44
59594BB8020153205C58B9010F00224544B1222245445958B90C00010F455258B9010F0C
004459594BB01C53205C58B125254544B12C2545445958B13725455258B125374459594B
B0AB53205C58B125254544B1232545445958B901590025455258B9002501594459594BB8
010153205C58B125254544B1282545445958B902080025455258B9002502084459592B2B
2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B
2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B
2B2B2B65422B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B
2B2B2B2B2B2B2B2B2B2B2B2B01B361DC6463456523456023456560234560B08B766818B0
80622020B164DC4565234520B003266062636820B003266165B0DC236544B064234420B1
61634565234520B003266062636820B003266165B063236544B0612344B10063455458B1
63406544B26140614523614459B3A67F434B456523456023456560234560B089766818B0
80622020B1437F4565234520B003266062636820B003266165B07F236544B043234420B1
A64B4565234520B003266062636820B003266165B04B236544B0A62344B1004B455458B1
4B406544B2A640A645236144594B5242014B5058B108004259435C58B108004259B3020B
0A124358601B2159421610703EB0124358B93B21187E1BBA040001A8000B2B59B00C2342
B00D2342B0124358B92D412D411BBA04000400000B2B59B00E2342B00F2342B0124358B9
187E3B211BBA01A80400000B2B59B0102342B0112342002B001845694445694445694445
6944737373747373737475752B7373747475184569447373742B4BB021534BB046515A58
B03CB03C45B040604459012B2B2B2B757575757575757543584010BF3CCF3C026F3C7F3C
8F3C9F3CAF3C0575755943584012BF22CF22025F226F227F228F229F22AF220675755943
5C58B6403C9F22EF220375592B2B01747474744544737374747575454473454473744544
737475737373737300757575737575752B2B757575752B752B435841220063032D000100
03032D0013032D0023032D0033032D0053032D000500C3032D00D3032D00E3032D00F303
2D00040083032D0093032D00A3032D00B3032D0004032D032D4518694474747575592B43
58B900180332B330353238B80332B361663238B80332B3535A3238B80332B3454E3238B8
0332B33C413218B80332B23F330A410F0332000100BA033200CA033200DA033200EA0332
00FA03320005033203324518694474752B2B2B2B2B2B597300732B012B7575002B2B2B74
002B2B2B732B74012B002B2B017373737474732B2B00732B2B002B2B2B017374732B012B
2B012B2B2B2B2B2B2B2B2B2B2B2B2B2B00017375007373004569440073730173742B2B2B
2B2B732B00732B752B2B732B2B2B2B2B2B2B2B2B00>]def
/CharStrings 31 dict dup begin
/.notdef 0 def
/minus 31 def
/space 3 def
/parenleft 4 def
/parenright 5 def
/slash 6 def
/zero 7 def
/one 8 def
/two 9 def
/three 10 def
/four 11 def
/five 12 def
/six 13 def
/eight 14 def
/mu 32 def
/C 15 def
/G 16 def
/P 17 def
/a 18 def
/c 19 def
/e 20 def
/i 21 def
/l 22 def
/m 23 def
/n 24 def
/o 25 def
/r 26 def
/s 27 def
/t 28 def
/u 29 def
/z 30 def
end readonly def

systemdict/resourcestatus known
 {42 /FontType resourcestatus
   {pop pop false}{true}ifelse}
 {true}ifelse
{/TrueDict where{pop}{(%%[ Error: no TrueType rasterizer ]%%)= flush}ifelse
/FontType 3 def
 /TrueState 271 string def
 TrueDict begin sfnts save
 72 0 matrix defaultmatrix dtransform dup
 mul exch dup mul add sqrt cvi 0 72 matrix
 defaultmatrix dtransform dup mul exch dup
 mul add sqrt cvi 3 -1 roll restore
 TrueState initer end
 /BuildGlyph{exch begin
  CharStrings dup 2 index known
    {exch}{exch pop /.notdef}ifelse
  get dup xcheck
    {currentdict systemdict begin begin exec end end}
    {TrueDict begin /bander load cvlit exch TrueState render end}
    ifelse
 end}bind def
 /BuildChar{
  1 index /Encoding get exch get
  1 index /BuildGlyph get exec
 }bind def
}if

FontName currentdict end definefont pop
end
%%EndProlog
mpldict begin
36.475 174.577 translate
539.05 442.846 0 0 clipbox
gsave
0 -0 m
539.05 -0 l
539.05 442.845937 l
0 442.845937 l
cl
1.000 setgray
fill
grestore
gsave
79.2 66.60625 m
525.6 66.60625 l
525.6 426.96625 l
79.2 426.96625 l
cl
1.000 setgray
fill
grestore
2.500 setlinewidth
1 setlinejoin
0 setlinecap
[] 0 setdash
0.000 setgray
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

65.9031 39.2469 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /six glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
116.4 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
116.4 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

103.103 39.2469 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /five glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
153.6 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
153.6 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

140.303 39.2469 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /four glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
190.8 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
190.8 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

177.503 39.2469 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /three glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
228 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
228 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

214.703 39.2469 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /two glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
265.2 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
265.2 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

251.903 39.2469 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /one glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
302.4 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
302.4 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

296.15 39.2469 translate
0 rotate
0 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
339.6 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
339.6 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

333.35 39.2469 translate
0 rotate
0 0 m /one glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
376.8 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
376.8 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

370.55 39.2469 translate
0 rotate
0 0 m /two glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
414 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
414 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

407.75 39.2469 translate
0 rotate
0 0 m /three glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
451.2 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
451.2 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

444.95 39.2469 translate
0 rotate
0 0 m /four glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
488.4 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
488.4 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

482.15 39.2469 translate
0 rotate
0 0 m /five glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.6 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.6 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

519.35 39.2469 translate
0 rotate
0 0 m /six glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
86.64 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
86.64 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
94.08 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
94.08 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
101.52 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
101.52 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
108.96 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
108.96 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
123.84 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
123.84 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
131.28 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
131.28 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
138.72 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
138.72 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
146.16 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
146.16 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
161.04 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
161.04 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
168.48 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
168.48 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
175.92 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
175.92 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
183.36 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
183.36 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
198.24 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
198.24 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
205.68 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
205.68 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
213.12 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
213.12 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
220.56 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
220.56 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
235.44 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
235.44 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
242.88 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
242.88 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
250.32 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
250.32 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
257.76 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
257.76 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
272.64 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
272.64 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
280.08 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
280.08 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
287.52 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
287.52 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
294.96 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
294.96 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
309.84 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
309.84 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
317.28 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
317.28 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
324.72 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
324.72 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
332.16 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
332.16 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
347.04 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
347.04 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
354.48 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
354.48 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
361.92 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
361.92 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
369.36 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
369.36 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
384.24 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
384.24 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
391.68 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
391.68 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
399.12 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
399.12 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
406.56 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
406.56 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
421.44 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
421.44 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
428.88 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
428.88 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
436.32 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
436.32 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
443.76 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
443.76 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
458.64 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
458.64 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
466.08 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
466.08 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
473.52 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
473.52 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
480.96 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
480.96 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
495.84 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
495.84 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
503.28 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
503.28 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
510.72 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
510.72 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
518.16 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
518.16 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

227.056 12.5438 translate
0 rotate
0 0 m /P glyphshow
13.9038 0 m /r glyphshow
22.229 0 m /e glyphshow
33.3252 0 m /s glyphshow
43.0542 0 m /s glyphshow
52.7832 0 m /u glyphshow
65.2832 0 m /r glyphshow
73.6084 0 m /e glyphshow
84.7046 0 m /space glyphshow
90.9546 0 m /parenleft glyphshow
99.2798 0 m /G glyphshow
117.334 0 m /P glyphshow
131.238 0 m /a glyphshow
142.334 0 m /parenright glyphshow
grestore
2.500 setlinewidth
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.6 66.6063 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

44.2 57.9266 translate
0 rotate
0 0 m /four glyphshow
12.5 0 m /four glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 156.696 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.6 156.696 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

44.2 148.017 translate
0 rotate
0 0 m /four glyphshow
12.5 0 m /six glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 246.786 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.6 246.786 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

44.2 238.107 translate
0 rotate
0 0 m /four glyphshow
12.5 0 m /eight glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 336.876 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.6 336.876 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

44.2 328.197 translate
0 rotate
0 0 m /five glyphshow
12.5 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.6 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

44.2 418.287 translate
0 rotate
0 0 m /five glyphshow
12.5 0 m /two glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 89.1288 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.6 89.1288 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 111.651 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.6 111.651 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 134.174 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.6 134.174 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 179.219 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.6 179.219 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 201.741 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.6 201.741 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 224.264 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.6 224.264 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 269.309 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.6 269.309 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 291.831 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.6 291.831 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 314.354 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.6 314.354 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 359.399 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.6 359.399 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 381.921 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.6 381.921 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 404.444 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.6 404.444 o
grestore
gsave
34.2 135.786 translate
90 rotate
/TimesNewRomanPSMT 25.0 selectfont
0 0.148438 moveto
/P glyphshow
13.9038 0.148438 moveto
/o glyphshow
26.4038 0.148438 moveto
/l glyphshow
33.3496 0.148438 moveto
/a glyphshow
44.4458 0.148438 moveto
/r glyphshow
52.771 0.148438 moveto
/i glyphshow
59.7168 0.148438 moveto
/z glyphshow
70.813 0.148438 moveto
/a glyphshow
81.9092 0.148438 moveto
/t glyphshow
88.855 0.148438 moveto
/i glyphshow
95.8008 0.148438 moveto
/o glyphshow
108.301 0.148438 moveto
/n glyphshow
120.801 0.148438 moveto
/space glyphshow
127.051 0.148438 moveto
/parenleft glyphshow
135.376 0.148438 moveto
/mu glyphshow
148.779 0.148438 moveto
/C glyphshow
165.454 0.148438 moveto
/slash glyphshow
172.4 0.148438 moveto
/c glyphshow
183.496 0.148438 moveto
/m glyphshow
/TimesNewRomanPSMT 17.5 selectfont
203.318 15.1766 moveto
/two glyphshow
/TimesNewRomanPSMT 25.0 selectfont
213.141 0.148438 moveto
/parenright glyphshow
grestore
[9.6 2.4 1.5 2.4] 0 setdash
0.031 0.659 0.255 setrgbcolor
gsave
446.4 360.36 79.2 66.606 clipbox
116.4 394.835484 m
153.6 364.367903 l
190.8 334.473916 l
228 305.71714 l
265.2 277.453701 l
302.4 245.032727 l
339.6 213.591169 l
376.8 184.522885 l
414 152.568948 l
451.2 122.098646 l
488.4 90.096644 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
446.4 360.36 79.2 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
116.4 394.835 o
153.6 364.368 o
190.8 334.474 o
228 305.717 o
265.2 277.454 o
302.4 245.033 o
339.6 213.591 o
376.8 184.523 o
414 152.569 o
451.2 122.099 o
488.4 90.0966 o
grestore
2.500 setlinewidth
0 setlinejoin
2 setlinecap
0.000 setgray
gsave
79.2 66.60625 m
79.2 426.96625 l
stroke
grestore
gsave
525.6 66.60625 m
525.6 426.96625 l
stroke
grestore
gsave
79.2 66.60625 m
525.6 66.60625 l
stroke
grestore
gsave
79.2 426.96625 m
525.6 426.96625 l
stroke
grestore

end
showpage
