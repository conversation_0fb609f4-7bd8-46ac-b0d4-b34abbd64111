%!PS-Adobe-3.0 EPSF-3.0
%%Title: temp_vs_Ec_inset.eps
%%Creator: Mat<PERSON>lotlib v3.7.1, https://matplotlib.org/
%%CreationDate: Thu Mar 28 12:53:48 2024
%%Orientation: portrait
%%BoundingBox: 39 178 573 614
%%HiResBoundingBox: 39.568750 178.323125 572.431250 613.676875
%%EndComments
%%BeginProlog
/mpldict 11 dict def
mpldict begin
/_d { bind def } bind def
/m { moveto } _d
/l { lineto } _d
/r { rlineto } _d
/c { curveto } _d
/cl { closepath } _d
/ce { closepath eofill } _d
/box {
      m
      1 index 0 r
      0 exch r
      neg 0 r
      cl
    } _d
/clipbox {
      box
      clip
      newpath
    } _d
/sc { setcachedevice } _d
%!PS-TrueTypeFont-1.0-2.0
%%Title: unknown
%%Creator: Converted from TrueType to type 42 by PPR
15 dict begin
/FontName /LiberationSans def
/PaintType 0 def
/FontMatrix[1 0 0 1 0 0]def
/FontBBox[-544 -303 1302 980]def
/FontType 42 def
/Encoding StandardEncoding def
/FontInfo 10 dict dup begin
/FamilyName (unknown) def
/FullName (unknown) def
/Weight (unknown) def
/Version (unknown) def
/ItalicAngle 0.0 def
/isFixedPitch false def
/UnderlinePosition -217 def
/UnderlineThickness 150 def
end readonly def
/sfnts[<000100000009008000030010637674204ADA4BFA0000009C000002886670676D
7E61B61100000324000007B4676C7966C666DFDC00000AD800001A1668656164FEFF42D6
000024F000000036686865610D9403A30000252800000024686D747898600E220000254C
0000009A6C6F63616148676B000025E8000000506D617870039603E40000263800000020
70726570FDAE4749000026580000034305CC05CC007D0581001500790581001500000000
00000000000000000000043A001400770000FFEC00000000FFEC00000000FFEC0000FE57
000000000000000000000000000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000000000000000000000000000
00000000000008000000000000B400BD00AF00A00000000000000000000000000088007E
000000AC00000000000000000000000000BF00C300AB00000000009B008D000000000000
000000000000000000000000000000000000000000B900AA000000000000009400990087
000000000000000000000000000000000000000000000000006A0083008D00A400B40000
00000000000000000060006A0079009800AC00B800A700000122013300C3006B00000000
000000DB00C90000000000000000000000000000000000000000000001E101C9009200A8
006B009200B7006B009B0000027B02F200920252006E02D703810082008900A0009F0169
008F0000016000A4015B005E0082000000000000005E0065006F00000000000000000000
00000000008A009000A5007A0080000000000000000000000581FFF3000DFCB300830089
008F00960069007105CC000FFC1EFFF2003404E6000DFED400BF031F00A700AE00B50000
0000008100000000000000000748036A02B60202FD930000009100670091006101D90000
028D03410000000000000000000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000000000000000000000000000
00000000000000000000000000000000000000000000000006810468001404CB0000FFEC
FFD3FE7F008300DB00AA00BA00A000CF40475B5A59585554535251504F4E4D4C4B4A4948
47464544434241403F3E3D3C3B3A393837363531302F2E2D2C28272625242322211F1814
11100F0E0D0B0A090807060504030201002C20B0016045B003252011466123452361482D
2C20451868442D2C45234660B0206120B04660B004262348482D2C4523462361B0206020
B02661B02061B004262348482D2C45234660B0406120B06660B004262348482D2C452346
2361B0406020B02661B04061B004262348482D2C0110203C003C2D2C20452320B0CD4423
20B8015A51582320B08D44235920B0ED51582320B04D44235920B0042651582320B00D44
235921212D2C20204518684420B001602045B04676688A4560442D2C01B10B0A43234365
0A2D2C00B10A0B4323430B2D2C00B0282370B101283E01B0282370B10228453AB1020008
0D2D2C2045B00325456164B050515845441B2121592D2C49B00E23442D2C2045B0004360
442D2C01B00643B00743650A2D2C2069B04061B0008B20B12CC08A8CB8100062602B0C64
2364615C58B00361592D2C8A03458A8A87B0112BB0292344B0297AE4182D2C4565B02C23
4445B02B23442D2C4B525845441B2121592D2C4B515845441B2121592D2C01B005251023
208AF500B0016023EDEC2D2C01B005251023208AF500B0016123EDEC2D2C01B0062510F5
00EDEC2D2CB00243B001525821212121211B462346608A8A462320468A608A61B8FF8062
232010238AB10C0C8A70456020B0005058B00161B8FFBA8B1BB0468C59B0106068013A59
2D2C2045B0032546524BB013515B58B0022546206861B00325B003253F2321381B211159
2D2C2045B00325465058B0022546206861B00325B003253F2321381B2111592D2C00B007
43B006430B2D2C21210C6423648BB84000622D2C21B08051580C6423648BB82000621BB2
00402F2B59B002602D2C21B0C051580C6423648BB81555621BB200802F2B59B002602D2C
0C6423648BB84000626023212D2C4B53588AB004254964234569B0408B61B08062B02061
6AB00E23442310B00EF61B21238A121120392F592D2C4B535820B0032549646920B00526
B0062549642361B08062B020616AB00E2344B0042610B00EF68A10B00E2344B00EF6B00E
2344B00EED1B8AB00426111220392320392F2F592D2C4523456023456023456023766818
B08062202D2CB0482B2D2C2045B0005458B040442045B04061441B2121592D2C45B1302F
4523456160B0016069442D2C4B5158B02F2370B01423421B2121592D2C4B515820B00325
45695358441B2121591B2121592D2C45B01443B0006063B0016069442D2CB02F45442D2C
452320458A60442D2C45234560442D2C4B235158B90033FFE0B134201BB3330034005944
442D2CB0164358B00326458A586466B01F601B64B020606620581B21B04059B001615923
586559B02923442310B029E01B2121212121592D2CB0024354584B53234B515A58381B21
21591B21212121592D2CB0164358B004254564B020606620581B21B04059B0016123581B
6559B0292344B00525B00825082058021B0359B0042510B005252046B0042523423CB004
25B0072508B0072510B006252046B00425B0016023423C2058011B0059B0042510B00525
B029E0B02920456544B0072510B00625B029E0B00525B00825082058021B0359B00525B0
03254348B00425B0072508B00625B00325B0016043481B2159212121212121212D2C02B0
0425202046B004252342B0052508B003254548212121212D2C02B0032520B0042508B002
2543482121212D2C452320451820B00050205823652359236820B040505821B040592358
65598A60442D2C4B53234B515A5820458A60441B2121592D2C4B545820458A60441B2121
592D2C4B53234B515A58381B2121592D2CB000214B5458381B2121592D2CB002435458B0
462B1B21212121592D2CB002435458B0472B1B212121592D2CB002435458B0482B1B2121
2121592D2CB002435458B0492B1B212121592D2C208A08234B538A4B515A5823381B2121
592D2C00B0022549B000535820B04038111B21592D2C014623466023466123201020468A
61B8FF80628AB140408A704560683A2D2C208A2349648A2353583C1B21592D2C4B52587D
1B7A592D2CB012004B014B54422D2CB1020042B123018851B1400188535A58B910000020
885458B202010243604259B12401885158B920000040885458B2020202436042B1240188
5458B2022002436042004B014B5258B2020802436042591BB940000080885458B2020402
43604259B94000008063B80100885458B202080243604259B94000010063B80200885458
B202100243604259B12601885158B94000020063B80400885458B202400243604259B940
00040063B80800885458B2028002436042595959595959B10002435458400A0540084009
400C020D021BB10102435458B2054008BA010000090100B30C010D011BB18002435258B2
054008B80180B109401BB2054008BA01800009014059B9400000808855B94000020063B8
040088555A58B30C000D011BB30C000D0159595942424242422D2C451868234B51582320
452064B04050587C59688A6059442D2CB00016B00225B0022501B001233E00B002233EB1
0102060CB00A236542B00B234201B001233F00B002233FB10102060CB006236542B00723
42B00116012D2CB080B0024350B001B00243545B58212310B0201AC91B8A10ED592D2CB0
592B2D2C8A10E52D0001007FFE58029E05CC000E0022401007000B0A03040A0400030F10
0A1B0300003F3F111201173911331133113331301310123733060211101217232602117F
B5BCAEBBAFADBDAEBDB40214012101CCCBD0FE2CFEEAFEEBFE2ED3CC01CD011F0001000C
FE58022B05CC000E00224010070003040B0A00040A030F100A00031B003F3F1112011739
1133113311333130011002072336121110022733161211022BB5BCAEBCAEAFBBAEBDB402
10FEDFFE34CBD201D10117011701D2D1CCFE33FEE100000100BB0000017E00DB00030017
400A030000040500019B5B00002F2B111201391133313033353315BBC3DBDB0000010000
FFEC023905CC00030013B70205000401000013003F3F1101331133313015013301019B9E
FE691405E0FA200000020050FFEC04230596000B00170028401412060C0006001819090F
73590907031573590319003F2B00183F2B11120139391133113331300110022322021110
122132120310022322021110123332120423F9F3F3F4ED0100F9EDB78DA2A69193A09F94
02C1FE9FFE8C01720163016B016AFE92FE9901310112FEF2FECBFED4FEEA011C0001009C
0000040F0581000A002B4014040802020B0C0504040306060801000174590018003F2B11
0033183F33332F3311120139113333313033352111053525331121159C0167FEC2014DA6
015799043CE3AAE5FB189900000100670000040C0596001E003E40201C000E0F08151D15
0F00041F20081C0E12120B7359120701001C001C74590018003F2B11120039183F2B1100
3312391112011739113311331133313033353E0535342623220607273E0133321615140E
01070E01072115673393A29F804F887973950DB814F7C2D5E54B94D173881E02DF7F75B3
917C7C885674807D7111A9C8C9B952A2A2AA5E9746990001004EFFEC0419059600280063
403907061B1C15220C00002225101C0606292A25111011107459111813824D111813014D
0F110110051111031B1F1F1873591F0703097359070319003F332B00183F2B1100331239
182F5F5E5D2B2B2B11120039111201173911331133113311333130011406232226273716
2132363534262B013533323635342623220607273E0133321615140607151E010419F8E6
D6FF18BA24010F889BB1A7666294A3858377930CB514F7C2D4EB97909EB00185C3D6C1BD
11FA868473819C817271837A6F0EADC2C5B087A91E0411B20002002F000004370581000A
00120036401A1105120202080105011314000411047359081111010B06060118003F3F33
12392F332B11003311120139391133331133113331300111231121350133113315010E01
07010F01210371AAFD680285BDC6FE9002340DFE97361001F2013FFEC1013F8C03B6FC4C
8E0377065E13FDEC4A1400010052FFEC041D0581001C005E403512131817141400130706
18130C0000161306041D1E1A0F73591A1A03141417745914060309735967070116070173
07830702070319003F335D5D5D2B00183F2B11120039182F2B1112011739113311331133
111239113311123931300114002322262737163332363534262322060723132115210336
333216041DFEF7EBC5F220B639EC91A4A58C497E3FB02F0321FD831B75AED0F701CBDFFF
00ACA315D1AF9985A42E3702F699FE415AF400020068FFEC04190596001600220050402B
1D11060C0B1700000B06032324111A141A755914140309090E73590F0C8F0C020A050C09
07032073590319003F2B00183F335F5E5D2B11120039182F2B1100331112011739113311
3311333331300114022322021110003320130726232202113E0133321607342623220615
14163332360419F2D5EEFC0106F2013F53AC35B39AA931B273C3E5B796867E9BA17E8294
01CDDFFEFE01620152016E0188FEE11FACFEE1FEF05B5FF4D699A69381A3D0AF00030059
FFEC041A059600190024002F0055402E2B061F0D1A132500001316090D06063031160922
28222875590F2201130622220310101C75591007032D75590319003F2B00183F2B111200
39182F5F5E5D2B1112003939111201173911331133113311333130011406232226353436
37352E0135343633321615140607151E0103342122061514163332361334262322061510
213236041AF8E8E2FF9E7B7385F1CBD0F186748796DEFEFA7F85897D7F85239C8D899A01
2993900189C3DAD6C58ABC14041BB479A1C8C4A779B4170416B9020DEF7877797F75FDFC
83858F7DFEDD8D0000020060FFEC04120596001700240054402E1E120706180C00000612
0325260C210F2173590F0F1F0F020E030F0F0315151B7359150703097359170701070319
003F335D2B00183F2B11120039182F5F5E5D2B1100331112011739113333113311333130
011000232226273716333212130E0123220235343633321207342623220615141633323E
010412FEF7F5A5C72BAC36B89BAA0428C274BEE4F8DDEBF2C49C83829696804E864D02DD
FE95FE7A8B9B1BB00120010B5A6D0104D7DDFDFEA4AFAACFB1979AB3478200010068FFEC
057905960019005E4039031017160809091610031A1B0F17010D0317171313005F591304
00081008400850089008A008D008E008080C0308080C0C065F590C13201B015D003F2B11
0033182F5F5E5D3F2B110033182F5F5E5D11120117391133113311333130012200111000
3320131706042322240235100021320417072E010318EAFEFC010FE70128959C57FEC5D0
D5FEC9A3016C0142E1012E47B531D904FAFED3FEFAFEFDFEC501254EB6BEB10149E10151
017EB0AD3C7B8200000100A8000004FE0581000B00544032050909000A030700040C0D05
085F598F0501BA050179058905020F050108030505000101045F59010300095F59001220
0D015D003F2B00183F2B11120039182F5F5E5D5D5D712B11120117391133113331303311
21152111211521112115A8042DFC920332FCCE039705819CFE3C9AFE159C000100A80000
0491058100090036401D01050506080206030A0B01045F590101050707005F5907030512
200B015D003F3F2B11120039182F2B111201173911331133313001112115211123112115
01670312FCEEBF03E904E5FDF49EFDC505819C00000100A80000053F0581000B0034401B
000B0809070303040B090A04040C0D0A0701020404080503000412003F333F3312173911
12011739113311331133113331302101071123113311013309010452FDCDB8BFBF02A7E1
FDA802A802A88CFDE40581FD3E02C2FD9CFCE300000100A8000006020581001A015240EF
18001A100D0E1A0E1C1B030A180A0F03000714030E12901C01841C01701C01641C01441C
01341C01201C01141C01F41C01D01C01C41C01A41C01841C01741C01601C01541C01341C
01101C01041C0167E41C01C41C01B41C01941C01741C01501C01441C01241C01041C01F4
1C01D41C01B41C01841C01641C01441C01341C01141C01F41C01C41C01A41C018B1C0174
1C01541C01341C01041C0137E41C01CB1C01B41C01941C01741C01441C01241C010B1C01
F41C01D41C01BB1C01841C01641C014B1C01341C01141C01FB1C01E41C01C41C01A41C01
801C0102701C01501C01401C013F1C01201C01001C01075E5D5D5D5D5D5D5F5D5D5D5D5D
717171717171717172727272727272725E5D5D5D5D5D5D5D5D7171717171717171727272
7272727272725E5D5D5D5D5D5D5D5D5D5D5D7171717171717171003F17333F3333113311
1201393911333311333331302111343706070123012F011F0111231133011E01173E0137
0133110556093127FE9486FE8F38210304AAFB01771425060833090170F503AC9C90B365
FC4003C0AA6E6FBDFC540581FC2F3B871E28A31503D1FA7F0001002E000004B405810007
013040D90102040207030908000405045F59050301127B09014B09013B0901240901FB09
01CB0901BB09019B09018B09017F0901025F09014F09013009010F090167DF0901CF0901
B009018F09015F09014F09010F0901F00901DF0901CF0901AF09019F09017009015F0901
4009011F0901EF0901DF09019F09016F09015F09013F09011F090100090137EF0901D009
019009018009016F09015009012F0901000901D00901AF09019009017F09016F09015009
01400901200901100901FF0901E00901BF0901A009019009016009014009013F09012009
010F0901075E5D5D5D5D5D5D5D5D5D5D71717171717171717172727272727272725E5D5D
5D5D5D5D5D5D717171717171717171727272727272725E5D5D5D5D5F5D5D5D5D5D5D7171
7171003F3F2B110033111201173911333130011123112135211502D0BEFE1C048604E5FB
1B04E59C9C00000100090000054D05810008003E402607080100050302020508030A0920
0A500A02300A600A900AC00AF00A052F0A01070203050112003F333F33015D5D71111217
39113311333311333130212301330117370133030EC6FDC1C9018654540184C90581FC20
F9F903E000020057FFEC0473044E00230030008E40562029290707150E0F2E03030F151A
0432311D1851591D1620001107295159070711003F0E010F0E6F0E020B030E0E11110B50
591110002450590016C03201A03201903201803201703201603201503201303201A03201
5D7171717171717171003F2B00183F2B110033182F5F5E5D711112392F2B11120039183F
2B11120117391133113311331133113331300522263534363F0135342623220607271221
321615111416333237150623222627230E0127323E013D01070E02151416019EA3A4DDF6
F37078796E0BBC2E0184CCCE2A3B1A214447645B060645B75A639A59C57F83465F14AC96
A8B406043B8472525A110124BBB1FE2E505107701069707C67875A9D5359040230645158
600000010057FFEC03CA044E001900664045000D1413060707130D031A1B101750591F14
7F148F14DF140414141010200670068006D006E0060500061006600670068006C006D006
07090306060A0A0350590A161F1B015D003F2B110033182F5F5E5D713F332F5D2B111201
1739113311331133313001141633323637170E0123220211101233321617072E01232206
0113888960810FB615E0ACE3EFF0E0A6DB1CB90E72698F800222D8D0686C0C9CBA011F01
130111011FAC970E5A6ABE0000020056FFEC03EF05CC00160022005640311D000E0E0F17
060F06242312150E00000B0308082050590810031A505903169024017024011F2401FF24
01E02401C024015D5D5D717171003F2B00183F2B1112003939183F3F1112013939113311
331133333130250E0123220211102132161733271133111417232E013501141633323635
3426232206033532A57ACDC1018E7BA4320202B406AC0307FDDA7887998A8A978879AE68
5A0114011802365A627901C1FB13A93610742A0170E3C4D4DFD7C8C900020057FFEC0418
044E0012001900774046131106071900000C0C0711031B1A06060003190050590D190114
041919090F0F1650590F10090350590916D01B01C01B01A01B01901B01801B01701B0160
1B01501B01301B01717171717171717171003F2B00183F2B11120039182F5F5E5D2B1112
0039182F1112011739113311331133113331300114163332363717022122021110123320
1115272E012322060701149A94758D199E61FEA8F0FBFBE901DDBA0F908783990601F7BA
CA5E482DFF00011E011A010C011EFDC1188AAB9DAF99000200890000013D05CC00030007
006E40480307070004040809050F0415010053590100FF0901E00901DF0901C00901B009
019F09018009017009011F0901000901F00901DF0901C00901B00901A009019009014F09
011F09015D7171717171717172727272727272727272003F2B00183F3F11120139113333
11333130133533150311331189B4B4B40520ACACFAE0043AFBC60001008A0000040305CC
000B008D40670809010A090A04000B0B070304040D0C1F0D3F0D021F0D3F0D5F0DFF0D04
0F0D1F0D3F0D5F0D7F0D05390D40535648600D800DA00DC00DD00D05DF0D01000D600D80
0DA00D04000D100D300D400D800DA00DC00DE00DF00D09070201070A04080F0500041500
15003F3F3F3F1739015E5D7171722B5E5D71721112391133333211331239391133113331
302101071123113311013309010330FE9284B4B401DBD3FE4901CE01EE6DFE7F05CCFC61
020DFE2FFD970001008A0000013E05CC000300764051030000050401000015F00501E005
01DF0501C00501B005019F05018005017005011F05010F0501F00501DF0501C00501B005
01A005019005014F0501FF0501E00501D00501C005018005017005011F05015D5D5D5D5D
5D5D7171717171717172727272727272727272003F3F1112013911333130331133118AB4
05CCFA340001008800000623044E0029017E40FF182900002109212012080920092B2A1C
2550591C10181100151504505915100C0F21090015642B014B2B013F2B012B2B011F2B01
0F2B01EB2B01DF2B01BB2B01AB2B018B2B017B2B016F2B013B2B011F2B010B2B016AEB2B
01CB2B01BB2B01AF2B018B2B017F2B015B2B014F2B011B2B01FB2B01EF2B01DF2B01CB2B
01BB2B01AF2B01942B01642B014B2B012B2B011B2B01042B01F42B01DB2B01AB2B018B2B
017F2B016B2B01342B011B2B010F2B0139FB2B01DB2B01BB2B01A02B01942B01742B015B
2B014B2B012B2B011F2B010B2B01FB2B01EB2B01CB2B01A42B017B2B015B2B014B2B011B
2B01F42B01D02B0102C02B01A02B01902B01602B014F2B400B01302B012F2B01002B0108
5E5D5D5D5D5D5D5D5D5F5D5D717171717171717172727272727272727272725E5D5D5D5D
5D5D5D5D5D7171717171717171717171717272727272727272725E5D5D5D5D5D5D5D5D5D
5D717171717171003F32323F3F2B1112003939183F2B1112013939113333113311123911
3333313021113426232206151123113427331E0217333E0133321617333E013332161511
231134262322061511030056707386B306AA01020302033A966C7B8F1C03389F71A495B2
5670768302AE9D78B0A0FD8D0353BD2A052C394F735A626B6D60B2CBFD2F02AE9D78AFA1
FD8D00020056FFEC041D044E000A00160048402C11060B0006001718080E505908100314
50590316A01801901801801801701801601801501801301801DF18015D71717171717171
003F2B00183F2B1112013939113311333130011002232202111021321203342623220615
1416333236041DFAEEEDF201E5F8EABD859D9E8D8B95A28B021EFEE4FEEA012101110230
FEEFFEE1E0CBCFDCD6D7D00000020084FE57041D044D00170024005D403718001F110808
090009262511041502151B505915100C0F081B022250590216B026013F26019026017026
011F2601FF2601E02601C026015D5D5D7171717272003F2B00183F3F3F2B111200393911
1201393911331133331133313001102122272316151123113427331E0215333E01333212
03342623220E01151416333236041DFE72FA560504B406AE01040504309E81C8C6BD7A85
6B793F8899867B0222FDCABC08A2FE590506A73604316613645DFEF4FEDDE2C25ABF99D5
CAC50001008800000288044E00130023401006131300000C14150F060A10040F0015003F
3F3F3333111201393911331133313033113427331615333E01333217152623220615118E
06AA08042B70662425243C7076033E728AB8258B660AA50AC1B4FDCC0001001FFFF0022A
052C0014004540240D14140B0F0F09040416150E05080550590B0F09010C030940080F02
12505902168016015D003F2B00183F1ACD5F5E5D332B1100331112013911333311333211
333130250623223511233533373315331523111416333237022A595DD87D843578C8C833
3F24440818F502D283F2F283FD554E3F0E0000010085FFEC03EB043A001A005F403B1208
080B01190B191C1B12051605505916160D1509000FD01C01C01C01B01C01F01C01B01C01
FF1C01E01C01D01C01C01C01B01C01A01C01701C015D5D5D5D5D5D5D7171727272003F32
3F3F2B110033111201393911331133113331300111141E01333236351133111417232E02
27230E012322263511013A2A5C598296B406AA01020302033EA379B2A5043AFD526B7634
B29E0273FCADBD2A052C394F705DB1CC02D100010007000003F9043A000A015C40F80100
06090A030202060A030B0C09020F0601159B0C01840C01600C01540C01440C01200C0114
0C01040C01E00C01D40C01C40C01A00C01940C01840C01600C01540C01440C01140C0104
0C0167D40C01C40C01940C01840C01540C01440C01140C01040C01DB0C01C40C019B0C01
840C015B0C01440C011B0C01040C01DB0C01C40C019B0C018B0C015B0C014B0C011B0C01
0B0C0137DB0C01CB0C019B0C018B0C015B0C014B0C013F0C01200C01140C01040C01E00C
01D40C01C40C01A00C01940C01840C01600C01540C01440C01200C01140C01040C01D40C
01C40C01A00C01940C01840C01600C0102500C012F0C01100C01000C01075E5D5D5D5D5F
5D5D5D5D5D5D717171717171717171717171727272727272727272725E5D5D5D5D5D5D5D
5D717171717171717172727272727272725E5D5D5D5D5D5D5D5D5D5D5D71717171717171
71003F333F331112011739113311331133333130212301331316173F0113330265D5FE77
C0EE0D382327F6BF043AFD4028C5757602C200010017000003EA043A000B015440A0000B
08090A04010706050203030507090B050D0C840D01760D01440D540D640D03360D01240D
01160D01040D01F60D01E40D01D60D01C40D01B60D01A40D01760D860D960D03640D0106
0D160D260D460D560D0567060D460D560D860D960DC60DD60DE60D08990DD90D02640D01
560D01440D01360D01240D01160D01040D01D60DE60DF60D03C40D01060D260D360D460D
0437660DA60DB60DE60DF60D050DB8FFC040363D4248390D01220D0101000D100D02F40D
01C00DD00DE00D03B40D01800D900DA00D03740D01600D01540D01400D01340D01200D01
0DB8FFC04022121848A00D0102000D100D500D700D800D900D06070A0401040703020806
0F000215003F333F331217391133015E5D5F5D2B71717171717171717171725F72722B72
5E5D5D5D7171717171717171725E5D5D5D5D5D5D5D5D5D71717171717171111217391133
1133113333331133113331302109012309013309013309010321FEDDFEDBC20181FE91C7
010E010CC9FE91018601BCFE44022C020EFE5B01A5FDF4FDD20000000001000000020000
307C29E85F0F3CF5021F080000000000C840F99A00000000CF7BFED5FBA6FD930A6A07D7
00000008000000000000000000010000073EFE4E00430AB4FBA6FA7A0A6A000100000000
000000000000000000000026060000CD00000000023900000239000002AA007F02AA000C
023900BB02390000047300500473009C047300670473004E0473002F0473005204730068
047300590473006005C70068055600A804E300A8055600A806AA00A804E3002E05560009
0473005704000057047300560473005701C700890400008A01C7008A06AA008804730056
0473008402AA00880239001F047300850400000700170000000000000000000000000030
00610078009000D200FE014B01B801F9025702B8032A038F03F00431046204980572061D
065206E1074007A2080B085508B708FE09F90A460AAD0ADE0B210B7B0C410D0B00010000
002701520054005C000600020010002F005C000002A402040004000141210009013F0001
01390055013E000101390055014201400014001F01410140001F001F013B0033013A0055
0138003301390055004001070001001F01070001009F010440AA01C0FD01AFFD0100FD01
0A4FFB0120FB01F550281FF246281FF1462A1FF0462B1F5FEF7FEF020FEF4FEF5FEF8FEF
AFEF050BE5E41E1FE3E2461F0FE20140E246161FE1E0461FCFE0DFE0EFE00340E0333646
E046181FEEEDFF1FED01E855EC48EB55EA320055E9E8E855E7480055E600FF1FDD3DDF55
DF010355DE3D0355DC03FF1F0FD51FD5020FD51FD50240CA181B46CFC201BDC03C1FC150
261FBCBE281FFFB90150B870B880B803B8FFC040FFB81232461FB73FB74FB76FB77FB79F
B7AFB70718B60170B2A0B2B0B2030FB20190B501B0B5010FB501080FB33FB3EFB30380B0
90B002B0B0C0B0D0B0032FAF3FAF02A0ADB0AD02C0ADD0AD022FAC3FAC029FAB01C0AAD0
AA024FA98FA9022FA96FA9BFA9FFA9049C9B241F509B016F9601BF960196461D1F959417
1F0F941F947F948F94FF94053091409102809101708F808F02908F01C08FD08F024F8C5F
8C6F8C038646FF1F9F85018483311F74733F1F7350261F6F6E3C1F6E46351F1A01185519
331855073303550603FF1F6050261F5F50261F5C46311F5B5A481F5A46311F1332125505
010355043203556C03010C033C034C036C037C0305EF51FF406451024051353846405125
2846CF50014946201F4846351F4746351FAF4601DF46EF46028046011632155511010F55
10320F55020100550100011F1F0F3F0F5F0F7F0F040F0F2F0F4F0F6F0F8F0FDF0FFF0F07
3F0F7F0FEF0F036F00014F00018016010501B80190B154532B2B4BB807FF524BB007505B
B00188B02553B00188B040515AB00688B000555A5B58B101018E59858D8D00421D4BB032
5358B0601D594BB0645358B0401D594BB0805358B0101DB11600425973747374752B2B2B
2B2B017374752B2B2B00742B2B7373752B2B2B012B2B2B002B2B2B2B2B2B012B2B002B2B
012B732B00747374757374732B012B747500732B73740173737400737474737473015E73
737473730073732B7373012B002B012B00732B74752B2B2B2B2B2B2B2B2B2B2B012B2B74
2B2B5E732B002B5E7374012B2B2B002B73735E73737301737373002B2B2B2B2B2B185E00
00>]def
/CharStrings 37 dict dup begin
/.notdef 0 def
/space 3 def
/parenleft 4 def
/parenright 5 def
/period 6 def
/slash 7 def
/zero 8 def
/one 9 def
/two 10 def
/three 11 def
/four 12 def
/five 13 def
/six 14 def
/eight 15 def
/nine 16 def
/C 17 def
/E 18 def
/F 19 def
/K 20 def
/M 21 def
/T 22 def
/V 23 def
/a 24 def
/c 25 def
/d 26 def
/e 27 def
/i 28 def
/k 29 def
/l 30 def
/m 31 def
/o 32 def
/p 33 def
/r 34 def
/t 35 def
/u 36 def
/v 37 def
/x 38 def
end readonly def

systemdict/resourcestatus known
 {42 /FontType resourcestatus
   {pop pop false}{true}ifelse}
 {true}ifelse
{/TrueDict where{pop}{(%%[ Error: no TrueType rasterizer ]%%)= flush}ifelse
/FontType 3 def
 /TrueState 271 string def
 TrueDict begin sfnts save
 72 0 matrix defaultmatrix dtransform dup
 mul exch dup mul add sqrt cvi 0 72 matrix
 defaultmatrix dtransform dup mul exch dup
 mul add sqrt cvi 3 -1 roll restore
 TrueState initer end
 /BuildGlyph{exch begin
  CharStrings dup 2 index known
    {exch}{exch pop /.notdef}ifelse
  get dup xcheck
    {currentdict systemdict begin begin exec end end}
    {TrueDict begin /bander load cvlit exch TrueState render end}
    ifelse
 end}bind def
 /BuildChar{
  1 index /Encoding get exch get
  1 index /BuildGlyph get exec
 }bind def
}if

FontName currentdict end definefont pop
end
%%EndProlog
mpldict begin
39.569 178.323 translate
532.862 435.354 0 0 clipbox
gsave
0 0 m
532.8625 0 l
532.8625 435.35375 l
0 435.35375 l
cl
1.000 setgray
fill
grestore
gsave
79.2625 67.79375 m
525.6625 67.79375 l
525.6625 428.15375 l
79.2625 428.15375 l
cl
1.000 setgray
fill
grestore
2.500 setlinewidth
1 setlinejoin
0 setlinecap
[] 0 setdash
0.000 setgray
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2625 67.7938 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2625 428.154 o
grestore
/LiberationSans 25.000 selectfont
gsave

72.3094 39.6844 translate
0 rotate
0 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
207.11 67.7938 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
207.11 428.154 o
grestore
/LiberationSans 25.000 selectfont
gsave

186.25 39.6844 translate
0 rotate
0 0 m /three glyphshow
13.9038 0 m /zero glyphshow
27.8076 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
334.957 67.7938 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
334.957 428.154 o
grestore
/LiberationSans 25.000 selectfont
gsave

314.098 39.6844 translate
0 rotate
0 0 m /six glyphshow
13.9038 0 m /zero glyphshow
27.8076 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
462.804 67.7938 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
462.804 428.154 o
grestore
/LiberationSans 25.000 selectfont
gsave

441.945 39.6844 translate
0 rotate
0 0 m /nine glyphshow
13.9038 0 m /zero glyphshow
27.8076 0 m /zero glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
111.224 67.7938 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
111.224 428.154 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
143.186 67.7938 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
143.186 428.154 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
175.148 67.7938 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
175.148 428.154 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
239.072 67.7938 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
239.072 428.154 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
271.033 67.7938 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
271.033 428.154 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
302.995 67.7938 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
302.995 428.154 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
366.919 67.7938 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
366.919 428.154 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
398.881 67.7938 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
398.881 428.154 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
430.842 67.7938 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
430.842 428.154 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
494.766 67.7938 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
494.766 428.154 o
grestore
/LiberationSans 25.000 selectfont
gsave

212.127 12.3875 translate
0 rotate
0 0 m /T glyphshow
12.521 0 m /e glyphshow
26.4248 0 m /m glyphshow
47.25 0 m /p glyphshow
61.1538 0 m /e glyphshow
75.0576 0 m /r glyphshow
83.3828 0 m /a glyphshow
97.2866 0 m /t glyphshow
104.232 0 m /u glyphshow
118.136 0 m /r glyphshow
126.461 0 m /e glyphshow
140.365 0 m /space glyphshow
147.311 0 m /parenleft glyphshow
155.636 0 m /K glyphshow
172.311 0 m /parenright glyphshow
grestore
2.500 setlinewidth
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2625 71.5738 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.663 71.5738 o
grestore
/LiberationSans 25.000 selectfont
gsave

34.4969 62.5191 translate
0 rotate
0 0 m /zero glyphshow
13.9038 0 m /period glyphshow
20.8496 0 m /five glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2625 134.574 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.663 134.574 o
grestore
/LiberationSans 25.000 selectfont
gsave

34.4969 125.519 translate
0 rotate
0 0 m /one glyphshow
13.9038 0 m /period glyphshow
20.8496 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2625 197.574 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.663 197.574 o
grestore
/LiberationSans 25.000 selectfont
gsave

34.4969 188.519 translate
0 rotate
0 0 m /one glyphshow
13.9038 0 m /period glyphshow
20.8496 0 m /five glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2625 260.574 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.663 260.574 o
grestore
/LiberationSans 25.000 selectfont
gsave

34.4969 251.519 translate
0 rotate
0 0 m /two glyphshow
13.9038 0 m /period glyphshow
20.8496 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2625 323.574 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.663 323.574 o
grestore
/LiberationSans 25.000 selectfont
gsave

34.4969 314.519 translate
0 rotate
0 0 m /two glyphshow
13.9038 0 m /period glyphshow
20.8496 0 m /five glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2625 386.574 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.663 386.574 o
grestore
/LiberationSans 25.000 selectfont
gsave

34.4969 377.519 translate
0 rotate
0 0 m /three glyphshow
13.9038 0 m /period glyphshow
20.8496 0 m /zero glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2625 84.1737 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.663 84.1737 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2625 96.7737 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.663 96.7737 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2625 109.374 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.663 109.374 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2625 121.974 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.663 121.974 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2625 147.174 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.663 147.174 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2625 159.774 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.663 159.774 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2625 172.374 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.663 172.374 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2625 184.974 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.663 184.974 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2625 210.174 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.663 210.174 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2625 222.774 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.663 222.774 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2625 235.374 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.663 235.374 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2625 247.974 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.663 247.974 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2625 273.174 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.663 273.174 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2625 285.774 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.663 285.774 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2625 298.374 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.663 298.374 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2625 310.974 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.663 310.974 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2625 336.174 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.663 336.174 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2625 348.774 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.663 348.774 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2625 361.374 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.663 361.374 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2625 373.974 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.663 373.974 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2625 399.174 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.663 399.174 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2625 411.774 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.663 411.774 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2625 424.374 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.663 424.374 o
grestore
/LiberationSans 25.000 selectfont
gsave

25.3094 117.364 translate
90 rotate
0 0 m /C glyphshow
18.0542 0 m /o glyphshow
31.958 0 m /e glyphshow
45.8618 0 m /r glyphshow
54.187 0 m /c glyphshow
66.687 0 m /i glyphshow
72.2412 0 m /v glyphshow
84.7412 0 m /e glyphshow
98.645 0 m /space glyphshow
105.591 0 m /F glyphshow
120.862 0 m /i glyphshow
126.416 0 m /e glyphshow
140.32 0 m /l glyphshow
145.874 0 m /d glyphshow
159.778 0 m /space glyphshow
166.724 0 m /parenleft glyphshow
175.049 0 m /M glyphshow
195.874 0 m /V glyphshow
212.549 0 m /slash glyphshow
219.495 0 m /c glyphshow
231.995 0 m /m glyphshow
252.82 0 m /parenright glyphshow
grestore
2 setlinecap
0.031 0.659 0.255 setrgbcolor
gsave
446.4 360.36 79.263 67.794 clipbox
100.570376 411.77375 m
121.878252 386.57375 l
164.494004 361.37375 l
207.109755 310.97375 l
249.725507 285.77375 l
292.341259 235.37375 l
334.957011 210.17375 l
377.572763 184.97375 l
420.188514 159.77375 l
505.420018 84.17375 l
stroke
grestore
1.000 setlinewidth
0 setlinecap
gsave
446.4 360.36 79.263 67.794 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
100.57 411.774 o
121.878 386.574 o
164.494 361.374 o
207.11 310.974 o
249.726 285.774 o
292.341 235.374 o
334.957 210.174 o
377.573 184.974 o
420.189 159.774 o
505.42 84.1737 o
grestore
2.500 setlinewidth
0 setlinejoin
2 setlinecap
0.000 setgray
gsave
79.2625 67.79375 m
79.2625 428.15375 l
stroke
grestore
gsave
525.6625 67.79375 m
525.6625 428.15375 l
stroke
grestore
gsave
79.2625 67.79375 m
525.6625 67.79375 l
stroke
grestore
gsave
79.2625 428.15375 m
525.6625 428.15375 l
stroke
grestore
1.500 setlinewidth
1 setlinejoin
0.031 0.659 0.255 setrgbcolor
gsave
97.2625 96.95 m
117.2625 96.95 l
137.2625 96.95 l
stroke
grestore
1.000 setlinewidth
0 setlinecap
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
117.263 96.95 o
grestore
0.000 setgray
gsave
153.262 89.95 translate
0 rotate
/LiberationSans 20.0 selectfont
0 0.234375 moveto
/E glyphshow
/LiberationSans 14.0 selectfont
13.5247 -2.93437 moveto
/c glyphshow
grestore
gsave
327.1025 264.00975 m
505.6625 264.00975 l
505.6625 408.15375 l
327.1025 408.15375 l
cl
1.000 setgray
fill
grestore
0.761 0.000 0.471 setrgbcolor
gsave
178.56 144.144 327.102 264.01 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

-5 8.660254 m
-10 0 l
-5 -8.660254 l
5 -8.660254 l
10 -0 l
5 8.660254 l
cl

gsave
0.761 0.000 0.471 setrgbcolor
fill
grestore
stroke
grestore
} bind def
335.219 280.026 o
397.652 283.229 o
447.599 296.042 o
497.546 284.03 o
grestore
0.996 0.259 0.059 setrgbcolor
gsave
178.56 144.144 327.102 264.01 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 10 m
-2.24514 3.09017 l
-9.510565 3.09017 l
-3.632713 -1.18034 l
-5.877853 -8.09017 l
-0 -3.81966 l
5.877853 -8.09017 l
3.632713 -1.18034 l
9.510565 3.09017 l
2.24514 3.09017 l
cl

gsave
0.996 0.259 0.059 setrgbcolor
fill
grestore
stroke
grestore
} bind def
398.651 384.13 o
grestore
2.500 setlinewidth
0.000 setgray
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
348.705 264.01 o
grestore
/LiberationSans 20.000 selectfont
gsave

332.017 239.51 translate
0 rotate
0 0 m /two glyphshow
11.123 0 m /zero glyphshow
22.2461 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
448.598 264.01 o
grestore
/LiberationSans 20.000 selectfont
gsave

431.911 239.51 translate
0 rotate
0 0 m /four glyphshow
11.123 0 m /zero glyphshow
22.2461 0 m /zero glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
373.678 264.01 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
398.651 264.01 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
423.625 264.01 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
473.572 264.01 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
498.545 264.01 o
grestore
/LiberationSans 20.000 selectfont
gsave

344.148 216.853 translate
0 rotate
0 0 m /T glyphshow
9.9668 0 m /e glyphshow
21.0898 0 m /m glyphshow
37.75 0 m /p glyphshow
48.873 0 m /e glyphshow
59.9961 0 m /r glyphshow
66.6562 0 m /a glyphshow
77.7793 0 m /t glyphshow
83.3359 0 m /u glyphshow
94.459 0 m /r glyphshow
101.119 0 m /e glyphshow
112.242 0 m /space glyphshow
117.799 0 m /parenleft glyphshow
124.459 0 m /K glyphshow
137.799 0 m /parenright glyphshow
grestore
2.500 setlinewidth
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
327.102 264.01 o
grestore
/LiberationSans 20.000 selectfont
gsave

294.852 256.76 translate
0 rotate
0 0 m /three glyphshow
11.123 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
327.102 288.034 o
grestore
/LiberationSans 20.000 selectfont
gsave

294.852 280.784 translate
0 rotate
0 0 m /six glyphshow
11.123 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
327.102 312.058 o
grestore
/LiberationSans 20.000 selectfont
gsave

294.852 304.808 translate
0 rotate
0 0 m /nine glyphshow
11.123 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
327.102 336.082 o
grestore
/LiberationSans 20.000 selectfont
gsave

283.727 328.832 translate
0 rotate
0 0 m /one glyphshow
11.123 0 m /two glyphshow
22.2461 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
327.102 360.106 o
grestore
/LiberationSans 20.000 selectfont
gsave

283.727 352.856 translate
0 rotate
0 0 m /one glyphshow
11.123 0 m /five glyphshow
22.2461 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
327.102 384.13 o
grestore
/LiberationSans 20.000 selectfont
gsave

283.727 376.88 translate
0 rotate
0 0 m /one glyphshow
11.123 0 m /eight glyphshow
22.2461 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
327.102 408.154 o
grestore
/LiberationSans 20.000 selectfont
gsave

283.727 400.904 translate
0 rotate
0 0 m /two glyphshow
11.123 0 m /one glyphshow
22.2461 0 m /zero glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
327.102 270.016 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
327.102 276.022 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
327.102 282.028 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
327.102 294.04 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
327.102 300.046 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
327.102 306.052 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
327.102 318.064 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
327.102 324.07 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
327.102 330.076 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
327.102 342.088 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
327.102 348.094 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
327.102 354.1 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
327.102 366.112 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
327.102 372.118 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
327.102 378.124 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
327.102 390.136 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
327.102 396.142 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
327.102 402.148 o
grestore
gsave
274.727 288.082 translate
90 rotate
/LiberationSans 20.0 selectfont
0 0.5 moveto
/E glyphshow
/LiberationSans 14.0 selectfont
13.5247 -2.66875 moveto
/c glyphshow
/LiberationSans 20.0 selectfont
21.0528 0.5 moveto
/space glyphshow
26.6095 0.5 moveto
/parenleft glyphshow
33.2696 0.5 moveto
/k glyphshow
43.2696 0.5 moveto
/V glyphshow
56.6095 0.5 moveto
/slash glyphshow
62.1661 0.5 moveto
/c glyphshow
72.1661 0.5 moveto
/m glyphshow
88.8263 0.5 moveto
/parenright glyphshow
grestore
2.500 setlinewidth
0 setlinejoin
2 setlinecap
gsave
327.1025 264.00975 m
327.1025 408.15375 l
stroke
grestore
gsave
505.6625 264.00975 m
505.6625 408.15375 l
stroke
grestore
gsave
327.1025 264.00975 m
505.6625 264.00975 l
stroke
grestore
gsave
327.1025 408.15375 m
505.6625 408.15375 l
stroke
grestore
1.000 setlinewidth
1 setlinejoin
0 setlinecap
0.761 0.000 0.471 setrgbcolor
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

-5 8.660254 m
-10 0 l
-5 -8.660254 l
5 -8.660254 l
10 -0 l
5 8.660254 l
cl

gsave
0.761 0.000 0.471 setrgbcolor
fill
grestore
stroke
grestore
} bind def
355.602 346.879 o
grestore
0.000 setgray
/LiberationSans 15.000 selectfont
gsave

382.602 342.941 translate
0 rotate
0 0 m /E glyphshow
10.0049 0 m /x glyphshow
17.5049 0 m /p glyphshow
25.8472 0 m /period glyphshow
grestore
0.996 0.259 0.059 setrgbcolor
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 10 m
-2.24514 3.09017 l
-9.510565 3.09017 l
-3.632713 -1.18034 l
-5.877853 -8.09017 l
-0 -3.81966 l
5.877853 -8.09017 l
3.632713 -1.18034 l
9.510565 3.09017 l
2.24514 3.09017 l
cl

gsave
0.996 0.259 0.059 setrgbcolor
fill
grestore
stroke
grestore
} bind def
355.602 325.394 o
grestore
0.000 setgray
/LiberationSans 15.000 selectfont
gsave

382.602 321.457 translate
0 rotate
0 0 m /E glyphshow
10.0049 0 m /x glyphshow
17.5049 0 m /p glyphshow
25.8472 0 m /period glyphshow
grestore

end
showpage
