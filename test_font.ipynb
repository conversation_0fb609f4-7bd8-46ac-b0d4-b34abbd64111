{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAxYAAAKACAYAAAAB9u5NAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjcuMSwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy/bCgiHAAAACXBIWXMAAA9hAAAPYQGoP6dpAAB3xklEQVR4nO3dZ3gV1f728TsdSCAJhBoI3SAdUUCKNKUjVQVRkaKoqCgCIkpRxEI/qBwQpRcFDkiRIr0IIiBNSqgSikCABAiBtD3PC57MP5v0THYKfD/XlcuZPWvW/HYx7DuzZpaTYRiGAAAAAMAC56wuAAAAAEDOR7AAAAAAYBnBAgAAAIBlBAsAAAAAlhEsAAAAAFhGsAAAAABgGcECAAAAgGUECwAAAACWESwAAAAAWOaa1QXAusjISK1evVrHjh1T/vz51bhxY5UvXz6rywIAAMBDhDMWOdzSpUtVunRpvfLKK9q0aZOmTZumwMBAvfjii7px40ZWlwcAAICHBMEiBxs/frw6deokPz8/HT58WGvXrtXu3bs1b948LViwQA0bNlRYWFhWlwkAAICHgJNhGEZWF4G0W7lypZ599lk5Ozvr77//VoUKFey2v/LKK5ozZ45atmypVatWZVGVAAAAeFhwxiIHioiIUJ8+fWQYhp599tkEoUKS3n//fUnS6tWrNW/evMwuEQAAAA8ZgkUO9P333+vixYuSpPbt2yfapkaNGvL395ckjRgxQpyYAgAAgCMRLHKgH374wVyuXbt2ku3q1asnSTp58qQ2b97s6LIAAADwECNY5DAnTpzQ4cOHJUkuLi4qW7Zskm0rVqxoLi9btszhtQEAAODhRbDIYbZv324uFylSRK6uSU9FUrx4cXN59+7dDq0LAAAADzeCRQ5z5MgRc7lEiRLJti1YsKC5fODAAYfVBAAAADDzdg4THBxsLvv6+ibbNleuXOby7du3FRkZKQ8PD7u+Vq5cqevXr+v69esqVqyY8uTJI0kqXLiwChcunMHVAwAAIDu7fPmyLl++LOnenUgvXryo/PnzK3/+/GrTpo0CAgKS3JdgkcPcvHnTXI4LAUlxc3OzW79x44YKFSpkrq9cuVJ9+/bN2AIBAADwwHrrrbeS3MZQqBwmKirKXE4pWERHRye7DgAAAGQUgkUO4+npaS7HxsYm2/b+IJFSEAEAAADSi6FQOUz86yru3r2bbNv4w6bc3d3l7e1tt71MmTJ26++//77KlSsnKXOusWg0ebJD+08rIyREtvXrzXXnp5+WU7wL4LODzcmcfsxpstv7L2X/z8CD9P5LfAbSg8+AY2X39196sD4D2e39l7L/ZyAz3v/411icPHlSEyZMMLfd/93xfgSLHKZ8+fLm8vXr15NtGxoaai4HBATI2dn+BJWXl5fdeseOHVW/fv0MqDJ1nJYvz7RjpYdTwYJyinfL3uwgM98fR8vu77+U/T4DD9L7L/EZSA8+A5kru73/0oP1Gcju77+U/T4Dmf3+b9++3S5Y3P/d8X4MhcphqlWrZi6fP38+2bYXLlwwl2vWrOmwmgAAAADOWOQwjRs3loeHhyIjI3X27NkEt5CN78yZM+ZykyZNMqvEnCtfPjnVrWu3jocMnwHwGXi48f6Dz4AlBIscxsvLSy1bttQvv/yi6OhoHThwQLVq1Uq07b59+yTdu+1sx44dM7PMHMkpXz651KuX1WUgC/EZAJ+BhxvvP/gMWMNQqBxo4MCB5vLq1asTbRMSEqKgoCBJ0ssvvyw/P79MqQ0AAAAPJ4JFDlS3bl3zDMSCBQtks9kStFmwYIEMw5CXl5dGjBiRyRUCAADgYUOwyKGmTp2qIkWKKCgoSDNmzLDbduHCBY0aNUqSNGXKFJUoUSIrSgQAAMBDhGsscig/Pz9t3rxZzZo1U9++fXX16lU1bdpUBw8e1NChQ3Xr1i3NnDlT3bp1y+pSAQAA8BAgWORggYGB2r9/v8aPH6+ZM2dqxIgR8vPzU/v27TVw4ECVKlUqq0sEAADAQ4JgkcP5+vpq5MiRGjlyZFaXAgAAgIcY11gAAAAAsIxgAQAAAMAyggUAAAAAywgWAAAAACwjWAAAAACwjGABAAAAwDKCBQAAAADLCBYAAAAALCNYAAAAALCMYAEAAADAMoIFAAAAAMsIFgAAAAAsI1gAAAAAsIxgAQAAAMAyggUAAAAAywgWAAAAACwjWAAAAACwjGABAAAAwDKCBQAAAADLCBYAAAAALCNYAAAAALCMYAEAAADAMoIFAAAAAMsIFgAAAAAsI1gAAAAAsIxgAQAAAMAyggUAAAAAywgWAAAAACwjWAAAAACwjGABAAAAwDKCBQAAAADLCBYAAAAALCNYAAAAALCMYAEAAADAMoIFAAAAAMsIFgAAAAAsI1gAAAAAsIxgAQAAAMAyggUAAAAAywgWAAAAACwjWAAAAACwjGABAAAAwDKCBQAAAADLCBYAAAAALCNYAAAAALCMYAEAAADAMoIFAAAAAMsIFgAAAAAsI1gAAAAAsIxgAQAAAMAyggUAAAAAywgWAAAAACwjWAAAAACwjGABAAAAwDKCBQAAAADLCBYAAAAALCNYAAAAALCMYAEAAADAMoIFAAAAAMsIFgAAAAAsI1gAAAAAsIxgAQAAAMAyggUAAAAAywgWAAAAACwjWAAAAACwjGABAAAAwDKCBQAAAADLCBYAAAAALCNYAAAAALCMYAEAAADAMtesLgDWREZGavXq1Tp27Jjy58+vxo0bq3z58lldFgAAAB4ynLHIQnXr1pWTk1OSP0OHDk12/6VLl6p06dJ65ZVXtGnTJk2bNk2BgYF68cUXdePGjUx6FgAAAADBIsts3LhRO3fuTHJ7rly59M477yS5ffz48erUqZP8/Px0+PBhrV27Vrt379a8efO0YMECNWzYUGFhYQ6oHAAAAEiIoVBZ5PPPP5e3t7e8vLwS3d61a1cVKlQo0W0rV67UgAED5OzsrIULF6pEiRJ2+61evVpz5szRiy++qFWrVjmkfgAAACA+gkUW2LlzpzZv3qz9+/eratWqado3IiJCffr0kWEYevbZZ1WhQoUEbd5//33NmTNHq1ev1rx589StW7eMKh0AAABIFEOhssDIkSPVtm3bNIcKSfr+++918eJFSVL79u0TbVOjRg35+/tLkkaMGCHDMNJdKwAAAJAaBItMtm/fPq1evVpDhgxJ1/4//PCDuVy7du0k29WrV0+SdPLkSW3evDldxwIAAABSi2CRyT7//HNJUpcuXfTCCy/ohx9+SPUdnE6cOKHDhw9LklxcXFS2bNkk21asWNFcXrZsmYWKAQAAgJQRLDLRkSNHtHTpUknSP//8o4ULF+q1115T4cKF9e677+ratWvJ7r99+3ZzuUiRInJ1TfoSmeLFi5vLu3fvtlg5AAAAkDyCRSYaNWpUotc7REZG6ptvvlGFChWSHbZ05MgRczn+naASU7BgQXP5wIEDaS8WAAAASAPuCpWJJk6cqJEjR+rSpUv6+++/tXHjRi1btkx3796VJF29elXPPPOMFi9erHbt2iXYPzg42Fz29fVN9li5cuUyl2/fvq3IyEh5eHgku8/BgwfN5YCAAAUEBKTqeQEAAODBEBwcbH7njP/dMDUIFkm4cuWKjh8/brkfd3d31apVS9K9swgFCxZUmTJlVLduXb3++uu6dOmShg0bpmnTpkmSYmJi1K1bN+3bt0/ly5e36+vmzZvmcp48eZI9rpubm936jRs3kpwXI07fvn3N5eHDh2vEiBEpPj8AAAA8OKZPn65PP/00XfsSLJKwatUq9ejRw3I/hQsX1qVLl5LcXqRIEX3//fd65pln9NJLLykqKkq3b9/W4MGD9b///c+ubVRUlLmcUrCIjo5Odh0AAADISASLJHTo0EF16tSx3E9yF1jH99xzz+n27dtmmFm+fLlCQ0Pthjx5enqay7Gxscn2d3+QSCmIAAAAAFYQLJLg7e0tb2/vTD3mq6++qp9//llr1qxRTEyM9uzZo2eeecbcHj9kxF2XkZT4w6bc3d1T9Vy+++47c9I+rq8AAAB4+PTs2VNPP/20pHvXWMQfKp8SgkU2M3DgQK1Zs0bSvYu544t/zcX169eT7Sc0NNRcDggIkLNzyjcAq1q1qurXr5+WcgEAAPAAsXIDH243m800aNBA7u7ukiQ/Pz+7bdWqVTOXz58/n2w/Fy5cMJdr1qyZgRUCAAAACREsshk3NzcVKFBATk5OqlSpkt22xo0bm7eMPXv2rCIjI5Ps58yZM+ZykyZNHFMsAAAA8P8RLLIZwzB048YNNWzYUMWKFbPb5uXlpZYtW0q6d3F2chPf7du3T9K9oNKxY0fHFQwAAACIYJHt/PHHH4qIiNBnn32W6PaBAweay6tXr060TUhIiIKCgiRJL7/8coIhVQAAAEBGI1hkI4ZhaPjw4erfv78aNGiQaJu6deuaZyAWLFggm82WoM2CBQtkGIa8vLyY5A4AAACZgmCRSZYsWaLKlSurc+fOOnXqVILtNptNb7/9tkqWLKkxY8Yk29fUqVNVpEgRBQUFacaMGXbbLly4oFGjRkmSpkyZohIlSmTckwAAAACSwO1mM8muXbt0+PBhHT58WCtXrlSfPn3Utm1bFShQQAcPHtSCBQv0wgsvpGq2bz8/P23evFnNmjVT3759dfXqVTVt2lQHDx7U0KFDdevWLc2cOVPdunXLhGcGAAAAECwyzeeffy5XV1ctWbJE//zzj6ZOnaqVK1eqUqVKeu6557R48WJ5eXmlur/AwEDt379f48eP18yZMzVixAj5+fmpffv2GjhwoEqVKuW4JwMAAADch2CRSdzc3DRq1ChzmFJG8PX11ciRIzVy5MgM6xMAAABID66xAAAAAGAZwQIAAACAZQQLAAAAAJYRLAAAAABYRrAAAAAAYBnBAgAAAIBlBAsAAAAAlhEsAAAAAFhGsAAAAABgGcECAAAAgGUECwAAAACWESwAAAAAWEawAAAAAGAZwQIAAACAZQQLAAAAAJYRLAAAAABYRrAAAAAAYBnBAgAAAIBlBAsAAAAAlhEsAAAAAFhGsAAAAABgGcECAAAAgGUECwAAAACWESwAAAAAWEawAAAAAGAZwQIAAACAZQQLAAAAAJYRLAAAAABYRrAAAAAAYBnBAgAAAIBlBAsAAAAAlhEsAAAAAFhGsAAAAABgGcECAAAAgGUECwAAAACWESwAAAAAWEawAAAAAGAZwQIAAACAZQQLAAAAAJYRLAAAAABYRrAAAAAAYBnBAgAAAIBlBAsAAAAAlhEsAAAAAFhGsAAAAABgGcECAAAAgGUECwAAAACWESwAAAAAWEawAAAAAGAZwQIAAACAZQQLAAAAAJYRLAAAAABYRrAAAAAAYBnBAgAAAIBlBAsAAAAAlhEsAAAAAFhGsAAAAABgGcECAAAAgGUECwAAAACWESwAAAAAWEawAAAAAGAZwQIAAACAZQQLAAAAAJYRLAAAAABYRrAAAAAAYBnBAgAAAIBlBAsAAAAAlhEsAAAAAFhGsAAAAABgGcECAAAAgGWuWV3AwywyMlKrV6/WsWPHlD9/fjVu3Fjly5dPcz9//fWXtm3bptjYWFWpUkVNmzaVszOZEQAAAJmHb58W7N27V88995yaNm2a5n2XLl2q0qVL65VXXtGmTZs0bdo0BQYG6sUXX9SNGzdS1ce5c+fUrFkz1axZU3PmzNG6devUoUMHBQYGaufOnWmuCQAAAEgvgkU6bNiwQc8884wef/xxLV68WLGxsWnaf/z48erUqZP8/Px0+PBhrV27Vrt379a8efO0YMECNWzYUGFhYcn2cezYMT3xxBPatGmTFi1apD179mj16tUKCgqSzWZTo0aNtHbtWgvPEgAAAEg9gkUanDlzRs8995ymTp2qgwcPpquPlStXasCAAXJ2dtbChQtVokQJc1vXrl318ssv68CBA3rxxReT7CM8PFytW7fW5cuXNWjQIHXu3Nnc5u/vr2nTpikqKkqdOnXS6dOn01UnAAAAkBYEizQICAjQwoULtXDhQv3vf/9L8/4RERHq06ePDMPQs88+qwoVKiRo8/7770uSVq9erXnz5iXaz9ChQ3X69Gm5ubmpf//+CbY3adJEVatW1e3bt9WnT5801wkAAACkFcEiDVxcXOTk5CRJqly5cpr3//7773Xx4kVJUvv27RNtU6NGDfn7+0uSRowYIcMw7LZfunRJU6ZMkSTVr19fBQoUSLSftm3bSpLWr1+v7du3p7lWAAAAIC0IFunk5eWV5n1++OEHc7l27dpJtqtXr54k6eTJk9q8ebPdtrlz5+ru3bup7kOSpk2bluZaAQAAgLQgWKSTq2va7tR74sQJHT58WNK9Mx9ly5ZNsm3FihXN5WXLltltW7p0qbkcGBiYqj6WL1+e4MwHAAAAkJEIFpkk/nCkIkWKJBtMihcvbi7v3r3bXI6MjNSePXsSbXc/f39/c9hWWFiYTp48ma66AQAAgNQgWGSSI0eOmMvx7wSVmIIFC5rLBw4cMJdPnjypqKioVPXj6uoqHx+fRPsBAAAAMhozb2eS4OBgc9nX1zfZtrly5TKXb9++rcjISHl4eNj1kdZ+rl27lmKN8W+hGxAQoICAgBT3AQAAwIMjODjY/M6Z1ukVHshgceXKFR0/ftxyP+7u7qpVq1YGVCTdvHnTXM6TJ0+ybd3c3OzWb9y4oUKFCtn1kdZ+UjObd9++fc3l4cOHa8SIESnuAwAAgAfH9OnT9emnn6Zr3wcyWKxatUo9evSw3E/hwoV16dKlDKhIdkOYUgoE0dHRia7H7yOt/dzfJwAAAJCRHshg0aFDB9WpU8dyP2m981NyPD09zeXY2Nhk294fAuICRPw+4vpxdk76Mpn4/aQUQgAAAAArHshg4e3tLW9v76wuw0786yHi5qFISvwhT+7u7uZzuf+airt37yYYNpVUP/EvCE/Kd999p6pVq0oS11cAAAA8hHr27Kmnn35a0r1rLOIPlU/JAxkssqPy5cuby9evX0+2bWhoqLkcEBBgnpWI30dcP3nz5k20j4iICLuhU6VLl06xxqpVq6p+/foptgMAAMCDycoNfLjdbCapVq2auXz+/Plk2164cMFcrlmzprlcvHhxFShQIFX9xO/DxcVF1atXT0u5AAAAQJoQLDJJ48aN5eHhIUk6e/asIiMjk2x75swZc7lJkyZ225o3b24uBwUFpaqPWrVqJbg+AwAAAMhIBItM4uXlpZYtW0q6d1F1chPW7du3T9K928V27NjRbtvzzz9vLsefhTupPiSpS5cu6aoZAAAASC2CRSYaOHCgubx69epE24SEhJhnIl5++WX5+fnZbW/btq0CAwMlSWvWrEnyWNu3b5ck+fj46NVXX7VSNgAAAJAigkU6xcTEmMsp3T42Tt26dc0zEAsWLJDNZkvQZsGCBTIMQ15eXolOUOfs7KwxY8ZIujfcaceOHQnahISEaN26dZKkYcOGKV++fKmqDwAAAEivTAsWhmEoLCxMFy9eTNUs0Nnd1atXE11OydSpU1WkSBEFBQVpxowZdtsuXLigUaNGSZKmTJmiEiVKJNpH27Zt1bt3b0nSkCFDEgSb999/X5GRkWrRooXefffdVNcGAAAApJfDbjd7+fJlLVu2TFu2bNHevXv1zz//2E3Y5uTkpCJFiqhGjRp64okn1K5dO7s7J2VXx44dU0hIiCZMmGD32PDhw9W6dWv5+/vL398/yf39/Py0efNmNWvWTH379tXVq1fVtGlTHTx4UEOHDtWtW7c0c+ZMdevWLdk6Jk+erDt37mjevHnq0KGDBgwYoOjoaI0dO1Zr1qxR586dNWvWLLm4uGTYcwcAAACSkuHBYv369Ro/frzWrVtnN9THMAy7doZh6N9//9W///6rVatW6dNPP1VgYKDeeecd9e7dO9mJ37LS448/rtu3byd4/LPPPtNnn32mF154QT/99FOyfQQGBmr//v0aP368Zs6cqREjRsjPz0/t27fXwIEDVapUqRTrcHNz05w5c9S+fXtNmjRJ7du3V0xMjOrWrasVK1aoTZs26X2KAAAAQJplWLA4dOiQ3n33XW3dulVSwiCRmPvbHDt2TG+//ba+/vprffXVV9nybkbh4eEZ0o+vr69GjhypkSNHprsPJycnde7cWZ07d86QmgAAAID0ypBgMX78eA0ZMsSc6dnPz0+1atVS+fLlVbZsWZUpU0be3t7y9PRUnjx55OzsrFu3bik8PFyhoaEKCgrSsWPHdPToUf31118KDg5Wt27dtGTJEk2dOlW+vr4ZUSYAAAAAB7EULGw2m3r37q2ZM2eqRIkS6tmzp1544QVVqFAh3X3euXNH69ev15w5c7R06VIdPnxY69atU7FixayUCgAAAMCBLN0Vqk+fPvr55581duxYnThxQsOHD7cUKiQpd+7catu2rRYuXKigoCCVLVtWdevW1eXLly31CwAAAMBx0h0sxo8fr02bNmnv3r3q37+/3N3dM7IuSVKZMmW0fPlyvfrqq+rcuXOq54sAAAAAkLnSFSxOnDihGTNmaPv27ZbPUKTGiBEj1LlzZ33++ecOPxYAAACAtEtXsPj444+1cOFCFSlSJKPrSVK/fv0UFBSkS5cuZdoxAQAAAKROmi/ettlsGjt2rAICAhxRT7KmTZtm3nkKAAAAQPaR5mDh7OycJaFCkjw9PeXp6ZklxwYAAACQtHRfvD1u3LiMrAMAAABADpbuYDFq1KgMm4UaAAAAQM6W7mARFhamrl27Ei4AAAAAWJsgb9WqVapUqZKmTJmiyMjIjKoJAAAAQA5jKVgYhqFz587prbfeUrFixTRgwACdOHEio2oDAAAAkEOk+a5Q8U2dOlXdunXTzp07tWDBAn3//feaMGGCmjZtqjfffFPt2rWTs7Ol7AIAeEBFjx6d1SUAADJQur/1BwYG6rXXXlOePHnUtGlT/fDDD7pw4YImTZqkS5cuqVOnTgoICNBnn32mixcvZmTNAAAAALKZdAeL8ePHJ3gsb9686tu3rw4ePKitW7eqYcOG+vLLL1WqVCl17txZGzZssFQsAAAAgOwp3cGiZcuWyW6vX7++5s2bp/Pnz+vzzz/X/v371axZMz3yyCOaMGGCQkND03toAAAAANmMwy+AKFCggAYNGqSTJ0/q119/VcWKFTVo0CD5+/urR48e2rVrl6NLAAAAAOBgmXpldYsWLfTLL7/ozJkzGjBggNatW6e6devqscce048//qiIiIjMLAcAAABABsmSWzYVL15cgwcP1sCBA+Xh4aEDBw7o9ddfl7+/v/r166czZ85kRVkAAAAA0inTg8XZs2c1cOBAFS9eXP379zcn1jMMQzdu3NDy5cuZCwMAAADIYSzNY5EW27Zt08SJE7V8+XLZbDYZhmG3vX79+nrvvffUvn175r4AAAAAchiHBovo6GjNnz9f//nPf3TgwAFJ985MODk5SZLc3d3VpUsX9evXTzVq1HBkKQAAAAAcKN3BYuLEiXrvvfcS3XblyhVNnjxZU6dO1ZUrVyTJ7gxFoUKF9MYbb+jNN99UoUKF0lsCAAAAgGwi3cFi7ty5CYLFX3/9pf/85z9auHChoqKizLMTcaGiRo0a6tevn7p06SJ3d3dLhQMAAADIPtIdLPbt26fZs2erevXq+v333zV79mz9+eefkuyHOzk7O+vZZ5/Ve++9pwYNGmRM1QAAAACylXQHC8Mw1KNHjwSPxfH29lavXr309ttvq2TJkumvEAAAAEC2Z/ni7fuHOz3yyCN699131b17d3l6elouEAAAAED2lyF3hTIMQ88884zee+89tWzZMiO6BAAAAJCDWAoWTk5OeuWVVzRo0CA9+uijGVUTAAAAgBzGUrAYO3ZskrecBQAAAPDwSPcU1+XKlSNUAAAAAJBkIVh07949I+sAAAAAkIOlO1h8/PHHqW57+PBh/fLLL7p586b52K1bt7Rt2zbFxMSktwQAAAAA2US6g0Vq/PLLL3rkkUdUtWpVderUSefPnze3RUdHa8mSJSpTpozGjRun2NhYR5YCAAAAwIEcFiwmTJigTp066dSpU3YT58XJnz+/JkyYoIULF2rkyJFq1KiRrl275qhyAAAAADiQQ4LF3r17NXDgQBmGkWioiK9OnToaNWqUfv/9d7Vu3ZqhUQAAAEAO5JBgMX78eNlsNgUGBmrKlClatWqVnJ2TPlSvXr3k4uKi3bt3a/LkyY4oCQAAAIADZcjM2/fbsmWLHn30Ue3atUteXl4pts+VK5f8/f117tw5zZkzR++++64jygIAAADgIA4JFiEhIRo9enSqQkWc8PBwGYahoKAgR5QEAAAAwIEcMhTKx8dHlStXTnX7ffv26fr165IkJycnR5QEAAAAwIEcEiyqV6+us2fPprr9J598IuleqHjkkUccURIAAAAAB3JIsHjhhRc0bty4VLUdMGCAVq9eba4/99xzjigJAAAAgAM5JFi8+uqrunHjhtq3b28OcZLshznt3LlTTz/9tCZMmGA+XqJECb3zzjuOKAkAAACAAznk4m1nZ2ctW7ZMDRo0UNGiRVWrVi0ZhqGBAwfKZrNp//79unz5stneMAz5+vpqxYoVyp07tyNKAgAAAOBADgkWkhQQEKB9+/apb9++WrRokWw2m1atWpVo26ZNm+qHH35QyZIlHVUOAAAAAAdyWLCQpPz582vBggX68ssvtWrVKu3Zs0chISGKiopS/vz5VaVKFbVq1UrVq1d3ZBkAAAAAHMyhwSJOqVKl9NZbb2XGoQAAAABkAYdcvJ1eQUFBqlixYlaXAQAAACCNslWw2LFjBzNvAwAAADmQQ4dCXbt2TRs3blRwcLDu3Lkjm82WaLvo6Gj9+++/+vnnnx1ZDgAAAAAHcViwGD16tEaOHKmIiIhU72MYht1cFwAAAAByBocEi2nTpmnw4MFycnKSYRiOOAQAAACAbMQhwWLSpEnm8nPPPacnn3xS3t7ecnZO/JIOwzAUGhqq8ePH6+LFi44oCQAAAIADOSRYnDp1Sk5OTho3bpzee++9VO9XvHhxdenSxRElAQAAAHAgh9wVqlChQpKkl19+OU371ahRQ4888ogjSgIAAADgQA4JFq1bt5YkRUVFpWm/cuXK6ejRo44oCQAAAIADOSRYDBgwQJ6envr111/TtN/FixfVs2dPR5QEAAAAwIEcEixKly6t+fPna9SoUbpx40aq9zt69KhmzZrliJIAAAAAOJDD5rFo06aNJKl79+765ptvkr3trGEYunbtmj799FNHlQMAAADAgRwWLP78809NnDhRmzdv1ooVK1K1DxPkAQAAADmTQ4LFn3/+qUaNGikyMlKSmCQPAAAAeMA5JFgMGzZMd+/elSTlyZNHdevWVaFCheTm5pZoe8MwFBISot9++002m80RJQEAAABwIIcEi927d8vJyUnPPPOMFi1apLx586Zqv7Fjx+rDDz90REkAAAAAHMghd4WKO+swYcKEVIcKSWrSpAnDpgAAAIAcyCHBon79+pIkf3//NO1XqVIl7d692xElAQAAAHAghwSLgQMHSpIOHTqU5n25KxQAAACQ8zgkWDz11FMaNGiQJkyYkKb9jh49qieeeMIRJQEAAABwIIcEC0n68ssvVahQIS1fvjzV+6xfv95R5QAAAABwIIfcFapnz56S7t1GtlevXmrbtm2y7W02my5dukSwAAAAAHIoh02Qd/ToUXN91qxZqdqPmbfT56+//tK2bdsUGxurKlWqqGnTpnJ2dtjJKAAAACABh3z77NOnjwzDSPNPTrN3714999xzatq0abr2//777+Xk5JTkT+nSpRUTE5Pk/ufOnVOzZs1Us2ZNzZkzR+vWrVOHDh0UGBionTt3pvdpAQAAAGnmkGDRvXt35cmTRx07dtSWLVt05MgRnTp1SmfOnEn05/jx49q8ebOqV6/uiHIy3IYNG/TMM8/o8ccf1+LFixUbG5vmPmJiYvT1118n2+aDDz6Qq2viJ5WOHTumJ554Qps2bdKiRYu0Z88erV69WkFBQbLZbGrUqJHWrl2b5roAAACA9HDIUKh8+fKpa9eu6tChgxo0aJCqfcqVK6cJEyaocePGjigpQ5w5c0aDBg2Sk5OTDh48aKmv+fPn69y5c0nO9ZEvXz7zWpX7hYeHq3Xr1rp8+bKGDBmizp07m9v8/f01bdo0NW3aVJ06ddLBgwdVpkwZS7UCAAAAKXFIsJCkd955R9evX0/TPpUqVdLIkSMdVJF1AQEBWrhwoZycnLR9+/ZUh6b72Ww2ffnll3r33Xc1duzYNO8/dOhQnT59Wm5uburfv3+C7U2aNFHVqlV18OBB9enTR+vWrUtXnQAAAEBqOewK36pVq6pRo0Zp2sfPz08ff/yxYwrKAC4uLubF5ZUrV053P4sXL9aZM2f0wQcfpHnfS5cuacqUKZLuzXBeoECBRNvF3Ylr/fr12r59e7prBQAAAFIjzcHCZrOZM2tntrlz5yooKChLjn0/Ly+vdO/7xRdfqGfPnipatGia9507d67u3r0rSapdu3aS7erVq2cuT5s2Le1FAgAAAGmQ5mDh7Owsf3//FC88zmh79+7VypUrFRgYmKnHTUpSF1WnZMWKFTpw4IDmzZunpk2baujQoTpy5Eiq91+6dKm5nNxrUbFiRXN5+fLlOfKuWwAAAMg50jUUqm/fvvrll180f/78jK4nUfv27dPzzz+v0aNHZ8rxHGnUqFGSpJs3b2rjxo36/PPPValSJdWvXz/FW8RGRkZqz5495nrx4sWTbOvv728O2woLC9PJkyczoHoAAAAgcekKFm5ublqwYIHeffddffrpp7LZbBldl2nu3Ll6+umn9e233yogIMBhx8kM69at065duxLd9vvvv6tu3boaNGhQkq/nyZMnFRUVZa6XKFEiyWO5urrKx8fHXD9w4ED6igYAAABSId13hSpVqpR+/fVXtW7dWkuWLNHHH3+sTp06ycXFJUMK27lzp4YNG6ZNmzbpu+++U8uWLTOk36xUt25dBQcH69q1azp9+rR27NihxYsX6+zZs2abMWPG6OzZs/rpp58SzEIeHBxst+7r65vs8XLlymUuX7t2LcX64t9CNyAgIMcHOQAAAKRNcHCw+Z0zrdMrWLrdbO3atbV582a1adNGXbt2Vf78+fXss8+qadOmql69usqXLy83N7cU+4mNjdXZs2e1d+9e7dixQytXrtTp06fl4eGhOXPmqGvXrmmq68qVKzp+/Hh6n5bJ3d1dtWrVstxPHE9PT3l6eqpEiRKqXr26OnbsqK+//lqzZ8/WwIEDzS//CxcuVLVq1TRkyBC7/W/evGm3nidPnmSPF/+1v3HjRor19e3b11wePny4RowYkeI+AAAAeHBMnz5dn376abr2tTyPReXKlXXo0CG9/fbbmjNnjmbOnKmZM2dKund71lKlSqlYsWLKmzevvLy85O7urjt37uj27du6ffu2zp07p3PnztnNXm0YhqpXr645c+aoUqVKaa5p1apV6tGjh9WnpsKFC+vSpUuW+0mOi4uLevTooWbNmqlFixb6+++/Jd27FqNnz54qUqSI2Tb+MCgp5WARHR2d6DIAAACQ0TJkgry8efNq1qxZ6tmzp4YOHWrOmxATE6NTp07p1KlTye4f/45FAQEBGjJkiHr37i1n5/RNs9GhQwfVqVMnXfvGl947P6WHv7+/1q5dq8cff1z//vuvIiIitHjxYr399ttmG09PT7t9YmNjk32N4oeJlEIIAAAAYEWGfnNu2LChtm7dqp07d2revHlavHixrly5kuJ+efLkUfPmzdWtWze1a9fO8nUa3t7e8vb2ttRHVihWrJgmTJigLl26SLp3nUn8YHH/NRV3795NdqhZ/KFTBQsWTPH43333napWrSpJXF8BAADwEOrZs6eefvppSfeusYg/VD4lDvmT/JNPPqknn3xS3377rU6cOKG9e/fq5MmTCg0N1c2bN+Xm5iYfHx+VKVNGVapUUc2aNTP17EB29txzz+nDDz/U2bNndfXqVbtt5cuXt1u/fv268ubNm2g/ERERdkOnSpcuneKxq1atqvr166ejagAAADwIrNzAx+Hf5suXL5/gCzGS5uzsrMaNG2vmzJny8/Oz21a8eHEVKFDAvMj7/PnzKlmyZKL9XLhwwVx2cXFR9erVHVYzAAAAkL6LGDLI33//bXfRNu4pWrSoJKlKlSoJtjVv3txcDgoKSrKPM2fOmMu1atVKcH0GAAAAkJGyNFjs3btXlSpV0ptvvqmtW7dmZSnZSlhYmJycnPTCCy8k2Pb888+by/Fn4b7fvn37zOW4azYAAAAAR0n3UKipU6fq1q1b5k94eLj++9//punC6+7du6tNmzZ655131LhxYxUtWlQvvPCCxo0bl96yHggbN25Uz549E70uom3btgoMDFRQUJDWrFmTZB9xd+by8fHRq6++6qhSAQAAAEkWzli8+eab+vDDD7Vo0SIFBgZq8ODB6bqbU4ECBTR//nx9/PHHunjxoiZOnJjekjJVTEyMuZyRw7nmzp2r2NhYjR49OtHtzs7OGjNmjKR7w5127NiRoE1ISIjWrVsnSRo2bJjy5cuXYfUBAAAAibF08fajjz6qXbt2JRi//+mnn8rJySnJ/YYNG5bgsc8++0zbt2/Xli1brJSUaeLfsen+uzcl5sqVK+rSpYtu376t/v37JzrMacOGDRo3bpzWrFmj/PnzJ9lX27Zt1bt3b/3www8aMmSINmzYYBfq3n//fUVGRqpFixZ699130/jMAAAAgLSzFCz69u2b6EXBzz//vP78808NGTLEnLm6a9eueu6555KdSfu1117L9sHi2LFjCgkJ0YQJE+weGz58uFq3bi1/f3/5+/sn2C8kJERbtmyRzWZTly5d9N1336lv374KDAzU5cuXtWjRIjk7O2vTpk3y8fFJsY7Jkyfrzp07mjdvnjp06KABAwYoOjpaY8eO1Zo1a9S5c2fNmjXL8pwgAAAAQGpYChZJ3er00Ucf1aOPPqpChQqpdevWevHFFzV37twU+ytXrpyVcjLF448/rtu3byd4/LPPPtNnn32mF154QT/99FOC7ZUqVdKKFSv01Vdf6dChQ9qxY4eOHz+uUqVKqVWrVho8eHCanr+bm5vmzJmj9u3ba9KkSWrfvr1iYmJUt25drVixQm3atLH0PAEAAIC0sBQscuXKlez2Bg0aSJJeeumlVPXn5eVlpZxMER4enu59W7VqpVatWmVYLU5OTurcubM6d+6cYX0CAAAA6eHQ2816eXnJ2dlZxYoVS10xzll691sAAAAA6eTwb/JOTk5ydXX4BN8AAAAAshCnCAAAAABYRrAAAAAAYFm2ChY3b97M6hIAAAAApIOlix8OHjyY4vUThmFoz549yU4iZxiGIiIicsys2wAAAADsWQoWH3zwQara9ejRI1XtDMNIdsZuAAAAANmT5aFQhmFkRB0AAAAAcjBLZyziQgXhAgAAAHi4WTpjMWXKFF2+fFnR0dGy2Wzp/omNjdX169f1448/MkkeAAAAkAOl+4zFE088oddffz1DinBycpKPj4969OihxYsXZ0ifAAAAADJPuk8PlC1bNiPrMFWqVMkh/QIAAABwnHQHi48++igj65B071qNgICADO8XAAAAgGOlO1hUqVIlw4o4duyYPvroIwUEBKhfv34Z1i8AAACAzGHprlBWhIaGasGCBZo1a5b27NkjiXksAAAAgJwqU4NFbGysVq1apVmzZunXX39VVFSUJG5XCwAAAOR0mRIs9u/fr1mzZmn+/Pm6evWqpP87OxEXKuIvAwAAAMhZHBYsrly5onnz5mnWrFk6dOiQpIRnJlxcXPTMM8+oW7duevPNN3Xr1i1HlQMAAADAgTI0WERFRWn58uWaNWuW1q5dq9jYWEkJA0WdOnXUrVs3vfDCC/Lz85Mk9e3bNyNLAQAAAJCJMiRY7Nq1S7NmzdLPP/+ssLAwSQnDRIUKFdStWze9+OKLKl26dEYcFgAAAEA2ke5gcf78ec2ZM0ezZs3SiRMnJCUME8WKFVOXLl3UrVs31ahRw1qlAAAAALKtNAWLO3fu6H//+59mzZqlTZs2mUEifqDw9vZWp06d1K1bNzVq1IjbxwIAAAAPgTQFi8KFC+v27duS7MOEh4eHWrVqpW7duqlNmzZyd3fP2CoBAAAAZGtpChbh4eHmbWFdXFz01FNPqVu3burUqZO8vb0dVSMAAACAbM45PTs5OTmpWLFiatKkiRo3bkyoAAAAAB5yaQ4WcUOgzp8/r2HDhqlcuXKqW7euvv32W4WEhGR4gQAAAACyvzQFi02bNql79+7y9PSUYRjmz65du9SvXz/5+/urVatWmjt3rnktBgAAAIAHX5qCRcOGDTVjxgxdvnxZs2bNUpMmTeTs7GwGjJiYGK1du1bdu3dX4cKF9eKLL2rlypWKiYlxVP0AAAAAsoF0XWORO3duvfzyy1q/fr3++ecfjRw5Uo888ogkmSEjIiJCP//8s9q1a6eiRYvqrbfe0vbt2zO0eAAAAADZQ7qCRXzFixfXxx9/rGPHjun333/Xa6+9Jh8fH0n/FzKuXbumqVOnqmHDhipVqpSGDBmiv//+2+qhAQAAAGQTloNFfE8++aSmTp2qf//9VwsWLFCLFi3k4uIi6f9CRnBwsL7++mtVq1ZN1apV0+jRoxUcHJyRZQAAAADIZBkaLOJ4eHjohRde0KpVq3Tu3Dl9/fXXqlSpkl0bwzD0999/66OPPlKZMmV069YtR5QCAAAAIBM4JFjEV6RIEQ0cOFCHDh3Snj171LdvX+XPn1/S/9261maz2c3kDQAAACBncXiwiO+xxx7TN998o3///VdLlixRu3bt5Oqapsm/AQAAAGRDmRos4ri6uqp9+/ZaunSpLly4oAkTJqh69epZUQoAAACADJAlwSI+Pz8/9evXT3v37tWBAwfUv3//rC4JAAAAQBplebCIr0qVKhozZkxWlwEAAAAgjbJVsAAAAACQMxEsAAAAAFhGsAAAAABgGcECAAAAgGUECwAAAACWESwAAAAAWEawAAAAAGAZwQIAAACAZQQLAAAAAJYRLAAAAABYRrAAAAAAYBnBAgAAAIBlBAsAAAAAlhEsAAAAAFhGsAAAAABgGcECAAAAgGUECwAAAACWESwAAAAAWEawAAAAAGAZwQIAAACAZQQLAAAAAJYRLAAAAABYRrAAAAAAYBnBAgAAAIBlBAsAAAAAlhEsAAAAAFhGsAAAAABgGcECAAAAgGUECwAAAACWESwAAAAAWEawAAAAAGAZwQIAAACAZQQLAAAAAJa5ZnUBOVFQUJC2bdumK1euKF++fKpRo4bq1KkjFxeXNPUTGRmp1atX69ixY8qfP78aN26s8uXLp7mev/76S9u2bVNsbKyqVKmipk2bytmZzAgAAIDMQ7BIg4sXL+q1117TqlWrEmwrW7asJkyYoLZt26aqr6VLl6pv374KDw/Xk08+qevXr+uNN95Qly5d9N///lfe3t4p9nHu3Dn16tVL69atU82aNVWwYEENGzZMRYsW1ezZs/Xkk0+m+TkCAAAA6cGftVPp/PnzqlWrVqKhQpJOnTqldu3aadKkSSn2NX78eHXq1El+fn46fPiw1q5dq927d2vevHlasGCBGjZsqLCwsGT7OHbsmJ544glt2rRJixYt0p49e7R69WoFBQXJZrOpUaNGWrt2bXqeKgAAAJBmBItUeumll3ThwgWVKVNGgwcP1g8//KAJEyaoYcOGZhvDMPTee+9p27ZtSfazcuVKDRgwQM7Ozlq4cKFKlChhbuvatatefvllHThwQC+++GKSfYSHh6t169a6fPmyBg0apM6dO5vb/P39NW3aNEVFRalTp046ffq0xWcOAAAApIxgkQobNmzQli1bNHz4cAUFBenLL79Ur1699N5772nz5s2aNm2aeU2DYRgaPnx4ov1ERESoT58+MgxDzz77rCpUqJCgzfvvvy9JWr16tebNm5doP0OHDtXp06fl5uam/v37J9jepEkTVa1aVbdv31afPn3S+7QBAACAVCNYpMKCBQs0YMAAjRgxQq6uCS9L6d27tz788ENzfevWrYqMjEzQ7vvvv9fFixclSe3bt0/0WDVq1JC/v78kacSIETIMw277pUuXNGXKFElS/fr1VaBAgUT7ibvWY/369dq+fXsKzxAAAACwhmCRCkeOHNHQoUOTbTNo0CAzdMTGxio0NDRBmx9++MFcrl27dpJ91atXT5J08uRJbd682W7b3Llzdffu3VT3IUnTpk1LtnYAAADAKoJFKixdulT58uVLto2Pj48qVqwoSXJxcVHBggXttp84cUKHDx82t5ctWzbJvuL6kaRly5YlqCVOYGBgqvpYvnx5gjMfAAAAQEYiWKRC4cKFU9Uud+7ckqSqVasmmNMi/nCkIkWKJDqkKk7x4sXN5d27d5vLkZGR2rNnT6Lt7ufv7y8nJydJUlhYmE6ePJmq5wAAAACkB8EiA50/f16SEr2j05EjR8zl+HeCSkz8sx0HDhwwl0+ePKmoqKhU9ePq6iofH59E+wEAAAAyGhPkZZCTJ0/qwoUL8vHxUa9evRJsDw4ONpd9fX2T7StXrlzm8u3btxUZGSkPDw+7PtLaz7Vr15JtK0kHDx40lwMCAhQQEJDiPgAAAHhwBAcHm9854383TI0HMlhcuXJFx48ft9yPu7u7atWqlaq28+fPl3TvVrCJfeG/efOmuZwnT55k+3Jzc7Nbv3HjhgoVKmTXR1r7uXHjRrJtJalv377m8vDhwzVixIgU9wEAAMCDY/r06fr000/Tte8DGSxWrVqlHj16WO6ncOHCunTpUortwsPDNWnSJNWqVUv9+vVLtE38IUwpBYLo6OhE1+P3kdZ+7u8TAAAAyEgPZLDo0KGD6tSpY7mf5C6wju+TTz5RbGysfvrppwQXbcfx9PQ0l2NjY5Pt7/4QEBcg4vcR10/cxHwp9ZNSCAEAAACseCCDhbe3t7y9vTPlWL///rv++9//6pdfflHp0qWTbBd/eFTcPBRJiT/kyd3d3Xwu9w+xunv3boJhU0n1c//tbxPz3XffqWrVqpLE9RVwuOjRo7O6BAAAcJ+ePXvq6aeflnTvGov4Q+VT8kAGi8xy6dIlPf/88/rmm2/UsmXLZNuWL1/eXL5+/XqybeNPrhcQEGCelYjfR1w/efPmTbSPiIgIu6FTyYWeOFWrVlX9+vVTbAcAAIAHk5Ub+HC72XS6c+eOOnXqpLfffluvv/56iu2rVatmLsfdljYpFy5cMJdr1qxpLhcvXlwFChRIVT/x+3BxcVH16tVTrBEAAABIL4JFOsTGxqpLly6qV6+ePvroo1Tt07hxY3l4eEiSzp49q8jIyCTbnjlzxlxu0qSJ3bbmzZuby0FBQanqo1atWgmuzwAAAAAyEsEijWw2m3r16qWiRYtqdBrGiHt5eZnDpaKjo5OdsG7fvn2S7t0utmPHjnbbnn/+eXM5/izcSfUhSV26dEl1nQAAAEB6ECzSwGazqXfv3rLZbJo8eXKybf/44w+tW7fO7rGBAweay6tXr050v5CQEPNMxMsvvyw/Pz+77W3btlVgYKAkac2aNUkef/v27ZIkHx8fvfrqq8nWCgAAAFhFsEiluDMVkZGRmjFjRpK3eY2JidGSJUvUtm1blStXzm5b3bp1zTMQCxYskM1mS7D/ggULZBiGvLy8Ep2gztnZWWPGjJF0b7jTjh07ErQJCQkxQ82wYcOUL1++ND1XAAAAIK0IFqkQFRWlLl26aObMmdq9e7cqVaqkChUq2P0EBgaqVKlSypcvnzp16qSyZcsmeiemqVOnqkiRIgoKCtKMGTPstl24cEGjRo2SJE2ZMkUlSpRItJ62bduqd+/ekqQhQ4YkmBfj/fffV2RkpFq0aKF33303I14CAAAAIFncbjYFt2/fVseOHfXbb79Jkk6cOJGq/ZK6rsHPz0+bN29Ws2bN1LdvX129elVNmzbVwYMHNXToUN26dUszZ85Ut27dku1/8uTJunPnjubNm6cOHTpowIABio6O1tixY7VmzRp17txZs2bNSnLCPgAAACAjccYiBYMHDzZDRWo5OzvbXWR9v8DAQO3fv18DBw7UzJkz1aBBAw0fPlzt27fXkSNH1L179xSP4ebmpjlz5mjRokUKCwtT+/bt1aFDBxmGoRUrVmjRokXMtg0AAIBMwxmLFHzzzTf65ptvMrxfX19fjRw5UiNHjkx3H05OTurcubM6d+6cgZUBAAAAaccZCwAAAACWESwAAAAAWEawAAAAAGAZwQIAAACAZQQLAAAAAJYRLAAAAABYRrAAAAAAYBnBAgAAAIBlBAsAAAAAlhEsAAAAAFhGsAAAAABgGcECAAAAgGUECwAAAACWESwAAAAAWEawAAAAAGAZwQIAAACAZQQLAAAAAJYRLAAAAABYRrAAAAAAYBnBAgAAAIBlBAsAAAAAlhEsAAAAAFhGsAAAAABgGcECAAAAgGWuWV0AHl7Ro0dndQkAAADIIJyxAAAAAGAZwQIAAACAZQQLAAAAAJYRLAAAAABYRrAAAAAAYBnBAgAAAIBlBAsAAAAAlhEsAAAAAFhGsAAAAABgGcECAAAAgGUECwAAAACWESwAAAAAWEawAAAAAGAZwQIAAACAZa5ZXQAAAAAePtGjR2d1CchgnLEAAAAAYBnBAgAAAIBlBAsAAAAAlhEsAAAAAFhGsAAAAABgGcECAAAAgGUECwAAAACWESwAAAAAWEawAAAAAGAZwQIAAACAZQQLAAAAAJYRLAAAAABYRrAAAAAAYBnBAgAAAIBlBAsAAAAAlhEsAAAAAFhGsAAAAABgGcECAAAAgGUECwAAAACWESwAAAAAWEawAAAAAGAZwQIAAACAZQQLAAAAAJYRLAAAAABYRrAAAAAAYBnBAgAAAIBlBAsAAAAAlhEsAAAAAFhGsAAAAABgGcECAAAAgGWuWV1AThQUFKRt27bpypUrypcvn2rUqKE6derIxcUlS+r566+/tG3bNsXGxqpKlSpq2rSpnJ3JjAAAAMg8BIs0uHjxol577TWtWrUqwbayZctqwoQJatu2bar7+/7779WnT58kt5cqVUonTpyQq2vib9O5c+fUq1cvrVu3TjVr1lTBggU1bNgwFS1aVLNnz9aTTz6Z6loAAAAAK/izdiqdP39etWrVSjRUSNKpU6fUrl07TZo0KVX9xcTE6Ouvv062zQcffJBkqDh27JieeOIJbdq0SYsWLdKePXu0evVqBQUFyWazqVGjRlq7dm2qagEAAACs4oxFKr300ku6cOGCypQpo+eff17lypXTrVu39Msvv2jLli2SJMMw9N5776lGjRpq0KBBsv3Nnz9f586dk7+/f6Lb8+XLp549eya6LTw8XK1bt9bly5c1ZMgQde7c2dzm7++vadOmqWnTpurUqZMOHjyoMmXKpPNZAwAAAKlDsEiFDRs2aMuWLRo+fLg++eQTu7MI7733nn744Qf16dNHNptNhmFo+PDh2rhxY5L92Ww2ffnll3r33Xc1duzYNNczdOhQnT59Wm5uburfv3+C7U2aNFHVqlV18OBB9enTR+vWrUvzMQAAAIC0IFikwoIFCzRgwACNGDEi0e29e/fW6dOn9eWXX0qStm7dqsjISHl4eCTafvHixTpz5ow++OCDNNdy6dIlTZkyRZJUv359FShQINF2bdu21cGDB7V+/Xpt375d9evXT/Ox0sIwDMXExCg2NtahxwEygrOzs9zc3OTk5JTVpQAA8MAgWKTCkSNHtGbNmmTbDBo0SGPGjDG/XIeGhqpIkSKJtv3iiy/Us2dPFS1aNM21zJ07V3fv3pUk1a5dO8l29erVM5enTZvmsGBhGIZCQ0MVFhamyMhIhxwDcAQXFxflzZtX3t7eypMnT1aXAwBAjkewSIWlS5cqX758ybbx8fFRxYoVdfDgQbm4uKhgwYKJtluxYoUOHDigM2fOKCgoSHXr1lXXrl1VsWLFVNcSJzAwMMl28ftbvny5DMNwyF9nL1++rNDQUOXNm1cFCxaUq6srfwVGtmYYhmw2m27fvq2bN28qLCxMxYsXV968ebO6NAAAcjSCRSoULlw4Ve1y584tSapatWqSc1qMGjVKknTz5k1t3LhRGzdu1Oeff6569eppzJgxyd4iNjIyUnv27DHXixcvnmRbf39/OTk5yTAMhYWF6eTJkypfvnyqnkdq3bhxQ6GhoSpatKh8fHwytG/A0Tw9PVWwYEFdvHhR58+fV8mSJTlzAQCABdxuNgOdP39ekvTiiy8mun3dunXatWtXott+//131a1bV4MGDZLNZku0zcmTJxUVFWWulyhRIslaXF1d7b7sHzhwIKXy0+zmzZvKkycPoQI5lpOTk4oVKyY3NzfduHEjq8sBACBH44xFBjl58qQuXLggHx8f9erVK9E2devWVXBwsK5du6bTp09rx44dWrx4sc6ePWu2GTNmjM6ePauffvopwZCi4OBgu3VfX99ka8qVK5e5fO3atRSfw8GDB83lgIAABQQEJNk2bihJUkO+gJzCyclJ+fLlU1hYmIoUKcJQPgDAQy04ONj8zhn/u2FqPJDB4sqVKzp+/Ljlftzd3VWrVq1UtZ0/f76ke7eCTeoLv6enpzw9PVWiRAlVr15dHTt21Ndff63Zs2dr4MCB5pf/hQsXqlq1ahoyZIjd/jdv3rRbT2nYhpubm7mcmr/G9u3b11wePnx4knfBku5N8GcYhl14AXKqPHny6Nq1a4qOjpa7u3tWlwMAQJaZPn26Pv3003Tt+0AGi1WrVqlHjx6W+ylcuLAuXbqUYrvw8HBNmjRJtWrVUr9+/dJ0DBcXF/Xo0UPNmjVTixYt9Pfff0u6dy1Gz5497e4sFX8YlJRysIiOjk50OSPEDddydmY0HXK+uGuikhqGCAAAUvZABosOHTqoTp06lvuJPxFecj755BPFxsbqp59+SvKi7ZT4+/tr7dq1evzxx/Xvv/8qIiJCixcv1ttvv2228fT0tNsnNjY22S/28cOEoy5KZdgIHgR8jgEAsO6BDBbe3t7y9vbOlGP9/vvv+u9//6tffvlFpUuXttRXsWLFNGHCBHXp0kWStHPnTrtgcf8Qq7t379oNd7pf/KFTqbkW4rvvvlPVqlUlKdnrKwAAAPBg6tmzp55++mlJ966xiD9UPiUPZLDILJcuXdLzzz+vb775Ri1btsyQPp977jl9+OGHOnv2rK5evWq37f7bxV6/fj3Je+9HRETYDZ1KTeipWrWqw2foBgAAQPaV0g18ksMA+XS6c+eOOnXqpLfffluvv/56hvXr7Oysxo0bS5L8/PzsthUvXlwFChQw1+Nub5uYCxcumMsuLi6qXr16htUIAAAA3I9gkQ6xsbHq0qWL6tWrp48++ijD+y9atKgkqUqVKgm2NW/e3FwOCgpKso8zZ86Yy7Vq1UpwfQYAAACQkRgKlUY2m029evVS0aJFNXr0aIccIywsTE5OTnrhhRcSbHv++efNW9vu2bNHPXv2TLSPffv2mctx12xkJbdBg7K6BIeKdtBnYd++fZozZ47Wr1+vQ4cOpXq/unXr6vfff3dITQAAAInhjEUa2Gw29e7dWzabTZMnT0627R9//KF169al6zgbN25Uz549E70uom3btgoMDJQkrVmzJsk+tm/fLkny8fHRq6++mq46kPVq1Kih8ePHa8+ePeYwuJIlSyo6OjrBz61bt/TXX3+pQYMGunXrVhZXDgAAHjYEi1SKO1MRGRmpGTNmJHmb15iYGC1ZskRt27ZVuXLl0nycuXPnKjY2NsmzIc7OzhozZoyke8OdduzYkaBNSEiIGWqGDRumfPnypbkOZC/u7u52nydXV9cEP15eXqpRo4YWL16c4fOWAAAApIShUKkQFRWll156SYsWLVL58uVVqVKlBG0Mw1BkZKSuXLmiO3fuqHbt2nZnHK5cuaIuXbro9u3b6t+/f6LDnDZs2KBx48ZpzZo1yp8/f5L1tG3bVr1799YPP/ygIUOGaMOGDXbzZ7z//vuKjIxUixYt9O6771p89sguUjvLeaFChRK9PgcAAMCRCBYpuH37tjp27KjffvtNknTixIlU7Xf/dQ0hISHasmWLbDabunTpou+++059+/ZVYGCgLl++rEWLFsnZ2VmbNm2Sj49Piv1PnjxZd+7c0bx589ShQwcNGDBA0dHRGjt2rNasWaPOnTtr1qxZ6Z6wDznbp59+mtUlAACAhwzBIgWDBw82Q0VqOTs76/nnn7d7rFKlSlqxYoW++uorHTp0SDt27NDx48dVqlQptWrVSoMHD07T0Ck3NzfNmTNH7du316RJk9S+fXvFxMSobt26WrFihdq0aZOmmvFgefTRR7O6BAAA8JDhGosUfPPNNzIMI00/sbGxKlasWIK+WrVqpa1btyo0NFQxMTG6dOmS/vjjDw0bNixd12M4OTmpc+fO2rp1q65fv66bN29qzZo1hIqH2Jo1a7R582a7x6KiojR79mzVqFFDI0aMkHTvjmLNmzeXl5eXypUrpzlz5iTZ57lz5/TWW2+pfPnyypUrl4oUKaLu3bvb3dJYkiIjIzV37lzVrVtXPXr0kCR99dVX8vPzU+XKlXXy5Emz7bVr1/TBBx+obNmy8vT0VKFChfTiiy/q+PHjdn2ePn1aTk5OCX7efPNNu3a9e/e22/7999+n9aUDAAAWESyAB8iRI0fs1n/44QfVrFlT3bt31/79+yVJP/30k5566ikdPXpUUVFROnXqlLp3765t27Yl6G/Lli1q1KiRmjRposOHD+vSpUvq0qWLZs+erZo1a+rAgQOSpB9//FFVqlTRyy+/rJ07d8owDH3xxRf66quvdO3aNR0+fFizZ8+WJJ09e1aPPfaYFixYoLlz5+rWrVtasGCB1q5dq0cffVSenp7y8fFRgQIFFBYWpiNHjthdr7Ry5Ur997//TfA8hw8fLicnJy1evFivvfZaRr6sAAAgFQgWwAPAZrNp165dmjBhgt3j3bp1019//aVChQpJkjZt2qTly5fr2LFjCg4O1vnz51WyZEkZhpHgFsoXL15Ux44dNWnSJHXu3Fnu7u7y8fHRxIkT9dRTTyk0NFTdu3eXYRjq1auXgoKCVLVqVUnS4cOHdfXqVV27dk1ff/21GjVqZA4P7NGjh4KDg/Xpp5/qySeflLOzs5o2baqhQ4fKZrPJ3d1dZ8+e1bVr1/TYY4/p0Ucf1fTp0826PDw8En0NQkJC1KpVK3Xq1ElOTk4Z9toCAIDUIVgAOUxwcLB8fHzsftzd3VWnTh2dP3/erm3u3Lnl5uamUqVKSZL8/f01f/58BQQESJI5BEmSjh49arfvuHHj5O7urtatWyeo4cknn5QkHThwQAcPHpR0b2hexYoVJd2b5PHLL7+Ui4uLBg0apE2bNqly5coKDg7Wpk2bJElPPPGEXZ/du3c39/3111/ttjVq1EitWrWSdO/sxP2io6O1ePFivfHGG8m9dAAAwIEIFkAOExAQoLCwMLufO3fuaO3atfLz80t0n7i/8j/yyCMJtpUoUUKSEkyqt3TpUoWEhCQIMT4+Pvr222/l4eEhDw8Pu2sn3N3dJUn16tVL9MzCxYsXzeX7zyr4+vqad0Q7d+5cgn0/+ugjSdLixYvtjhlXq4eHh1q2bJno8wcAAI5HsAAeAG5ubmrWrJnee++9RLcnNaGjdO+shiTFxsaaj92+fVtnzpzR448/niDEhIWFKTw8XHfv3tXdu3fVqVMnc7+UhiCVKlXKbHP/xd/SvflgJKlw4cIJttWvX1/169dXbGysvvrqK7ttU6dOVe/evbm9MgAAWYhgATxAatSokSH9hIWFSZIuXLiQIf3FKVKkiNq1aydJmjlzpt22kydP6saNG8qbN2+SdzaLO2sxZ84cc9jXiRMntG3bNvXu3TtDawUAAGlDsAAeIK1atVKjRo0s95MnTx5J0vnz53Xq1Kkk28XdYjktfvzxR9WuXVvLli3T2LFjFR4ern/++Ue9evWSm5ubpk+fnuSQrlatWqlatWqKiorSmDFjJEnff/+9WrdunegtngEAQOYhWAAPqAsXLujSpUvp2tfX19e8k9S4ceOSbPfTTz9p/fr1aeo7f/78GjFihKpUqaIFCxaoePHiqlevngoVKqQdO3aoc+fOye4/ePBgSfcu4j5//rxmzpypPn36pKkGAACQ8QgWwAPqrbfeUq5cudK9f9xwpClTpmj+/PkJtoeEhGjMmDGqV69emvpdt26dunfvrs2bN2vv3r0KCwvThQsXtGjRIj3++OMp7v/cc8+pbNmyioiIUOvWreXl5aVmzZqlqQYAAJDxCBZADhEZGSnJ/iLrpIwfP14eHh7mXZZu3rxp10di7ty5Y7c+ePBgeXp6yjAMvfTSS3r11Ve1YcMG7du3TzNmzFC9evXUtWtXc9iUJMXExEiSrl+/nugxDMNQjx49FBMTo99//12HDh3SsWPHdPz4cZ06dUoXL15UdHR0ss8t7ha2knTw4EG99tpryV6cDgAAMgf/GgM5QHR0tHkXpZCQEAUHByfa7vz58/roo480cOBAvfLKK5LuzU9x+PBhSdLatWvNkCFJUVFRWrFihSTpypUr2rJli7mtfPnymj9/vnLlyiXDMDRr1iw9/fTTeuyxx9SzZ09Vq1ZNH3zwgdn+7Nmz5hwVGzdu1ObNmxPUFxsbq+joaF2/fl3PPvusqlatqkcffVSBgYEqV66c/P395ePjo1deeUWhoaFJvh7du3dX0aJF5ebmpp49e6bmJQQAAA5GsACysSNHjuirr75S/fr1dfnyZUn3zjqULl1a3t7e5rwS3t7ecnd3V4kSJfTVV1+pQIECatGihXr37q2KFSsqKipKkrRv3z75+vpq+PDhWrhwoTw9PbVkyRLzeI0aNVLjxo3N9WeffVZ79+5Vly5dVLBgQXl4eKhSpUqaOHGifvrpJ/NMQf/+/VWqVClznorbt2+rcePGCYZJubq6auvWrSpQoIAqV66sQoUKKXfu3HZnHCIiIjRnzhxz4r7EeHh46Nlnn1W7du1UpEgRi68yAADICE5GWm/pggfG9u3b1aBBA3N927Ztql+/fqr2vXv3rs6cOaPSpUtbGsePh0/fvn1VtWrVRC+4jo6O1tWrV7V8+XK9+eabCgsLU758+RK0i4mJUUBAgGbPnq2nn37ack18noGs4fb/hzUi9aJHj87qEvAQSet3Rc5YAMg0//3vf7Vhw4Yk7+Lk5uamokWLqk+fPipQoECSE+4tW7ZMnp6eatq0qSPLBQAAaUCwAJApwsLCNGjQIPOajeQsXbpUjz/+uPLmzZtgW9zM2++8806KM30DAIDM45rVBQB4OJw7d07h4eE6cOCAWrVqpY8++kh169aVq+v//Rq6fPmyZsyYocmTJ5sXf584cUK9e/eWh4eH2rZtq+3btyskJIS5KwAAyGYIFgAyRZUqVfTJJ5/oiy++0Jo1a7RmzRpz6JOHh4du3LihK1euqFq1atq0aZPKlCkjSVqxYoW2bt0q6d4cGHnz5tWmTZvk4eGRlU8HAADch6FQADLNyJEjtX//fr311lt69NFH5erqqkuXLikiIkJ169bV/Pnz9ddff6ls2bLmPl27dlWtWrWUN29etWnTRrt27VLNmjWz8FkAAIDEcMYCQKaqUqWKvvvuu1S3L1q0qHbt2uXAigBkFe5wBDxYOGMBAAAAwDKCBQAAAADLCBYAAAAALCNYAAAAALCMYAEAAADAMoIFLElpBmUgJ+BzDACAdQQLpIuz872PTmxsbBZXAlgXExMjSXJxccniSgAAyLkIFkgXNzc3ubm5KTw8PKtLASy7ceOGPDw85OrK1D4AAKQXwQLp4uTkpLx58+rGjRu6c+dOVpcDpFtoaKhu3bolHx8fOTk5ZXU5AADkWPx5Dunm5+enO3fuKDg4WPny5VPevHnl4uLClzNka4ZhyGaz6e7duwoPD1dERIR8fX3l6+ub1aUBAJCjESyQbi4uLipRooSuXr2qW7duKSwsLKtLAlLN2dlZefLkUbFixeTt7Z3V5QAAkOMRLGCJi4uLChcurEKFCik6Olo2my2rSwJS5OzsLFdXV/MmBAAAwDqCBTKEk5OT3N3ds7oMAAAAZBH+XAcAAADAMoIFAAAAAMsIFgAAAAAsI1gAAAAAsIxgAQAAAMAyggUAAAAAy7jdLPD/BQcHa/r06eZ6z549FRAQkIUVIbPxGQCfgYcb7z/4DFhDsAD+v+DgYH366afm+tNPP80vk4cMnwHwGXi48f6Dz4A1DIUCAAAAYBlnLB5i4eHhdusHDx7Mokqyh/uf/8P+ejyM+AyAz8DDjfcffAbs3f/87//ueD8nwzAMRxaE7Gvy5Mnq27dvVpcBAACAHOC7777TW2+9leR2hkIBAAAAsIxgAQAAAMAyrrF4iLVp08ZuvUyZMvLy8sqiagAAAJCdhIeH6/Tp0+b6/d8d78c1Fnhovfrqq5o1a1a69z9z5oxKlSqVcQUBAABJ/BudUzEUCgAAAIBlDIUCJPn6+qpWrVpp2id37twOqgYAAMTh3+icg2ABSKpatarWrFmT1WUgBWvXrtWPP/6ogwcPKigoKNE2Li4ucnd3V758+VSkSBFVqFBBzZs3V7du3eTu7p7JFSMrzZw5Uz169EhyW/fu3dPdd+/evfXjjz8muo0hGEljeAvSI6v/jeZ3SeoxFApAjtG8eXMtXLhQR48eTXAf7ddff11Tp07VokWL9P333+u5557TmTNn9PPPP6tnz54qXbq0tm7dmkWVIyu0b99ef/zxh8aNGydvb2+7bV9//bXSe4nhpUuXNHfuXLvHnnzySa1cuVKHDx+Wv79/umsGkP3wuyT1OGMBIMdxcnLSyy+/rMmTJ5uPffTRR3Z/2XnppZc0ZMgQtW7dWvv27dPFixfVrFkzLV++XM2aNcuCqpHZfHx8VLt2bdWuXVuenp564403zG1Hjx7VsmXL1L59+zT3O3HiREVGRprrzs7OWrlypfLnz58RZT80GN6CrFa5cmWNGjVK7dq1S7Ydv0tSj2ABIEfy8/NLsU3RokW1fPlyBQYGKiIiQpGRkerevbvOnDmjXLlyZUKVyC7q1KmT4LGvvvoqzV8Gbt26pSlTptg9VrBgwRz9RSCrZPXwFoZW4vDhwwoNDU3TPvwuSR5DoQDkSK6uqfu7SPHixdWlSxdz/dKlS1q9erWjykI2lS9fPklS7dq1zcd27dqlTZs2pamfKVOm6MaNG3ZfLgipORNDK5Ee/C5JHsECwAOvRo0adutJ/XUSDy4nJydJ9y4eLly4sPn4V199leo+oqKi9J///EeFCxe2dLEmHKty5cpatmxZqtvHDa2M76OPPtLrr7+uDh066KWXXtI333yjY8eOmb9L4oZW/vbbbxlaO7I/fpckj2AB4IEX9w9BnAfhr0JIn1y5cqlfv37m+m+//aa//vorVfvOmzdPFy5cUL9+/fgMZWPpGd6SlqGVefLkkSRzaOXdu3fTVSdyNn6XJI5gAeCBt3fvXrv1J554IosqQXbw1ltvmcMZpNT9pdEwDI0dO1Z58+ZNMGwGOR9DK5Ee/C5JiGAB4IF29uxZ/fzzz+b6448/rnr16mVhRchq3t7e6tOnj7n+v//9TydPnkx2nxUrVujIkSN64403EtxuEg8XhlYiDr9LEiJYAHhgBQcHq3Xr1oqIiJAkFSpUKME9w/Fwev/99+Xh4SFJstlsGj16dLLtR48eLXd3d73//vuZUR6yMYZWIj5+l9gjWAB4YNhsNl27dk1btmzRBx98oMqVK+vw4cOSpLZt2+rPP/9UYGBgFleJ7KBo0aJ2F+zOnj1b//77b6Jtd+zYod9//12vvPKKihYtmlklIptiaCXi43eJPYIFgAdCuXLl5OLiIj8/PzVq1Ejjx49XdHS0Pv/8cx09elTLly9XyZIlU+zn/PnzmjRpktq1a6dSpUrJw8NDXl5eqlSpkvr166fTp08nue/MmTPl5OSU4s/Vq1cz8qkjnQYNGiRn53v/DEZGRmr8+PGJtvv666/l7OysgQMHZmZ5yIYYWplzzJ49W66ursn+SFKvXr2SbVOuXLkUj8Xvkv/DBHkAHgjr1q1TwYIFdfToUXXp0kU2m013796Vr6+vKlSokKo+zp07p5IlS8owDPOxfPny6c6dOzpy5IiOHDmiadOmaebMmXr++eeT7MfZ2VkFCxZMdjuyXvny5dWhQwf973//kyRNnTpVQ4YMka+vr9nm2LFjWrFihTp27KhHHnkkq0pFNsDQypzl2Wef1f79+5NtU6VKFX322WfJzrydmokQ+V3yfwgWAB4I5cuXV/HixVW5cmXt3btXX3/9tSSpf//+evLJJxNccJmY2NhYSVLr1q316quvqnHjxipQoIBiYmK0Y8cOvfPOOzp48KC6deumChUqqGrVqon2U6JECf3zzz8Z9tzgOIMHDza/DNy6dUvfffedPvnkE3P7mDFjZBiGPvzww6wqEVnEZrMpNDRUf//9t5YvX65p06bp1q1bku4Nrfzmm29SdRYUWcPHx0c+Pj4ptvP391flypUtH4/fJffwZzMAD5yRI0fq8ccfl3TvtPTzzz+vmzdvprifr6+v9u3bp5UrV6pz584qUKCApHu3onzqqaf022+/qWDBgoqJidHEiRMd+RSQSR5//HE1adLEXJ80aZLu3Lkj6d4kaHPnzlXjxo0ZR59NZNbwlowaWikxvPJhwe+SezhjAeCB4+bmpvnz56tGjRq6ffu2Tp48qddee81ubHRivL29Va1atSS3Fy5cWK1atdKsWbMSXMCJnGvw4MHauHGjJCkkJEQ//vij3n77bU2cOFFRUVEaPHhwFleIOJk1vCUjhlZKDK982PC7hGAB4AFVvnx5TZo0Sb169ZIkLVy4UA0bNrQ8IVHcDL0xMTGWa0T28Mwzz+ixxx4zZ80dO3asunbtqqlTp6p69epq1qxZFleIOJk1vCUjhlZKDK982PC7hKFQAB5gPXv2tPsrYP/+/bVv3z5LfW7ZskWSkv3SEhISoscee0yenp7y9PTUI488otdff12HDh2ydGw4Tvxxz2fPnlWbNm108+bNB348NFKW3qGVEsMrH0YP++8SggWAHCn+0ILkTJ06VQEBAZLufSno3LmzQkND03XMX375RXv27JEk9ejRI8l2ERER2r9/vzw8PBQdHa0TJ05o2rRpqlGjhsaOHZuuY8OxOnfubDfu/o8//lCZMmX03HPPZWFVyA7ihlZ6enpKkjm0MjVSO7xSSjg/BnKmh/13CcECQI4Ud1FcUutxfHx8NHfuXHMM8unTp9WxY0dFRkam6Xhnz541v0y0a9dOLVq0SNCmWLFiGjZsmPbv36+IiAhdv35dt2/f1saNG1W7dm3FxsZq4MCBmj9/fpqODevu3r1r99/7OTs7a8CAAXaPDRgwQC4uLsn2l1yfeHDEDa2Ms3DhQk2ePDlD+mZ4Zc7C75LkESwA5Eh///233fqRI0eSbNugQQMNGTLEXN+8ebNatmypK1eupOpYYWFhatu2ra5evaoyZcpo+vTpibZr1qyZPv30U1WrVk25cuWSdO+vnY0bN9aWLVtUu3ZtSfdOldtstlQdGxnj2LFjkqTjx48n2ebVV19VkSJFJN2boyC5s1Jx/Un3hr6l9ywYcg5HDK2UGF6ZlTp16qRSpUqlaR9+l6TAAB5S3bt3NyQZkoyGDRtmdTlIhevXrxv79+83fvzxRyN//vzm+yfJCAgIMObNm2ccPHjQCAsLS7BvdHS0UadOHbt9PD09jT59+hg///yzcfny5USPeevWLXO/YsWKGadOnUp3/evXrzePvWfPnnT3g9QJCwszDhw4YMyePdsoUqSI+Z6PGzfO2L17t3Hx4sUE+3z55ZeGJGPUqFEJtl28eNHYtWuX8cUXXxju7u52n6WGDRsaq1evNo4cOWJERUVlxtPLkTLj964kY8aMGWna5/Tp03bv57lz5xJtFxoaagQEBJjtypQpY1y/fj3dtS5dutTsa/Xq1Qm2z5gxw9zu5ORk+Pr6Gm5ubuZjLi4uxpgxY9J9/OwsO/0bze+S1CNY4KE1duxYo3nz5kbz5s2NgQMHZnU5SIUJEybY/QJO6mfatGmJ7n/q1CkjX758ie4zYcKEBO1v375tPPXUU4Yko2DBgsaRI0cs1R8eHm4eb+HChZb6QsrifylL7Kd79+4J9gkLCzOKFy9uhIaGJtgW/4tOcj9nzpxx+HPLqbJrsDh8+LDde3j8+PEk227dutVwdnY22zZq1Mi4e/dumuv8559/DD8/P0OS0a5du0TbrF271hg2bJixf/9+486dO4ZhGEZUVJSxceNGo3bt2mYN8+bNS/Pxs7vs9G80v0tSj2ABAImIiIgwmjRpYkgyfH19jX379lnuM36wWLRokfUigRwmM4JFp06djE2bNqVpn59//tnuC90vv/ySbPtPPvnErn3jxo2TPOuZmNDQUKNKlSrmWY9r166lqV7DMIy7d++a4aJ48eJGbGxsmvsAMhrXWADAfSIjI9WhQwdt3LhR+fLl05o1a1S9enXL/e7atctcTuu4XgCps3jxYjVq1CjFdqGhoTpw4ICmT5+uN998027bu+++q/nz5+vQoUO6ceNGgn2HDx+uOnXqmOubNm1SmTJl9MYbb2jhwoXJXr8VHh6uli1b6tChQypWrJjWrVun/Pnzp/4J/n8eHh4aNWqUpHuze2fE9R6AVQQLAIgnOjpanTt31tq1a+Xp6alVq1apVq1aKe5npHD72+joaA0dOlTSvcm7HnvssQypF8hJqlSpoubNm6t58+ap+v/KkWbNmqXq1aurV69eun79ut224OBgdevWTVWrVtWiRYsS7Ovq6qp58+YpX7585mO3b9/W1KlT9cILLyR557eIiAi1bt1af/zxhwoWLKj169erTJky6X4O8cPN6dOn090PkFGcjJT+NQSAh0RsbKy6du2qRYsWKXfu3Pr111/VuHHjVO37zz//6IUXXlDv3r31zDPPmGckYmJi9Pvvv2vIkCHasWOHJGnu3Lnq1q2bo54GgGzozp07atOmjTZu3ChfX19t3LjR8pnQ27dvy8vLS5K0aNEide7cOQMqBdLPNasLAIDs4vfffzf/Ommz2dS1a9dk21+6dMlu/c8//9Sff/4pScqVK5e8vLx08+ZNRUVFSbp369nRo0cTKoCHDMMr8bAgWADA/xd/bonIyEhdvnw51fsWLlxYkyZN0o4dO7R//36FhIQoLCxMefLkUcWKFdW4cWO98cYbeuSRRxxROoBsysrwSicnp2T7ZXglshuGQgEAADgAwyvxsCFYAAAAOMDWrVvVsGFDSffu4uTj45Ns+/jDK//55x+VLl3aXE9ueOV7772X4bUD6cFQKAAAAAdgeCUeNpyxAAAAAGAZ81gAAAAAsIxgAQAAAMAyggUAAAAAywgWAAAAACwjWAAAAACwjGABAAAAwDKCBQAAAADLCBYAkM399ddfKl++vAoUKKA5c+ZkdTnZTt++feXl5aXGjRvr5s2bWV0OEnHnzh3NmDFDtWrVUqNGjbK6HAAOwgR5AJBGa9eu1Y8//qgDBw7o+PHjSbZzdXVVrly55OXlpaJFiyogIEA1atRQgwYN1LBhQ7m4uKTqeP369dOkSZMkSVWrVtWBAwcy5Hk8CEJDQ5U/f35zfcmSJerQoUMWVoT4zpw5o0mTJmnmzJkKCwuTJDVs2FCbN2/O0roAOIZrVhcAADlN8+bN1bx5c9lsNr311luaOnWqua1x48aqW7euypQpI19fX924cUPnz5/Xhg0b9Ouvv2rZsmWSpMKFC+vDDz/U22+/LTc3t2SP17NnT61evVpXr17VwIEDHfrcchpfX1+98847mj59up544gk1bdo0q0tCPOHh4WbQmzhxYtYWA8DhOGMBABb8/vvvql+/vrl+5swZlSpVKtG2wcHB+uSTT+yGM9WuXVtLlixRsWLFHF1qkgzDUIsWLbR27dosqyEpO3bs0G+//aYRI0ZkdSmw4N9//zU/45yxAB5cXGMBABYUKlQo1W0DAgI0e/ZszZw50zxLsWvXLjVp0kRXr151VIkp+vXXXxUUFJRlx0/O+PHjs7oEZABfX9+sLgFAJiBYAIAFKQ1jSkz37t3thk8FBQWpR48eGVlWqt29e1eDBw/OkmOnZOvWrVqyZElWl4EM4O7untUlAMgEBAsAyAI9evRQly5dzPWVK1dq6dKlmV5H3759dfjw4Uw/bkouXLigbt26idG6DwZnZ75uAA8D/k8HgCwyYsQIuy9cn3/+eaYdOzo6Wm+88YamT5+eacdMrRMnTqhJkyY6f/58VpcCAEgDggUAZJHAwEA1btzYXP/rr7909OjRBO3++ecfDR06VMWLF0/2IuaNGzeqRYsW8vX1lZOTk91Prly5zHZBQUGqWbOm3XCss2fP2rV/7733Ej3G9u3b9fLLLyswMFC5c+dW4cKF1aBBA40bN07h4eFJ1hYTE6Nly5apXbt25vCx2NhYffHFFypevLgKFiyoiRMnaurUqapZs6bdbXw//fRTu9r2799v1+8vv/yi1q1bp+r2vadOndLAgQNVrVo15cuXTz4+Pqpatao+/PBD/fPPP8nu+/fff+vtt9+Wt7e3efFxRESERo8erUqVKilPnjyqUKGCvvnmmxTrSOtxoqKiNGHCBFWvXl25c+dWqVKl9P777ys0NNRu3xUrVqhFixbKnz+/fHx81KpVq1TdnjgiIkI//vijGjdurGLFipnH6Nq1q1atWpXq2nfu3Knu3burVKlSypUrl4oWLarevXvrwoULqe5jw4YN6tq1q0qUKCEPDw8VKFBAzZo106JFi5I9g7V48WI99dRTyps3b4LPf4UKFVJ9fAAWGACAdDtz5owhyfw5c+ZMmvYfOXKk3f6TJk0yt/3yyy9Gs2bNDCcnJ3P78OHDE+1n8uTJhpOTk/HSSy8ZmzZtMv78809j+vTpRsWKFQ1JhoeHh9n25s2bxtGjR42jR48axYoVMyQZxYoVMx87evSocfnyZbv+b9y4YTz77LOGJKN79+7Gxo0bjT179hjjx483ChQoYEgyihcvbmzbts1uv3///df44IMPjEKFCtk9T5vNZrz00kt2j0ky/vnnH+Po0aPGrFmzzMf69u1rV9vdu3eNf//91/joo4+MIkWK2O2fFJvNZgwdOtRwcXEx6tevbyxdutTYt2+fMW/ePKNKlSqGJCN37tzGd999Z7dfTEyMMXfuXKN+/fp2x9m0aZNx/Phxo3LlygmegyRj1KhRqf0IpHicixcvGjVq1Ej0OHXq1DHu3r1rxMTEGL179060Tb58+YygoKAkj71t2zajePHiRoECBYzx48cbe/bsMTZs2GC8+uqrZh9t2rQxbt68mezr279/f8PJycmoXr26sXjxYmPfvn3GtGnTjBIlSti9Tw0bNky0jzt37hhdu3Y1SpUqZUybNs3Yv3+/8euvvxrNmzc3933uueeMqKioBPsOHjzYcHJyMt577z1j27Ztxo4dO4xvv/3WCAgIMCQZgYGBaXo/AKQPwQIALLAaLFavXm23f+/evc1t69atM7Zs2WI89thjyQaL4OBgw8PDw2jTpk2CbXfv3jUaNGhgFyziK1mypCHJKFmyZJI1RkZGGnXr1jUkGV988UWC7adOnTIKFy5sSDLy5MljbN++3dx2+fJlY9OmTcaCBQsMFxcXuwDVu3dv47fffjO/2BcqVMi4deuWYRiGsWnTpmSf84ULF4zffvvNmDBhQqqCxYABAwxJRqtWrYzo6Gi7bXfu3DEaNGhg9jFhwgRzm81mM1atWmUcPnzYKFu2rNlm2rRpRtmyZY2hQ4caBw4cME6cOGEMHz7c3O7p6WmEhIQkWc/9bDabMX36dGPJkiXmaynJmDVrllGjRg3js88+M44fP26EhYUZS5YsMcOcJGPmzJlG165djWeeecbYsGGDcf36dePo0aMJvpAn5s8//zRy585teHl5JRo+vv32W7sQc+fOnUT7efvttw1JRv369Y2IiAi7bZcvXzaKFy+ebLCw2WxGmzZtjBIlShhXr15NsK1bt27m/u+++67d9l27dhmSjLfffjtBv6GhocYjjzxCsAAyCcECACywGix2795tt3/btm0TtPniiy+S/ZI9ZcqURL9wxTl06JCRO3fuRLelJlgMGzbMkGSUKVMmwZfyOD///LNZY8mSJc2AEF/8v+63adPGiI2NNQzDMKKjo43169cb//77r9k2pWARx2azGR4eHskGiw0bNhiSDCcnJ+PUqVOJtjl9+rSRO3duQ5Lh6upq/PXXXwnavPXWW+ZxKlasaBw/fjxBm/hf5n/++eck605Ojx49zD7q169vXLhwIUGbH374wWyTN29euzAUJzQ01HB3dzcDX9zrHScyMtIoX768IckYNmxYkvW0bNnSPNYHH3yQYPvSpUvN1y2pz3/8ehMLFnGf4aVLlya6/7Fjx8z9XVxcjNOnT5vbBg8ebEgyxo8fn+i+v/76K8ECyCRcYwEAWcjb29tu/e7duwnapDQHwI0bNyRJCxcu1LVr1xJsr1y5surWrZuu+q5fv66xY8dKktq1aydXV9dE2z3//PPmOPazZ89q2rRpCdrky5fPXB49erR54bqrq6uaNm2qIkWKpLk+Jycn+fj4JNvm448/liRVr15dZcqUSbRN6dKl9corr0i6d91GYteyxH+v/vOf/6h8+fIJ2jz11FPmcnBwcErlJ6pgwYLm8siRIxOdPLFFixbm8mOPPZboNTE+Pj7mexIREZHgszF9+nSdOHFCktSpU6ck6xk2bJi5/O233+rff/8112NjY/X+++9Lklq1apXk5JCtW7dOsn/DMDR69Gi5ubnpmWeeSbRNyZIl7Y65cuVKcz3u8z9jxoxE//9p2bKlAgICkjw+gIxDsACALHTr1i279fz58ydok9IcAJUqVZIkXbp0SXXq1NG6desStFm/fn266ps/f74iIiIkSVWqVEm2bbdu3czluXPnJtgeP5Q8+uij6aonMcm9Pn///bf++OMPSSnX/9JLL5nLK1euVFhYmN32+HOWJBWw4k+YGPe6pZWHh0eKbVI7MWP8MHTnzh27bT/88IOke+Es7jOUmDp16qhs2bKSpMjISC1evNjctnz5cvOi9/ih6n7JhcYjR47o9OnTio6Olre3t1xdXRP8eHl52e0TF4ik//v8Hzp0SPXr19fu3bvt2jo5Oem3335L8vgAMg7BAgCy0PXr1+3WCxcunKBNSnMAtGzZUrVq1ZIknTx5Us2aNVPdunW1bNkyy/NAxA8kBQoUSLZtXA2StH//fkVFRdltd3JyslRLUpJ7fdJSf82aNc07S9lsNu3Zs8due2rqjx84bDZbiu0Tk5o5H1I7MWP8vuLXExoaqr1790q6d2YjpTtqxX9vd+3aZS7Hv2NUes8KxL3Ofn5+2r9/f5I/hw4dMn8+/PBDc/9XX33VPKOxd+9e1apVSy1btjTvqAUg8xAsACAL7du3z269Tp06ae7D2dlZa9asUceOHc3Hdu7cqfbt26ty5cpavXp1uuuL/5fhlL4oBwYG2rVNbFhWZktL/blz51aJEiXM9ZCQEIfVldVOnjxpLqcmAMV/b+O/LocOHTKX8+TJk65a4voLDw9XpUqVVLly5RR//P39zf09PT21bds2uzMma9asUePGjVW3bl27IATAsQgWAJCFtm7dai47OTmpSZMm6erH19dX//vf//Tbb7/ZhZMjR46oVatWeuONN9L1F/TY2FhzOaWgEH/YjZOTU4rXhmSGtNQv2T+HxIalPSjivy63bt1KcHbpfkm9LvHPuN0/rC+14o599+5dHTt2LF19lChRQlu2bNHPP/+sihUrmo/v3LlTTz75ZKZOPgk8zAgWAJBFzp8/r7Vr15rrHTt2THQoVFo888wz2rlzp9auXavq1aubj0+dOlWjRo1Kc3/xx8YfOXIk2bbxh9MUKVLEblK+rJKW+iX751C6dGmH1JQdxH9dbDabgoKCkm2f1Ovi6elpLsc/O5QW8YPKkiVLUmxvGEaSkxk+//zzOnTokH766SfzuhDDMDR06FDNmTMnXfUBSD2CBQBkka+++krR0dGS7g1nirt7UVpNnDgxwXjyZs2aac+ePRo+fLj52Lhx49J8zUX8u0lt3Lgx2bbxL3Zu06ZNmo7jKPHrP3DgQIpnLeKeQ/ny5fXII484srQsVapUKbu7TaX3vY378p6aPuLc/xmMf1H9f/7zH125ciXZ/ZctW6ZZs2aZ6++9957OnDljrjs7O+uFF17Q33//rddee818PO7uZgAch2ABAFlg9erVmjx5srk+dOhQ1ahRI939JfaXXhcXF40YMUIdOnSQdO+2nPdfNxB3QXJSgeOVV14x2+zfv1+HDx9Osobjx4/b7WdF/AulrVyA3qRJExUvXlzSveE/P/30U5JtIyMjzVvEWq0/J4j/HOfNm5ds27j3tmzZsnZhLf7tYbds2aIDBw6keNz7h13VqlVLfn5+ku5db9G5c+ckh1Vdu3ZN77//vtq1a2f3+NKlSxO0zZUrl6ZMmaKaNWtKSv8ZFQCpR7AAAAtiYmLSvM/GjRvVtWtX8wtz586d7eYKuF/cWY37l+ObMWOGzp8/n+i2xo0bS7p3ce391z3EXXB7/fp1uy/wcWdAKlSooJdfftl8PLH5HeL8+uuvkqQOHTqofv36CbbH7z+lW7HGvxD46tWrdvv9+eefdm2Te31cXV312WefmetjxozR7du3Ez3m2rVrFRMTo4CAAPXr1y/B9vh9p+Z6laTeq7Tsl9Rx4r+WyQWv+Nvur6d///7mnbJ2795tvn+J1RN3u9Zx48bZbXvppZfMUGAYhl555ZUEt+mV7M943H8nNDc3N7vXe9u2bapVq5ZWrFhh/v9lGIZ+/fVX1a9fX48++qjdMD9JmjBhgsLDwxMc19nZWQ0bNpQkM2ACcByCBQBYkJY7B0VERGjkyJFq0aKFOanXwIEDtXDhwmRvMXrx4kVz+cKFC4m2CQ8PV8eOHRN8aZOkHTt2SJK6d++e4DalcXdBCg8P19SpUxUTE6OFCxdq0aJFZpuJEyea804sXrxY33//fYJjHD16VFOnTlWxYsXszsTEFz8g3H8r1/vFvzvTL7/8ohMnTigsLEx9+vSx+4IcFRVl129ir0+PHj3UpUsXSfcm70vsQvbw8HB98skncnd3148//qi8efMm6Ofs2bPmclLDdeLPFZHUe5WS+O/3pUuXEm0T/3OX3PCu0NBQczn+xHbSvYn4Zs6cad4i97XXXkv02oUvvvhCV65cUc+ePROcKfD09LSbDPHgwYOqX7++tm3bZj62fft2NWrUyFw/fvy4fvzxR61YscIMw4MGDbK7pe2xY8f07LPPysfHR6VLl1aePHnUpk0bhYSE6L///W+CGs+fP6+uXbsmmKvDMAxzHpPevXsn+hoByECZPtc3ADwgbDab0atXL0OS+fPaa68Z06dPN7Zs2WIcPHjQ2LFjh7Fo0SLjzTffNAoXLmy2a9mypfHHH38k239wcLCxcuVKo2jRouZ+7u7uxtSpU42///7bbDdhwgRzu7+/vzFhwgRjz549xrZt24zXX3/dkGQ8/fTTRnh4eIJjTJ8+3a5+Jycno1y5csbly5ft2p0/f9544oknzHa9evUyNm7caPz111/Gf/7zH6NAgQJGzZo1jfPnz9vtd/fuXWPnzp3GwIED7Y5Trlw5Y+HChcapU6eSfP5PPfVUgtreffdds989e/YYb731ll2bpk2bGtu2bTOuXbtm11dUVJTRvXt3s13Dhg2NX375xdi/f7/x888/G1WqVDEKFixobNu2LUEdx44dM6ZPn27kyZPH3L9ixYrG2rVrzfpPnDhhrFy50ihbtqzZxsPDw5g+fbpx9OjRZN/n+Mf58ccfDXd3d7OP0qVLG6tWrTKOHz9uGIZhXLx40di0aZPRpk0bu+f98ccfG3/++adx+/Zt486dO8aePXuMjz/+2K7Nk08+aaxfv94ICQmxO+6iRYsMb29vQ5JRqFAhY+LEicbu3buNLVu2GD179jScnJyMjz76yLDZbEnW/uOPPxpubm52x8ufP79RsGBBw9vb21i3bl2C93/cuHHGjRs3zD6uX79uNGrUyK5d/B8/Pz/jzz//THDsfv36mW0CAwONadOmGfv27TM2bNhgdOjQwZBkvPTSS0Z0dHSq3gcA6UewAIA02rBhg/Hqq68aVapUSfJLUNxPnjx5jOLFixvVqlUzunTpYkyZMsU4ceJEqo7TqVOnJPstUKCA2S5+sIj/4+npadSrV8+YPn26ERsbm+gxbDab8fXXXxtFixY18ufPb7zyyivGxYsXE20bExNjzJgxw3jmmWeMwoULG66uroafn5/RrFkzY+bMmYl+cdu3b1+yr0/Tpk2TfP6XLl0yOnToYHh6ehrlypUzJk2aZH65PXToULL9fvPNN4n2uWHDBqNr165GQECA4e7ubnh5eRm1atUyRo0aZVy/fj3RfeKHhft/6tWrZxiGYQQGBibZpmTJkkk+x9Qep1KlSoZhGEbfvn2Tfd6bNm0ydu7cmWybL7/8MsGxL1y4YAwZMsSoUqWKkS9fPsPd3d0oW7as0atXL2PPnj2pqv/o0aNG9+7dDX9/f8Pd3d0oWbKk8fbbb5ufJ2dnZ6Nt27bGmjVrkgwpNpvNmDt3rvH0008b+fPnN9zd3Y1y5coZ/fv3N65cuZLoPvGDRfyffPnyGU8//bSxZMmSVNUPwDonw7A4LSsAAACAhx7XWAAAAACwjGABAAAAwDKCBQAAAADLCBYAAAAALCNYAAAAALCMYAEAAADAMoIFAAAAAMsIFgAAAAAsI1gAAAAAsIxgAQAAAMAyggUAAAAAywgWAAAAACwjWAAAAACwjGABAAAAwDKCBQAAAADLCBYAAAAALPt/b/5MwFnx7L0AAAAASUVORK5CYII=", "text/plain": ["<Figure size 800x650 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "import matplotlib as mpl\n", "import numpy as np\n", "\n", "# # Specify the full path to the Times New Roman font file\n", "# times_font_path = '/usr/share/fonts/truetype/msttcorefonts/Times_New_Roman.ttf'\n", "\n", "# # Setting font family to Times New Roman\n", "# mpl.rcParams['font.family'] = mpl.font_manager.FontProperties(fname=times_font_path).get_name()\n", "mpl.rcParams['font.family'] = 'Times New Roman'\n", "\n", "l_w=2.5\n", "f_s=25\n", "# Adjusting Matplotlib's default settings\n", "mpl.rcParams['axes.linewidth'] = l_w  # Thickness of the axis lines\n", "mpl.rcParams['xtick.major.width'] = l_w  # Thickness of major ticks on x-axis\n", "mpl.rcParams['ytick.major.width'] = l_w  # Thickness of major ticks on y-axis\n", "mpl.rcParams['xtick.minor.width'] = 0  # Thickness of minor ticks on x-axis\n", "# mpl.rcParams['ytick.minor.width'] = 1.0  # Thickness of minor ticks on y-axis\n", "# mpl.rcParams['xtick.major.size'] = 6  # Length of major ticks on x-axis\n", "# mpl.rcParams['ytick.major.size'] = 6  # Length of major ticks on y-axis\n", "mpl.rcParams['xtick.minor.size'] = 0  # Length of minor ticks on x-axis\n", "# mpl.rcParams['ytick.minor.size'] = 4  # Length of minor ticks on y-axis\n", "mpl.rcParams['xtick.labelsize'] = f_s  # Font size of x-axis tick labels\n", "mpl.rcParams['ytick.labelsize'] = f_s  # Font size of y-axis tick labels\n", "\n", "# Define your data\n", "categories = ['$\\Gamma$', 'R$_{25}$', 'M', '$\\Gamma$+R$_{25}$', '$\\Gamma$+M']\n", "values = [-219.516249999998, -201.014999999998, -113.788750000005, -266.318749999996,-227.231250000003]\n", "exp_values=[-200,-180,np.nan,-250,np.nan]\n", "\n", "# Define color gradient from light to dark\n", "colors = [ '#005151','#047575','#08a8a8', '#003232','#005145']\n", "\n", "# Create a bar plot\n", "plt.figure(figsize=(8, 6.5))  # Adjust the figure size as needed\n", "bars = plt.bar(categories, values, color='#047575', width=0.6, label='Energy')  # Adjust bar width here\n", "\n", "# Add scatter plot for experimental data\n", "# plt.scatter(categories, exp_values, color='red', marker='*', label='Experiment',s=500)\n", "\n", "# Add title and labels\n", "plt.xlabel('Distortion modes', fontsize=f_s)  # Set font size for X-axis label\n", "plt.ylabel('$\\Delta$E(meV)', fontsize=f_s)  # Set font size for Y-axis label\n", "\n", "# Add ticks on the upper and right sides\n", "plt.tick_params(axis='x', direction='in', which='both', bottom=True, top=True, labelbottom=True)\n", "plt.tick_params(axis='y', direction='in', which='both', left=True, right=True, labelleft=True)\n", "\n", "# Add legend\n", "plt.legend(loc='lower left', fontsize='20')\n", "\n", "\n", "# Save the plot in high-resolution EPS format\n", "# savepath='/home/<USER>/Documents/Work/Parameter/BiAlO/Production_data/energy_bar_without_exp.eps'\n", "# plt.savefig(savepath, format='eps', dpi=600)\n", "\n", "# Show the plot\n", "plt.tight_layout()  # Adjust layout to prevent clipping of labels\n", "plt.show()\n"]}], "metadata": {"kernelspec": {"display_name": "work", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}}, "nbformat": 4, "nbformat_minor": 2}