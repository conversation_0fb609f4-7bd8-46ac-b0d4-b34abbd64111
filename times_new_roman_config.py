"""
Times New Roman Configuration for Matplotlib
============================================

This script configures matplotlib to use Times New Roman exclusively.
It will fail hard if Times New Roman is not available, ensuring no silent fallbacks.

Usage:
    Place this code at the top of your notebook, before any plotting imports.
"""

import matplotlib as mpl
from matplotlib import font_manager

def configure_times_new_roman():
    """
    Configure matplotlib to use Times New Roman exclusively.

    Raises:
        ValueError: If Times New Roman is not found on the system
    """

    # First, try to register the system Times New Roman fonts explicitly
    import os
    import glob

    # Common system paths for Times New Roman
    system_font_paths = [
        "/usr/share/fonts/truetype/msttcorefonts/Times_New_Roman.ttf",
        "/usr/share/fonts/truetype/msttcorefonts/Times_New_Roman_Bold.ttf",
        "/usr/share/fonts/truetype/msttcorefonts/Times_New_Roman_Italic.ttf",
        "/usr/share/fonts/truetype/msttcorefonts/Times_New_Roman_Bold_Italic.ttf",
        "/usr/share/fonts/truetype/msttcorefonts/times.ttf",
        "/usr/share/fonts/truetype/msttcorefonts/timesbd.ttf",
        "/usr/share/fonts/truetype/msttcorefonts/timesi.ttf",
        "/usr/share/fonts/truetype/msttcorefonts/timesbi.ttf"
    ]

    # Register any Times New Roman fonts we find
    fonts_found = []
    for font_path in system_font_paths:
        if os.path.exists(font_path):
            try:
                font_manager.fontManager.addfont(font_path)
                fonts_found.append(font_path)
                print(f"✓ Registered: {os.path.basename(font_path)}")
            except Exception as e:
                print(f"⚠ Could not register {font_path}: {e}")

    if not fonts_found:
        print("✗ No Times New Roman fonts found in system paths!")
        print("Install with: sudo apt install ttf-mscorefonts-installer")
        print("Then run: fc-cache -f -v")
        raise ValueError("Times New Roman fonts not found")

    # Now test if Times New Roman is available (fail hard if not)
    try:
        font_path = font_manager.findfont("Times New Roman", fallback_to_default=False)
        print(f"✓ Times New Roman resolved to: {font_path}")
    except ValueError as e:
        print("✗ Times New Roman still not found after registration!")
        print("This might be a matplotlib font cache issue.")
        print("Try restarting your Python kernel/notebook.")
        raise e
    
    # Configure matplotlib to use Times New Roman exclusively
    mpl.rcParams.update({
        "font.family": "Times New Roman",
        "font.serif": ["Times New Roman"],           # Only Times New Roman for serif
        "font.sans-serif": ["Times New Roman"],      # Use TNR even for sans-serif
        "font.cursive": ["Times New Roman"],         # Use TNR for cursive
        "font.fantasy": ["Times New Roman"],         # Use TNR for fantasy
        "font.monospace": ["Times New Roman"],       # Optional: use TNR for monospace too
        
        # Math text configuration (for equations)
        "mathtext.fontset": "custom",
        "mathtext.rm": "Times New Roman",
        "mathtext.it": "Times New Roman:italic",
        "mathtext.bf": "Times New Roman:bold",
        "mathtext.cal": "Times New Roman:italic",
        "mathtext.sf": "Times New Roman",
        "mathtext.tt": "Times New Roman",
        
        # Additional settings for consistency
        "axes.unicode_minus": False,  # Use ASCII minus instead of Unicode
        "font.size": 12,              # Default font size
    })
    
    print("✓ Matplotlib configured to use Times New Roman exclusively")
    print("✓ No fallback fonts will be used")
    
    # Verify the configuration
    test_font = mpl.rcParams['font.family']
    print(f"✓ Current font family: {test_font}")

# Run the configuration
if __name__ == "__main__":
    configure_times_new_roman()
