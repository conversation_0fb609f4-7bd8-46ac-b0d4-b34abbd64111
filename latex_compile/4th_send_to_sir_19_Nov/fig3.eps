%!PS-Adobe-3.0 EPSF-3.0
%%Title: Fig3.eps
%%Creator: Matplotlib v3.7.1, https://matplotlib.org/
%%CreationDate: Thu Oct 17 12:49:43 2024
%%Orientation: portrait
%%BoundingBox: -378 -186 991 979
%%HiResBoundingBox: -378.430487 -186.458070 990.430487 978.458070
%%EndComments
%%BeginProlog
/mpldict 12 dict def
mpldict begin
/_d { bind def } bind def
/m { moveto } _d
/l { lineto } _d
/r { rlineto } _d
/c { curveto } _d
/cl { closepath } _d
/ce { closepath eofill } _d
/box {
      m
      1 index 0 r
      0 exch r
      neg 0 r
      cl
    } _d
/clipbox {
      box
      clip
      newpath
    } _d
/sc { setcachedevice } _d
%!PS-TrueTypeFont-1.0-2.53740
%%Title: unknown
%%Creator: Converted from TrueType to type 42 by PPR
15 dict begin
/FontName /TimesNewRomanPSMT def
/PaintType 0 def
/FontMatrix[1 0 0 1 0 0]def
/FontBBox[-568 -307 2028 1007]def
/FontType 42 def
/Encoding StandardEncoding def
/FontInfo 10 dict dup begin
/FamilyName (unknown) def
/FullName (unknown) def
/Weight (unknown) def
/Version (unknown) def
/ItalicAngle 0.0 def
/isFixedPitch false def
/UnderlinePosition -223 def
/UnderlineThickness 100 def
end readonly def
/sfnts[<00010000000900800003001063767420F34DDA810000009C000007C46670676D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>]def
/CharStrings 48 dict dup begin
/.notdef 0 def
/minus 46 def
/space 3 def
/parenleft 4 def
/parenright 5 def
/comma 6 def
/period 7 def
/slash 8 def
/zero 9 def
/one 10 def
/two 11 def
/three 12 def
/four 13 def
/five 14 def
/six 15 def
/seven 16 def
/eight 17 def
/nine 18 def
/eta 47 def
/mu 48 def
/less 19 def
/greater 20 def
/C 21 def
/E 22 def
/F 23 def
/omega 49 def
/M 24 def
/P 25 def
/S 26 def
/V 27 def
/a 28 def
/b 29 def
/c 30 def
/d 31 def
/e 32 def
/f 33 def
/g 34 def
/h 35 def
/i 36 def
/l 37 def
/m 38 def
/n 39 def
/o 40 def
/r 41 def
/t 42 def
/x 43 def
/y 44 def
/z 45 def
end readonly def

systemdict/resourcestatus known
 {42 /FontType resourcestatus
   {pop pop false}{true}ifelse}
 {true}ifelse
{/TrueDict where{pop}{(%%[ Error: no TrueType rasterizer ]%%)= flush}ifelse
/FontType 3 def
 /TrueState 271 string def
 TrueDict begin sfnts save
 72 0 matrix defaultmatrix dtransform dup
 mul exch dup mul add sqrt cvi 0 72 matrix
 defaultmatrix dtransform dup mul exch dup
 mul add sqrt cvi 3 -1 roll restore
 TrueState initer end
 /BuildGlyph{exch begin
  CharStrings dup 2 index known
    {exch}{exch pop /.notdef}ifelse
  get dup xcheck
    {currentdict systemdict begin begin exec end end}
    {TrueDict begin /bander load cvlit exch TrueState render end}
    ifelse
 end}bind def
 /BuildChar{
  1 index /Encoding get exch get
  1 index /BuildGlyph get exec
 }bind def
}if

FontName currentdict end definefont pop
%!PS-TrueTypeFont-1.0-2.53740
%%Title: unknown
%%Creator: Converted from TrueType to type 42 by PPR
15 dict begin
/FontName /TimesNewRomanPS-BoldMT def
/PaintType 0 def
/FontMatrix[1 0 0 1 0 0]def
/FontBBox[-558 -307 2034 1026]def
/FontType 42 def
/Encoding StandardEncoding def
/FontInfo 10 dict dup begin
/FamilyName (unknown) def
/FullName (unknown) def
/Weight (unknown) def
/Version (unknown) def
/ItalicAngle 0.0 def
/isFixedPitch false def
/UnderlinePosition -223 def
/UnderlineThickness 195 def
end readonly def
/sfnts[<00010000000900800003001063767420610EE8E40000009C000008086670676D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>]def
/CharStrings 7 dict dup begin
/.notdef 0 def
/space 3 def
/E 6 def
/zero 4 def
/one 5 def
/bracketleft 7 def
/bracketright 8 def
end readonly def

systemdict/resourcestatus known
 {42 /FontType resourcestatus
   {pop pop false}{true}ifelse}
 {true}ifelse
{/TrueDict where{pop}{(%%[ Error: no TrueType rasterizer ]%%)= flush}ifelse
/FontType 3 def
 /TrueState 271 string def
 TrueDict begin sfnts save
 72 0 matrix defaultmatrix dtransform dup
 mul exch dup mul add sqrt cvi 0 72 matrix
 defaultmatrix dtransform dup mul exch dup
 mul add sqrt cvi 3 -1 roll restore
 TrueState initer end
 /BuildGlyph{exch begin
  CharStrings dup 2 index known
    {exch}{exch pop /.notdef}ifelse
  get dup xcheck
    {currentdict systemdict begin begin exec end end}
    {TrueDict begin /bander load cvlit exch TrueState render end}
    ifelse
 end}bind def
 /BuildChar{
  1 index /Encoding get exch get
  1 index /BuildGlyph get exec
 }bind def
}if

FontName currentdict end definefont pop
end
%%EndProlog
mpldict begin
-378.43 -186.458 translate
1368.861 1164.916 0 0 clipbox
gsave
0 0 m
1368.860975 0 l
1368.860975 1164.91614 l
0 1164.91614 l
cl
1.000 setgray
fill
grestore
gsave
103.45 867.24625 m
429.392975 867.24625 l
429.392975 1124.29925 l
103.45 1124.29925 l
cl
1.000 setgray
fill
grestore
2.500 setlinewidth
1 setlinejoin
0 setlinecap
[] 0 setdash
0.000 setgray
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
121.558 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
121.558 1124.3 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

108.261 839.887 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /four glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
157.774 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
157.774 1124.3 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

144.477 839.887 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /three glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
193.99 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
193.99 1124.3 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

180.693 839.887 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /two glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
230.206 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
230.206 1124.3 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

216.909 839.887 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /one glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
266.421 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
266.421 1124.3 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

260.171 839.887 translate
0 rotate
0 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
302.637 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
302.637 1124.3 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

296.387 839.887 translate
0 rotate
0 0 m /one glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
338.853 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
338.853 1124.3 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

332.603 839.887 translate
0 rotate
0 0 m /two glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
375.069 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
375.069 1124.3 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

368.819 839.887 translate
0 rotate
0 0 m /three glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
411.285 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
411.285 1124.3 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

405.035 839.887 translate
0 rotate
0 0 m /four glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
107.072 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
107.072 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
114.315 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
114.315 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
128.801 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
128.801 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
136.044 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
136.044 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
143.287 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
143.287 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
150.531 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
150.531 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
165.017 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
165.017 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
172.26 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
172.26 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
179.503 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
179.503 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
186.747 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
186.747 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
201.233 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
201.233 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
208.476 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
208.476 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
215.719 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
215.719 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
222.962 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
222.962 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
237.449 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
237.449 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
244.692 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
244.692 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
251.935 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
251.935 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
259.178 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
259.178 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
273.665 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
273.665 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
280.908 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
280.908 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
288.151 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
288.151 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
295.394 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
295.394 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
309.881 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
309.881 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
317.124 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
317.124 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
324.367 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
324.367 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
331.61 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
331.61 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
346.096 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
346.096 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
353.34 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
353.34 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
360.583 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
360.583 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
367.826 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
367.826 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
382.312 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
382.312 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
389.556 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
389.556 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
396.799 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
396.799 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
404.042 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
404.042 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
418.528 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
418.528 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
425.771 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
425.771 1124.3 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

148.375 813.184 translate
0 rotate
0 0 m /E glyphshow
15.271 0 m /l glyphshow
22.2168 0 m /e glyphshow
33.313 0 m /c glyphshow
44.4092 0 m /t glyphshow
51.355 0 m /r glyphshow
59.6802 0 m /i glyphshow
66.626 0 m /c glyphshow
77.7222 0 m /space glyphshow
83.9722 0 m /F glyphshow
97.876 0 m /i glyphshow
104.822 0 m /e glyphshow
115.918 0 m /l glyphshow
122.864 0 m /d glyphshow
135.364 0 m /space glyphshow
141.614 0 m /parenleft glyphshow
149.939 0 m /M glyphshow
172.168 0 m /V glyphshow
190.222 0 m /slash glyphshow
197.168 0 m /c glyphshow
208.264 0 m /m glyphshow
227.71 0 m /parenright glyphshow
grestore
2.500 setlinewidth
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 874.564 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
429.393 874.564 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

54.3563 865.884 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /six glyphshow
26.5991 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 914.96 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
429.393 914.96 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

54.3563 906.28 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /four glyphshow
26.5991 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 955.356 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
429.393 955.356 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

54.3563 946.676 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /two glyphshow
26.5991 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 995.752 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
429.393 995.752 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

80.95 987.072 translate
0 rotate
0 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 1036.15 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
429.393 1036.15 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

68.45 1027.47 translate
0 rotate
0 0 m /two glyphshow
12.5 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 1076.54 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
429.393 1076.54 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

68.45 1067.86 translate
0 rotate
0 0 m /four glyphshow
12.5 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 1116.94 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
429.393 1116.94 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

68.45 1108.26 translate
0 rotate
0 0 m /six glyphshow
12.5 0 m /zero glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 884.663 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
429.393 884.663 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 894.762 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
429.393 894.762 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 904.861 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
429.393 904.861 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 925.059 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
429.393 925.059 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 935.158 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
429.393 935.158 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 945.257 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
429.393 945.257 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 965.455 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
429.393 965.455 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 975.554 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
429.393 975.554 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 985.653 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
429.393 985.653 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 1005.85 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
429.393 1005.85 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 1015.95 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
429.393 1015.95 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 1026.05 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
429.393 1026.05 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 1046.25 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
429.393 1046.25 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 1056.35 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
429.393 1056.35 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 1066.44 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
429.393 1066.44 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 1086.64 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
429.393 1086.64 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 1096.74 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
429.393 1096.74 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 1106.84 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
429.393 1106.84 o
grestore
gsave
44.3562 884.773 translate
90 rotate
/TimesNewRomanPSMT 25.0 selectfont
0 0.148438 moveto
/P glyphshow
13.9038 0.148438 moveto
/o glyphshow
26.4038 0.148438 moveto
/l glyphshow
33.3496 0.148438 moveto
/a glyphshow
44.4458 0.148438 moveto
/r glyphshow
52.771 0.148438 moveto
/i glyphshow
59.7168 0.148438 moveto
/z glyphshow
70.813 0.148438 moveto
/a glyphshow
81.9092 0.148438 moveto
/t glyphshow
88.855 0.148438 moveto
/i glyphshow
95.8008 0.148438 moveto
/o glyphshow
108.301 0.148438 moveto
/n glyphshow
120.801 0.148438 moveto
/space glyphshow
127.051 0.148438 moveto
/parenleft glyphshow
135.376 0.148438 moveto
/mu glyphshow
148.779 0.148438 moveto
/C glyphshow
165.454 0.148438 moveto
/slash glyphshow
172.4 0.148438 moveto
/c glyphshow
183.496 0.148438 moveto
/m glyphshow
/TimesNewRomanPSMT 17.5 selectfont
203.318 15.1766 moveto
/two glyphshow
/TimesNewRomanPSMT 25.0 selectfont
213.141 0.148438 moveto
/parenright glyphshow
grestore
2 setlinecap
0.749 0.212 0.047 setrgbcolor
gsave
325.943 257.053 103.45 867.246 clipbox
404.041855 1111.795899 m
396.798677 1110.943451 l
389.5555 1110.168772 l
382.312323 1109.318643 l
375.069146 1108.457351 l
367.825969 1107.58756 l
360.582791 1106.687333 l
353.339614 1105.818844 l
346.096437 1104.88023 l
338.85326 1103.903231 l
331.610082 1102.927451 l
324.366905 1101.90444 l
317.123728 1100.876266 l
309.880551 1099.764203 l
302.637374 1098.785739 l
295.394196 1097.597147 l
288.151019 1096.42173 l
280.907842 1095.219008 l
273.664665 1093.919019 l
266.421487 1092.586967 l
259.17831 1091.2148 l
251.935133 1089.751649 l
244.691956 1088.233093 l
237.448779 1086.642523 l
230.205601 1084.978902 l
222.962424 1083.148255 l
215.719247 1081.105406 l
208.47607 1078.967648 l
201.232892 1076.447086 l
193.989715 1073.508564 l
186.746538 1070.084135 l
179.503361 1065.404202 l
172.260184 884.837225 l
165.017006 883.932383 l
157.773829 883.048584 l
150.530652 882.131949 l
143.287475 881.325431 l
136.044297 880.522897 l
128.80112 879.729574 l
121.557943 878.930477 l
128.80112 879.697471 l
136.044297 880.548048 l
143.287475 881.366115 l
150.530652 882.131766 l
157.773829 883.018351 l
165.017006 883.87826 l
172.260184 884.80258 l
179.503361 885.734769 l
186.746538 886.691518 l
193.989715 887.598353 l
201.232892 888.552398 l
208.47607 889.579211 l
215.719247 890.599598 l
222.962424 891.626065 l
230.205601 892.753926 l
237.448779 893.900817 l
244.691956 895.053117 l
251.935133 896.261349 l
259.17831 897.581914 l
266.421487 898.846241 l
273.664665 900.250593 l
280.907842 901.628921 l
288.151019 903.1698 l
295.394196 904.778994 l
302.637374 906.513228 l
309.880551 908.392996 l
317.123728 910.406445 l
324.366905 912.619755 l
331.610082 915.042685 l
338.85326 917.940869 l
346.096437 921.460308 l
353.339614 926.088517 l
360.582791 1106.695608 l
367.825969 1107.613666 l
375.069146 1108.453244 l
382.312323 1109.287474 l
389.5555 1110.159481 l
396.798677 1110.920619 l
404.041855 1111.784859 l
411.285032 1112.615023 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
0 setlinecap
gsave
325.943 257.053 103.45 867.246 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

0 2 m
-2 -2 l
2 -2 l
cl

gsave
0.749 0.212 0.047 setrgbcolor
fill
grestore
stroke
grestore
} bind def
404.042 1111.8 o
396.799 1110.94 o
389.556 1110.17 o
382.312 1109.32 o
375.069 1108.46 o
367.826 1107.59 o
360.583 1106.69 o
353.34 1105.82 o
346.096 1104.88 o
338.853 1103.9 o
331.61 1102.93 o
324.367 1101.9 o
317.124 1100.88 o
309.881 1099.76 o
302.637 1098.79 o
295.394 1097.6 o
288.151 1096.42 o
280.908 1095.22 o
273.665 1093.92 o
266.421 1092.59 o
259.178 1091.21 o
251.935 1089.75 o
244.692 1088.23 o
237.449 1086.64 o
230.206 1084.98 o
222.962 1083.15 o
215.719 1081.11 o
208.476 1078.97 o
201.233 1076.45 o
193.99 1073.51 o
186.747 1070.08 o
179.503 1065.4 o
172.26 884.837 o
165.017 883.932 o
157.774 883.049 o
150.531 882.132 o
143.287 881.325 o
136.044 880.523 o
128.801 879.73 o
121.558 878.93 o
128.801 879.697 o
136.044 880.548 o
143.287 881.366 o
150.531 882.132 o
157.774 883.018 o
165.017 883.878 o
172.26 884.803 o
179.503 885.735 o
186.747 886.692 o
193.99 887.598 o
201.233 888.552 o
208.476 889.579 o
215.719 890.6 o
222.962 891.626 o
230.206 892.754 o
237.449 893.901 o
244.692 895.053 o
251.935 896.261 o
259.178 897.582 o
266.421 898.846 o
273.665 900.251 o
280.908 901.629 o
288.151 903.17 o
295.394 904.779 o
302.637 906.513 o
309.881 908.393 o
317.124 910.406 o
324.367 912.62 o
331.61 915.043 o
338.853 917.941 o
346.096 921.46 o
353.34 926.089 o
360.583 1106.7 o
367.826 1107.61 o
375.069 1108.45 o
382.312 1109.29 o
389.556 1110.16 o
396.799 1110.92 o
404.042 1111.78 o
411.285 1112.62 o
grestore
1.500 setlinewidth
1 setlinejoin
[5.55 2.4] 0 setdash
0.847 0.263 0.082 setrgbcolor
gsave
325.943 257.053 103.45 867.246 clipbox
404.041855 1111.79657 m
396.798677 1111.012376 l
389.5555 1110.136343 l
382.312323 1109.323644 l
375.069146 1108.420103 l
367.825969 1107.579529 l
360.582791 1106.771852 l
353.339614 1105.770922 l
346.096437 1104.898671 l
338.85326 1103.895281 l
331.610082 1102.930175 l
324.366905 1101.942989 l
317.123728 1100.881816 l
309.880551 1099.847705 l
302.637374 1098.746499 l
295.394196 1097.623843 l
288.151019 1096.386231 l
280.907842 1095.254609 l
273.664665 1093.914993 l
266.421487 1092.630253 l
259.17831 1091.280979 l
251.935133 1089.820492 l
244.691956 1088.280731 l
237.448779 1086.602734 l
230.205601 1084.922176 l
222.962424 1083.080855 l
215.719247 1081.129092 l
208.47607 1078.889778 l
201.232892 1076.471016 l
193.989715 1073.632892 l
186.746538 1070.176237 l
179.503361 1065.406601 l
172.260184 884.795993 l
165.017006 883.909652 l
157.773829 883.032197 l
150.530652 882.220595 l
143.287475 881.355074 l
136.044297 880.549064 l
128.80112 879.741347 l
121.557943 878.943673 l
128.80112 879.764931 l
136.044297 880.552012 l
143.287475 881.372112 l
150.530652 882.196299 l
157.773829 883.031587 l
165.017006 883.887247 l
172.260184 884.810266 l
179.503361 885.724034 l
186.746538 886.625623 l
193.989715 887.599512 l
201.232892 888.551666 l
208.47607 889.626543 l
215.719247 890.636907 l
222.962424 891.675613 l
230.205601 892.735607 l
237.448779 893.913362 l
244.691956 895.062185 l
251.935133 896.237683 l
259.17831 897.561501 l
266.421487 898.795331 l
273.664665 900.229367 l
280.907842 901.699085 l
288.151019 903.264098 l
295.394196 904.862415 l
302.637374 906.416876 l
309.880551 908.388523 l
317.123728 910.356775 l
324.366905 912.620691 l
331.610082 915.079791 l
338.85326 917.839373 l
346.096437 921.405413 l
353.339614 926.086281 l
360.582791 1106.656937 l
367.825969 1107.596465 l
375.069146 1108.489536 l
382.312323 1109.334664 l
389.5555 1110.176702 l
396.798677 1110.960245 l
404.041855 1111.761762 l
411.285032 1112.544838 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
325.943 257.053 103.45 867.246 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-4 -4 m
4 -4 l
4 4 l
-4 4 l
cl

gsave
grestore
stroke
grestore
} bind def
404.042 1111.8 o
396.799 1111.01 o
389.556 1110.14 o
382.312 1109.32 o
375.069 1108.42 o
367.826 1107.58 o
360.583 1106.77 o
353.34 1105.77 o
346.096 1104.9 o
338.853 1103.9 o
331.61 1102.93 o
324.367 1101.94 o
317.124 1100.88 o
309.881 1099.85 o
302.637 1098.75 o
295.394 1097.62 o
288.151 1096.39 o
280.908 1095.25 o
273.665 1093.91 o
266.421 1092.63 o
259.178 1091.28 o
251.935 1089.82 o
244.692 1088.28 o
237.449 1086.6 o
230.206 1084.92 o
222.962 1083.08 o
215.719 1081.13 o
208.476 1078.89 o
201.233 1076.47 o
193.99 1073.63 o
186.747 1070.18 o
179.503 1065.41 o
172.26 884.796 o
165.017 883.91 o
157.774 883.032 o
150.531 882.221 o
143.287 881.355 o
136.044 880.549 o
128.801 879.741 o
121.558 878.944 o
128.801 879.765 o
136.044 880.552 o
143.287 881.372 o
150.531 882.196 o
157.774 883.032 o
165.017 883.887 o
172.26 884.81 o
179.503 885.724 o
186.747 886.626 o
193.99 887.6 o
201.233 888.552 o
208.476 889.627 o
215.719 890.637 o
222.962 891.676 o
230.206 892.736 o
237.449 893.913 o
244.692 895.062 o
251.935 896.238 o
259.178 897.562 o
266.421 898.795 o
273.665 900.229 o
280.908 901.699 o
288.151 903.264 o
295.394 904.862 o
302.637 906.417 o
309.881 908.389 o
317.124 910.357 o
324.367 912.621 o
331.61 915.08 o
338.853 917.839 o
346.096 921.405 o
353.34 926.086 o
360.583 1106.66 o
367.826 1107.6 o
375.069 1108.49 o
382.312 1109.33 o
389.556 1110.18 o
396.799 1110.96 o
404.042 1111.76 o
411.285 1112.54 o
grestore
1.500 setlinewidth
1 setlinejoin
[1.5 2.475] 0 setdash
0.937 0.424 0.000 setrgbcolor
gsave
325.943 257.053 103.45 867.246 clipbox
404.041855 1111.752613 m
396.798677 1110.961506 l
389.5555 1110.16617 l
382.312323 1109.294204 l
375.069146 1108.479655 l
367.825969 1107.589736 l
360.582791 1106.662447 l
353.339614 1105.823967 l
346.096437 1104.902412 l
338.85326 1103.983236 l
331.610082 1102.929626 l
324.366905 1101.91731 l
317.123728 1100.900257 l
309.880551 1099.836807 l
302.637374 1098.691786 l
295.394196 1097.611339 l
288.151019 1096.454708 l
280.907842 1095.189344 l
273.664665 1093.934979 l
266.421487 1092.602541 l
259.17831 1091.236026 l
251.935133 1089.758317 l
244.691956 1088.25987 l
237.448779 1086.706629 l
230.205601 1084.925937 l
222.962424 1083.100739 l
215.719247 1081.083 l
208.47607 1078.909601 l
201.232892 1076.486956 l
193.989715 1073.600423 l
186.746538 1070.103531 l
179.503361 1065.414429 l
172.260184 884.788734 l
165.017006 883.92732 l
157.773829 883.036121 l
150.530652 882.240256 l
143.287475 881.38606 l
136.044297 880.577732 l
128.80112 879.690233 l
121.557943 878.981307 l
128.80112 879.730937 l
136.044297 880.536764 l
143.287475 881.334194 l
150.530652 882.187882 l
157.773829 883.02876 l
165.017006 883.900828 l
172.260184 884.764133 l
179.503361 885.687355 l
186.746538 886.62603 l
193.989715 887.577818 l
201.232892 888.561913 l
208.47607 889.580715 l
215.719247 890.617836 l
222.962424 891.677301 l
230.205601 892.739328 l
237.448779 893.911939 l
244.691956 895.125762 l
251.935133 896.278245 l
259.17831 897.556093 l
266.421487 898.878467 l
273.664665 900.240041 l
280.907842 901.704738 l
288.151019 903.237667 l
295.394196 904.80451 l
302.637374 906.549886 l
309.880551 908.406537 l
317.123728 910.37117 l
324.366905 912.625814 l
331.610082 915.118685 l
338.85326 917.955975 l
346.096437 921.348301 l
353.339614 926.09177 l
360.582791 1106.681904 l
367.825969 1107.559807 l
375.069146 1108.473006 l
382.312323 1109.3013 l
389.5555 1110.121623 l
396.798677 1110.995521 l
404.041855 1111.818731 l
411.285032 1112.448282 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
325.943 257.053 103.45 867.246 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
404.042 1111.75 o
396.799 1110.96 o
389.556 1110.17 o
382.312 1109.29 o
375.069 1108.48 o
367.826 1107.59 o
360.583 1106.66 o
353.34 1105.82 o
346.096 1104.9 o
338.853 1103.98 o
331.61 1102.93 o
324.367 1101.92 o
317.124 1100.9 o
309.881 1099.84 o
302.637 1098.69 o
295.394 1097.61 o
288.151 1096.45 o
280.908 1095.19 o
273.665 1093.93 o
266.421 1092.6 o
259.178 1091.24 o
251.935 1089.76 o
244.692 1088.26 o
237.449 1086.71 o
230.206 1084.93 o
222.962 1083.1 o
215.719 1081.08 o
208.476 1078.91 o
201.233 1076.49 o
193.99 1073.6 o
186.747 1070.1 o
179.503 1065.41 o
172.26 884.789 o
165.017 883.927 o
157.774 883.036 o
150.531 882.24 o
143.287 881.386 o
136.044 880.578 o
128.801 879.69 o
121.558 878.981 o
128.801 879.731 o
136.044 880.537 o
143.287 881.334 o
150.531 882.188 o
157.774 883.029 o
165.017 883.901 o
172.26 884.764 o
179.503 885.687 o
186.747 886.626 o
193.99 887.578 o
201.233 888.562 o
208.476 889.581 o
215.719 890.618 o
222.962 891.677 o
230.206 892.739 o
237.449 893.912 o
244.692 895.126 o
251.935 896.278 o
259.178 897.556 o
266.421 898.878 o
273.665 900.24 o
280.908 901.705 o
288.151 903.238 o
295.394 904.805 o
302.637 906.55 o
309.881 908.407 o
317.124 910.371 o
324.367 912.626 o
331.61 915.119 o
338.853 917.956 o
346.096 921.348 o
353.34 926.092 o
360.583 1106.68 o
367.826 1107.56 o
375.069 1108.47 o
382.312 1109.3 o
389.556 1110.12 o
396.799 1111 o
404.042 1111.82 o
411.285 1112.45 o
grestore
2.500 setlinewidth
0 setlinejoin
2 setlinecap
0.000 setgray
gsave
103.45 867.24625 m
103.45 1124.29925 l
stroke
grestore
gsave
429.392975 867.24625 m
429.392975 1124.29925 l
stroke
grestore
gsave
103.45 867.24625 m
429.392975 867.24625 l
stroke
grestore
gsave
103.45 1124.29925 m
429.392975 1124.29925 l
stroke
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

196.299 1140.36 translate
0 rotate
0 0 m /parenleft glyphshow
8.3252 0 m /a glyphshow
19.4214 0 m /parenright glyphshow
grestore
1.500 setlinewidth
1 setlinejoin
0.749 0.212 0.047 setrgbcolor
gsave
228.421487 1030.27275 m
248.421487 1030.27275 l
268.421487 1030.27275 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
0 setlinecap
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

0 2 m
-2 -2 l
2 -2 l
cl

gsave
0.749 0.212 0.047 setrgbcolor
fill
grestore
stroke
grestore
} bind def
248.421 1030.27 o
grestore
0.000 setgray
gsave
284.421 1023.27 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.75 moveto
/P glyphshow
/TimesNewRomanPSMT 14.0 selectfont
11.4236 -4.4025 moveto
/x glyphshow
grestore
1.500 setlinewidth
1 setlinejoin
[5.55 2.4] 0 setdash
0.847 0.263 0.082 setrgbcolor
gsave
228.421487 1000.27275 m
248.421487 1000.27275 l
268.421487 1000.27275 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-4 -4 m
4 -4 l
4 4 l
-4 4 l
cl

gsave
grestore
stroke
grestore
} bind def
248.421 1000.27 o
grestore
0.000 setgray
gsave
284.421 993.273 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.75 moveto
/P glyphshow
/TimesNewRomanPSMT 14.0 selectfont
11.4236 -4.4025 moveto
/y glyphshow
grestore
1.500 setlinewidth
1 setlinejoin
[1.5 2.475] 0 setdash
0.937 0.424 0.000 setrgbcolor
gsave
228.421487 967.27275 m
248.421487 967.27275 l
268.421487 967.27275 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
248.421 967.273 o
grestore
0.000 setgray
gsave
284.421 960.273 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.75 moveto
/P glyphshow
/TimesNewRomanPSMT 14.0 selectfont
11.4236 -4.4025 moveto
/z glyphshow
grestore
gsave
569.584 867.24625 m
895.526975 867.24625 l
895.526975 1124.29925 l
569.584 1124.29925 l
cl
1.000 setgray
fill
grestore
2.500 setlinewidth
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
587.692 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
587.692 1124.3 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

574.395 839.887 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /four glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
623.908 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
623.908 1124.3 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

610.611 839.887 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /three glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
660.124 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
660.124 1124.3 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

646.827 839.887 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /two glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
696.34 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
696.34 1124.3 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

683.043 839.887 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /one glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
732.555 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
732.555 1124.3 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

726.305 839.887 translate
0 rotate
0 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
768.771 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
768.771 1124.3 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

762.521 839.887 translate
0 rotate
0 0 m /one glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
804.987 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
804.987 1124.3 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

798.737 839.887 translate
0 rotate
0 0 m /two glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
841.203 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
841.203 1124.3 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

834.953 839.887 translate
0 rotate
0 0 m /three glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
877.419 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
877.419 1124.3 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

871.169 839.887 translate
0 rotate
0 0 m /four glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
573.206 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
573.206 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
580.449 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
580.449 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
594.935 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
594.935 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
602.178 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
602.178 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
609.421 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
609.421 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
616.665 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
616.665 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
631.151 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
631.151 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
638.394 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
638.394 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
645.637 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
645.637 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
652.881 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
652.881 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
667.367 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
667.367 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
674.61 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
674.61 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
681.853 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
681.853 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
689.096 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
689.096 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
703.583 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
703.583 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
710.826 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
710.826 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
718.069 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
718.069 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
725.312 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
725.312 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
739.799 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
739.799 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
747.042 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
747.042 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
754.285 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
754.285 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
761.528 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
761.528 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
776.015 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
776.015 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
783.258 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
783.258 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
790.501 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
790.501 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
797.744 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
797.744 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
812.23 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
812.23 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
819.474 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
819.474 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
826.717 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
826.717 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
833.96 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
833.96 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
848.446 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
848.446 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
855.69 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
855.69 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
862.933 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
862.933 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
870.176 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
870.176 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
884.662 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
884.662 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
891.905 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
891.905 1124.3 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

614.509 813.184 translate
0 rotate
0 0 m /E glyphshow
15.271 0 m /l glyphshow
22.2168 0 m /e glyphshow
33.313 0 m /c glyphshow
44.4092 0 m /t glyphshow
51.355 0 m /r glyphshow
59.6802 0 m /i glyphshow
66.626 0 m /c glyphshow
77.7222 0 m /space glyphshow
83.9722 0 m /F glyphshow
97.876 0 m /i glyphshow
104.822 0 m /e glyphshow
115.918 0 m /l glyphshow
122.864 0 m /d glyphshow
135.364 0 m /space glyphshow
141.614 0 m /parenleft glyphshow
149.939 0 m /M glyphshow
172.168 0 m /V glyphshow
190.222 0 m /slash glyphshow
197.168 0 m /c glyphshow
208.264 0 m /m glyphshow
227.71 0 m /parenright glyphshow
grestore
2.500 setlinewidth
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
569.584 877.301 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
895.527 877.301 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

520.49 868.622 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /six glyphshow
26.5991 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
569.584 916.798 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
895.527 916.798 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

520.49 908.118 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /four glyphshow
26.5991 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
569.584 956.294 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
895.527 956.294 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

520.49 947.615 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /two glyphshow
26.5991 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
569.584 995.791 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
895.527 995.791 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

547.084 987.111 translate
0 rotate
0 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
569.584 1035.29 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
895.527 1035.29 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

534.584 1026.61 translate
0 rotate
0 0 m /two glyphshow
12.5 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
569.584 1074.78 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
895.527 1074.78 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

534.584 1066.1 translate
0 rotate
0 0 m /four glyphshow
12.5 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
569.584 1114.28 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
895.527 1114.28 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

534.584 1105.6 translate
0 rotate
0 0 m /six glyphshow
12.5 0 m /zero glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
569.584 867.427 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
895.527 867.427 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
569.584 887.175 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
895.527 887.175 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
569.584 897.049 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
895.527 897.049 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
569.584 906.924 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
895.527 906.924 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
569.584 926.672 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
895.527 926.672 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
569.584 936.546 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
895.527 936.546 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
569.584 946.42 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
895.527 946.42 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
569.584 966.168 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
895.527 966.168 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
569.584 976.042 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
895.527 976.042 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
569.584 985.917 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
895.527 985.917 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
569.584 1005.66 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
895.527 1005.66 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
569.584 1015.54 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
895.527 1015.54 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
569.584 1025.41 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
895.527 1025.41 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
569.584 1045.16 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
895.527 1045.16 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
569.584 1055.04 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
895.527 1055.04 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
569.584 1064.91 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
895.527 1064.91 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
569.584 1084.66 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
895.527 1084.66 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
569.584 1094.53 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
895.527 1094.53 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
569.584 1104.41 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
895.527 1104.41 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
569.584 1124.15 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
895.527 1124.15 o
grestore
gsave
510.49 884.773 translate
90 rotate
/TimesNewRomanPSMT 25.0 selectfont
0 0.148438 moveto
/P glyphshow
13.9038 0.148438 moveto
/o glyphshow
26.4038 0.148438 moveto
/l glyphshow
33.3496 0.148438 moveto
/a glyphshow
44.4458 0.148438 moveto
/r glyphshow
52.771 0.148438 moveto
/i glyphshow
59.7168 0.148438 moveto
/z glyphshow
70.813 0.148438 moveto
/a glyphshow
81.9092 0.148438 moveto
/t glyphshow
88.855 0.148438 moveto
/i glyphshow
95.8008 0.148438 moveto
/o glyphshow
108.301 0.148438 moveto
/n glyphshow
120.801 0.148438 moveto
/space glyphshow
127.051 0.148438 moveto
/parenleft glyphshow
135.376 0.148438 moveto
/mu glyphshow
148.779 0.148438 moveto
/C glyphshow
165.454 0.148438 moveto
/slash glyphshow
172.4 0.148438 moveto
/c glyphshow
183.496 0.148438 moveto
/m glyphshow
/TimesNewRomanPSMT 17.5 selectfont
203.318 15.1766 moveto
/two glyphshow
/TimesNewRomanPSMT 25.0 selectfont
213.141 0.148438 moveto
/parenright glyphshow
grestore
2 setlinecap
0.749 0.212 0.047 setrgbcolor
gsave
325.943 257.053 569.584 867.246 clipbox
870.175855 1111.670906 m
862.932677 1110.695242 l
855.6895 1109.68624 l
848.446323 1108.783551 l
841.203146 1107.721711 l
833.959969 1106.717977 l
826.716791 1105.687466 l
819.473614 1104.653556 l
812.230437 1103.595354 l
804.98726 1102.484054 l
797.744082 1101.400526 l
790.500905 1100.279804 l
783.257728 1099.078492 l
776.014551 1098.049154 l
768.771374 1096.749142 l
761.528196 1095.542722 l
754.285019 1094.338209 l
747.041842 1093.080043 l
739.798665 1091.854916 l
732.555488 1090.547609 l
725.31231 1089.1716 l
718.069133 1087.772392 l
710.825956 1086.322513 l
703.582779 1084.804209 l
696.339601 1083.400509 l
689.096424 1081.795731 l
681.853247 1080.171015 l
674.61007 1078.342638 l
667.366893 1076.654846 l
660.123715 1074.765042 l
652.880538 1072.844864 l
645.637361 1070.635723 l
638.394184 1068.427318 l
631.151006 1065.942653 l
623.907829 1063.273511 l
616.664652 1060.208775 l
609.421475 1056.515304 l
602.178298 1051.461449 l
594.93512 879.860698 l
587.691943 878.930477 l
594.93512 879.913219 l
602.178298 880.823243 l
609.421475 881.826102 l
616.664652 882.803615 l
623.907829 883.851143 l
631.151006 884.825813 l
638.394184 885.857716 l
645.637361 886.93373 l
652.880538 888.026283 l
660.123715 889.086652 l
667.366893 890.155649 l
674.61007 891.28156 l
681.853247 892.450508 l
689.096424 893.595125 l
696.339601 894.752107 l
703.582779 895.972065 l
710.825956 897.157692 l
718.069133 898.513445 l
725.31231 899.761373 l
732.555488 901.04725 l
739.798665 902.400299 l
747.041842 903.740287 l
754.285019 905.156949 l
761.528196 906.752542 l
768.771374 908.281103 l
776.014551 909.811573 l
783.257728 911.454279 l
790.500905 913.152966 l
797.744082 914.911269 l
804.98726 916.776919 l
812.230437 918.783771 l
819.473614 920.87308 l
826.716791 923.037275 l
833.959969 925.591993 l
841.203146 928.27191 l
848.446323 931.355789 l
855.6895 934.976841 l
862.932677 939.818506 l
870.175855 1111.707066 l
877.419032 1112.615023 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
0 setlinecap
gsave
325.943 257.053 569.584 867.246 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

0 2 m
-2 -2 l
2 -2 l
cl

gsave
0.749 0.212 0.047 setrgbcolor
fill
grestore
stroke
grestore
} bind def
870.176 1111.67 o
862.933 1110.7 o
855.69 1109.69 o
848.446 1108.78 o
841.203 1107.72 o
833.96 1106.72 o
826.717 1105.69 o
819.474 1104.65 o
812.23 1103.6 o
804.987 1102.48 o
797.744 1101.4 o
790.501 1100.28 o
783.258 1099.08 o
776.015 1098.05 o
768.771 1096.75 o
761.528 1095.54 o
754.285 1094.34 o
747.042 1093.08 o
739.799 1091.85 o
732.555 1090.55 o
725.312 1089.17 o
718.069 1087.77 o
710.826 1086.32 o
703.583 1084.8 o
696.34 1083.4 o
689.096 1081.8 o
681.853 1080.17 o
674.61 1078.34 o
667.367 1076.65 o
660.124 1074.77 o
652.881 1072.84 o
645.637 1070.64 o
638.394 1068.43 o
631.151 1065.94 o
623.908 1063.27 o
616.665 1060.21 o
609.421 1056.52 o
602.178 1051.46 o
594.935 879.861 o
587.692 878.93 o
594.935 879.913 o
602.178 880.823 o
609.421 881.826 o
616.665 882.804 o
623.908 883.851 o
631.151 884.826 o
638.394 885.858 o
645.637 886.934 o
652.881 888.026 o
660.124 889.087 o
667.367 890.156 o
674.61 891.282 o
681.853 892.451 o
689.096 893.595 o
696.34 894.752 o
703.583 895.972 o
710.826 897.158 o
718.069 898.513 o
725.312 899.761 o
732.555 901.047 o
739.799 902.4 o
747.042 903.74 o
754.285 905.157 o
761.528 906.753 o
768.771 908.281 o
776.015 909.812 o
783.258 911.454 o
790.501 913.153 o
797.744 914.911 o
804.987 916.777 o
812.23 918.784 o
819.474 920.873 o
826.717 923.037 o
833.96 925.592 o
841.203 928.272 o
848.446 931.356 o
855.69 934.977 o
862.933 939.819 o
870.176 1111.71 o
877.419 1112.62 o
grestore
1.500 setlinewidth
1 setlinejoin
[5.55 2.4] 0 setdash
0.847 0.263 0.082 setrgbcolor
gsave
325.943 257.053 569.584 867.246 clipbox
870.175855 1111.673491 m
862.932677 1110.712735 l
855.6895 1109.767048 l
848.446323 1108.732045 l
841.203146 1107.709704 l
833.959969 1106.705096 l
826.716791 1105.697108 l
819.473614 1104.723093 l
812.230437 1103.581856 l
804.98726 1102.476023 l
797.744082 1101.413447 l
790.500905 1100.26076 l
783.257728 1099.148387 l
776.014551 1097.959539 l
768.771374 1096.783593 l
761.528196 1095.562183 l
754.285019 1094.372938 l
747.041842 1093.100598 l
739.798665 1091.848754 l
732.555488 1090.545124 l
725.31231 1089.160687 l
718.069133 1087.847933 l
710.825956 1086.395012 l
703.582779 1084.924597 l
696.339601 1083.382617 l
689.096424 1081.792829 l
681.853247 1080.11764 l
674.61007 1078.444319 l
667.366893 1076.69351 l
660.123715 1074.781741 l
652.880538 1072.798863 l
645.637361 1070.691424 l
638.394184 1068.43016 l
631.151006 1066.028053 l
623.907829 1063.311062 l
616.664652 1060.14061 l
609.421475 1056.501468 l
602.178298 1051.498384 l
594.93512 879.89165 l
587.691943 878.968844 l
594.93512 879.896163 l
602.178298 880.852644 l
609.421475 881.879755 l
616.664652 882.829359 l
623.907829 883.845338 l
631.151006 884.871157 l
638.394184 885.88827 l
645.637361 886.92389 l
652.880538 887.981019 l
660.123715 889.094624 l
667.366893 890.163362 l
674.61007 891.308556 l
681.853247 892.41902 l
689.096424 893.608762 l
696.339601 894.781071 l
703.582779 895.995841 l
710.825956 897.182263 l
718.069133 898.41717 l
725.31231 899.765667 l
732.555488 901.071702 l
739.798665 902.447552 l
747.041842 903.801356 l
754.285019 905.202114 l
761.528196 906.721034 l
768.771374 908.28333 l
776.014551 909.781675 l
783.257728 911.419551 l
790.500905 913.139647 l
797.744082 914.961364 l
804.98726 916.882577 l
812.230437 918.733079 l
819.473614 920.855686 l
826.716791 923.127824 l
833.959969 925.527764 l
841.203146 928.338346 l
848.446323 931.286192 l
855.6895 935.052381 l
862.932677 939.774574 l
870.175855 1111.679613 l
877.419032 1112.605143 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
325.943 257.053 569.584 867.246 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-4 -4 m
4 -4 l
4 4 l
-4 4 l
cl

gsave
grestore
stroke
grestore
} bind def
870.176 1111.67 o
862.933 1110.71 o
855.69 1109.77 o
848.446 1108.73 o
841.203 1107.71 o
833.96 1106.71 o
826.717 1105.7 o
819.474 1104.72 o
812.23 1103.58 o
804.987 1102.48 o
797.744 1101.41 o
790.501 1100.26 o
783.258 1099.15 o
776.015 1097.96 o
768.771 1096.78 o
761.528 1095.56 o
754.285 1094.37 o
747.042 1093.1 o
739.799 1091.85 o
732.555 1090.55 o
725.312 1089.16 o
718.069 1087.85 o
710.826 1086.4 o
703.583 1084.92 o
696.34 1083.38 o
689.096 1081.79 o
681.853 1080.12 o
674.61 1078.44 o
667.367 1076.69 o
660.124 1074.78 o
652.881 1072.8 o
645.637 1070.69 o
638.394 1068.43 o
631.151 1066.03 o
623.908 1063.31 o
616.665 1060.14 o
609.421 1056.5 o
602.178 1051.5 o
594.935 879.892 o
587.692 878.969 o
594.935 879.896 o
602.178 880.853 o
609.421 881.88 o
616.665 882.829 o
623.908 883.845 o
631.151 884.871 o
638.394 885.888 o
645.637 886.924 o
652.881 887.981 o
660.124 889.095 o
667.367 890.163 o
674.61 891.309 o
681.853 892.419 o
689.096 893.609 o
696.34 894.781 o
703.583 895.996 o
710.826 897.182 o
718.069 898.417 o
725.312 899.766 o
732.555 901.072 o
739.799 902.448 o
747.042 903.801 o
754.285 905.202 o
761.528 906.721 o
768.771 908.283 o
776.015 909.782 o
783.258 911.42 o
790.501 913.14 o
797.744 914.961 o
804.987 916.883 o
812.23 918.733 o
819.474 920.856 o
826.717 923.128 o
833.96 925.528 o
841.203 928.338 o
848.446 931.286 o
855.69 935.052 o
862.933 939.775 o
870.176 1111.68 o
877.419 1112.61 o
grestore
1.500 setlinewidth
1 setlinejoin
[1.5 2.475] 0 setdash
0.937 0.424 0.000 setrgbcolor
gsave
325.943 257.053 569.584 867.246 clipbox
870.175855 1087.111354 m
862.932677 1087.380874 l
855.6895 1087.683195 l
848.446323 1087.943273 l
841.203146 1088.162758 l
833.959969 1088.469074 l
826.716791 1088.68506 l
819.473614 1088.879418 l
812.230437 1089.073199 l
804.98726 1089.288608 l
797.744082 1089.452631 l
790.500905 1089.705751 l
783.257728 1089.854307 l
776.014551 1089.984157 l
768.771374 1090.137623 l
761.528196 1090.252147 l
754.285019 1090.359891 l
747.041842 1090.436187 l
739.798665 1090.498925 l
732.555488 1090.511449 l
725.31231 1090.552102 l
718.069133 1090.54743 l
710.825956 1090.492664 l
703.582779 1090.492922 l
696.339601 1090.378518 l
689.096424 1090.286259 l
681.853247 1090.099157 l
674.61007 1089.989405 l
667.366893 1089.672891 l
660.123715 1089.417882 l
652.880538 1089.004358 l
645.637361 1088.599441 l
638.394184 1087.987981 l
631.151006 1087.31247 l
623.907829 1086.422624 l
616.664652 1085.275522 l
609.421475 1083.64357 l
602.178298 1080.955125 l
594.93512 904.481399 l
587.691943 904.733764 l
594.93512 904.526942 l
602.178298 904.227146 l
609.421475 903.872006 l
616.664652 903.648744 l
623.907829 903.413654 l
631.151006 903.151628 l
638.394184 902.93765 l
645.637361 902.648171 l
652.880538 902.521124 l
660.123715 902.258879 l
667.366893 902.089788 l
674.61007 901.941311 l
681.853247 901.79363 l
689.096424 901.66046 l
696.339601 901.516078 l
703.582779 901.324504 l
710.825956 901.253118 l
718.069133 901.210835 l
725.31231 901.12615 l
732.555488 901.063074 l
739.798665 901.007969 l
747.041842 901.060033 l
754.285019 901.091024 l
761.528196 901.140026 l
768.771374 901.217972 l
776.014551 901.273355 l
783.257728 901.457117 l
790.500905 901.640024 l
797.744082 901.927893 l
804.98726 902.165985 l
812.230437 902.515637 l
819.473614 902.981563 l
826.716791 903.582169 l
833.959969 904.296305 l
841.203146 905.159891 l
848.446323 906.229862 l
855.6895 907.876922 l
862.932677 910.362919 l
870.175855 1087.058793 l
877.419032 1086.878033 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
325.943 257.053 569.584 867.246 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
870.176 1087.11 o
862.933 1087.38 o
855.69 1087.68 o
848.446 1087.94 o
841.203 1088.16 o
833.96 1088.47 o
826.717 1088.69 o
819.474 1088.88 o
812.23 1089.07 o
804.987 1089.29 o
797.744 1089.45 o
790.501 1089.71 o
783.258 1089.85 o
776.015 1089.98 o
768.771 1090.14 o
761.528 1090.25 o
754.285 1090.36 o
747.042 1090.44 o
739.799 1090.5 o
732.555 1090.51 o
725.312 1090.55 o
718.069 1090.55 o
710.826 1090.49 o
703.583 1090.49 o
696.34 1090.38 o
689.096 1090.29 o
681.853 1090.1 o
674.61 1089.99 o
667.367 1089.67 o
660.124 1089.42 o
652.881 1089 o
645.637 1088.6 o
638.394 1087.99 o
631.151 1087.31 o
623.908 1086.42 o
616.665 1085.28 o
609.421 1083.64 o
602.178 1080.96 o
594.935 904.481 o
587.692 904.734 o
594.935 904.527 o
602.178 904.227 o
609.421 903.872 o
616.665 903.649 o
623.908 903.414 o
631.151 903.152 o
638.394 902.938 o
645.637 902.648 o
652.881 902.521 o
660.124 902.259 o
667.367 902.09 o
674.61 901.941 o
681.853 901.794 o
689.096 901.66 o
696.34 901.516 o
703.583 901.325 o
710.826 901.253 o
718.069 901.211 o
725.312 901.126 o
732.555 901.063 o
739.799 901.008 o
747.042 901.06 o
754.285 901.091 o
761.528 901.14 o
768.771 901.218 o
776.015 901.273 o
783.258 901.457 o
790.501 901.64 o
797.744 901.928 o
804.987 902.166 o
812.23 902.516 o
819.474 902.982 o
826.717 903.582 o
833.96 904.296 o
841.203 905.16 o
848.446 906.23 o
855.69 907.877 o
862.933 910.363 o
870.176 1087.06 o
877.419 1086.88 o
grestore
2.500 setlinewidth
0 setlinejoin
2 setlinecap
0.000 setgray
gsave
569.584 867.24625 m
569.584 1124.29925 l
stroke
grestore
gsave
895.526975 867.24625 m
895.526975 1124.29925 l
stroke
grestore
gsave
569.584 867.24625 m
895.526975 867.24625 l
stroke
grestore
gsave
569.584 1124.29925 m
895.526975 1124.29925 l
stroke
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

661.027 1140.36 translate
0 rotate
0 0 m /parenleft glyphshow
8.3252 0 m /b glyphshow
20.8252 0 m /parenright glyphshow
grestore
1.500 setlinewidth
1 setlinejoin
0.749 0.212 0.047 setrgbcolor
gsave
694.555488 1030.27275 m
714.555488 1030.27275 l
734.555488 1030.27275 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
0 setlinecap
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

0 2 m
-2 -2 l
2 -2 l
cl

gsave
0.749 0.212 0.047 setrgbcolor
fill
grestore
stroke
grestore
} bind def
714.555 1030.27 o
grestore
0.000 setgray
gsave
750.555 1023.27 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.75 moveto
/P glyphshow
/TimesNewRomanPSMT 14.0 selectfont
11.4236 -4.4025 moveto
/x glyphshow
grestore
1.500 setlinewidth
1 setlinejoin
[5.55 2.4] 0 setdash
0.847 0.263 0.082 setrgbcolor
gsave
694.555488 1000.27275 m
714.555488 1000.27275 l
734.555488 1000.27275 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-4 -4 m
4 -4 l
4 4 l
-4 4 l
cl

gsave
grestore
stroke
grestore
} bind def
714.555 1000.27 o
grestore
0.000 setgray
gsave
750.555 993.273 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.75 moveto
/P glyphshow
/TimesNewRomanPSMT 14.0 selectfont
11.4236 -4.4025 moveto
/y glyphshow
grestore
1.500 setlinewidth
1 setlinejoin
[1.5 2.475] 0 setdash
0.937 0.424 0.000 setrgbcolor
gsave
694.555488 967.27275 m
714.555488 967.27275 l
734.555488 967.27275 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
714.555 967.273 o
grestore
0.000 setgray
gsave
750.555 960.273 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.75 moveto
/P glyphshow
/TimesNewRomanPSMT 14.0 selectfont
11.4236 -4.4025 moveto
/z glyphshow
grestore
gsave
1035.718 867.24625 m
1361.660975 867.24625 l
1361.660975 1124.29925 l
1035.718 1124.29925 l
cl
1.000 setgray
fill
grestore
2.500 setlinewidth
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1053.83 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1053.83 1124.3 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

1040.53 839.887 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /four glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1090.04 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1090.04 1124.3 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

1076.74 839.887 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /three glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1126.26 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1126.26 1124.3 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

1112.96 839.887 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /two glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1162.47 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1162.47 1124.3 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

1149.18 839.887 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /one glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1198.69 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1198.69 1124.3 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

1192.44 839.887 translate
0 rotate
0 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1234.91 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1234.91 1124.3 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

1228.66 839.887 translate
0 rotate
0 0 m /one glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1271.12 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1271.12 1124.3 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

1264.87 839.887 translate
0 rotate
0 0 m /two glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1307.34 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1307.34 1124.3 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

1301.09 839.887 translate
0 rotate
0 0 m /three glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1343.55 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1343.55 1124.3 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

1337.3 839.887 translate
0 rotate
0 0 m /four glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1039.34 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1039.34 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1046.58 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1046.58 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1061.07 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1061.07 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1068.31 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1068.31 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1075.56 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1075.56 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1082.8 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1082.8 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1097.29 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1097.29 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1104.53 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1104.53 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1111.77 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1111.77 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1119.01 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1119.01 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1133.5 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1133.5 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1140.74 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1140.74 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1147.99 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1147.99 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1155.23 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1155.23 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1169.72 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1169.72 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1176.96 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1176.96 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1184.2 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1184.2 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1191.45 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1191.45 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1205.93 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1205.93 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1213.18 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1213.18 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1220.42 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1220.42 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1227.66 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1227.66 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1242.15 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1242.15 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1249.39 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1249.39 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1256.63 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1256.63 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1263.88 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1263.88 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1278.36 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1278.36 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1285.61 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1285.61 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1292.85 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1292.85 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1300.09 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1300.09 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1314.58 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1314.58 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1321.82 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1321.82 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1329.07 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1329.07 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1336.31 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1336.31 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1350.8 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1350.8 1124.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1358.04 867.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1358.04 1124.3 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

1080.64 813.184 translate
0 rotate
0 0 m /E glyphshow
15.271 0 m /l glyphshow
22.2168 0 m /e glyphshow
33.313 0 m /c glyphshow
44.4092 0 m /t glyphshow
51.355 0 m /r glyphshow
59.6802 0 m /i glyphshow
66.626 0 m /c glyphshow
77.7222 0 m /space glyphshow
83.9722 0 m /F glyphshow
97.876 0 m /i glyphshow
104.822 0 m /e glyphshow
115.918 0 m /l glyphshow
122.864 0 m /d glyphshow
135.364 0 m /space glyphshow
141.614 0 m /parenleft glyphshow
149.939 0 m /M glyphshow
172.168 0 m /V glyphshow
190.222 0 m /slash glyphshow
197.168 0 m /c glyphshow
208.264 0 m /m glyphshow
227.71 0 m /parenright glyphshow
grestore
2.500 setlinewidth
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1035.72 879.323 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1361.66 879.323 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

986.624 870.644 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /six glyphshow
26.5991 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1035.72 918.142 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1361.66 918.142 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

986.624 909.463 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /four glyphshow
26.5991 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1035.72 956.961 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1361.66 956.961 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

986.624 948.281 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /two glyphshow
26.5991 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1035.72 995.78 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1361.66 995.78 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

1013.22 987.1 translate
0 rotate
0 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1035.72 1034.6 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1361.66 1034.6 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

1000.72 1025.92 translate
0 rotate
0 0 m /two glyphshow
12.5 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1035.72 1073.42 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1361.66 1073.42 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

1000.72 1064.74 translate
0 rotate
0 0 m /four glyphshow
12.5 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1035.72 1112.24 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1361.66 1112.24 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

1000.72 1103.56 translate
0 rotate
0 0 m /six glyphshow
12.5 0 m /zero glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1035.72 869.619 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1361.66 869.619 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1035.72 889.028 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1361.66 889.028 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1035.72 898.733 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1361.66 898.733 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1035.72 908.437 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1361.66 908.437 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1035.72 927.847 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1361.66 927.847 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1035.72 937.552 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1361.66 937.552 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1035.72 947.256 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1361.66 947.256 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1035.72 966.666 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1361.66 966.666 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1035.72 976.371 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1361.66 976.371 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1035.72 986.075 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1361.66 986.075 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1035.72 1005.48 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1361.66 1005.48 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1035.72 1015.19 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1361.66 1015.19 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1035.72 1024.89 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1361.66 1024.89 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1035.72 1044.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1361.66 1044.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1035.72 1054.01 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1361.66 1054.01 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1035.72 1063.71 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1361.66 1063.71 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1035.72 1083.12 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1361.66 1083.12 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1035.72 1092.83 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1361.66 1092.83 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1035.72 1102.53 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1361.66 1102.53 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1035.72 1121.94 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1361.66 1121.94 o
grestore
gsave
976.624 884.773 translate
90 rotate
/TimesNewRomanPSMT 25.0 selectfont
0 0.148438 moveto
/P glyphshow
13.9038 0.148438 moveto
/o glyphshow
26.4038 0.148438 moveto
/l glyphshow
33.3496 0.148438 moveto
/a glyphshow
44.4458 0.148438 moveto
/r glyphshow
52.771 0.148438 moveto
/i glyphshow
59.7168 0.148438 moveto
/z glyphshow
70.813 0.148438 moveto
/a glyphshow
81.9092 0.148438 moveto
/t glyphshow
88.855 0.148438 moveto
/i glyphshow
95.8008 0.148438 moveto
/o glyphshow
108.301 0.148438 moveto
/n glyphshow
120.801 0.148438 moveto
/space glyphshow
127.051 0.148438 moveto
/parenleft glyphshow
135.376 0.148438 moveto
/mu glyphshow
148.779 0.148438 moveto
/C glyphshow
165.454 0.148438 moveto
/slash glyphshow
172.4 0.148438 moveto
/c glyphshow
183.496 0.148438 moveto
/m glyphshow
/TimesNewRomanPSMT 17.5 selectfont
203.318 15.1766 moveto
/two glyphshow
/TimesNewRomanPSMT 25.0 selectfont
213.141 0.148438 moveto
/parenright glyphshow
grestore
2 setlinecap
0.749 0.212 0.047 setrgbcolor
gsave
325.943 257.053 1035.718 867.246 clipbox
1336.309855 965.204675 m
1329.066677 965.148718 l
1321.8235 965.195277 l
1314.580323 965.101456 l
1307.337146 965.076271 l
1300.093969 965.10763 l
1292.850791 964.969535 l
1285.607614 965.021115 l
1278.364437 964.946422 l
1271.12126 964.952889 l
1263.878083 964.992981 l
1256.634905 964.967366 l
1249.391728 964.977214 l
1242.148551 964.921608 l
1234.905374 964.899921 l
1227.662196 964.912347 l
1220.419019 964.939701 l
1213.175842 964.878781 l
1205.932665 964.855941 l
1198.689488 964.841288 l
1191.44631 964.742249 l
1184.203133 964.830698 l
1176.959956 964.858344 l
1169.716779 964.850666 l
1162.473601 964.879133 l
1155.230424 964.879895 l
1147.987247 964.902793 l
1140.74407 964.893278 l
1133.500892 964.87204 l
1126.257715 964.819972 l
1119.014538 964.919928 l
1111.771361 964.901054 l
1104.528184 964.9169 l
1097.285006 964.964123 l
1090.041829 964.897479 l
1082.798652 965.00318 l
1075.555475 964.946519 l
1068.312298 964.940189 l
1061.06912 964.926024 l
1053.825943 965.222767 l
1061.06912 965.155947 l
1068.312298 965.174626 l
1075.555475 965.20911 l
1082.798652 965.102218 l
1090.041829 965.124081 l
1097.285006 965.049895 l
1104.528184 965.044424 l
1111.771361 965.03532 l
1119.014538 964.99894 l
1126.257715 964.97524 l
1133.500892 964.921003 l
1140.74407 964.940306 l
1147.987247 964.981453 l
1155.230424 964.893356 l
1162.473601 964.970278 l
1169.716779 964.836012 l
1176.959956 964.905529 l
1184.203133 964.876749 l
1191.44631 964.876046 l
1198.689488 964.831401 l
1205.932665 964.910022 l
1213.175842 964.846426 l
1220.419019 964.855081 l
1227.662196 964.948043 l
1234.905374 964.870575 l
1242.148551 964.838259 l
1249.391728 964.881008 l
1256.634905 964.799574 l
1263.878083 964.902813 l
1271.12126 964.884388 l
1278.364437 964.897577 l
1285.607614 964.915395 l
1292.850791 964.970825 l
1300.093969 964.920846 l
1307.337146 964.971157 l
1314.580323 964.964064 l
1321.8235 964.951306 l
1329.066677 964.957597 l
1336.309855 965.008474 l
1343.553032 965.180565 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
0 setlinecap
gsave
325.943 257.053 1035.718 867.246 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

0 2 m
-2 -2 l
2 -2 l
cl

gsave
0.749 0.212 0.047 setrgbcolor
fill
grestore
stroke
grestore
} bind def
1336.31 965.205 o
1329.07 965.149 o
1321.82 965.195 o
1314.58 965.101 o
1307.34 965.076 o
1300.09 965.108 o
1292.85 964.97 o
1285.61 965.021 o
1278.36 964.946 o
1271.12 964.953 o
1263.88 964.993 o
1256.63 964.967 o
1249.39 964.977 o
1242.15 964.922 o
1234.91 964.9 o
1227.66 964.912 o
1220.42 964.94 o
1213.18 964.879 o
1205.93 964.856 o
1198.69 964.841 o
1191.45 964.742 o
1184.2 964.831 o
1176.96 964.858 o
1169.72 964.851 o
1162.47 964.879 o
1155.23 964.88 o
1147.99 964.903 o
1140.74 964.893 o
1133.5 964.872 o
1126.26 964.82 o
1119.01 964.92 o
1111.77 964.901 o
1104.53 964.917 o
1097.29 964.964 o
1090.04 964.897 o
1082.8 965.003 o
1075.56 964.947 o
1068.31 964.94 o
1061.07 964.926 o
1053.83 965.223 o
1061.07 965.156 o
1068.31 965.175 o
1075.56 965.209 o
1082.8 965.102 o
1090.04 965.124 o
1097.29 965.05 o
1104.53 965.044 o
1111.77 965.035 o
1119.01 964.999 o
1126.26 964.975 o
1133.5 964.921 o
1140.74 964.94 o
1147.99 964.981 o
1155.23 964.893 o
1162.47 964.97 o
1169.72 964.836 o
1176.96 964.906 o
1184.2 964.877 o
1191.45 964.876 o
1198.69 964.831 o
1205.93 964.91 o
1213.18 964.846 o
1220.42 964.855 o
1227.66 964.948 o
1234.91 964.871 o
1242.15 964.838 o
1249.39 964.881 o
1256.63 964.8 o
1263.88 964.903 o
1271.12 964.884 o
1278.36 964.898 o
1285.61 964.915 o
1292.85 964.971 o
1300.09 964.921 o
1307.34 964.971 o
1314.58 964.964 o
1321.82 964.951 o
1329.07 964.958 o
1336.31 965.008 o
1343.55 965.181 o
grestore
1.500 setlinewidth
1 setlinejoin
[5.55 2.4] 0 setdash
0.847 0.263 0.082 setrgbcolor
gsave
325.943 257.053 1035.718 867.246 clipbox
1336.309855 965.187951 m
1329.066677 965.143013 l
1321.8235 965.114175 l
1314.580323 965.146413 l
1307.337146 965.078049 l
1300.093969 965.075978 l
1292.850791 965.034948 l
1285.607614 965.054604 l
1278.364437 965.02262 l
1271.12126 964.95201 l
1263.878083 965.002144 l
1256.634905 964.908752 l
1249.391728 964.971782 l
1242.148551 964.909104 l
1234.905374 964.884603 l
1227.662196 964.877081 l
1220.419019 964.908752 l
1213.175842 964.874268 l
1205.932665 964.858247 l
1198.689488 964.872216 l
1191.44631 964.81284 l
1184.203133 964.814657 l
1176.959956 964.837947 l
1169.716779 964.856488 l
1162.473601 964.815869 l
1155.230424 964.883763 l
1147.987247 964.88771 l
1140.74407 964.926532 l
1133.500892 964.888784 l
1126.257715 964.884173 l
1119.014538 964.854652 l
1111.771361 964.901894 l
1104.528184 964.873682 l
1097.285006 964.924168 l
1090.041829 964.935422 l
1082.798652 964.987745 l
1075.555475 965.017345 l
1068.312298 964.899823 l
1061.06912 964.989054 l
1053.825943 965.231755 l
1061.06912 965.19688 l
1068.312298 965.159816 l
1075.555475 965.168628 l
1082.798652 965.09833 l
1090.041829 965.077737 l
1097.285006 965.060778 l
1104.528184 965.071641 l
1111.771361 965.06533 l
1119.014538 964.998158 l
1126.257715 964.938489 l
1133.500892 965.002613 l
1140.74407 964.972993 l
1147.987247 964.913481 l
1155.230424 964.91139 l
1162.473601 964.965276 l
1169.716779 964.958438 l
1176.959956 964.909944 l
1184.203133 964.937864 l
1191.44631 964.936281 l
1198.689488 964.842987 l
1205.932665 964.933976 l
1213.175842 964.84074 l
1220.419019 964.848829 l
1227.662196 964.844843 l
1234.905374 964.902383 l
1242.148551 964.886323 l
1249.391728 964.816513 l
1256.634905 964.904923 l
1263.878083 964.890348 l
1271.12126 964.891871 l
1278.364437 964.887436 l
1285.607614 964.963889 l
1292.850791 964.984736 l
1300.093969 964.907189 l
1307.337146 964.920417 l
1314.580323 964.970766 l
1321.8235 964.949528 l
1329.066677 964.961739 l
1336.309855 964.968089 l
1343.553032 965.198169 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
325.943 257.053 1035.718 867.246 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-4 -4 m
4 -4 l
4 4 l
-4 4 l
cl

gsave
grestore
stroke
grestore
} bind def
1336.31 965.188 o
1329.07 965.143 o
1321.82 965.114 o
1314.58 965.146 o
1307.34 965.078 o
1300.09 965.076 o
1292.85 965.035 o
1285.61 965.055 o
1278.36 965.023 o
1271.12 964.952 o
1263.88 965.002 o
1256.63 964.909 o
1249.39 964.972 o
1242.15 964.909 o
1234.91 964.885 o
1227.66 964.877 o
1220.42 964.909 o
1213.18 964.874 o
1205.93 964.858 o
1198.69 964.872 o
1191.45 964.813 o
1184.2 964.815 o
1176.96 964.838 o
1169.72 964.856 o
1162.47 964.816 o
1155.23 964.884 o
1147.99 964.888 o
1140.74 964.927 o
1133.5 964.889 o
1126.26 964.884 o
1119.01 964.855 o
1111.77 964.902 o
1104.53 964.874 o
1097.29 964.924 o
1090.04 964.935 o
1082.8 964.988 o
1075.56 965.017 o
1068.31 964.9 o
1061.07 964.989 o
1053.83 965.232 o
1061.07 965.197 o
1068.31 965.16 o
1075.56 965.169 o
1082.8 965.098 o
1090.04 965.078 o
1097.29 965.061 o
1104.53 965.072 o
1111.77 965.065 o
1119.01 964.998 o
1126.26 964.938 o
1133.5 965.003 o
1140.74 964.973 o
1147.99 964.913 o
1155.23 964.911 o
1162.47 964.965 o
1169.72 964.958 o
1176.96 964.91 o
1184.2 964.938 o
1191.45 964.936 o
1198.69 964.843 o
1205.93 964.934 o
1213.18 964.841 o
1220.42 964.849 o
1227.66 964.845 o
1234.91 964.902 o
1242.15 964.886 o
1249.39 964.817 o
1256.63 964.905 o
1263.88 964.89 o
1271.12 964.892 o
1278.36 964.887 o
1285.61 964.964 o
1292.85 964.985 o
1300.09 964.907 o
1307.34 964.92 o
1314.58 964.971 o
1321.82 964.95 o
1329.07 964.962 o
1336.31 964.968 o
1343.55 965.198 o
grestore
1.500 setlinewidth
1 setlinejoin
[1.5 2.475] 0 setdash
0.937 0.424 0.000 setrgbcolor
gsave
325.943 257.053 1035.718 867.246 clipbox
1336.309855 1111.43055 m
1329.066677 1110.325421 l
1321.8235 1109.215602 l
1314.580323 1108.052484 l
1307.337146 1106.849353 l
1300.093969 1105.769818 l
1292.850791 1104.604883 l
1285.607614 1103.396398 l
1278.364437 1102.157687 l
1271.12126 1101.000392 l
1263.878083 1099.76975 l
1256.634905 1098.470101 l
1249.391728 1097.193272 l
1242.148551 1095.912691 l
1234.905374 1094.621111 l
1227.662196 1093.285843 l
1220.419019 1091.899015 l
1213.175842 1090.579065 l
1205.932665 1089.147241 l
1198.689488 1087.713052 l
1191.44631 1086.315478 l
1184.203133 1084.757165 l
1176.959956 1083.328271 l
1169.716779 1081.767417 l
1162.473601 1080.196228 l
1155.230424 1078.492239 l
1147.987247 1076.749467 l
1140.74407 1075.02807 l
1133.500892 1073.256069 l
1126.257715 1071.285328 l
1119.014538 1069.408036 l
1111.771361 1067.127676 l
1104.528184 1065.096503 l
1097.285006 1062.850979 l
1090.041829 1060.30662 l
1082.798652 1057.529798 l
1075.555475 1054.278398 l
1068.312298 1050.558124 l
1061.06912 1045.739149 l
1053.825943 878.930477 l
1061.06912 880.120675 l
1068.312298 881.130419 l
1075.555475 882.348185 l
1082.798652 883.517614 l
1090.041829 884.627217 l
1097.285006 885.832635 l
1104.528184 887.016405 l
1111.771361 888.162583 l
1119.014538 889.404947 l
1126.257715 890.617497 l
1133.500892 891.858864 l
1140.74407 893.100662 l
1147.987247 894.41264 l
1155.230424 895.725869 l
1162.473601 896.949789 l
1169.716779 898.301683 l
1176.959956 899.589434 l
1184.203133 900.98863 l
1191.44631 902.392945 l
1198.689488 903.798432 l
1205.932665 905.323922 l
1213.175842 906.819577 l
1220.419019 908.291845 l
1227.662196 909.897694 l
1234.905374 911.446923 l
1242.148551 913.025673 l
1249.391728 914.772939 l
1256.634905 916.46458 l
1263.878083 918.340327 l
1271.12126 920.216466 l
1278.364437 922.172046 l
1285.607614 924.248313 l
1292.850791 926.535453 l
1300.093969 928.769919 l
1307.337146 931.25537 l
1314.580323 934.163136 l
1321.8235 937.115565 l
1329.066677 940.950801 l
1336.309855 946.144436 l
1343.553032 1112.615023 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
325.943 257.053 1035.718 867.246 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
1336.31 1111.43 o
1329.07 1110.33 o
1321.82 1109.22 o
1314.58 1108.05 o
1307.34 1106.85 o
1300.09 1105.77 o
1292.85 1104.6 o
1285.61 1103.4 o
1278.36 1102.16 o
1271.12 1101 o
1263.88 1099.77 o
1256.63 1098.47 o
1249.39 1097.19 o
1242.15 1095.91 o
1234.91 1094.62 o
1227.66 1093.29 o
1220.42 1091.9 o
1213.18 1090.58 o
1205.93 1089.15 o
1198.69 1087.71 o
1191.45 1086.32 o
1184.2 1084.76 o
1176.96 1083.33 o
1169.72 1081.77 o
1162.47 1080.2 o
1155.23 1078.49 o
1147.99 1076.75 o
1140.74 1075.03 o
1133.5 1073.26 o
1126.26 1071.29 o
1119.01 1069.41 o
1111.77 1067.13 o
1104.53 1065.1 o
1097.29 1062.85 o
1090.04 1060.31 o
1082.8 1057.53 o
1075.56 1054.28 o
1068.31 1050.56 o
1061.07 1045.74 o
1053.83 878.93 o
1061.07 880.121 o
1068.31 881.13 o
1075.56 882.348 o
1082.8 883.518 o
1090.04 884.627 o
1097.29 885.833 o
1104.53 887.016 o
1111.77 888.163 o
1119.01 889.405 o
1126.26 890.617 o
1133.5 891.859 o
1140.74 893.101 o
1147.99 894.413 o
1155.23 895.726 o
1162.47 896.95 o
1169.72 898.302 o
1176.96 899.589 o
1184.2 900.989 o
1191.45 902.393 o
1198.69 903.798 o
1205.93 905.324 o
1213.18 906.82 o
1220.42 908.292 o
1227.66 909.898 o
1234.91 911.447 o
1242.15 913.026 o
1249.39 914.773 o
1256.63 916.465 o
1263.88 918.34 o
1271.12 920.216 o
1278.36 922.172 o
1285.61 924.248 o
1292.85 926.535 o
1300.09 928.77 o
1307.34 931.255 o
1314.58 934.163 o
1321.82 937.116 o
1329.07 940.951 o
1336.31 946.144 o
1343.55 1112.62 o
grestore
2.500 setlinewidth
0 setlinejoin
2 setlinecap
0.000 setgray
gsave
1035.718 867.24625 m
1035.718 1124.29925 l
stroke
grestore
gsave
1361.660975 867.24625 m
1361.660975 1124.29925 l
stroke
grestore
gsave
1035.718 867.24625 m
1361.660975 867.24625 l
stroke
grestore
gsave
1035.718 1124.29925 m
1361.660975 1124.29925 l
stroke
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

1128.57 1140.36 translate
0 rotate
0 0 m /parenleft glyphshow
8.3252 0 m /c glyphshow
19.4214 0 m /parenright glyphshow
grestore
1.500 setlinewidth
1 setlinejoin
0.749 0.212 0.047 setrgbcolor
gsave
1160.689488 1030.27275 m
1180.689488 1030.27275 l
1200.689488 1030.27275 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
0 setlinecap
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

0 2 m
-2 -2 l
2 -2 l
cl

gsave
0.749 0.212 0.047 setrgbcolor
fill
grestore
stroke
grestore
} bind def
1180.69 1030.27 o
grestore
0.000 setgray
gsave
1216.69 1023.27 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.75 moveto
/P glyphshow
/TimesNewRomanPSMT 14.0 selectfont
11.4236 -4.4025 moveto
/x glyphshow
grestore
1.500 setlinewidth
1 setlinejoin
[5.55 2.4] 0 setdash
0.847 0.263 0.082 setrgbcolor
gsave
1160.689488 1000.27275 m
1180.689488 1000.27275 l
1200.689488 1000.27275 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-4 -4 m
4 -4 l
4 4 l
-4 4 l
cl

gsave
grestore
stroke
grestore
} bind def
1180.69 1000.27 o
grestore
0.000 setgray
gsave
1216.69 993.273 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.75 moveto
/P glyphshow
/TimesNewRomanPSMT 14.0 selectfont
11.4236 -4.4025 moveto
/y glyphshow
grestore
1.500 setlinewidth
1 setlinejoin
[1.5 2.475] 0 setdash
0.937 0.424 0.000 setrgbcolor
gsave
1160.689488 967.27275 m
1180.689488 967.27275 l
1200.689488 967.27275 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
1180.69 967.273 o
grestore
0.000 setgray
gsave
1216.69 960.273 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.75 moveto
/P glyphshow
/TimesNewRomanPSMT 14.0 selectfont
11.4236 -4.4025 moveto
/z glyphshow
grestore
gsave
103.45 466.92625 m
429.392975 466.92625 l
429.392975 723.97925 l
103.45 723.97925 l
cl
1.000 setgray
fill
grestore
2.500 setlinewidth
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
121.558 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
121.558 723.979 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

108.261 439.567 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /four glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
157.774 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
157.774 723.979 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

144.477 439.567 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /three glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
193.99 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
193.99 723.979 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

180.693 439.567 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /two glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
230.206 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
230.206 723.979 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

216.909 439.567 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /one glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
266.421 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
266.421 723.979 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

260.171 439.567 translate
0 rotate
0 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
302.637 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
302.637 723.979 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

296.387 439.567 translate
0 rotate
0 0 m /one glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
338.853 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
338.853 723.979 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

332.603 439.567 translate
0 rotate
0 0 m /two glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
375.069 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
375.069 723.979 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

368.819 439.567 translate
0 rotate
0 0 m /three glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
411.285 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
411.285 723.979 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

405.035 439.567 translate
0 rotate
0 0 m /four glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
107.072 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
107.072 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
114.315 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
114.315 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
128.801 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
128.801 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
136.044 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
136.044 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
143.287 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
143.287 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
150.531 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
150.531 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
165.017 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
165.017 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
172.26 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
172.26 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
179.503 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
179.503 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
186.747 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
186.747 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
201.233 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
201.233 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
208.476 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
208.476 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
215.719 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
215.719 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
222.962 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
222.962 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
237.449 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
237.449 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
244.692 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
244.692 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
251.935 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
251.935 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
259.178 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
259.178 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
273.665 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
273.665 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
280.908 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
280.908 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
288.151 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
288.151 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
295.394 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
295.394 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
309.881 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
309.881 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
317.124 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
317.124 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
324.367 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
324.367 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
331.61 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
331.61 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
346.096 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
346.096 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
353.34 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
353.34 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
360.583 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
360.583 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
367.826 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
367.826 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
382.312 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
382.312 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
389.556 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
389.556 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
396.799 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
396.799 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
404.042 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
404.042 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
418.528 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
418.528 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
425.771 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
425.771 723.979 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

148.375 412.864 translate
0 rotate
0 0 m /E glyphshow
15.271 0 m /l glyphshow
22.2168 0 m /e glyphshow
33.313 0 m /c glyphshow
44.4092 0 m /t glyphshow
51.355 0 m /r glyphshow
59.6802 0 m /i glyphshow
66.626 0 m /c glyphshow
77.7222 0 m /space glyphshow
83.9722 0 m /F glyphshow
97.876 0 m /i glyphshow
104.822 0 m /e glyphshow
115.918 0 m /l glyphshow
122.864 0 m /d glyphshow
135.364 0 m /space glyphshow
141.614 0 m /parenleft glyphshow
149.939 0 m /M glyphshow
172.168 0 m /V glyphshow
190.222 0 m /slash glyphshow
197.168 0 m /c glyphshow
208.264 0 m /m glyphshow
227.71 0 m /parenright glyphshow
grestore
2.500 setlinewidth
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 500.587 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
429.393 500.587 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

37.2 491.908 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /five glyphshow
43.75 0 m /five glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 558.499 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
429.393 558.499 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

37.2 549.82 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /five glyphshow
43.75 0 m /six glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 616.411 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
429.393 616.411 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

37.2 607.731 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /five glyphshow
43.75 0 m /seven glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 674.323 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
429.393 674.323 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

37.2 665.643 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /five glyphshow
43.75 0 m /eight glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 477.423 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
429.393 477.423 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 489.005 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
429.393 489.005 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 512.17 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
429.393 512.17 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 523.752 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
429.393 523.752 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 535.335 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
429.393 535.335 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 546.917 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
429.393 546.917 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 570.082 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
429.393 570.082 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 581.664 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
429.393 581.664 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 593.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
429.393 593.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 604.829 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
429.393 604.829 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 627.993 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
429.393 627.993 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 639.576 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
429.393 639.576 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 651.158 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
429.393 651.158 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 662.74 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
429.393 662.74 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 685.905 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
429.393 685.905 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 697.488 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
429.393 697.488 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 709.07 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
429.393 709.07 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 720.652 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
429.393 720.652 o
grestore
gsave
22.2 552.453 translate
90 rotate
/TimesNewRomanPSMT 25.0 selectfont
0 0.6875 moveto
/less glyphshow
14.0991 0.6875 moveto
/omega glyphshow
/TimesNewRomanPSMT 17.5 selectfont
30.9299 -5.75313 moveto
/x glyphshow
39.6799 -5.75313 moveto
/comma glyphshow
46.7009 -5.75313 moveto
/y glyphshow
55.4509 -5.75313 moveto
/comma glyphshow
62.4719 -5.75313 moveto
/z glyphshow
/TimesNewRomanPSMT 25.0 selectfont
71.3127 0.6875 moveto
/greater glyphshow
grestore
2 setlinecap
0.749 0.212 0.047 setrgbcolor
gsave
325.943 257.053 103.45 466.926 clipbox
404.041855 483.899562 m
396.798677 489.314895 l
389.5555 495.528831 l
382.312323 501.32059 l
375.069146 507.803238 l
367.825969 511.830424 l
360.582791 516.953302 l
353.339614 523.811797 l
346.096437 530.341353 l
338.85326 534.394021 l
331.610082 540.705828 l
324.366905 545.60922 l
317.123728 552.094184 l
309.880551 556.856271 l
302.637374 566.287787 l
295.394196 570.648546 l
288.151019 577.196634 l
280.907842 583.421573 l
273.664665 589.971398 l
266.421487 598.217459 l
259.17831 605.30876 l
251.935133 612.024791 l
244.691956 618.036036 l
237.448779 627.329722 l
230.205601 635.472122 l
222.962424 644.124145 l
215.719247 652.159407 l
208.47607 664.529368 l
201.232892 671.858107 l
193.989715 678.787254 l
186.746538 694.471507 l
179.503361 710.485279 l
172.260184 518.36635 l
165.017006 511.850693 l
157.773829 506.384978 l
150.530652 501.502434 l
143.287475 496.530126 l
136.044297 491.264785 l
128.80112 485.314927 l
121.557943 480.807072 l
128.80112 486.509068 l
136.044297 490.288392 l
143.287475 495.692142 l
150.530652 502.168419 l
157.773829 506.68554 l
165.017006 511.981574 l
172.260184 517.000211 l
179.503361 523.035779 l
186.746538 528.787 l
193.989715 534.784925 l
201.232892 540.575526 l
208.47607 546.934242 l
215.719247 553.091425 l
222.962424 559.808615 l
230.205601 564.725906 l
237.448779 571.719914 l
244.691956 578.038671 l
251.935133 583.637005 l
259.17831 590.657653 l
266.421487 597.435071 l
273.664665 603.898028 l
280.907842 612.027687 l
288.151019 619.469933 l
295.394196 627.949958 l
302.637374 632.952958 l
309.880551 642.772483 l
317.123728 651.459833 l
324.366905 660.828225 l
331.610082 673.105527 l
338.85326 681.636515 l
346.096437 691.317631 l
353.339614 710.626005 l
360.582791 517.778545 l
367.825969 511.848956 l
375.069146 506.960621 l
382.312323 501.789676 l
389.5555 496.230143 l
396.798677 490.619069 l
404.041855 484.245296 l
411.285032 481.729607 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
0 setlinecap
gsave
325.943 257.053 103.45 466.926 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

0 2 m
-2 -2 l
2 -2 l
cl

gsave
0.749 0.212 0.047 setrgbcolor
fill
grestore
stroke
grestore
} bind def
404.042 483.9 o
396.799 489.315 o
389.556 495.529 o
382.312 501.321 o
375.069 507.803 o
367.826 511.83 o
360.583 516.953 o
353.34 523.812 o
346.096 530.341 o
338.853 534.394 o
331.61 540.706 o
324.367 545.609 o
317.124 552.094 o
309.881 556.856 o
302.637 566.288 o
295.394 570.649 o
288.151 577.197 o
280.908 583.422 o
273.665 589.971 o
266.421 598.217 o
259.178 605.309 o
251.935 612.025 o
244.692 618.036 o
237.449 627.33 o
230.206 635.472 o
222.962 644.124 o
215.719 652.159 o
208.476 664.529 o
201.233 671.858 o
193.99 678.787 o
186.747 694.472 o
179.503 710.485 o
172.26 518.366 o
165.017 511.851 o
157.774 506.385 o
150.531 501.502 o
143.287 496.53 o
136.044 491.265 o
128.801 485.315 o
121.558 480.807 o
128.801 486.509 o
136.044 490.288 o
143.287 495.692 o
150.531 502.168 o
157.774 506.686 o
165.017 511.982 o
172.26 517 o
179.503 523.036 o
186.747 528.787 o
193.99 534.785 o
201.233 540.576 o
208.476 546.934 o
215.719 553.091 o
222.962 559.809 o
230.206 564.726 o
237.449 571.72 o
244.692 578.039 o
251.935 583.637 o
259.178 590.658 o
266.421 597.435 o
273.665 603.898 o
280.908 612.028 o
288.151 619.47 o
295.394 627.95 o
302.637 632.953 o
309.881 642.772 o
317.124 651.46 o
324.367 660.828 o
331.61 673.106 o
338.853 681.637 o
346.096 691.318 o
353.34 710.626 o
360.583 517.779 o
367.826 511.849 o
375.069 506.961 o
382.312 501.79 o
389.556 496.23 o
396.799 490.619 o
404.042 484.245 o
411.285 481.73 o
grestore
1.500 setlinewidth
1 setlinejoin
[5.55 2.4] 0 setdash
0.847 0.263 0.082 setrgbcolor
gsave
325.943 257.053 103.45 466.926 clipbox
404.041855 485.152774 m
396.798677 490.320823 l
389.5555 496.473373 l
382.312323 500.809229 l
375.069146 505.26728 l
367.825969 512.671883 l
360.582791 518.893927 l
353.339614 522.483879 l
346.096437 528.579097 l
338.85326 534.744966 l
331.610082 540.436538 l
324.366905 548.178188 l
317.123728 552.377373 l
309.880551 559.32621 l
302.637374 565.6884 l
295.394196 571.122844 l
288.151019 577.059383 l
280.907842 585.066268 l
273.664665 591.309161 l
266.421487 598.385404 l
259.17831 604.798557 l
251.935133 613.089789 l
244.691956 620.234947 l
237.448779 626.093305 l
230.205601 634.568698 l
222.962424 641.289362 l
215.719247 653.667431 l
208.47607 660.250265 l
201.232892 670.376144 l
193.989715 684.26629 l
186.746538 697.528092 l
179.503361 709.784546 l
172.260184 517.096924 l
165.017006 511.588353 l
157.773829 507.198638 l
150.530652 501.571349 l
143.287475 495.322086 l
136.044297 491.073676 l
128.80112 485.117447 l
121.557943 481.119796 l
128.80112 485.395424 l
136.044297 490.236272 l
143.287475 495.217845 l
150.530652 500.534148 l
157.773829 507.246705 l
165.017006 511.590669 l
172.260184 517.680095 l
179.503361 523.884187 l
186.746538 530.477445 l
193.989715 535.343195 l
201.232892 540.322452 l
208.47607 546.418827 l
215.719247 552.938538 l
222.962424 558.304066 l
230.205601 565.099437 l
237.448779 572.268918 l
244.691956 577.386005 l
251.935133 584.445454 l
259.17831 589.947654 l
266.421487 599.841306 l
273.664665 606.290365 l
280.907842 612.031162 l
288.151019 619.500626 l
295.394196 626.349855 l
302.637374 637.622387 l
309.880551 642.678087 l
317.123728 652.455916 l
324.366905 661.191332 l
331.610082 670.757204 l
338.85326 685.540928 l
346.096437 696.998199 l
353.339614 711.498735 l
360.582791 518.110959 l
367.825969 511.614413 l
375.069146 507.623132 l
382.312323 501.364024 l
389.5555 496.521439 l
396.798677 490.083963 l
404.041855 484.007278 l
411.285032 480.881778 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
325.943 257.053 103.45 466.926 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-4 -4 m
4 -4 l
4 4 l
-4 4 l
cl

gsave
grestore
stroke
grestore
} bind def
404.042 485.153 o
396.799 490.321 o
389.556 496.473 o
382.312 500.809 o
375.069 505.267 o
367.826 512.672 o
360.583 518.894 o
353.34 522.484 o
346.096 528.579 o
338.853 534.745 o
331.61 540.437 o
324.367 548.178 o
317.124 552.377 o
309.881 559.326 o
302.637 565.688 o
295.394 571.123 o
288.151 577.059 o
280.908 585.066 o
273.665 591.309 o
266.421 598.385 o
259.178 604.799 o
251.935 613.09 o
244.692 620.235 o
237.449 626.093 o
230.206 634.569 o
222.962 641.289 o
215.719 653.667 o
208.476 660.25 o
201.233 670.376 o
193.99 684.266 o
186.747 697.528 o
179.503 709.785 o
172.26 517.097 o
165.017 511.588 o
157.774 507.199 o
150.531 501.571 o
143.287 495.322 o
136.044 491.074 o
128.801 485.117 o
121.558 481.12 o
128.801 485.395 o
136.044 490.236 o
143.287 495.218 o
150.531 500.534 o
157.774 507.247 o
165.017 511.591 o
172.26 517.68 o
179.503 523.884 o
186.747 530.477 o
193.99 535.343 o
201.233 540.322 o
208.476 546.419 o
215.719 552.939 o
222.962 558.304 o
230.206 565.099 o
237.449 572.269 o
244.692 577.386 o
251.935 584.445 o
259.178 589.948 o
266.421 599.841 o
273.665 606.29 o
280.908 612.031 o
288.151 619.501 o
295.394 626.35 o
302.637 637.622 o
309.881 642.678 o
317.124 652.456 o
324.367 661.191 o
331.61 670.757 o
338.853 685.541 o
346.096 696.998 o
353.34 711.499 o
360.583 518.111 o
367.826 511.614 o
375.069 507.623 o
382.312 501.364 o
389.556 496.521 o
396.799 490.084 o
404.042 484.007 o
411.285 480.882 o
grestore
1.500 setlinewidth
1 setlinejoin
[1.5 2.475] 0 setdash
0.937 0.424 0.000 setrgbcolor
gsave
325.943 257.053 103.45 466.926 clipbox
404.041855 485.064748 m
396.798677 489.971615 l
389.5555 495.239272 l
382.312323 501.278315 l
375.069146 506.831478 l
367.825969 511.82637 l
360.582791 516.66606 l
353.339614 523.866234 l
346.096437 527.82798 l
338.85326 535.792012 l
331.610082 540.713356 l
324.366905 547.541158 l
317.123728 552.988342 l
309.880551 559.630826 l
302.637374 563.366137 l
295.394196 571.425722 l
288.151019 577.463028 l
280.907842 583.434314 l
273.664665 591.962406 l
266.421487 598.384825 l
259.17831 604.497994 l
251.935133 612.493877 l
244.691956 620.426636 l
237.448779 629.008585 l
230.205601 634.256553 l
222.962424 644.752488 l
215.719247 651.194597 l
208.47607 660.43095 l
201.232892 672.481238 l
193.989715 682.743209 l
186.746538 694.347576 l
179.503361 712.295023 l
172.260184 518.471171 l
165.017006 512.709525 l
157.773829 507.963074 l
150.530652 499.627828 l
143.287475 495.655079 l
136.044297 490.777168 l
128.80112 485.321297 l
121.557943 479.451357 l
128.80112 485.376313 l
136.044297 490.598799 l
143.287475 495.913365 l
150.530652 500.746684 l
157.773829 506.642106 l
165.017006 512.071917 l
172.260184 518.796635 l
179.503361 523.331708 l
186.746538 528.551878 l
193.989715 535.842395 l
201.232892 540.577843 l
208.47607 546.644683 l
215.719247 552.157308 l
222.962424 559.482572 l
230.205601 564.097563 l
237.448779 571.064932 l
244.691956 576.949929 l
251.935133 582.626444 l
259.17831 592.722209 l
266.421487 596.945716 l
273.664665 603.935092 l
280.907842 611.263251 l
288.151019 621.115207 l
295.394196 627.135139 l
302.637374 633.785151 l
309.880551 643.160493 l
317.123728 652.47271 l
324.366905 662.424853 l
331.610082 669.114246 l
338.85326 681.574549 l
346.096437 696.948974 l
353.339614 711.234658 l
360.582791 519.022491 l
367.825969 511.542024 l
375.069146 506.692489 l
382.312323 500.314083 l
389.5555 495.492926 l
396.798677 491.454736 l
404.041855 484.993516 l
411.285032 478.610477 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
325.943 257.053 103.45 466.926 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
404.042 485.065 o
396.799 489.972 o
389.556 495.239 o
382.312 501.278 o
375.069 506.831 o
367.826 511.826 o
360.583 516.666 o
353.34 523.866 o
346.096 527.828 o
338.853 535.792 o
331.61 540.713 o
324.367 547.541 o
317.124 552.988 o
309.881 559.631 o
302.637 563.366 o
295.394 571.426 o
288.151 577.463 o
280.908 583.434 o
273.665 591.962 o
266.421 598.385 o
259.178 604.498 o
251.935 612.494 o
244.692 620.427 o
237.449 629.009 o
230.206 634.257 o
222.962 644.752 o
215.719 651.195 o
208.476 660.431 o
201.233 672.481 o
193.99 682.743 o
186.747 694.348 o
179.503 712.295 o
172.26 518.471 o
165.017 512.71 o
157.774 507.963 o
150.531 499.628 o
143.287 495.655 o
136.044 490.777 o
128.801 485.321 o
121.558 479.451 o
128.801 485.376 o
136.044 490.599 o
143.287 495.913 o
150.531 500.747 o
157.774 506.642 o
165.017 512.072 o
172.26 518.797 o
179.503 523.332 o
186.747 528.552 o
193.99 535.842 o
201.233 540.578 o
208.476 546.645 o
215.719 552.157 o
222.962 559.483 o
230.206 564.098 o
237.449 571.065 o
244.692 576.95 o
251.935 582.626 o
259.178 592.722 o
266.421 596.946 o
273.665 603.935 o
280.908 611.263 o
288.151 621.115 o
295.394 627.135 o
302.637 633.785 o
309.881 643.16 o
317.124 652.473 o
324.367 662.425 o
331.61 669.114 o
338.853 681.575 o
346.096 696.949 o
353.34 711.235 o
360.583 519.022 o
367.826 511.542 o
375.069 506.692 o
382.312 500.314 o
389.556 495.493 o
396.799 491.455 o
404.042 484.994 o
411.285 478.61 o
grestore
2.500 setlinewidth
0 setlinejoin
2 setlinecap
0.000 setgray
gsave
103.45 466.92625 m
103.45 723.97925 l
stroke
grestore
gsave
429.392975 466.92625 m
429.392975 723.97925 l
stroke
grestore
gsave
103.45 466.92625 m
429.392975 466.92625 l
stroke
grestore
gsave
103.45 723.97925 m
429.392975 723.97925 l
stroke
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

250.303 740.037 translate
0 rotate
0 0 m /parenleft glyphshow
8.3252 0 m /d glyphshow
20.8252 0 m /parenright glyphshow
grestore
1.500 setlinewidth
1 setlinejoin
0.749 0.212 0.047 setrgbcolor
gsave
227.421487 560.92625 m
247.421487 560.92625 l
267.421487 560.92625 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
0 setlinecap
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

0 2 m
-2 -2 l
2 -2 l
cl

gsave
0.749 0.212 0.047 setrgbcolor
fill
grestore
stroke
grestore
} bind def
247.421 560.926 o
grestore
0.000 setgray
gsave
283.421 553.926 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.796875 moveto
/omega glyphshow
/TimesNewRomanPSMT 14.0 selectfont
13.4646 -4.35562 moveto
/x glyphshow
grestore
1.500 setlinewidth
1 setlinejoin
[5.55 2.4] 0 setdash
0.847 0.263 0.082 setrgbcolor
gsave
227.421487 530.92625 m
247.421487 530.92625 l
267.421487 530.92625 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-4 -4 m
4 -4 l
4 4 l
-4 4 l
cl

gsave
grestore
stroke
grestore
} bind def
247.421 530.926 o
grestore
0.000 setgray
gsave
283.421 523.926 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.796875 moveto
/omega glyphshow
/TimesNewRomanPSMT 14.0 selectfont
13.4646 -4.35562 moveto
/y glyphshow
grestore
1.500 setlinewidth
1 setlinejoin
[1.5 2.475] 0 setdash
0.937 0.424 0.000 setrgbcolor
gsave
227.421487 497.92625 m
247.421487 497.92625 l
267.421487 497.92625 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
247.421 497.926 o
grestore
0.000 setgray
gsave
283.421 490.926 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.796875 moveto
/omega glyphshow
/TimesNewRomanPSMT 14.0 selectfont
13.4646 -4.35562 moveto
/z glyphshow
grestore
gsave
569.584 466.92625 m
895.526975 466.92625 l
895.526975 723.97925 l
569.584 723.97925 l
cl
1.000 setgray
fill
grestore
2.500 setlinewidth
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
587.692 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
587.692 723.979 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

574.395 439.567 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /four glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
623.908 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
623.908 723.979 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

610.611 439.567 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /three glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
660.124 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
660.124 723.979 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

646.827 439.567 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /two glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
696.34 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
696.34 723.979 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

683.043 439.567 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /one glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
732.555 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
732.555 723.979 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

726.305 439.567 translate
0 rotate
0 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
768.771 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
768.771 723.979 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

762.521 439.567 translate
0 rotate
0 0 m /one glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
804.987 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
804.987 723.979 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

798.737 439.567 translate
0 rotate
0 0 m /two glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
841.203 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
841.203 723.979 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

834.953 439.567 translate
0 rotate
0 0 m /three glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
877.419 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
877.419 723.979 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

871.169 439.567 translate
0 rotate
0 0 m /four glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
573.206 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
573.206 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
580.449 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
580.449 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
594.935 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
594.935 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
602.178 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
602.178 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
609.421 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
609.421 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
616.665 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
616.665 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
631.151 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
631.151 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
638.394 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
638.394 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
645.637 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
645.637 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
652.881 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
652.881 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
667.367 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
667.367 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
674.61 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
674.61 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
681.853 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
681.853 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
689.096 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
689.096 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
703.583 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
703.583 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
710.826 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
710.826 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
718.069 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
718.069 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
725.312 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
725.312 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
739.799 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
739.799 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
747.042 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
747.042 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
754.285 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
754.285 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
761.528 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
761.528 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
776.015 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
776.015 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
783.258 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
783.258 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
790.501 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
790.501 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
797.744 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
797.744 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
812.23 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
812.23 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
819.474 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
819.474 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
826.717 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
826.717 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
833.96 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
833.96 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
848.446 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
848.446 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
855.69 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
855.69 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
862.933 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
862.933 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
870.176 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
870.176 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
884.662 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
884.662 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
891.905 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
891.905 723.979 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

614.509 412.864 translate
0 rotate
0 0 m /E glyphshow
15.271 0 m /l glyphshow
22.2168 0 m /e glyphshow
33.313 0 m /c glyphshow
44.4092 0 m /t glyphshow
51.355 0 m /r glyphshow
59.6802 0 m /i glyphshow
66.626 0 m /c glyphshow
77.7222 0 m /space glyphshow
83.9722 0 m /F glyphshow
97.876 0 m /i glyphshow
104.822 0 m /e glyphshow
115.918 0 m /l glyphshow
122.864 0 m /d glyphshow
135.364 0 m /space glyphshow
141.614 0 m /parenleft glyphshow
149.939 0 m /M glyphshow
172.168 0 m /V glyphshow
190.222 0 m /slash glyphshow
197.168 0 m /c glyphshow
208.264 0 m /m glyphshow
227.71 0 m /parenright glyphshow
grestore
2.500 setlinewidth
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
569.584 471.707 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
895.527 471.707 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

503.334 463.027 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /five glyphshow
43.75 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
569.584 505.379 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
895.527 505.379 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

503.334 496.699 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /five glyphshow
43.75 0 m /two glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
569.584 539.051 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
895.527 539.051 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

503.334 530.371 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /five glyphshow
43.75 0 m /four glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
569.584 572.723 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
895.527 572.723 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

503.334 564.044 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /five glyphshow
43.75 0 m /six glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
569.584 606.396 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
895.527 606.396 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

503.334 597.716 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /five glyphshow
43.75 0 m /eight glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
569.584 640.068 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
895.527 640.068 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

503.334 631.388 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /six glyphshow
43.75 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
569.584 673.74 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
895.527 673.74 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

503.334 665.06 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /six glyphshow
43.75 0 m /two glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
569.584 707.412 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
895.527 707.412 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

503.334 698.733 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /six glyphshow
43.75 0 m /four glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
569.584 480.125 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
895.527 480.125 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
569.584 488.543 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
895.527 488.543 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
569.584 496.961 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
895.527 496.961 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
569.584 513.797 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
895.527 513.797 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
569.584 522.215 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
895.527 522.215 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
569.584 530.633 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
895.527 530.633 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
569.584 547.469 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
895.527 547.469 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
569.584 555.887 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
895.527 555.887 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
569.584 564.305 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
895.527 564.305 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
569.584 581.141 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
895.527 581.141 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
569.584 589.56 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
895.527 589.56 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
569.584 597.978 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
895.527 597.978 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
569.584 614.814 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
895.527 614.814 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
569.584 623.232 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
895.527 623.232 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
569.584 631.65 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
895.527 631.65 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
569.584 648.486 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
895.527 648.486 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
569.584 656.904 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
895.527 656.904 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
569.584 665.322 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
895.527 665.322 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
569.584 682.158 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
895.527 682.158 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
569.584 690.576 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
895.527 690.576 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
569.584 698.994 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
895.527 698.994 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
569.584 715.83 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
895.527 715.83 o
grestore
gsave
488.334 552.453 translate
90 rotate
/TimesNewRomanPSMT 25.0 selectfont
0 0.6875 moveto
/less glyphshow
14.0991 0.6875 moveto
/omega glyphshow
/TimesNewRomanPSMT 17.5 selectfont
30.9299 -5.75313 moveto
/x glyphshow
39.6799 -5.75313 moveto
/comma glyphshow
46.7009 -5.75313 moveto
/y glyphshow
55.4509 -5.75313 moveto
/comma glyphshow
62.4719 -5.75313 moveto
/z glyphshow
/TimesNewRomanPSMT 25.0 selectfont
71.3127 0.6875 moveto
/greater glyphshow
grestore
2 setlinecap
0.749 0.212 0.047 setrgbcolor
gsave
325.943 257.053 569.584 466.926 clipbox
870.175855 587.412239 m
862.932677 587.806541 l
855.6895 588.024568 l
848.446323 588.292936 l
841.203146 588.247647 l
833.959969 587.983993 l
826.716791 588.513826 l
819.473614 587.848799 l
812.230437 588.288222 l
804.98726 588.133498 l
797.744082 587.858733 l
790.500905 587.716636 l
783.257728 586.782231 l
776.014551 587.181752 l
768.771374 586.562856 l
761.528196 586.347354 l
754.285019 585.615319 l
747.041842 584.900457 l
739.798665 584.394869 l
732.555488 584.062187 l
725.31231 583.47528 l
718.069133 582.707216 l
710.825956 581.294665 l
703.582779 580.39107 l
696.339601 579.98633 l
689.096424 578.244128 l
681.853247 577.816995 l
674.61007 575.454719 l
667.366893 574.075335 l
660.123715 572.794611 l
652.880538 571.515571 l
645.637361 568.391292 l
638.394184 567.333142 l
631.151006 563.848906 l
623.907829 560.738937 l
616.664652 558.319923 l
609.421475 554.591227 l
602.178298 549.978129 l
594.93512 587.204312 l
587.691943 587.456181 l
594.93512 587.708386 l
602.178298 588.423079 l
609.421475 587.8456 l
616.664652 588.056725 l
623.907829 588.373918 l
631.151006 588.143768 l
638.394184 588.584033 l
645.637361 588.305227 l
652.880538 588.176599 l
660.123715 588.018844 l
667.366893 587.882303 l
674.61007 588.000324 l
681.853247 587.088312 l
689.096424 587.187981 l
696.339601 586.810347 l
703.582779 586.110806 l
710.825956 585.81449 l
718.069133 585.343584 l
725.31231 585.050636 l
732.555488 584.227349 l
739.798665 583.463158 l
747.041842 582.775065 l
754.285019 582.290522 l
761.528196 580.771398 l
768.771374 579.994579 l
776.014551 578.36097 l
783.257728 577.152305 l
790.500905 575.718204 l
797.744082 574.693221 l
804.98726 572.999507 l
812.230437 570.386709 l
819.473614 568.377655 l
826.716791 567.605887 l
833.959969 564.307859 l
841.203146 562.246612 l
848.446323 558.096676 l
855.6895 554.73063 l
862.932677 549.723567 l
870.175855 587.71108 l
877.419032 586.931399 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
0 setlinecap
gsave
325.943 257.053 569.584 466.926 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

0 2 m
-2 -2 l
2 -2 l
cl

gsave
0.749 0.212 0.047 setrgbcolor
fill
grestore
stroke
grestore
} bind def
870.176 587.412 o
862.933 587.807 o
855.69 588.025 o
848.446 588.293 o
841.203 588.248 o
833.96 587.984 o
826.717 588.514 o
819.474 587.849 o
812.23 588.288 o
804.987 588.133 o
797.744 587.859 o
790.501 587.717 o
783.258 586.782 o
776.015 587.182 o
768.771 586.563 o
761.528 586.347 o
754.285 585.615 o
747.042 584.9 o
739.799 584.395 o
732.555 584.062 o
725.312 583.475 o
718.069 582.707 o
710.826 581.295 o
703.583 580.391 o
696.34 579.986 o
689.096 578.244 o
681.853 577.817 o
674.61 575.455 o
667.367 574.075 o
660.124 572.795 o
652.881 571.516 o
645.637 568.391 o
638.394 567.333 o
631.151 563.849 o
623.908 560.739 o
616.665 558.32 o
609.421 554.591 o
602.178 549.978 o
594.935 587.204 o
587.692 587.456 o
594.935 587.708 o
602.178 588.423 o
609.421 587.846 o
616.665 588.057 o
623.908 588.374 o
631.151 588.144 o
638.394 588.584 o
645.637 588.305 o
652.881 588.177 o
660.124 588.019 o
667.367 587.882 o
674.61 588 o
681.853 587.088 o
689.096 587.188 o
696.34 586.81 o
703.583 586.111 o
710.826 585.814 o
718.069 585.344 o
725.312 585.051 o
732.555 584.227 o
739.799 583.463 o
747.042 582.775 o
754.285 582.291 o
761.528 580.771 o
768.771 579.995 o
776.015 578.361 o
783.258 577.152 o
790.501 575.718 o
797.744 574.693 o
804.987 573 o
812.23 570.387 o
819.474 568.378 o
826.717 567.606 o
833.96 564.308 o
841.203 562.247 o
848.446 558.097 o
855.69 554.731 o
862.933 549.724 o
870.176 587.711 o
877.419 586.931 o
grestore
1.500 setlinewidth
1 setlinejoin
[5.55 2.4] 0 setdash
0.847 0.263 0.082 setrgbcolor
gsave
325.943 257.053 569.584 466.926 clipbox
870.175855 587.567468 m
862.932677 587.645082 l
855.6895 588.280814 l
848.446323 588.144105 l
841.203146 588.59969 l
833.959969 588.142421 l
826.716791 588.52881 l
819.473614 588.378969 l
812.230437 587.986519 l
804.98726 588.219026 l
797.744082 587.935842 l
790.500905 587.81479 l
783.257728 587.375536 l
776.014551 586.882406 l
768.771374 586.990662 l
761.528196 586.453253 l
754.285019 586.117204 l
747.041842 584.80668 l
739.798665 584.677379 l
732.555488 584.432076 l
725.31231 583.128287 l
718.069133 582.844767 l
710.825956 581.982757 l
703.582779 580.921576 l
696.339601 579.702809 l
689.096424 578.287228 l
681.853247 577.026034 l
674.61007 576.040953 l
667.366893 574.669314 l
660.123715 572.797979 l
652.880538 570.836065 l
645.637361 568.808323 l
638.394184 566.957696 l
631.151006 564.980462 l
623.907829 562.130274 l
616.664652 558.001889 l
609.421475 555.118366 l
602.178298 550.296332 l
594.93512 587.265259 l
587.691943 587.257683 l
594.93512 587.827081 l
602.178298 587.633465 l
609.421475 587.881966 l
616.664652 588.020864 l
623.907829 588.063123 l
631.151006 588.329807 l
638.394184 588.336205 l
645.637361 588.45793 l
652.880538 588.368699 l
660.123715 588.100836 l
667.366893 587.835835 l
674.61007 587.938872 l
681.853247 587.710911 l
689.096424 587.201619 l
696.339601 586.544673 l
703.582779 586.263342 l
710.825956 585.706908 l
718.069133 586.277652 l
725.31231 584.714587 l
732.555488 584.171285 l
739.798665 582.9574 l
747.041842 582.541548 l
754.285019 581.667585 l
761.528196 580.662132 l
768.771374 579.48192 l
776.014551 578.616711 l
783.257728 577.203824 l
790.500905 575.591765 l
797.744082 574.992567 l
804.98726 572.303502 l
812.230437 571.024967 l
819.473614 569.601136 l
826.716791 566.242666 l
833.959969 564.875909 l
841.203146 560.82396 l
848.446323 558.64082 l
855.6895 554.872559 l
862.932677 550.535573 l
870.175855 587.45113 l
877.419032 587.269132 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
325.943 257.053 569.584 466.926 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-4 -4 m
4 -4 l
4 4 l
-4 4 l
cl

gsave
grestore
stroke
grestore
} bind def
870.176 587.567 o
862.933 587.645 o
855.69 588.281 o
848.446 588.144 o
841.203 588.6 o
833.96 588.142 o
826.717 588.529 o
819.474 588.379 o
812.23 587.987 o
804.987 588.219 o
797.744 587.936 o
790.501 587.815 o
783.258 587.376 o
776.015 586.882 o
768.771 586.991 o
761.528 586.453 o
754.285 586.117 o
747.042 584.807 o
739.799 584.677 o
732.555 584.432 o
725.312 583.128 o
718.069 582.845 o
710.826 581.983 o
703.583 580.922 o
696.34 579.703 o
689.096 578.287 o
681.853 577.026 o
674.61 576.041 o
667.367 574.669 o
660.124 572.798 o
652.881 570.836 o
645.637 568.808 o
638.394 566.958 o
631.151 564.98 o
623.908 562.13 o
616.665 558.002 o
609.421 555.118 o
602.178 550.296 o
594.935 587.265 o
587.692 587.258 o
594.935 587.827 o
602.178 587.633 o
609.421 587.882 o
616.665 588.021 o
623.908 588.063 o
631.151 588.33 o
638.394 588.336 o
645.637 588.458 o
652.881 588.369 o
660.124 588.101 o
667.367 587.836 o
674.61 587.939 o
681.853 587.711 o
689.096 587.202 o
696.34 586.545 o
703.583 586.263 o
710.826 585.707 o
718.069 586.278 o
725.312 584.715 o
732.555 584.171 o
739.799 582.957 o
747.042 582.542 o
754.285 581.668 o
761.528 580.662 o
768.771 579.482 o
776.015 578.617 o
783.258 577.204 o
790.501 575.592 o
797.744 574.993 o
804.987 572.304 o
812.23 571.025 o
819.474 569.601 o
826.717 566.243 o
833.96 564.876 o
841.203 560.824 o
848.446 558.641 o
855.69 554.873 o
862.933 550.536 o
870.176 587.451 o
877.419 587.269 o
grestore
1.500 setlinewidth
1 setlinejoin
[1.5 2.475] 0 setdash
0.937 0.424 0.000 setrgbcolor
gsave
325.943 257.053 569.584 466.926 clipbox
870.175855 484.119088 m
862.932677 489.136758 l
855.6895 494.517246 l
848.446323 499.560844 l
841.203146 504.948908 l
833.959969 510.187299 l
826.716791 515.406161 l
819.473614 520.170447 l
812.230437 525.689496 l
804.98726 531.194067 l
797.744082 536.11998 l
790.500905 542.035015 l
783.257728 546.896782 l
776.014551 551.974052 l
768.771374 558.161327 l
761.528196 563.496021 l
754.285019 568.309132 l
747.041842 574.224335 l
739.798665 578.765206 l
732.555488 583.995684 l
725.31231 589.582078 l
718.069133 594.676689 l
710.825956 600.301469 l
703.582779 606.446991 l
696.339601 611.616522 l
689.096424 617.78612 l
681.853247 623.647784 l
674.61007 630.131207 l
667.366893 635.519945 l
660.123715 642.527981 l
652.880538 648.279706 l
645.637361 656.007823 l
638.394184 662.671729 l
631.151006 670.478807 l
623.907829 678.9265 l
616.664652 689.06151 l
609.421475 698.346463 l
602.178298 712.295023 l
594.93512 483.548681 l
587.691943 478.610477 l
594.93512 483.643805 l
602.178298 488.672586 l
609.421475 494.853464 l
616.664652 499.637111 l
623.907829 504.871967 l
631.151006 510.117766 l
638.394184 515.286288 l
645.637361 520.721493 l
652.880538 526.154342 l
660.123715 531.514627 l
667.366893 536.292382 l
674.61007 541.556701 l
681.853247 546.810413 l
689.096424 551.891723 l
696.339601 557.217494 l
703.582779 563.121417 l
710.825956 567.751014 l
718.069133 573.303399 l
725.31231 578.507445 l
732.555488 584.263884 l
739.798665 589.988334 l
747.041842 594.506139 l
754.285019 600.187489 l
761.528196 606.446991 l
768.771374 611.945164 l
776.014551 617.695036 l
783.257728 623.790723 l
790.500905 629.749195 l
797.744082 635.459335 l
804.98726 642.248838 l
812.230437 649.177576 l
819.473614 655.60544 l
826.716791 662.689743 l
833.959969 669.976586 l
841.203146 679.130891 l
848.446323 687.652158 l
855.6895 698.167495 l
862.932677 711.49396 l
870.175855 483.491101 l
877.419032 478.773451 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
325.943 257.053 569.584 466.926 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
870.176 484.119 o
862.933 489.137 o
855.69 494.517 o
848.446 499.561 o
841.203 504.949 o
833.96 510.187 o
826.717 515.406 o
819.474 520.17 o
812.23 525.689 o
804.987 531.194 o
797.744 536.12 o
790.501 542.035 o
783.258 546.897 o
776.015 551.974 o
768.771 558.161 o
761.528 563.496 o
754.285 568.309 o
747.042 574.224 o
739.799 578.765 o
732.555 583.996 o
725.312 589.582 o
718.069 594.677 o
710.826 600.301 o
703.583 606.447 o
696.34 611.617 o
689.096 617.786 o
681.853 623.648 o
674.61 630.131 o
667.367 635.52 o
660.124 642.528 o
652.881 648.28 o
645.637 656.008 o
638.394 662.672 o
631.151 670.479 o
623.908 678.927 o
616.665 689.062 o
609.421 698.346 o
602.178 712.295 o
594.935 483.549 o
587.692 478.61 o
594.935 483.644 o
602.178 488.673 o
609.421 494.853 o
616.665 499.637 o
623.908 504.872 o
631.151 510.118 o
638.394 515.286 o
645.637 520.721 o
652.881 526.154 o
660.124 531.515 o
667.367 536.292 o
674.61 541.557 o
681.853 546.81 o
689.096 551.892 o
696.34 557.217 o
703.583 563.121 o
710.826 567.751 o
718.069 573.303 o
725.312 578.507 o
732.555 584.264 o
739.799 589.988 o
747.042 594.506 o
754.285 600.187 o
761.528 606.447 o
768.771 611.945 o
776.015 617.695 o
783.258 623.791 o
790.501 629.749 o
797.744 635.459 o
804.987 642.249 o
812.23 649.178 o
819.474 655.605 o
826.717 662.69 o
833.96 669.977 o
841.203 679.131 o
848.446 687.652 o
855.69 698.167 o
862.933 711.494 o
870.176 483.491 o
877.419 478.773 o
grestore
2.500 setlinewidth
0 setlinejoin
2 setlinecap
0.000 setgray
gsave
569.584 466.92625 m
569.584 723.97925 l
stroke
grestore
gsave
895.526975 466.92625 m
895.526975 723.97925 l
stroke
grestore
gsave
569.584 466.92625 m
895.526975 466.92625 l
stroke
grestore
gsave
569.584 723.97925 m
895.526975 723.97925 l
stroke
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

717.843 740.037 translate
0 rotate
0 0 m /parenleft glyphshow
8.3252 0 m /e glyphshow
19.4214 0 m /parenright glyphshow
grestore
1.500 setlinewidth
1 setlinejoin
0.749 0.212 0.047 setrgbcolor
gsave
693.555488 560.92625 m
713.555488 560.92625 l
733.555488 560.92625 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
0 setlinecap
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

0 2 m
-2 -2 l
2 -2 l
cl

gsave
0.749 0.212 0.047 setrgbcolor
fill
grestore
stroke
grestore
} bind def
713.555 560.926 o
grestore
0.000 setgray
gsave
749.555 553.926 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.796875 moveto
/omega glyphshow
/TimesNewRomanPSMT 14.0 selectfont
13.4646 -4.35562 moveto
/x glyphshow
grestore
1.500 setlinewidth
1 setlinejoin
[5.55 2.4] 0 setdash
0.847 0.263 0.082 setrgbcolor
gsave
693.555488 530.92625 m
713.555488 530.92625 l
733.555488 530.92625 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-4 -4 m
4 -4 l
4 4 l
-4 4 l
cl

gsave
grestore
stroke
grestore
} bind def
713.555 530.926 o
grestore
0.000 setgray
gsave
749.555 523.926 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.796875 moveto
/omega glyphshow
/TimesNewRomanPSMT 14.0 selectfont
13.4646 -4.35562 moveto
/y glyphshow
grestore
1.500 setlinewidth
1 setlinejoin
[1.5 2.475] 0 setdash
0.937 0.424 0.000 setrgbcolor
gsave
693.555488 497.92625 m
713.555488 497.92625 l
733.555488 497.92625 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
713.555 497.926 o
grestore
0.000 setgray
gsave
749.555 490.926 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.796875 moveto
/omega glyphshow
/TimesNewRomanPSMT 14.0 selectfont
13.4646 -4.35562 moveto
/z glyphshow
grestore
gsave
1035.718 466.92625 m
1361.660975 466.92625 l
1361.660975 723.97925 l
1035.718 723.97925 l
cl
1.000 setgray
fill
grestore
2.500 setlinewidth
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1053.83 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1053.83 723.979 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

1040.53 439.567 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /four glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1090.04 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1090.04 723.979 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

1076.74 439.567 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /three glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1126.26 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1126.26 723.979 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

1112.96 439.567 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /two glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1162.47 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1162.47 723.979 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

1149.18 439.567 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /one glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1198.69 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1198.69 723.979 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

1192.44 439.567 translate
0 rotate
0 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1234.91 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1234.91 723.979 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

1228.66 439.567 translate
0 rotate
0 0 m /one glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1271.12 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1271.12 723.979 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

1264.87 439.567 translate
0 rotate
0 0 m /two glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1307.34 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1307.34 723.979 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

1301.09 439.567 translate
0 rotate
0 0 m /three glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1343.55 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1343.55 723.979 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

1337.3 439.567 translate
0 rotate
0 0 m /four glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1039.34 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1039.34 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1046.58 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1046.58 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1061.07 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1061.07 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1068.31 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1068.31 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1075.56 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1075.56 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1082.8 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1082.8 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1097.29 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1097.29 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1104.53 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1104.53 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1111.77 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1111.77 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1119.01 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1119.01 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1133.5 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1133.5 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1140.74 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1140.74 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1147.99 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1147.99 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1155.23 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1155.23 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1169.72 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1169.72 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1176.96 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1176.96 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1184.2 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1184.2 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1191.45 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1191.45 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1205.93 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1205.93 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1213.18 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1213.18 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1220.42 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1220.42 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1227.66 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1227.66 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1242.15 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1242.15 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1249.39 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1249.39 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1256.63 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1256.63 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1263.88 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1263.88 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1278.36 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1278.36 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1285.61 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1285.61 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1292.85 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1292.85 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1300.09 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1300.09 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1314.58 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1314.58 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1321.82 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1321.82 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1329.07 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1329.07 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1336.31 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1336.31 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1350.8 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1350.8 723.979 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1358.04 466.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1358.04 723.979 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

1080.64 412.864 translate
0 rotate
0 0 m /E glyphshow
15.271 0 m /l glyphshow
22.2168 0 m /e glyphshow
33.313 0 m /c glyphshow
44.4092 0 m /t glyphshow
51.355 0 m /r glyphshow
59.6802 0 m /i glyphshow
66.626 0 m /c glyphshow
77.7222 0 m /space glyphshow
83.9722 0 m /F glyphshow
97.876 0 m /i glyphshow
104.822 0 m /e glyphshow
115.918 0 m /l glyphshow
122.864 0 m /d glyphshow
135.364 0 m /space glyphshow
141.614 0 m /parenleft glyphshow
149.939 0 m /M glyphshow
172.168 0 m /V glyphshow
190.222 0 m /slash glyphshow
197.168 0 m /c glyphshow
208.264 0 m /m glyphshow
227.71 0 m /parenright glyphshow
grestore
2.500 setlinewidth
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1035.72 477.22 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1361.66 477.22 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

967.874 468.54 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /zero glyphshow
26.5991 0 m /period glyphshow
32.8491 0 m /zero glyphshow
45.3491 0 m /two glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1035.72 534.364 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1361.66 534.364 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

981.968 525.685 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1035.72 591.509 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1361.66 591.509 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

981.968 582.829 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /two glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1035.72 648.654 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1361.66 648.654 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

981.968 639.974 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /four glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1035.72 705.799 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1361.66 705.799 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

981.968 697.119 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /six glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1035.72 491.506 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1361.66 491.506 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1035.72 505.792 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1361.66 505.792 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1035.72 520.078 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1361.66 520.078 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1035.72 548.651 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1361.66 548.651 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1035.72 562.937 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1361.66 562.937 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1035.72 577.223 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1361.66 577.223 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1035.72 605.795 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1361.66 605.795 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1035.72 620.081 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1361.66 620.081 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1035.72 634.368 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1361.66 634.368 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1035.72 662.94 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1361.66 662.94 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1035.72 677.226 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1361.66 677.226 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1035.72 691.512 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1361.66 691.512 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1035.72 720.085 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1361.66 720.085 o
grestore
gsave
952.874 552.453 translate
90 rotate
/TimesNewRomanPSMT 25.0 selectfont
0 0.6875 moveto
/less glyphshow
14.0991 0.6875 moveto
/omega glyphshow
/TimesNewRomanPSMT 17.5 selectfont
30.9299 -5.75313 moveto
/x glyphshow
39.6799 -5.75313 moveto
/comma glyphshow
46.7009 -5.75313 moveto
/y glyphshow
55.4509 -5.75313 moveto
/comma glyphshow
62.4719 -5.75313 moveto
/z glyphshow
/TimesNewRomanPSMT 25.0 selectfont
71.3127 0.6875 moveto
/greater glyphshow
grestore
2 setlinecap
0.749 0.212 0.047 setrgbcolor
gsave
325.943 257.053 1035.718 466.926 clipbox
1336.309855 686.981113 m
1329.066677 687.478244 l
1321.8235 687.995746 l
1314.580323 688.520106 l
1307.337146 688.963978 l
1300.093969 689.407564 l
1292.850791 689.924352 l
1285.607614 690.468427 l
1278.364437 691.040331 l
1271.12126 691.540262 l
1263.878083 692.046935 l
1256.634905 692.563495 l
1249.391728 693.050539 l
1242.148551 693.508812 l
1234.905374 694.064944 l
1227.662196 694.478957 l
1220.419019 695.114178 l
1213.175842 695.534849 l
1205.932665 696.09401 l
1198.689488 696.636542 l
1191.44631 697.264705 l
1184.203133 697.851353 l
1176.959956 698.352912 l
1169.716779 698.891701 l
1162.473601 699.564923 l
1155.230424 700.110169 l
1147.987247 700.673988 l
1140.74407 701.35041 l
1133.500892 701.998488 l
1126.257715 702.673881 l
1119.014538 703.352389 l
1111.771361 704.220703 l
1104.528184 704.926269 l
1097.285006 705.694065 l
1090.041829 706.599152 l
1082.798652 707.6341 l
1075.555475 708.814395 l
1068.312298 710.19984 l
1061.06912 712.243421 l
1053.825943 686.389951 l
1061.06912 686.980942 l
1068.312298 687.367583 l
1075.555475 687.97786 l
1082.798652 688.526992 l
1090.041829 688.993979 l
1097.285006 689.421107 l
1104.528184 690.010212 l
1111.771361 690.506457 l
1119.014538 690.979901 l
1126.257715 691.481688 l
1133.500892 692.030506 l
1140.74407 692.514465 l
1147.987247 692.987195 l
1155.230424 693.605158 l
1162.473601 694.106431 l
1169.716779 694.511158 l
1176.959956 695.13455 l
1184.203133 695.707312 l
1191.44631 696.219128 l
1198.689488 696.621456 l
1205.932665 697.27162 l
1213.175842 697.89144 l
1220.419019 698.370541 l
1227.662196 698.973875 l
1234.905374 699.504349 l
1242.148551 700.097597 l
1249.391728 700.598071 l
1256.634905 701.355495 l
1263.878083 701.996145 l
1271.12126 702.689082 l
1278.364437 703.28733 l
1285.607614 704.196845 l
1292.850791 704.940783 l
1300.093969 705.741981 l
1307.337146 706.670097 l
1314.580323 707.736989 l
1321.8235 708.788366 l
1329.066677 710.271385 l
1336.309855 712.255136 l
1343.553032 686.430981 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
0 setlinecap
gsave
325.943 257.053 1035.718 466.926 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

0 2 m
-2 -2 l
2 -2 l
cl

gsave
0.749 0.212 0.047 setrgbcolor
fill
grestore
stroke
grestore
} bind def
1336.31 686.981 o
1329.07 687.478 o
1321.82 687.996 o
1314.58 688.52 o
1307.34 688.964 o
1300.09 689.408 o
1292.85 689.924 o
1285.61 690.468 o
1278.36 691.04 o
1271.12 691.54 o
1263.88 692.047 o
1256.63 692.563 o
1249.39 693.051 o
1242.15 693.509 o
1234.91 694.065 o
1227.66 694.479 o
1220.42 695.114 o
1213.18 695.535 o
1205.93 696.094 o
1198.69 696.637 o
1191.45 697.265 o
1184.2 697.851 o
1176.96 698.353 o
1169.72 698.892 o
1162.47 699.565 o
1155.23 700.11 o
1147.99 700.674 o
1140.74 701.35 o
1133.5 701.998 o
1126.26 702.674 o
1119.01 703.352 o
1111.77 704.221 o
1104.53 704.926 o
1097.29 705.694 o
1090.04 706.599 o
1082.8 707.634 o
1075.56 708.814 o
1068.31 710.2 o
1061.07 712.243 o
1053.83 686.39 o
1061.07 686.981 o
1068.31 687.368 o
1075.56 687.978 o
1082.8 688.527 o
1090.04 688.994 o
1097.29 689.421 o
1104.53 690.01 o
1111.77 690.506 o
1119.01 690.98 o
1126.26 691.482 o
1133.5 692.031 o
1140.74 692.514 o
1147.99 692.987 o
1155.23 693.605 o
1162.47 694.106 o
1169.72 694.511 o
1176.96 695.135 o
1184.2 695.707 o
1191.45 696.219 o
1198.69 696.621 o
1205.93 697.272 o
1213.18 697.891 o
1220.42 698.371 o
1227.66 698.974 o
1234.91 699.504 o
1242.15 700.098 o
1249.39 700.598 o
1256.63 701.355 o
1263.88 701.996 o
1271.12 702.689 o
1278.36 703.287 o
1285.61 704.197 o
1292.85 704.941 o
1300.09 705.742 o
1307.34 706.67 o
1314.58 707.737 o
1321.82 708.788 o
1329.07 710.271 o
1336.31 712.255 o
1343.55 686.431 o
grestore
1.500 setlinewidth
1 setlinejoin
[5.55 2.4] 0 setdash
0.847 0.263 0.082 setrgbcolor
gsave
325.943 257.053 1035.718 466.926 clipbox
1336.309855 687.010172 m
1329.066677 687.529103 l
1321.8235 687.953631 l
1314.580323 688.423989 l
1307.337146 689.154984 l
1300.093969 689.491881 l
1292.850791 690.031813 l
1285.607614 690.507085 l
1278.364437 691.008502 l
1271.12126 691.438744 l
1263.878083 691.941761 l
1256.634905 692.484064 l
1249.391728 693.01731 l
1242.148551 693.548641 l
1234.905374 694.008171 l
1227.662196 694.590904 l
1220.419019 695.101921 l
1213.175842 695.707597 l
1205.932665 696.265358 l
1198.689488 696.725973 l
1191.44631 697.118043 l
1184.203133 697.817495 l
1176.959956 698.29311 l
1169.716779 698.917216 l
1162.473601 699.466634 l
1155.230424 700.156685 l
1147.987247 700.797334 l
1140.74407 701.447927 l
1133.500892 701.941572 l
1126.257715 702.702425 l
1119.014538 703.356075 l
1111.771361 704.212931 l
1104.528184 704.957013 l
1097.285006 705.712323 l
1090.041829 706.68844 l
1082.798652 707.594927 l
1075.555475 708.939914 l
1068.312298 710.342045 l
1061.06912 712.084187 l
1053.825943 686.445553 l
1061.06912 686.960113 l
1068.312298 687.389984 l
1075.555475 687.917287 l
1082.798652 688.481934 l
1090.041829 688.933006 l
1097.285006 689.538425 l
1104.528184 690.001269 l
1111.771361 690.48477 l
1119.014538 691.036303 l
1126.257715 691.519804 l
1133.500892 691.973847 l
1140.74407 692.589067 l
1147.987247 693.1456 l
1155.230424 693.619558 l
1162.473601 693.990713 l
1169.716779 694.631648 l
1176.959956 694.933858 l
1184.203133 695.559564 l
1191.44631 695.997493 l
1198.689488 696.700144 l
1205.932665 697.289563 l
1213.175842 697.868382 l
1220.419019 698.337654 l
1227.662196 699.085022 l
1234.905374 699.469034 l
1242.148551 700.046367 l
1249.391728 700.802677 l
1256.634905 701.254149 l
1263.878083 701.984144 l
1271.12126 702.662881 l
1278.364437 703.314331 l
1285.607614 704.079013 l
1292.850791 705.041844 l
1300.093969 705.788182 l
1307.337146 706.677783 l
1314.580323 707.830706 l
1321.8235 708.723221 l
1329.066677 710.178297 l
1336.309855 712.295023 l
1343.553032 686.384751 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
325.943 257.053 1035.718 466.926 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-4 -4 m
4 -4 l
4 4 l
-4 4 l
cl

gsave
grestore
stroke
grestore
} bind def
1336.31 687.01 o
1329.07 687.529 o
1321.82 687.954 o
1314.58 688.424 o
1307.34 689.155 o
1300.09 689.492 o
1292.85 690.032 o
1285.61 690.507 o
1278.36 691.009 o
1271.12 691.439 o
1263.88 691.942 o
1256.63 692.484 o
1249.39 693.017 o
1242.15 693.549 o
1234.91 694.008 o
1227.66 694.591 o
1220.42 695.102 o
1213.18 695.708 o
1205.93 696.265 o
1198.69 696.726 o
1191.45 697.118 o
1184.2 697.817 o
1176.96 698.293 o
1169.72 698.917 o
1162.47 699.467 o
1155.23 700.157 o
1147.99 700.797 o
1140.74 701.448 o
1133.5 701.942 o
1126.26 702.702 o
1119.01 703.356 o
1111.77 704.213 o
1104.53 704.957 o
1097.29 705.712 o
1090.04 706.688 o
1082.8 707.595 o
1075.56 708.94 o
1068.31 710.342 o
1061.07 712.084 o
1053.83 686.446 o
1061.07 686.96 o
1068.31 687.39 o
1075.56 687.917 o
1082.8 688.482 o
1090.04 688.933 o
1097.29 689.538 o
1104.53 690.001 o
1111.77 690.485 o
1119.01 691.036 o
1126.26 691.52 o
1133.5 691.974 o
1140.74 692.589 o
1147.99 693.146 o
1155.23 693.62 o
1162.47 693.991 o
1169.72 694.632 o
1176.96 694.934 o
1184.2 695.56 o
1191.45 695.997 o
1198.69 696.7 o
1205.93 697.29 o
1213.18 697.868 o
1220.42 698.338 o
1227.66 699.085 o
1234.91 699.469 o
1242.15 700.046 o
1249.39 700.803 o
1256.63 701.254 o
1263.88 701.984 o
1271.12 702.663 o
1278.36 703.314 o
1285.61 704.079 o
1292.85 705.042 o
1300.09 705.788 o
1307.34 706.678 o
1314.58 707.831 o
1321.82 708.723 o
1329.07 710.178 o
1336.31 712.295 o
1343.55 686.385 o
grestore
1.500 setlinewidth
1 setlinejoin
[1.5 2.475] 0 setdash
0.937 0.424 0.000 setrgbcolor
gsave
325.943 257.053 1035.718 466.926 clipbox
1336.309855 478.632135 m
1329.066677 478.721338 l
1321.8235 478.754911 l
1314.580323 478.846485 l
1307.337146 478.934688 l
1300.093969 478.98749 l
1292.850791 479.18161 l
1285.607614 479.175381 l
1278.364437 479.330386 l
1271.12126 479.408475 l
1263.878083 479.500506 l
1256.634905 479.626939 l
1249.391728 479.786744 l
1242.148551 480.00258 l
1234.905374 480.151927 l
1227.662196 480.304647 l
1220.419019 480.419765 l
1213.175842 480.605399 l
1205.932665 480.857408 l
1198.689488 480.991583 l
1191.44631 481.339195 l
1184.203133 481.570745 l
1176.959956 481.782323 l
1169.716779 482.00113 l
1162.473601 482.307083 l
1155.230424 482.572092 l
1147.987247 482.957876 l
1140.74407 483.299116 l
1133.500892 483.679642 l
1126.257715 484.186059 l
1119.014538 484.661331 l
1111.771361 485.244093 l
1104.528184 485.705108 l
1097.285006 486.363044 l
1090.041829 487.285388 l
1082.798652 488.007097 l
1075.555475 489.14422 l
1068.312298 490.688299 l
1061.06912 492.729794 l
1053.825943 590.150249 l
1061.06912 590.095076 l
1068.312298 589.979529 l
1075.555475 590.061503 l
1082.798652 589.923384 l
1090.041829 589.792809 l
1097.285006 589.691005 l
1104.528184 589.647747 l
1111.771361 589.561287 l
1119.014538 589.415539 l
1126.257715 589.290821 l
1133.500892 589.162617 l
1140.74407 589.053213 l
1147.987247 588.883093 l
1155.230424 588.761375 l
1162.473601 588.694773 l
1169.716779 588.451594 l
1176.959956 588.364362 l
1184.203133 588.164241 l
1191.44631 587.970092 l
1198.689488 587.663368 l
1205.932665 587.535907 l
1213.175842 587.209725 l
1220.419019 587.015433 l
1227.662196 586.767396 l
1234.905374 586.471901 l
1242.148551 586.152805 l
1249.391728 585.796136 l
1256.634905 585.39698 l
1263.878083 585.072198 l
1271.12126 584.663213 l
1278.364437 584.198284 l
1285.607614 583.627094 l
1292.850791 583.11102 l
1300.093969 582.377225 l
1307.337146 581.624429 l
1314.580323 580.721885 l
1321.8235 579.647679 l
1329.066677 578.114857 l
1336.309855 575.887756 l
1343.553032 478.610477 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
325.943 257.053 1035.718 466.926 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
1336.31 478.632 o
1329.07 478.721 o
1321.82 478.755 o
1314.58 478.846 o
1307.34 478.935 o
1300.09 478.987 o
1292.85 479.182 o
1285.61 479.175 o
1278.36 479.33 o
1271.12 479.408 o
1263.88 479.501 o
1256.63 479.627 o
1249.39 479.787 o
1242.15 480.003 o
1234.91 480.152 o
1227.66 480.305 o
1220.42 480.42 o
1213.18 480.605 o
1205.93 480.857 o
1198.69 480.992 o
1191.45 481.339 o
1184.2 481.571 o
1176.96 481.782 o
1169.72 482.001 o
1162.47 482.307 o
1155.23 482.572 o
1147.99 482.958 o
1140.74 483.299 o
1133.5 483.68 o
1126.26 484.186 o
1119.01 484.661 o
1111.77 485.244 o
1104.53 485.705 o
1097.29 486.363 o
1090.04 487.285 o
1082.8 488.007 o
1075.56 489.144 o
1068.31 490.688 o
1061.07 492.73 o
1053.83 590.15 o
1061.07 590.095 o
1068.31 589.98 o
1075.56 590.062 o
1082.8 589.923 o
1090.04 589.793 o
1097.29 589.691 o
1104.53 589.648 o
1111.77 589.561 o
1119.01 589.416 o
1126.26 589.291 o
1133.5 589.163 o
1140.74 589.053 o
1147.99 588.883 o
1155.23 588.761 o
1162.47 588.695 o
1169.72 588.452 o
1176.96 588.364 o
1184.2 588.164 o
1191.45 587.97 o
1198.69 587.663 o
1205.93 587.536 o
1213.18 587.21 o
1220.42 587.015 o
1227.66 586.767 o
1234.91 586.472 o
1242.15 586.153 o
1249.39 585.796 o
1256.63 585.397 o
1263.88 585.072 o
1271.12 584.663 o
1278.36 584.198 o
1285.61 583.627 o
1292.85 583.111 o
1300.09 582.377 o
1307.34 581.624 o
1314.58 580.722 o
1321.82 579.648 o
1329.07 578.115 o
1336.31 575.888 o
1343.55 478.61 o
grestore
2.500 setlinewidth
0 setlinejoin
2 setlinecap
0.000 setgray
gsave
1035.718 466.92625 m
1035.718 723.97925 l
stroke
grestore
gsave
1361.660975 466.92625 m
1361.660975 723.97925 l
stroke
grestore
gsave
1035.718 466.92625 m
1361.660975 466.92625 l
stroke
grestore
gsave
1035.718 723.97925 m
1361.660975 723.97925 l
stroke
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

1186.74 740.037 translate
0 rotate
0 0 m /parenleft glyphshow
8.3252 0 m /f glyphshow
16.6504 0 m /parenright glyphshow
grestore
1.500 setlinewidth
1 setlinejoin
0.749 0.212 0.047 setrgbcolor
gsave
1159.689488 560.92625 m
1179.689488 560.92625 l
1199.689488 560.92625 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
0 setlinecap
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

0 2 m
-2 -2 l
2 -2 l
cl

gsave
0.749 0.212 0.047 setrgbcolor
fill
grestore
stroke
grestore
} bind def
1179.69 560.926 o
grestore
0.000 setgray
gsave
1215.69 553.926 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.796875 moveto
/omega glyphshow
/TimesNewRomanPSMT 14.0 selectfont
13.4646 -4.35562 moveto
/x glyphshow
grestore
1.500 setlinewidth
1 setlinejoin
[5.55 2.4] 0 setdash
0.847 0.263 0.082 setrgbcolor
gsave
1159.689488 530.92625 m
1179.689488 530.92625 l
1199.689488 530.92625 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-4 -4 m
4 -4 l
4 4 l
-4 4 l
cl

gsave
grestore
stroke
grestore
} bind def
1179.69 530.926 o
grestore
0.000 setgray
gsave
1215.69 523.926 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.796875 moveto
/omega glyphshow
/TimesNewRomanPSMT 14.0 selectfont
13.4646 -4.35562 moveto
/y glyphshow
grestore
1.500 setlinewidth
1 setlinejoin
[1.5 2.475] 0 setdash
0.937 0.424 0.000 setrgbcolor
gsave
1159.689488 497.92625 m
1179.689488 497.92625 l
1199.689488 497.92625 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
1179.69 497.926 o
grestore
0.000 setgray
gsave
1215.69 490.926 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.796875 moveto
/omega glyphshow
/TimesNewRomanPSMT 14.0 selectfont
13.4646 -4.35562 moveto
/z glyphshow
grestore
gsave
103.45 66.60625 m
429.392975 66.60625 l
429.392975 323.65925 l
103.45 323.65925 l
cl
1.000 setgray
fill
grestore
2.500 setlinewidth
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
121.558 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
121.558 323.659 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

108.261 39.2469 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /four glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
157.774 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
157.774 323.659 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

144.477 39.2469 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /three glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
193.99 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
193.99 323.659 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

180.693 39.2469 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /two glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
230.206 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
230.206 323.659 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

216.909 39.2469 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /one glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
266.421 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
266.421 323.659 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

260.171 39.2469 translate
0 rotate
0 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
302.637 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
302.637 323.659 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

296.387 39.2469 translate
0 rotate
0 0 m /one glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
338.853 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
338.853 323.659 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

332.603 39.2469 translate
0 rotate
0 0 m /two glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
375.069 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
375.069 323.659 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

368.819 39.2469 translate
0 rotate
0 0 m /three glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
411.285 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
411.285 323.659 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

405.035 39.2469 translate
0 rotate
0 0 m /four glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
107.072 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
107.072 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
114.315 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
114.315 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
128.801 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
128.801 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
136.044 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
136.044 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
143.287 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
143.287 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
150.531 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
150.531 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
165.017 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
165.017 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
172.26 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
172.26 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
179.503 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
179.503 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
186.747 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
186.747 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
201.233 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
201.233 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
208.476 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
208.476 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
215.719 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
215.719 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
222.962 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
222.962 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
237.449 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
237.449 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
244.692 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
244.692 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
251.935 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
251.935 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
259.178 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
259.178 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
273.665 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
273.665 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
280.908 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
280.908 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
288.151 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
288.151 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
295.394 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
295.394 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
309.881 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
309.881 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
317.124 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
317.124 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
324.367 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
324.367 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
331.61 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
331.61 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
346.096 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
346.096 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
353.34 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
353.34 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
360.583 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
360.583 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
367.826 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
367.826 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
382.312 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
382.312 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
389.556 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
389.556 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
396.799 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
396.799 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
404.042 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
404.042 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
418.528 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
418.528 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
425.771 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
425.771 323.659 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

148.375 12.5438 translate
0 rotate
0 0 m /E glyphshow
15.271 0 m /l glyphshow
22.2168 0 m /e glyphshow
33.313 0 m /c glyphshow
44.4092 0 m /t glyphshow
51.355 0 m /r glyphshow
59.6802 0 m /i glyphshow
66.626 0 m /c glyphshow
77.7222 0 m /space glyphshow
83.9722 0 m /F glyphshow
97.876 0 m /i glyphshow
104.822 0 m /e glyphshow
115.918 0 m /l glyphshow
122.864 0 m /d glyphshow
135.364 0 m /space glyphshow
141.614 0 m /parenleft glyphshow
149.939 0 m /M glyphshow
172.168 0 m /V glyphshow
190.222 0 m /slash glyphshow
197.168 0 m /c glyphshow
208.264 0 m /m glyphshow
227.71 0 m /parenright glyphshow
grestore
2.500 setlinewidth
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
429.393 66.6063 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

37.2 57.9266 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /one glyphshow
43.75 0 m /five glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 108.265 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
429.393 108.265 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

37.2 99.5852 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /one glyphshow
43.75 0 m /six glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 149.924 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
429.393 149.924 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

37.2 141.244 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /one glyphshow
43.75 0 m /seven glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 191.582 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
429.393 191.582 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

37.2 182.902 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /one glyphshow
43.75 0 m /eight glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 233.241 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
429.393 233.241 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

37.2 224.561 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /one glyphshow
43.75 0 m /nine glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 274.899 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
429.393 274.899 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

37.2 266.22 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /two glyphshow
43.75 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 316.558 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
429.393 316.558 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

37.2 307.878 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /two glyphshow
43.75 0 m /one glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 74.938 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
429.393 74.938 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 83.2697 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
429.393 83.2697 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 91.6014 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
429.393 91.6014 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 99.9332 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
429.393 99.9332 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 116.597 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
429.393 116.597 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 124.928 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
429.393 124.928 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 133.26 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
429.393 133.26 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 141.592 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
429.393 141.592 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 158.255 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
429.393 158.255 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 166.587 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
429.393 166.587 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 174.919 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
429.393 174.919 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 183.25 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
429.393 183.25 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 199.914 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
429.393 199.914 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 208.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
429.393 208.246 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 216.577 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
429.393 216.577 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 224.909 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
429.393 224.909 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 241.573 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
429.393 241.573 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 249.904 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
429.393 249.904 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 258.236 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
429.393 258.236 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 266.568 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
429.393 266.568 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 283.231 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
429.393 283.231 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 291.563 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
429.393 291.563 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 299.895 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
429.393 299.895 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 308.226 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
429.393 308.226 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

27.8562 165.266 translate
90 rotate
0 0 m /S glyphshow
13.9038 0 m /t glyphshow
20.8496 0 m /r glyphshow
29.1748 0 m /a glyphshow
40.271 0 m /i glyphshow
47.2168 0 m /n glyphshow
grestore
2 setlinecap
0.749 0.212 0.047 setrgbcolor
gsave
325.943 257.053 103.45 66.606 clipbox
404.041855 311.605678 m
396.798677 307.425651 l
389.5555 303.909662 l
382.312323 299.985835 l
375.069146 295.838302 l
367.825969 292.61434 l
360.582791 288.95088 l
353.339614 284.839589 l
346.096437 280.247974 l
338.85326 276.936529 l
331.610082 272.734006 l
324.366905 269.116787 l
317.123728 264.325211 l
309.880551 260.082279 l
302.637374 255.490248 l
295.394196 251.098595 l
288.151019 246.237865 l
280.907842 241.70124 l
273.664665 237.220437 l
266.421487 231.764822 l
259.17831 226.396274 l
251.935133 221.330584 l
244.691956 216.051185 l
237.448779 210.118163 l
230.205601 203.736477 l
222.962424 197.367288 l
215.719247 191.15057 l
208.47607 182.882997 l
201.232892 175.758121 l
193.989715 167.74175 l
186.746538 157.156291 l
179.503361 144.290855 l
172.260184 287.974401 l
165.017006 292.46062 l
157.773829 296.661476 l
150.530652 300.357847 l
143.287475 303.892165 l
136.044297 307.744756 l
128.80112 310.997462 l
121.557943 314.964197 l
128.80112 311.212421 l
136.044297 307.435232 l
143.287475 303.647213 l
150.530652 300.264115 l
157.773829 296.525253 l
165.017006 292.758479 l
172.260184 289.086687 l
179.503361 284.789182 l
186.746538 280.601656 l
193.989715 277.231889 l
201.232892 272.762334 l
208.47607 268.469829 l
215.719247 264.175657 l
222.962424 260.370557 l
230.205601 255.507744 l
237.448779 251.091513 l
244.691956 246.429078 l
251.935133 241.572931 l
259.17831 236.800935 l
266.421487 232.408448 l
273.664665 226.915341 l
280.907842 221.640524 l
288.151019 216.723139 l
295.394196 210.103582 l
302.637374 204.747532 l
309.880551 197.31938 l
317.123728 191.095997 l
324.366905 183.692425 l
331.610082 175.228223 l
338.85326 167.370572 l
346.096437 158.121105 l
353.339614 144.407499 l
360.582791 288.94963 l
367.825969 292.705156 l
375.069146 296.28155 l
382.312323 299.588829 l
389.5555 303.981732 l
396.798677 307.124459 l
404.041855 310.874153 l
411.285032 314.774651 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
0 setlinecap
gsave
325.943 257.053 103.45 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

0 2 m
-2 -2 l
2 -2 l
cl

gsave
0.749 0.212 0.047 setrgbcolor
fill
grestore
stroke
grestore
} bind def
404.042 311.606 o
396.799 307.426 o
389.556 303.91 o
382.312 299.986 o
375.069 295.838 o
367.826 292.614 o
360.583 288.951 o
353.34 284.84 o
346.096 280.248 o
338.853 276.937 o
331.61 272.734 o
324.367 269.117 o
317.124 264.325 o
309.881 260.082 o
302.637 255.49 o
295.394 251.099 o
288.151 246.238 o
280.908 241.701 o
273.665 237.22 o
266.421 231.765 o
259.178 226.396 o
251.935 221.331 o
244.692 216.051 o
237.449 210.118 o
230.206 203.736 o
222.962 197.367 o
215.719 191.151 o
208.476 182.883 o
201.233 175.758 o
193.99 167.742 o
186.747 157.156 o
179.503 144.291 o
172.26 287.974 o
165.017 292.461 o
157.774 296.661 o
150.531 300.358 o
143.287 303.892 o
136.044 307.745 o
128.801 310.997 o
121.558 314.964 o
128.801 311.212 o
136.044 307.435 o
143.287 303.647 o
150.531 300.264 o
157.774 296.525 o
165.017 292.758 o
172.26 289.087 o
179.503 284.789 o
186.747 280.602 o
193.99 277.232 o
201.233 272.762 o
208.476 268.47 o
215.719 264.176 o
222.962 260.371 o
230.206 255.508 o
237.449 251.092 o
244.692 246.429 o
251.935 241.573 o
259.178 236.801 o
266.421 232.408 o
273.665 226.915 o
280.908 221.641 o
288.151 216.723 o
295.394 210.104 o
302.637 204.748 o
309.881 197.319 o
317.124 191.096 o
324.367 183.692 o
331.61 175.228 o
338.853 167.371 o
346.096 158.121 o
353.34 144.407 o
360.583 288.95 o
367.826 292.705 o
375.069 296.282 o
382.312 299.589 o
389.556 303.982 o
396.799 307.124 o
404.042 310.874 o
411.285 314.775 o
grestore
1.500 setlinewidth
1 setlinejoin
[5.55 2.4] 0 setdash
0.847 0.263 0.082 setrgbcolor
gsave
325.943 257.053 103.45 66.606 clipbox
404.041855 310.885817 m
396.798677 307.558125 l
389.5555 303.508906 l
382.312323 300.443247 l
375.069146 296.637731 l
367.825969 292.303983 l
360.582791 288.487219 l
353.339614 284.894578 l
346.096437 281.044487 l
338.85326 276.688244 l
331.610082 272.809409 l
324.366905 268.348602 l
317.123728 263.87863 l
309.880551 259.659444 l
302.637374 255.356524 l
295.394196 250.807401 l
288.151019 245.953753 l
280.907842 241.245911 l
273.664665 236.492661 l
266.421487 231.833976 l
259.17831 226.629146 l
251.935133 221.268513 l
244.691956 215.522537 l
237.448779 210.307293 l
230.205601 203.721896 l
222.962424 198.385842 l
215.719247 190.507777 l
208.47607 184.278978 l
201.232892 176.586295 l
193.989715 166.207463 l
186.746538 156.712627 l
179.503361 144.240031 l
172.260184 289.309144 l
165.017006 292.775975 l
157.773829 296.607737 l
150.530652 299.428859 l
143.287475 304.035888 l
136.044297 307.546461 l
128.80112 311.104108 l
121.557943 314.645925 l
128.80112 311.145767 l
136.044297 307.384825 l
143.287475 303.743444 l
150.530652 300.467409 l
157.773829 296.19365 l
165.017006 292.87179 l
172.260184 288.677599 l
179.503361 284.323855 l
186.746538 280.255056 l
193.989715 276.764896 l
201.232892 272.76525 l
208.47607 268.147391 l
215.719247 263.961531 l
222.962424 260.415548 l
230.205601 255.63272 l
237.448779 250.914463 l
244.691956 246.132052 l
251.935133 241.347975 l
259.17831 237.187527 l
266.421487 231.49904 l
273.664665 226.14174 l
280.907842 221.205192 l
288.151019 215.533785 l
295.394196 210.205646 l
302.637374 203.365298 l
309.880551 197.456854 l
317.123728 190.99185 l
324.366905 183.749913 l
331.610082 175.537747 l
338.85326 166.810263 l
346.096437 156.029008 l
353.339614 144.16338 l
360.582791 288.491801 l
367.825969 292.496029 l
375.069146 296.7123 l
382.312323 300.352431 l
389.5555 304.035888 l
396.798677 308.036366 l
404.041855 311.037454 l
411.285032 314.635094 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
325.943 257.053 103.45 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-4 -4 m
4 -4 l
4 4 l
-4 4 l
cl

gsave
grestore
stroke
grestore
} bind def
404.042 310.886 o
396.799 307.558 o
389.556 303.509 o
382.312 300.443 o
375.069 296.638 o
367.826 292.304 o
360.583 288.487 o
353.34 284.895 o
346.096 281.044 o
338.853 276.688 o
331.61 272.809 o
324.367 268.349 o
317.124 263.879 o
309.881 259.659 o
302.637 255.357 o
295.394 250.807 o
288.151 245.954 o
280.908 241.246 o
273.665 236.493 o
266.421 231.834 o
259.178 226.629 o
251.935 221.269 o
244.692 215.523 o
237.449 210.307 o
230.206 203.722 o
222.962 198.386 o
215.719 190.508 o
208.476 184.279 o
201.233 176.586 o
193.99 166.207 o
186.747 156.713 o
179.503 144.24 o
172.26 289.309 o
165.017 292.776 o
157.774 296.608 o
150.531 299.429 o
143.287 304.036 o
136.044 307.546 o
128.801 311.104 o
121.558 314.646 o
128.801 311.146 o
136.044 307.385 o
143.287 303.743 o
150.531 300.467 o
157.774 296.194 o
165.017 292.872 o
172.26 288.678 o
179.503 284.324 o
186.747 280.255 o
193.99 276.765 o
201.233 272.765 o
208.476 268.147 o
215.719 263.962 o
222.962 260.416 o
230.206 255.633 o
237.449 250.914 o
244.692 246.132 o
251.935 241.348 o
259.178 237.188 o
266.421 231.499 o
273.665 226.142 o
280.908 221.205 o
288.151 215.534 o
295.394 210.206 o
302.637 203.365 o
309.881 197.457 o
317.124 190.992 o
324.367 183.75 o
331.61 175.538 o
338.853 166.81 o
346.096 156.029 o
353.34 144.163 o
360.583 288.492 o
367.826 292.496 o
375.069 296.712 o
382.312 300.352 o
389.556 304.036 o
396.799 308.036 o
404.042 311.037 o
411.285 314.635 o
grestore
1.500 setlinewidth
1 setlinejoin
[1.5 2.475] 0 setdash
0.937 0.424 0.000 setrgbcolor
gsave
325.943 257.053 103.45 66.606 clipbox
404.041855 310.675441 m
396.798677 307.141539 l
389.5555 304.084628 l
382.312323 299.79004 l
375.069146 296.20948 l
367.825969 292.68516 l
360.582791 288.590949 l
353.339614 285.077043 l
346.096437 281.67145 l
338.85326 277.144406 l
331.610082 272.549042 l
324.366905 268.301944 l
317.123728 264.128582 l
309.880551 259.493643 l
302.637374 256.033893 l
295.394196 250.884469 l
288.151019 246.478652 l
280.907842 241.669579 l
273.664665 236.208965 l
266.421487 231.628599 l
259.17831 226.654974 l
251.935133 221.029392 l
244.691956 215.184269 l
237.448779 209.690745 l
230.205601 203.823543 l
222.962424 197.029436 l
215.719247 191.195978 l
208.47607 184.326052 l
201.232892 175.853103 l
193.989715 166.617383 l
186.746538 157.360002 l
179.503361 143.591406 l
172.260184 288.891724 l
165.017006 292.310232 l
157.773829 296.408608 l
150.530652 300.01583 l
143.287475 303.736779 l
136.044297 307.596035 l
128.80112 311.396552 l
121.557943 315.084591 l
128.80112 311.538191 l
136.044297 307.254434 l
143.287475 303.796351 l
150.530652 300.054572 l
157.773829 296.558163 l
165.017006 292.340643 l
172.260184 288.563871 l
179.503361 285.083709 l
186.746538 281.460241 l
193.989715 276.699908 l
201.232892 272.595283 l
208.47607 268.491074 l
215.719247 264.538503 l
222.962424 259.819413 l
230.205601 255.58148 l
237.448779 251.43228 l
244.691956 246.180793 l
251.935133 241.54627 l
259.17831 236.093988 l
266.421487 232.12517 l
273.664665 226.857019 l
280.907842 221.196027 l
288.151019 215.392146 l
295.394196 210.08192 l
302.637374 204.265541 l
309.880551 197.348958 l
317.123728 190.9931 l
324.366905 183.127117 l
331.610082 176.28177 l
338.85326 167.563034 l
346.096437 156.468507 l
353.339614 143.863854 l
360.582791 288.694262 l
367.825969 292.18359 l
375.069146 296.644396 l
382.312323 300.361596 l
389.5555 303.889666 l
396.798677 307.579788 l
404.041855 310.982882 l
411.285032 314.5197 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
325.943 257.053 103.45 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
404.042 310.675 o
396.799 307.142 o
389.556 304.085 o
382.312 299.79 o
375.069 296.209 o
367.826 292.685 o
360.583 288.591 o
353.34 285.077 o
346.096 281.671 o
338.853 277.144 o
331.61 272.549 o
324.367 268.302 o
317.124 264.129 o
309.881 259.494 o
302.637 256.034 o
295.394 250.884 o
288.151 246.479 o
280.908 241.67 o
273.665 236.209 o
266.421 231.629 o
259.178 226.655 o
251.935 221.029 o
244.692 215.184 o
237.449 209.691 o
230.206 203.824 o
222.962 197.029 o
215.719 191.196 o
208.476 184.326 o
201.233 175.853 o
193.99 166.617 o
186.747 157.36 o
179.503 143.591 o
172.26 288.892 o
165.017 292.31 o
157.774 296.409 o
150.531 300.016 o
143.287 303.737 o
136.044 307.596 o
128.801 311.397 o
121.558 315.085 o
128.801 311.538 o
136.044 307.254 o
143.287 303.796 o
150.531 300.055 o
157.774 296.558 o
165.017 292.341 o
172.26 288.564 o
179.503 285.084 o
186.747 281.46 o
193.99 276.7 o
201.233 272.595 o
208.476 268.491 o
215.719 264.539 o
222.962 259.819 o
230.206 255.581 o
237.449 251.432 o
244.692 246.181 o
251.935 241.546 o
259.178 236.094 o
266.421 232.125 o
273.665 226.857 o
280.908 221.196 o
288.151 215.392 o
295.394 210.082 o
302.637 204.266 o
309.881 197.349 o
317.124 190.993 o
324.367 183.127 o
331.61 176.282 o
338.853 167.563 o
346.096 156.469 o
353.34 143.864 o
360.583 288.694 o
367.826 292.184 o
375.069 296.644 o
382.312 300.362 o
389.556 303.89 o
396.799 307.58 o
404.042 310.983 o
411.285 314.52 o
grestore
2.500 setlinewidth
0 setlinejoin
2 setlinecap
0.000 setgray
gsave
103.45 66.60625 m
103.45 323.65925 l
stroke
grestore
gsave
429.392975 66.60625 m
429.392975 323.65925 l
stroke
grestore
gsave
103.45 66.60625 m
429.392975 66.60625 l
stroke
grestore
gsave
103.45 323.65925 m
429.392975 323.65925 l
stroke
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

250.303 339.717 translate
0 rotate
0 0 m /parenleft glyphshow
8.3252 0 m /g glyphshow
20.8252 0 m /parenright glyphshow
grestore
1.500 setlinewidth
1 setlinejoin
0.749 0.212 0.047 setrgbcolor
gsave
228.921487 157.60625 m
248.921487 157.60625 l
268.921487 157.60625 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
0 setlinecap
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

0 2 m
-2 -2 l
2 -2 l
cl

gsave
0.749 0.212 0.047 setrgbcolor
fill
grestore
stroke
grestore
} bind def
248.921 157.606 o
grestore
0.000 setgray
gsave
284.921 150.606 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.796875 moveto
/eta glyphshow
/TimesNewRomanPSMT 14.0 selectfont
10.7595 -4.35562 moveto
/one glyphshow
grestore
1.500 setlinewidth
1 setlinejoin
[5.55 2.4] 0 setdash
0.847 0.263 0.082 setrgbcolor
gsave
228.921487 127.60625 m
248.921487 127.60625 l
268.921487 127.60625 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-4 -4 m
4 -4 l
4 4 l
-4 4 l
cl

gsave
grestore
stroke
grestore
} bind def
248.921 127.606 o
grestore
0.000 setgray
gsave
284.921 120.606 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.796875 moveto
/eta glyphshow
/TimesNewRomanPSMT 14.0 selectfont
10.7595 -4.35562 moveto
/two glyphshow
grestore
1.500 setlinewidth
1 setlinejoin
[1.5 2.475] 0 setdash
0.937 0.424 0.000 setrgbcolor
gsave
228.921487 97.60625 m
248.921487 97.60625 l
268.921487 97.60625 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
248.921 97.6062 o
grestore
0.000 setgray
gsave
284.921 90.6062 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.796875 moveto
/eta glyphshow
/TimesNewRomanPSMT 14.0 selectfont
10.7595 -4.35562 moveto
/three glyphshow
grestore
gsave
569.584 66.60625 m
895.526975 66.60625 l
895.526975 323.65925 l
569.584 323.65925 l
cl
1.000 setgray
fill
grestore
2.500 setlinewidth
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
587.692 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
587.692 323.659 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

574.395 39.2469 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /four glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
623.908 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
623.908 323.659 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

610.611 39.2469 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /three glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
660.124 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
660.124 323.659 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

646.827 39.2469 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /two glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
696.34 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
696.34 323.659 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

683.043 39.2469 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /one glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
732.555 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
732.555 323.659 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

726.305 39.2469 translate
0 rotate
0 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
768.771 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
768.771 323.659 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

762.521 39.2469 translate
0 rotate
0 0 m /one glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
804.987 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
804.987 323.659 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

798.737 39.2469 translate
0 rotate
0 0 m /two glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
841.203 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
841.203 323.659 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

834.953 39.2469 translate
0 rotate
0 0 m /three glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
877.419 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
877.419 323.659 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

871.169 39.2469 translate
0 rotate
0 0 m /four glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
573.206 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
573.206 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
580.449 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
580.449 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
594.935 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
594.935 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
602.178 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
602.178 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
609.421 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
609.421 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
616.665 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
616.665 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
631.151 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
631.151 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
638.394 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
638.394 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
645.637 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
645.637 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
652.881 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
652.881 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
667.367 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
667.367 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
674.61 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
674.61 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
681.853 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
681.853 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
689.096 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
689.096 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
703.583 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
703.583 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
710.826 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
710.826 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
718.069 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
718.069 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
725.312 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
725.312 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
739.799 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
739.799 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
747.042 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
747.042 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
754.285 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
754.285 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
761.528 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
761.528 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
776.015 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
776.015 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
783.258 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
783.258 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
790.501 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
790.501 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
797.744 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
797.744 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
812.23 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
812.23 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
819.474 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
819.474 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
826.717 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
826.717 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
833.96 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
833.96 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
848.446 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
848.446 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
855.69 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
855.69 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
862.933 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
862.933 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
870.176 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
870.176 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
884.662 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
884.662 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
891.905 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
891.905 323.659 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

614.509 12.5438 translate
0 rotate
0 0 m /E glyphshow
15.271 0 m /l glyphshow
22.2168 0 m /e glyphshow
33.313 0 m /c glyphshow
44.4092 0 m /t glyphshow
51.355 0 m /r glyphshow
59.6802 0 m /i glyphshow
66.626 0 m /c glyphshow
77.7222 0 m /space glyphshow
83.9722 0 m /F glyphshow
97.876 0 m /i glyphshow
104.822 0 m /e glyphshow
115.918 0 m /l glyphshow
122.864 0 m /d glyphshow
135.364 0 m /space glyphshow
141.614 0 m /parenleft glyphshow
149.939 0 m /M glyphshow
172.168 0 m /V glyphshow
190.222 0 m /slash glyphshow
197.168 0 m /c glyphshow
208.264 0 m /m glyphshow
227.71 0 m /parenright glyphshow
grestore
2.500 setlinewidth
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
569.584 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
895.527 66.6063 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

503.334 57.9266 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /one glyphshow
43.75 0 m /five glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
569.584 109.448 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
895.527 109.448 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

503.334 100.769 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /one glyphshow
43.75 0 m /six glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
569.584 152.291 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
895.527 152.291 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

503.334 143.611 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /one glyphshow
43.75 0 m /seven glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
569.584 195.133 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
895.527 195.133 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

503.334 186.453 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /one glyphshow
43.75 0 m /eight glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
569.584 237.975 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
895.527 237.975 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

503.334 229.295 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /one glyphshow
43.75 0 m /nine glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
569.584 280.817 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
895.527 280.817 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

503.334 272.137 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /two glyphshow
43.75 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
569.584 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
895.527 323.659 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

503.334 314.98 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /two glyphshow
43.75 0 m /one glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
569.584 75.1747 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
895.527 75.1747 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
569.584 83.7431 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
895.527 83.7431 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
569.584 92.3116 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
895.527 92.3116 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
569.584 100.88 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
895.527 100.88 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
569.584 118.017 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
895.527 118.017 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
569.584 126.585 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
895.527 126.585 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
569.584 135.154 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
895.527 135.154 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
569.584 143.722 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
895.527 143.722 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
569.584 160.859 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
895.527 160.859 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
569.584 169.427 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
895.527 169.427 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
569.584 177.996 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
895.527 177.996 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
569.584 186.564 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
895.527 186.564 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
569.584 203.701 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
895.527 203.701 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
569.584 212.27 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
895.527 212.27 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
569.584 220.838 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
895.527 220.838 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
569.584 229.406 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
895.527 229.406 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
569.584 246.543 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
895.527 246.543 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
569.584 255.112 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
895.527 255.112 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
569.584 263.68 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
895.527 263.68 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
569.584 272.249 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
895.527 272.249 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
569.584 289.386 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
895.527 289.386 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
569.584 297.954 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
895.527 297.954 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
569.584 306.522 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
895.527 306.522 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
569.584 315.091 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
895.527 315.091 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

493.99 165.266 translate
90 rotate
0 0 m /S glyphshow
13.9038 0 m /t glyphshow
20.8496 0 m /r glyphshow
29.1748 0 m /a glyphshow
40.271 0 m /i glyphshow
47.2168 0 m /n glyphshow
grestore
2 setlinecap
0.749 0.212 0.047 setrgbcolor
gsave
325.943 257.053 569.584 66.606 clipbox
870.175855 308.425861 m
862.932677 304.213191 l
855.6895 300.625159 l
848.446323 297.431276 l
841.203146 293.682586 l
833.959969 289.995161 l
826.716791 286.368143 l
819.473614 282.730843 l
812.230437 278.81464 l
804.98726 275.392837 l
797.744082 271.432078 l
790.500905 268.165363 l
783.257728 263.700352 l
776.014551 260.287974 l
768.771374 256.536714 l
761.528196 252.611514 l
754.285019 248.601488 l
747.041842 244.607741 l
739.798665 241.015854 l
732.555488 236.982264 l
725.31231 232.369876 l
718.069133 228.131072 l
710.825956 224.113762 l
703.582779 220.063035 l
696.339601 215.671713 l
689.096424 211.727235 l
681.853247 207.213813 l
674.61007 203.049982 l
667.366893 198.533133 l
660.123715 194.272908 l
652.880538 188.806247 l
645.637361 185.050703 l
638.394184 179.872799 l
631.151006 176.09669 l
623.907829 172.047677 l
616.664652 166.697547 l
609.421475 161.155056 l
602.178298 155.568009 l
594.93512 308.116969 l
587.691943 311.608177 l
594.93512 308.101117 l
602.178298 304.017402 l
609.421475 301.23566 l
616.664652 297.485257 l
623.907829 293.430674 l
631.151006 290.38631 l
638.394184 286.680891 l
645.637361 282.773685 l
652.880538 279.007859 l
660.123715 275.505083 l
667.366893 271.577742 l
674.61007 267.716806 l
681.853247 264.060655 l
689.096424 259.97137 l
696.339601 255.989619 l
703.582779 252.640647 l
710.825956 248.450255 l
718.069133 244.469361 l
725.31231 239.799136 l
732.555488 236.558555 l
739.798665 232.451705 l
747.041842 227.880874 l
754.285019 224.033219 l
761.528196 219.848396 l
768.771374 214.848287 l
776.014551 211.583285 l
783.257728 207.246801 l
790.500905 202.870902 l
797.744082 198.502715 l
804.98726 193.57458 l
812.230437 190.243174 l
819.473614 186.044213 l
826.716791 179.635453 l
833.959969 175.812647 l
841.203146 170.384116 l
848.446323 166.160306 l
855.6895 161.257877 l
862.932677 156.034989 l
870.175855 307.754096 l
877.419032 311.845523 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
0 setlinecap
gsave
325.943 257.053 569.584 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

0 2 m
-2 -2 l
2 -2 l
cl

gsave
0.749 0.212 0.047 setrgbcolor
fill
grestore
stroke
grestore
} bind def
870.176 308.426 o
862.933 304.213 o
855.69 300.625 o
848.446 297.431 o
841.203 293.683 o
833.96 289.995 o
826.717 286.368 o
819.474 282.731 o
812.23 278.815 o
804.987 275.393 o
797.744 271.432 o
790.501 268.165 o
783.258 263.7 o
776.015 260.288 o
768.771 256.537 o
761.528 252.612 o
754.285 248.601 o
747.042 244.608 o
739.799 241.016 o
732.555 236.982 o
725.312 232.37 o
718.069 228.131 o
710.826 224.114 o
703.583 220.063 o
696.34 215.672 o
689.096 211.727 o
681.853 207.214 o
674.61 203.05 o
667.367 198.533 o
660.124 194.273 o
652.881 188.806 o
645.637 185.051 o
638.394 179.873 o
631.151 176.097 o
623.908 172.048 o
616.665 166.698 o
609.421 161.155 o
602.178 155.568 o
594.935 308.117 o
587.692 311.608 o
594.935 308.101 o
602.178 304.017 o
609.421 301.236 o
616.665 297.485 o
623.908 293.431 o
631.151 290.386 o
638.394 286.681 o
645.637 282.774 o
652.881 279.008 o
660.124 275.505 o
667.367 271.578 o
674.61 267.717 o
681.853 264.061 o
689.096 259.971 o
696.34 255.99 o
703.583 252.641 o
710.826 248.45 o
718.069 244.469 o
725.312 239.799 o
732.555 236.559 o
739.799 232.452 o
747.042 227.881 o
754.285 224.033 o
761.528 219.848 o
768.771 214.848 o
776.015 211.583 o
783.258 207.247 o
790.501 202.871 o
797.744 198.503 o
804.987 193.575 o
812.23 190.243 o
819.474 186.044 o
826.717 179.635 o
833.96 175.813 o
841.203 170.384 o
848.446 166.16 o
855.69 161.258 o
862.933 156.035 o
870.176 307.754 o
877.419 311.846 o
grestore
1.500 setlinewidth
1 setlinejoin
[5.55 2.4] 0 setdash
0.847 0.263 0.082 setrgbcolor
gsave
325.943 257.053 569.584 66.606 clipbox
870.175855 308.065558 m
862.932677 304.657892 l
855.6895 301.095566 l
848.446323 297.459551 l
841.203146 293.264875 l
833.959969 289.907334 l
826.716791 286.404987 l
819.473614 282.45751 l
812.230437 278.930314 l
804.98726 274.919431 l
797.744082 271.150177 l
790.500905 268.006419 l
783.257728 263.575253 l
776.014551 259.778581 l
768.771374 256.433036 l
761.528196 252.595234 l
754.285019 248.179492 l
747.041842 244.86051 l
739.798665 240.242553 l
732.555488 236.055588 l
725.31231 232.69462 l
718.069133 228.301155 l
710.825956 223.810011 l
703.582779 220.222408 l
696.339601 216.043583 l
689.096424 211.62527 l
681.853247 207.737344 l
674.61007 202.751372 l
667.366893 198.070437 l
660.123715 194.414715 l
652.880538 189.726925 l
645.637361 184.774371 l
638.394184 180.574125 l
631.151006 174.921529 l
623.907829 170.067084 l
616.664652 166.960598 l
609.421475 160.29907 l
602.178298 155.134018 l
594.93512 307.878338 l
587.691943 311.803537 l
594.93512 308.053991 l
602.178298 304.858822 l
609.421475 300.990174 l
616.664652 297.406427 l
623.907829 293.877518 l
631.151006 289.686269 l
638.394184 286.394705 l
645.637361 282.675148 l
652.880538 279.281192 l
660.123715 275.219754 l
667.366893 271.527188 l
674.61007 267.495312 l
681.853247 263.7689 l
689.096424 259.628205 l
696.339601 256.261239 l
703.582779 252.660783 l
710.825956 248.265177 l
718.069133 244.174178 l
725.31231 240.296534 l
732.555488 236.637384 l
739.798665 232.885696 l
747.041842 228.144353 l
754.285019 224.294128 l
761.528196 220.290099 l
768.771374 216.026875 l
776.014551 211.536587 l
783.257728 207.326059 l
790.500905 203.072689 l
797.744082 197.783395 l
804.98726 193.771226 l
812.230437 189.494721 l
819.473614 184.016065 l
826.716791 181.1345 l
833.959969 175.081759 l
841.203146 172.126507 l
848.446323 165.661195 l
855.6895 160.181682 l
862.932677 155.300246 l
870.175855 307.863343 l
877.419032 311.06708 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
325.943 257.053 569.584 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-4 -4 m
4 -4 l
4 4 l
-4 4 l
cl

gsave
grestore
stroke
grestore
} bind def
870.176 308.066 o
862.933 304.658 o
855.69 301.096 o
848.446 297.46 o
841.203 293.265 o
833.96 289.907 o
826.717 286.405 o
819.474 282.458 o
812.23 278.93 o
804.987 274.919 o
797.744 271.15 o
790.501 268.006 o
783.258 263.575 o
776.015 259.779 o
768.771 256.433 o
761.528 252.595 o
754.285 248.179 o
747.042 244.861 o
739.799 240.243 o
732.555 236.056 o
725.312 232.695 o
718.069 228.301 o
710.826 223.81 o
703.583 220.222 o
696.34 216.044 o
689.096 211.625 o
681.853 207.737 o
674.61 202.751 o
667.367 198.07 o
660.124 194.415 o
652.881 189.727 o
645.637 184.774 o
638.394 180.574 o
631.151 174.922 o
623.908 170.067 o
616.665 166.961 o
609.421 160.299 o
602.178 155.134 o
594.935 307.878 o
587.692 311.804 o
594.935 308.054 o
602.178 304.859 o
609.421 300.99 o
616.665 297.406 o
623.908 293.878 o
631.151 289.686 o
638.394 286.395 o
645.637 282.675 o
652.881 279.281 o
660.124 275.22 o
667.367 271.527 o
674.61 267.495 o
681.853 263.769 o
689.096 259.628 o
696.34 256.261 o
703.583 252.661 o
710.826 248.265 o
718.069 244.174 o
725.312 240.297 o
732.555 236.637 o
739.799 232.886 o
747.042 228.144 o
754.285 224.294 o
761.528 220.29 o
768.771 216.027 o
776.015 211.537 o
783.258 207.326 o
790.501 203.073 o
797.744 197.783 o
804.987 193.771 o
812.23 189.495 o
819.474 184.016 o
826.717 181.135 o
833.96 175.082 o
841.203 172.127 o
848.446 165.661 o
855.69 160.182 o
862.933 155.3 o
870.176 307.863 o
877.419 311.067 o
grestore
1.500 setlinewidth
1 setlinejoin
[1.5 2.475] 0 setdash
0.937 0.424 0.000 setrgbcolor
gsave
325.943 257.053 569.584 66.606 clipbox
870.175855 245.937562 m
862.932677 246.911364 l
855.6895 248.16921 l
848.446323 248.720589 l
841.203146 249.129732 l
833.959969 248.856399 l
826.716791 250.06669 l
819.473614 249.345656 l
812.230437 248.957935 l
804.98726 249.037621 l
797.744082 248.487956 l
790.500905 248.0771 l
783.257728 246.940068 l
776.014551 246.659024 l
768.771374 245.325776 l
761.528196 243.774032 l
754.285019 242.47977 l
747.041842 239.755437 l
739.798665 238.388772 l
732.555488 236.560268 l
725.31231 233.708265 l
718.069133 231.758947 l
710.825956 227.880445 l
703.582779 224.495914 l
696.339601 221.184643 l
689.096424 215.825517 l
681.853247 211.620986 l
674.61007 206.830804 l
667.366893 200.934436 l
660.123715 194.512824 l
652.880538 187.711201 l
645.637361 178.784179 l
638.394184 170.332705 l
631.151006 159.553616 l
623.907829 146.54245 l
616.664652 130.87764 l
609.421475 113.090429 l
602.178298 85.273439 l
594.93512 245.708356 l
587.691943 245.538701 l
594.93512 246.207039 l
602.178298 247.552711 l
609.421475 247.736504 l
616.664652 248.102805 l
623.907829 248.591634 l
631.151006 249.139157 l
638.394184 249.605708 l
645.637361 249.859762 l
652.880538 249.038906 l
660.123715 249.121163 l
667.366893 248.38642 l
674.61007 248.287455 l
681.853247 247.046317 l
689.096424 246.528355 l
696.339601 244.760687 l
703.582779 243.557679 l
710.825956 242.492623 l
718.069133 241.271621 l
725.31231 239.004414 l
732.555488 236.307071 l
739.798665 233.361672 l
747.041842 231.426491 l
754.285019 228.508083 l
761.528196 224.215298 l
768.771374 220.776786 l
776.014551 216.741482 l
783.257728 211.772648 l
790.500905 206.173605 l
797.744082 201.801562 l
804.98726 194.597223 l
812.230437 187.006876 l
819.473614 179.413531 l
826.716791 170.214889 l
833.959969 159.830376 l
841.203146 146.419921 l
848.446323 132.48465 l
855.6895 113.541129 l
862.932677 87.025255 l
870.175855 246.230174 l
877.419032 245.262369 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
325.943 257.053 569.584 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
870.176 245.938 o
862.933 246.911 o
855.69 248.169 o
848.446 248.721 o
841.203 249.13 o
833.96 248.856 o
826.717 250.067 o
819.474 249.346 o
812.23 248.958 o
804.987 249.038 o
797.744 248.488 o
790.501 248.077 o
783.258 246.94 o
776.015 246.659 o
768.771 245.326 o
761.528 243.774 o
754.285 242.48 o
747.042 239.755 o
739.799 238.389 o
732.555 236.56 o
725.312 233.708 o
718.069 231.759 o
710.826 227.88 o
703.583 224.496 o
696.34 221.185 o
689.096 215.826 o
681.853 211.621 o
674.61 206.831 o
667.367 200.934 o
660.124 194.513 o
652.881 187.711 o
645.637 178.784 o
638.394 170.333 o
631.151 159.554 o
623.908 146.542 o
616.665 130.878 o
609.421 113.09 o
602.178 85.2734 o
594.935 245.708 o
587.692 245.539 o
594.935 246.207 o
602.178 247.553 o
609.421 247.737 o
616.665 248.103 o
623.908 248.592 o
631.151 249.139 o
638.394 249.606 o
645.637 249.86 o
652.881 249.039 o
660.124 249.121 o
667.367 248.386 o
674.61 248.287 o
681.853 247.046 o
689.096 246.528 o
696.34 244.761 o
703.583 243.558 o
710.826 242.493 o
718.069 241.272 o
725.312 239.004 o
732.555 236.307 o
739.799 233.362 o
747.042 231.426 o
754.285 228.508 o
761.528 224.215 o
768.771 220.777 o
776.015 216.741 o
783.258 211.773 o
790.501 206.174 o
797.744 201.802 o
804.987 194.597 o
812.23 187.007 o
819.474 179.414 o
826.717 170.215 o
833.96 159.83 o
841.203 146.42 o
848.446 132.485 o
855.69 113.541 o
862.933 87.0253 o
870.176 246.23 o
877.419 245.262 o
grestore
2.500 setlinewidth
0 setlinejoin
2 setlinecap
0.000 setgray
gsave
569.584 66.60625 m
569.584 323.65925 l
stroke
grestore
gsave
895.526975 66.60625 m
895.526975 323.65925 l
stroke
grestore
gsave
569.584 66.60625 m
895.526975 66.60625 l
stroke
grestore
gsave
569.584 323.65925 m
895.526975 323.65925 l
stroke
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

716.437 339.717 translate
0 rotate
0 0 m /parenleft glyphshow
8.3252 0 m /h glyphshow
20.8252 0 m /parenright glyphshow
grestore
1.500 setlinewidth
1 setlinejoin
0.749 0.212 0.047 setrgbcolor
gsave
695.055488 157.60625 m
715.055488 157.60625 l
735.055488 157.60625 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
0 setlinecap
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

0 2 m
-2 -2 l
2 -2 l
cl

gsave
0.749 0.212 0.047 setrgbcolor
fill
grestore
stroke
grestore
} bind def
715.055 157.606 o
grestore
0.000 setgray
gsave
751.055 150.606 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.796875 moveto
/eta glyphshow
/TimesNewRomanPSMT 14.0 selectfont
10.7595 -4.35562 moveto
/one glyphshow
grestore
1.500 setlinewidth
1 setlinejoin
[5.55 2.4] 0 setdash
0.847 0.263 0.082 setrgbcolor
gsave
695.055488 127.60625 m
715.055488 127.60625 l
735.055488 127.60625 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-4 -4 m
4 -4 l
4 4 l
-4 4 l
cl

gsave
grestore
stroke
grestore
} bind def
715.055 127.606 o
grestore
0.000 setgray
gsave
751.055 120.606 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.796875 moveto
/eta glyphshow
/TimesNewRomanPSMT 14.0 selectfont
10.7595 -4.35562 moveto
/two glyphshow
grestore
1.500 setlinewidth
1 setlinejoin
[1.5 2.475] 0 setdash
0.937 0.424 0.000 setrgbcolor
gsave
695.055488 97.60625 m
715.055488 97.60625 l
735.055488 97.60625 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
715.055 97.6062 o
grestore
0.000 setgray
gsave
751.055 90.6062 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.796875 moveto
/eta glyphshow
/TimesNewRomanPSMT 14.0 selectfont
10.7595 -4.35562 moveto
/three glyphshow
grestore
gsave
1035.718 66.60625 m
1361.660975 66.60625 l
1361.660975 323.65925 l
1035.718 323.65925 l
cl
1.000 setgray
fill
grestore
2.500 setlinewidth
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1053.83 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1053.83 323.659 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

1040.53 39.2469 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /four glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1090.04 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1090.04 323.659 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

1076.74 39.2469 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /three glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1126.26 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1126.26 323.659 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

1112.96 39.2469 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /two glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1162.47 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1162.47 323.659 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

1149.18 39.2469 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /one glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1198.69 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1198.69 323.659 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

1192.44 39.2469 translate
0 rotate
0 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1234.91 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1234.91 323.659 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

1228.66 39.2469 translate
0 rotate
0 0 m /one glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1271.12 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1271.12 323.659 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

1264.87 39.2469 translate
0 rotate
0 0 m /two glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1307.34 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1307.34 323.659 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

1301.09 39.2469 translate
0 rotate
0 0 m /three glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1343.55 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1343.55 323.659 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

1337.3 39.2469 translate
0 rotate
0 0 m /four glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1039.34 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1039.34 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1046.58 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1046.58 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1061.07 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1061.07 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1068.31 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1068.31 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1075.56 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1075.56 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1082.8 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1082.8 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1097.29 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1097.29 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1104.53 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1104.53 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1111.77 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1111.77 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1119.01 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1119.01 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1133.5 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1133.5 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1140.74 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1140.74 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1147.99 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1147.99 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1155.23 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1155.23 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1169.72 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1169.72 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1176.96 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1176.96 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1184.2 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1184.2 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1191.45 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1191.45 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1205.93 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1205.93 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1213.18 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1213.18 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1220.42 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1220.42 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1227.66 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1227.66 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1242.15 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1242.15 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1249.39 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1249.39 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1256.63 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1256.63 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1263.88 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1263.88 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1278.36 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1278.36 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1285.61 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1285.61 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1292.85 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1292.85 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1300.09 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1300.09 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1314.58 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1314.58 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1321.82 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1321.82 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1329.07 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1329.07 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1336.31 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1336.31 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1350.8 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1350.8 323.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1358.04 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1358.04 323.659 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

1080.64 12.5438 translate
0 rotate
0 0 m /E glyphshow
15.271 0 m /l glyphshow
22.2168 0 m /e glyphshow
33.313 0 m /c glyphshow
44.4092 0 m /t glyphshow
51.355 0 m /r glyphshow
59.6802 0 m /i glyphshow
66.626 0 m /c glyphshow
77.7222 0 m /space glyphshow
83.9722 0 m /F glyphshow
97.876 0 m /i glyphshow
104.822 0 m /e glyphshow
115.918 0 m /l glyphshow
122.864 0 m /d glyphshow
135.364 0 m /space glyphshow
141.614 0 m /parenleft glyphshow
149.939 0 m /M glyphshow
172.168 0 m /V glyphshow
190.222 0 m /slash glyphshow
197.168 0 m /c glyphshow
208.264 0 m /m glyphshow
227.71 0 m /parenright glyphshow
grestore
2.500 setlinewidth
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1035.72 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1361.66 66.6063 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

969.468 57.9266 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /one glyphshow
43.75 0 m /five glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1035.72 108.671 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1361.66 108.671 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

969.468 99.9909 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /one glyphshow
43.75 0 m /six glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1035.72 150.735 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1361.66 150.735 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

969.468 142.055 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /one glyphshow
43.75 0 m /seven glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1035.72 192.799 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1361.66 192.799 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

969.468 184.119 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /one glyphshow
43.75 0 m /eight glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1035.72 234.863 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1361.66 234.863 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

969.468 226.184 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /one glyphshow
43.75 0 m /nine glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1035.72 276.928 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1361.66 276.928 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

969.468 268.248 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /two glyphshow
43.75 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1035.72 318.992 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1361.66 318.992 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

969.468 310.312 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /two glyphshow
43.75 0 m /one glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1035.72 75.0191 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1361.66 75.0191 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1035.72 83.432 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1361.66 83.432 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1035.72 91.8448 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1361.66 91.8448 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1035.72 100.258 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1361.66 100.258 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1035.72 117.083 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1361.66 117.083 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1035.72 125.496 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1361.66 125.496 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1035.72 133.909 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1361.66 133.909 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1035.72 142.322 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1361.66 142.322 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1035.72 159.148 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1361.66 159.148 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1035.72 167.561 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1361.66 167.561 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1035.72 175.973 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1361.66 175.973 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1035.72 184.386 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1361.66 184.386 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1035.72 201.212 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1361.66 201.212 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1035.72 209.625 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1361.66 209.625 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1035.72 218.038 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1361.66 218.038 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1035.72 226.451 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1361.66 226.451 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1035.72 243.276 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1361.66 243.276 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1035.72 251.689 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1361.66 251.689 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1035.72 260.102 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1361.66 260.102 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1035.72 268.515 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1361.66 268.515 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1035.72 285.341 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1361.66 285.341 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1035.72 293.754 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1361.66 293.754 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1035.72 302.166 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1361.66 302.166 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1035.72 310.579 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1361.66 310.579 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

960.124 165.266 translate
90 rotate
0 0 m /S glyphshow
13.9038 0 m /t glyphshow
20.8496 0 m /r glyphshow
29.1748 0 m /a glyphshow
40.271 0 m /i glyphshow
47.2168 0 m /n glyphshow
grestore
2 setlinecap
0.749 0.212 0.047 setrgbcolor
gsave
325.943 257.053 1035.718 66.606 clipbox
1336.309855 194.76148 m
1329.066677 195.850104 l
1321.8235 196.63208 l
1314.580323 197.102359 l
1307.337146 198.83625 l
1300.093969 198.755065 l
1292.850791 199.966938 l
1285.607614 200.281579 l
1278.364437 200.348041 l
1271.12126 200.162958 l
1263.878083 200.186935 l
1256.634905 200.152862 l
1249.391728 200.133513 l
1242.148551 200.222689 l
1234.905374 198.893878 l
1227.662196 199.324196 l
1220.419019 198.036607 l
1213.175842 197.794737 l
1205.932665 196.791924 l
1198.689488 195.477835 l
1191.44631 193.873923 l
1184.203133 192.173263 l
1176.959956 190.160486 l
1169.716779 188.355506 l
1162.473601 185.544348 l
1155.230424 183.730956 l
1147.987247 180.77047 l
1140.74407 177.447389 l
1133.500892 173.762976 l
1126.257715 169.968776 l
1119.014538 165.277764 l
1111.771361 159.333656 l
1104.528184 155.02585 l
1097.285006 149.235277 l
1090.041829 141.905571 l
1082.798652 133.048089 l
1075.555475 122.689333 l
1068.312298 109.63846 l
1061.06912 90.008308 l
1053.825943 193.523948 l
1061.06912 194.41487 l
1068.312298 195.38277 l
1075.555475 196.61273 l
1082.798652 197.401015 l
1090.041829 198.198975 l
1097.285006 198.938887 l
1104.528184 199.22871 l
1111.771361 199.878603 l
1119.014538 200.430908 l
1126.257715 200.845241 l
1133.500892 200.718207 l
1140.74407 200.593276 l
1147.987247 200.359819 l
1155.230424 199.769236 l
1162.473601 199.114295 l
1169.716779 199.63463 l
1176.959956 197.37157 l
1184.203133 197.409849 l
1191.44631 196.463402 l
1198.689488 195.273403 l
1205.932665 193.324563 l
1213.175842 191.893956 l
1220.419019 190.038079 l
1227.662196 188.109851 l
1234.905374 185.826179 l
1242.148551 183.384346 l
1249.391728 181.558334 l
1256.634905 177.423413 l
1263.878083 173.602291 l
1271.12126 170.025983 l
1278.364437 166.376904 l
1285.607614 160.379796 l
1292.850791 155.530201 l
1300.093969 149.06029 l
1307.337146 141.72806 l
1314.580323 132.600946 l
1321.8235 122.888297 l
1329.066677 109.211086 l
1336.309855 88.75353 l
1343.553032 193.065026 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
0 setlinecap
gsave
325.943 257.053 1035.718 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

0 2 m
-2 -2 l
2 -2 l
cl

gsave
0.749 0.212 0.047 setrgbcolor
fill
grestore
stroke
grestore
} bind def
1336.31 194.761 o
1329.07 195.85 o
1321.82 196.632 o
1314.58 197.102 o
1307.34 198.836 o
1300.09 198.755 o
1292.85 199.967 o
1285.61 200.282 o
1278.36 200.348 o
1271.12 200.163 o
1263.88 200.187 o
1256.63 200.153 o
1249.39 200.134 o
1242.15 200.223 o
1234.91 198.894 o
1227.66 199.324 o
1220.42 198.037 o
1213.18 197.795 o
1205.93 196.792 o
1198.69 195.478 o
1191.45 193.874 o
1184.2 192.173 o
1176.96 190.16 o
1169.72 188.356 o
1162.47 185.544 o
1155.23 183.731 o
1147.99 180.77 o
1140.74 177.447 o
1133.5 173.763 o
1126.26 169.969 o
1119.01 165.278 o
1111.77 159.334 o
1104.53 155.026 o
1097.29 149.235 o
1090.04 141.906 o
1082.8 133.048 o
1075.56 122.689 o
1068.31 109.638 o
1061.07 90.0083 o
1053.83 193.524 o
1061.07 194.415 o
1068.31 195.383 o
1075.56 196.613 o
1082.8 197.401 o
1090.04 198.199 o
1097.29 198.939 o
1104.53 199.229 o
1111.77 199.879 o
1119.01 200.431 o
1126.26 200.845 o
1133.5 200.718 o
1140.74 200.593 o
1147.99 200.36 o
1155.23 199.769 o
1162.47 199.114 o
1169.72 199.635 o
1176.96 197.372 o
1184.2 197.41 o
1191.45 196.463 o
1198.69 195.273 o
1205.93 193.325 o
1213.18 191.894 o
1220.42 190.038 o
1227.66 188.11 o
1234.91 185.826 o
1242.15 183.384 o
1249.39 181.558 o
1256.63 177.423 o
1263.88 173.602 o
1271.12 170.026 o
1278.36 166.377 o
1285.61 160.38 o
1292.85 155.53 o
1300.09 149.06 o
1307.34 141.728 o
1314.58 132.601 o
1321.82 122.888 o
1329.07 109.211 o
1336.31 88.7535 o
1343.55 193.065 o
grestore
1.500 setlinewidth
1 setlinejoin
[5.55 2.4] 0 setdash
0.847 0.263 0.082 setrgbcolor
gsave
325.943 257.053 1035.718 66.606 clipbox
1336.309855 194.799758 m
1329.066677 195.373936 l
1321.8235 197.048516 l
1314.580323 197.428778 l
1307.337146 197.615964 l
1300.093969 198.553157 l
1292.850791 198.542641 l
1285.607614 200.049384 l
1278.364437 199.500024 l
1271.12126 200.705588 l
1263.878083 200.44563 l
1256.634905 200.648801 l
1249.391728 200.016995 l
1242.148551 199.561859 l
1234.905374 199.568169 l
1227.662196 198.516981 l
1220.419019 198.435377 l
1213.175842 196.570245 l
1205.932665 196.078513 l
1198.689488 195.013445 l
1191.44631 194.403933 l
1184.203133 191.669753 l
1176.959956 190.558414 l
1169.716779 188.506517 l
1162.473601 186.378063 l
1155.230424 183.07307 l
1147.987247 179.614543 l
1140.74407 176.700327 l
1133.500892 174.315281 l
1126.257715 170.026404 l
1119.014538 165.698828 l
1111.771361 159.864508 l
1104.528184 155.300951 l
1097.285006 149.025376 l
1090.041829 141.366727 l
1082.798652 133.661808 l
1075.555475 121.87665 l
1068.312298 108.78203 l
1061.06912 90.967374 l
1053.825943 193.269879 l
1061.06912 194.76779 l
1068.312298 195.490034 l
1075.555475 196.951769 l
1082.798652 197.965518 l
1090.041829 198.138403 l
1097.285006 198.410979 l
1104.528184 199.625797 l
1111.771361 199.777228 l
1119.014538 200.150339 l
1126.257715 200.290833 l
1133.500892 200.794343 l
1140.74407 200.303032 l
1147.987247 199.913517 l
1155.230424 200.147815 l
1162.473601 199.790689 l
1169.716779 198.746232 l
1176.959956 199.009555 l
1184.203133 197.683687 l
1191.44631 196.712002 l
1198.689488 194.811116 l
1205.932665 193.229918 l
1213.175842 192.375592 l
1220.419019 190.173946 l
1227.662196 187.812035 l
1234.905374 185.81356 l
1242.148551 183.64893 l
1249.391728 180.167268 l
1256.634905 178.06952 l
1263.878083 173.856359 l
1271.12126 170.290147 l
1278.364437 166.060581 l
1285.607614 161.234963 l
1292.850791 154.813846 l
1300.093969 149.075012 l
1307.337146 142.031764 l
1314.580323 132.043173 l
1321.8235 123.240796 l
1329.066677 109.746565 l
1336.309855 88.685806 l
1343.553032 193.53194 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
325.943 257.053 1035.718 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-4 -4 m
4 -4 l
4 4 l
-4 4 l
cl

gsave
grestore
stroke
grestore
} bind def
1336.31 194.8 o
1329.07 195.374 o
1321.82 197.049 o
1314.58 197.429 o
1307.34 197.616 o
1300.09 198.553 o
1292.85 198.543 o
1285.61 200.049 o
1278.36 199.5 o
1271.12 200.706 o
1263.88 200.446 o
1256.63 200.649 o
1249.39 200.017 o
1242.15 199.562 o
1234.91 199.568 o
1227.66 198.517 o
1220.42 198.435 o
1213.18 196.57 o
1205.93 196.079 o
1198.69 195.013 o
1191.45 194.404 o
1184.2 191.67 o
1176.96 190.558 o
1169.72 188.507 o
1162.47 186.378 o
1155.23 183.073 o
1147.99 179.615 o
1140.74 176.7 o
1133.5 174.315 o
1126.26 170.026 o
1119.01 165.699 o
1111.77 159.865 o
1104.53 155.301 o
1097.29 149.025 o
1090.04 141.367 o
1082.8 133.662 o
1075.56 121.877 o
1068.31 108.782 o
1061.07 90.9674 o
1053.83 193.27 o
1061.07 194.768 o
1068.31 195.49 o
1075.56 196.952 o
1082.8 197.966 o
1090.04 198.138 o
1097.29 198.411 o
1104.53 199.626 o
1111.77 199.777 o
1119.01 200.15 o
1126.26 200.291 o
1133.5 200.794 o
1140.74 200.303 o
1147.99 199.914 o
1155.23 200.148 o
1162.47 199.791 o
1169.72 198.746 o
1176.96 199.01 o
1184.2 197.684 o
1191.45 196.712 o
1198.69 194.811 o
1205.93 193.23 o
1213.18 192.376 o
1220.42 190.174 o
1227.66 187.812 o
1234.91 185.814 o
1242.15 183.649 o
1249.39 180.167 o
1256.63 178.07 o
1263.88 173.856 o
1271.12 170.29 o
1278.36 166.061 o
1285.61 161.235 o
1292.85 154.814 o
1300.09 149.075 o
1307.34 142.032 o
1314.58 132.043 o
1321.82 123.241 o
1329.07 109.747 o
1336.31 88.6858 o
1343.55 193.532 o
grestore
1.500 setlinewidth
1 setlinejoin
[1.5 2.475] 0 setdash
0.937 0.424 0.000 setrgbcolor
gsave
325.943 257.053 1035.718 66.606 clipbox
1336.309855 307.890076 m
1329.066677 303.63443 l
1321.8235 299.011983 l
1314.580323 294.918705 l
1307.337146 290.961295 l
1300.093969 286.711117 l
1292.850791 282.609005 l
1285.607614 278.148927 l
1278.364437 274.736249 l
1271.12126 270.764116 l
1263.878083 267.37205 l
1256.634905 263.47984 l
1249.391728 260.228269 l
1242.148551 256.364241 l
1234.905374 253.2498 l
1227.662196 249.46275 l
1220.419019 246.81396 l
1213.175842 244.300197 l
1205.932665 241.380093 l
1198.689488 238.110013 l
1191.44631 235.62233 l
1184.203133 234.276693 l
1176.959956 231.498766 l
1169.716779 229.538569 l
1162.473601 228.367078 l
1155.230424 226.944883 l
1147.987247 225.774654 l
1140.74407 225.116769 l
1133.500892 223.844744 l
1126.257715 223.82245 l
1119.014538 224.185465 l
1111.771361 226.056065 l
1104.528184 226.874636 l
1097.285006 228.773419 l
1090.041829 232.326591 l
1082.798652 236.442163 l
1075.555475 244.21733 l
1068.312298 254.149555 l
1061.06912 270.476817 l
1053.825943 312.442276 l
1061.06912 307.87956 l
1068.312298 303.387933 l
1075.555475 298.602697 l
1082.798652 294.873276 l
1090.041829 290.562104 l
1097.285006 286.643393 l
1104.528184 281.97762 l
1111.771361 278.644865 l
1119.014538 274.288685 l
1126.257715 270.523088 l
1133.500892 266.616575 l
1140.74407 263.763353 l
1147.987247 260.002383 l
1155.230424 256.917807 l
1162.473601 253.088273 l
1169.716779 249.599459 l
1176.959956 246.684402 l
1184.203133 243.882078 l
1191.44631 240.453416 l
1198.689488 238.417083 l
1205.932665 236.537649 l
1213.175842 234.225795 l
1220.419019 231.774707 l
1227.662196 230.620883 l
1234.905374 227.721811 l
1242.148551 226.437167 l
1249.391728 225.02591 l
1256.634905 224.01216 l
1263.878083 224.069367 l
1271.12126 223.532627 l
1278.364437 223.025331 l
1285.607614 225.230342 l
1292.850791 227.095474 l
1300.093969 228.895826 l
1307.337146 232.428387 l
1314.580323 238.527712 l
1321.8235 243.07907 l
1329.066677 253.265363 l
1336.309855 271.639895 l
1343.553032 312.470038 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
325.943 257.053 1035.718 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
1336.31 307.89 o
1329.07 303.634 o
1321.82 299.012 o
1314.58 294.919 o
1307.34 290.961 o
1300.09 286.711 o
1292.85 282.609 o
1285.61 278.149 o
1278.36 274.736 o
1271.12 270.764 o
1263.88 267.372 o
1256.63 263.48 o
1249.39 260.228 o
1242.15 256.364 o
1234.91 253.25 o
1227.66 249.463 o
1220.42 246.814 o
1213.18 244.3 o
1205.93 241.38 o
1198.69 238.11 o
1191.45 235.622 o
1184.2 234.277 o
1176.96 231.499 o
1169.72 229.539 o
1162.47 228.367 o
1155.23 226.945 o
1147.99 225.775 o
1140.74 225.117 o
1133.5 223.845 o
1126.26 223.822 o
1119.01 224.185 o
1111.77 226.056 o
1104.53 226.875 o
1097.29 228.773 o
1090.04 232.327 o
1082.8 236.442 o
1075.56 244.217 o
1068.31 254.15 o
1061.07 270.477 o
1053.83 312.442 o
1061.07 307.88 o
1068.31 303.388 o
1075.56 298.603 o
1082.8 294.873 o
1090.04 290.562 o
1097.29 286.643 o
1104.53 281.978 o
1111.77 278.645 o
1119.01 274.289 o
1126.26 270.523 o
1133.5 266.617 o
1140.74 263.763 o
1147.99 260.002 o
1155.23 256.918 o
1162.47 253.088 o
1169.72 249.599 o
1176.96 246.684 o
1184.2 243.882 o
1191.45 240.453 o
1198.69 238.417 o
1205.93 236.538 o
1213.18 234.226 o
1220.42 231.775 o
1227.66 230.621 o
1234.91 227.722 o
1242.15 226.437 o
1249.39 225.026 o
1256.63 224.012 o
1263.88 224.069 o
1271.12 223.533 o
1278.36 223.025 o
1285.61 225.23 o
1292.85 227.095 o
1300.09 228.896 o
1307.34 232.428 o
1314.58 238.528 o
1321.82 243.079 o
1329.07 253.265 o
1336.31 271.64 o
1343.55 312.47 o
grestore
2.500 setlinewidth
0 setlinejoin
2 setlinecap
0.000 setgray
gsave
1035.718 66.60625 m
1035.718 323.65925 l
stroke
grestore
gsave
1361.660975 66.60625 m
1361.660975 323.65925 l
stroke
grestore
gsave
1035.718 66.60625 m
1361.660975 66.60625 l
stroke
grestore
gsave
1035.718 323.65925 m
1361.660975 323.65925 l
stroke
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

1188.12 339.717 translate
0 rotate
0 0 m /parenleft glyphshow
8.3252 0 m /i glyphshow
15.271 0 m /parenright glyphshow
grestore
1.500 setlinewidth
1 setlinejoin
0.749 0.212 0.047 setrgbcolor
gsave
1161.189488 157.60625 m
1181.189488 157.60625 l
1201.189488 157.60625 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
0 setlinecap
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

0 2 m
-2 -2 l
2 -2 l
cl

gsave
0.749 0.212 0.047 setrgbcolor
fill
grestore
stroke
grestore
} bind def
1181.19 157.606 o
grestore
0.000 setgray
gsave
1217.19 150.606 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.796875 moveto
/eta glyphshow
/TimesNewRomanPSMT 14.0 selectfont
10.7595 -4.35562 moveto
/one glyphshow
grestore
1.500 setlinewidth
1 setlinejoin
[5.55 2.4] 0 setdash
0.847 0.263 0.082 setrgbcolor
gsave
1161.189488 127.60625 m
1181.189488 127.60625 l
1201.189488 127.60625 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-4 -4 m
4 -4 l
4 4 l
-4 4 l
cl

gsave
grestore
stroke
grestore
} bind def
1181.19 127.606 o
grestore
0.000 setgray
gsave
1217.19 120.606 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.796875 moveto
/eta glyphshow
/TimesNewRomanPSMT 14.0 selectfont
10.7595 -4.35562 moveto
/two glyphshow
grestore
1.500 setlinewidth
1 setlinejoin
[1.5 2.475] 0 setdash
0.937 0.424 0.000 setrgbcolor
gsave
1161.189488 97.60625 m
1181.189488 97.60625 l
1201.189488 97.60625 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
1181.19 97.6062 o
grestore
0.000 setgray
gsave
1217.19 90.6062 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.796875 moveto
/eta glyphshow
/TimesNewRomanPSMT 14.0 selectfont
10.7595 -4.35562 moveto
/three glyphshow
grestore
/TimesNewRomanPS-BoldMT 25.000 selectfont
gsave

230.097 1137.64 translate
0 rotate
0 0 m /E glyphshow
16.6748 0 m /space glyphshow
22.9248 0 m /bracketleft glyphshow
31.25 0 m /one glyphshow
42.375 0 m /one glyphshow
53.5 0 m /one glyphshow
66 0 m /bracketright glyphshow
grestore
/TimesNewRomanPS-BoldMT 25.000 selectfont
gsave

697.409 1137.64 translate
0 rotate
0 0 m /E glyphshow
16.6748 0 m /space glyphshow
22.9248 0 m /bracketleft glyphshow
31.25 0 m /one glyphshow
42.375 0 m /one glyphshow
54.875 0 m /zero glyphshow
67.375 0 m /bracketright glyphshow
grestore
/TimesNewRomanPS-BoldMT 25.000 selectfont
gsave

1164.72 1137.64 translate
0 rotate
0 0 m /E glyphshow
16.6748 0 m /space glyphshow
22.9248 0 m /bracketleft glyphshow
31.25 0 m /zero glyphshow
43.75 0 m /zero glyphshow
56.25 0 m /one glyphshow
68.75 0 m /bracketright glyphshow
grestore

end
showpage
