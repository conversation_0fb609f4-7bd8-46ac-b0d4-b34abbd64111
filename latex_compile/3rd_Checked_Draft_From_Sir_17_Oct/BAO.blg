This is BibTeX, Version 0.99d (TeX Live 2017/Debian)
Capacity: max_strings=100000, hash_size=100000, hash_prime=85009
The top-level auxiliary file: BAO.aux
The style file: apsrev4-2.bst
Reallocated singl_function (elt_size=4) to 100 items from 50.
Reallocated singl_function (elt_size=4) to 100 items from 50.
Reallocated singl_function (elt_size=4) to 100 items from 50.
Reallocated singl_function (elt_size=4) to 100 items from 50.
Reallocated singl_function (elt_size=4) to 100 items from 50.
Reallocated singl_function (elt_size=4) to 100 items from 50.
Reallocated singl_function (elt_size=4) to 100 items from 50.
Reallocated wiz_functions (elt_size=4) to 6000 items from 3000.
Reallocated singl_function (elt_size=4) to 100 items from 50.
Database file #1: BAONotes.bib
Database file #2: BAO.bib
Repeated entry---line 1431 of file BAO.bib
 : @article{mani_atomistic_2013
 :                             ,
I'm skipping whatever remains of this entry
I was expecting a `,' or a `}'---line 1715 of file BAO.bib
 : 
 : @Article{korus-22,
(<PERSON><PERSON><PERSON> may have been on previous line)
I'm skipping whatever remains of this entry
Warning--I didn't find a database entry for ""
Reallocated singl_function (elt_size=4) to 100 items from 50.
Reallocated wiz_functions (elt_size=4) to 9000 items from 6000.
apsrev4-2.bst 2019-01-14 (MD) hand-edited version of apsrev4-1.bst
Control: key (0) 
Control: author (72) initials jnrlst
Control: editor formatted (1) identically to author
Control: production of article title (-1) disabled
Control: page (0) single
Control: year (1) truncated
Control: production of eprint (0) enabled
You've used 41 entries,
            6079 wiz_defined-function locations,
            2009 strings with 26945 characters,
and the built_in function-call counts, 46933 in all, are:
= -- 3296
> -- 1378
< -- 267
+ -- 425
- -- 336
* -- 7306
:= -- 4290
add.period$ -- 41
call.type$ -- 41
change.case$ -- 164
chr.to.int$ -- 40
cite$ -- 41
duplicate$ -- 4100
empty$ -- 3234
format.name$ -- 809
if$ -- 9702
int.to.chr$ -- 2
int.to.str$ -- 49
missing$ -- 660
newline$ -- 175
num.names$ -- 123
pop$ -- 1676
preamble$ -- 1
purify$ -- 205
quote$ -- 0
skip$ -- 2072
stack$ -- 0
substring$ -- 1112
swap$ -- 4091
text.length$ -- 167
text.prefix$ -- 0
top$ -- 8
type$ -- 619
warning$ -- 0
while$ -- 123
width$ -- 0
write$ -- 380
(There were 2 error messages)
