%%
%% This is file `apsrev4-2.bst', and is a hand-edited version of apsrev4-1.bst
 % ===============================================================
 % IMPORTANT NOTICE:
 % This file can be redistributed and/or modified under the terms
 % of the LaTeX Project Public License Distributed from CTAN
 % archives in directory macros/latex/base/lppl.txt; either
 % version 1 of the License, or any later version.
 % ===============================================================
 %-------------------------------------------------------------------
 % This bibliography style file is intended for texts in ENGLISH
 % This is an author-year citation style bibliography. As such, it is
 % non-standard LaTeX, and requires a special package file to function properly.
 % Such a package is natbib.sty   by <PERSON>
 % The form of the \bibitem entries is
 %   \bibitem[<PERSON> et al.(1990)]{key}...
 %   \bibitem[Jones et al.(1990)Jones, Baker, and Smith]{key}...
 % The essential feature is that the label (the part in brackets) consists
 % of the author names, as they should appear in the citation, with the year
 % in parentheses following. There must be no space before the opening
 % parenthesis!
 % With natbib v5.3, a full list of authors may also follow the year.
 % In natbib.sty, it is possible to define the type of enclosures that is
 % really wanted (brackets or parentheses), but in either case, there must
 % be parentheses in the label.
 % The \cite command functions as follows:
 %   \citet{key} ==>>                Jones et al. (1990)
 %   \citet*{key} ==>>               Jones, Baker, and Smith (1990)
 %   \citep{key} ==>>                (Jones et al., 1990)
 %   \citep*{key} ==>>               (Jones, Baker, and Smith, 1990)
 %   \citep[chap. 2]{key} ==>>       (Jones et al., 1990, chap. 2)
 %   \citep[e.g.][]{key} ==>>        (e.g. Jones et al., 1990)
 %   \citep[e.g.][p. 32]{key} ==>>   (e.g. Jones et al., p. 32)
 %   \citeauthor{key} ==>>           Jones et al.
 %   \citeauthor*{key} ==>>          Jones, Baker, and Smith
 %   \citeyear{key} ==>>             1990
 %---------------------------------------------------------------------

FUNCTION {id.bst} {"apsrev4-2.bst 2019-01-14 (MD) hand-edited version of apsrev4-1.bst"}
ENTRY
{
    address
    archive
    archivePrefix
    author
    bookaddress
    booktitle
    chapter
    collaboration
    doi
    edition
    editor
    eid
    eprint
    howpublished
    institution
    isbn
    issn
    journal
    key
    language
    month
    note
    number
    organization
    pages
    primaryClass
    publisher
    school
    SLACcitation
    series
    title
    translation
    type
    url
    volume
    year
}{
}{
    label
    extra.label sort.label
    short.list
}

INTEGERS
{
  output.state before.all
  after.word after.punctuation
  after.sentence after.block
}

INTEGERS
{
  punctuation.state punctuation.no punctuation.space punctuation.yes
}

STRINGS { bibfield output.bibfield }

FUNCTION {not}
{   { #0 }
    { #1 }
  if$
}

FUNCTION {and}
{   'skip$
    { pop$ #0 }
  if$
}

FUNCTION {or}
{   { pop$ #1 }
    'skip$
  if$
}

FUNCTION {non.stop}
{ duplicate$
   "}" * add.period$
   #-1 #1 substring$ "." =
}

INTEGERS { arith.mulitplier arith.multiplicand }

FUNCTION {multiply}
{
  'arith.multiplicand :=
  'arith.mulitplier :=
  #0
    { arith.mulitplier #0 > }
    { arith.multiplicand +
      arith.mulitplier #1 - 'arith.mulitplier :=
    }
  while$
}

FUNCTION {chr.to.hex}
{
  chr.to.int$
  duplicate$ "0" chr.to.int$ -
  duplicate$ duplicate$ #0 < swap$ #9 > or not
    { swap$ pop$ }
    { pop$
      duplicate$ "A" chr.to.int$ -
      duplicate$ duplicate$ #0 < swap$ #5 > or not
        { swap$ pop$ #10 + }
        { pop$
          duplicate$ "a" chr.to.int$ -
          duplicate$ duplicate$ #0 < swap$ #5 > or not
            { swap$ pop$ #10 + }
            { pop$
              pop$ #-1
            }
          if$
        }
      if$
    }
  if$
}

INTEGERS { arith.accumulator }

FUNCTION {str.to.hex}
{ #0 'arith.accumulator :=
    { duplicate$ empty$ not }
    { duplicate$ #1 #1 substring$ chr.to.hex
      duplicate$ #0 <
        { pop$ pop$ ""
        }
        { arith.accumulator #16 multiply + 'arith.accumulator :=
          #2 global.max$ substring$
        }
      if$
    }
  while$
  pop$ arith.accumulator
}

FUNCTION {diagn.cmntlog}
{
  duplicate$ top$ "%" swap$ * write$ newline$
}

INTEGERS { control.key control.author control.editor control.title control.pages control.eprint control.year }

INTEGERS { control.author.jnrlst control.author.dotless control.author.nospace control.author.initials control.author.nocomma control.author.first control.author.reversed }

FUNCTION { control.init }
{
  #0
  'control.key             :=
  #0
  #8 +
  #64 +
  'control.author :=
  #1
  'control.author.jnrlst   :=
  #0
  'control.author.dotless  :=
  #0
  'control.author.nospace  :=
  #1
  'control.author.initials :=
  #0
  'control.author.nocomma  :=
  #0
  'control.author.first    :=
  #0
  'control.author.reversed :=
  #1
  'control.editor :=
  #-1
  'control.title  :=
  #0
  'control.pages  :=
  #0
  'control.eprint :=
  #1
  'control.year   :=
}

FUNCTION {warning.dependency}
{
  " (dependency: " * swap$ * ") set " * swap$ int.to.str$ * warning$
}

FUNCTION {control.check}
{
  control.editor
    {
      "editor formatted same as author"
      control.author.reversed {
        duplicate$ #0 swap$ "reversed" warning.dependency
        #0 'control.author.reversed :=
      } 'skip$ if$
      control.author.first {
        duplicate$ #0 swap$ "first" warning.dependency
        #0 'control.author.first :=
      } 'skip$ if$
      control.author.nocomma {
        duplicate$ #0 swap$ "nocomma" warning.dependency
        #0 'control.author.nocomma :=
      } 'skip$ if$
      pop$
    } 'skip$ if$
  control.author.reversed 'skip$
    {
      "not reversed"
      control.author.nospace {
        duplicate$ #0 swap$ "nospace" warning.dependency
        #0 'control.author.nospace :=
      } 'skip$ if$
      control.author.jnrlst 'skip$ {
        duplicate$ #1 swap$ "jnrlst" warning.dependency
        #1 'control.author.jnrlst :=
      } if$
      control.author.initials {
        duplicate$ ", initials" *
        control.author.dotless {
          duplicate$ #0 swap$ "dotless" warning.dependency
          #0 'control.author.dotless :=
        } 'skip$ if$
        pop$
      } 'skip$ if$
      pop$
    }
  if$
  control.author.initials 'skip$ {
    "not initials"
    control.author.nocomma {
      duplicate$ #0 swap$ "nocomma" warning.dependency
      #0 'control.author.nocomma :=
    } 'skip$ if$
    control.author.nospace {
      duplicate$ #0 swap$ "nospace" warning.dependency
      #0 'control.author.nospace :=
    } 'skip$ if$
    control.author.dotless 'skip$ {
      duplicate$ #1 swap$ "dotless" warning.dependency
      #1 'control.author.dotless :=
    } if$
    pop$
  } if$
}

FUNCTION {control.parse}
{
  duplicate$ duplicate$ missing$
    {
      pop$ pop$ pop$
    }
    { empty$
        {
          pop$ #-1
        }{
          str.to.hex
        }
      if$
      swap$ :=
    }
  if$
}

FUNCTION {control.dump}
{
  duplicate$ missing$ { pop$ "N/A" } 'skip$ if$
  "{" swap$ * "}, " *
  *
}

INTEGERS { decode.threshold }

FUNCTION {control.decode}
{
  - duplicate$
  #0 <
    {
      skip$ pop$ swap$ #0
    }
    {
      swap$ pop$ swap$ #1
    }
  if$
  swap$ :=
}

FUNCTION {control.author.decode}
{
  control.author
  duplicate$ duplicate$ #0 < swap$ #128 < not or
    {
      int.to.str$ "(" swap$ * ")" *
      "Control cannot interpret author " swap$ *
      warning$
    }{
      'control.author.jnrlst   swap$ duplicate$ #64 control.decode
      'control.author.dotless  swap$ duplicate$ #32 control.decode
      'control.author.nospace  swap$ duplicate$ #16 control.decode
      'control.author.initials swap$ duplicate$  #8 control.decode
      'control.author.nocomma  swap$ duplicate$  #4 control.decode
      'control.author.first    swap$ duplicate$  #2 control.decode
      'control.author.reversed swap$ duplicate$  #1 control.decode
      duplicate$ #0 =
        'skip$
        {
          "Control: residue of author"
          "(" swap$ * ")" * *
          warning$
        }
      if$
      pop$
    }
  if$
}

FUNCTION {control.setup}
{
  type$ cite$ "{" swap$ * "}, " * *
  "control.key"    key    control.dump *
  "control.author" author control.dump *
  "control.editor" editor control.dump *
  "control.title"  title  control.dump *
  "control.pages"  pages  control.dump *
  "control.year"   year   control.dump *
  "control.eprint" eprint control.dump *
  top$
  'control.key    key    control.parse
  'control.author author control.parse
  'control.editor editor control.parse
  'control.title  title  control.parse
  'control.pages  pages  control.parse
  'control.year   year   control.parse
  'control.eprint eprint control.parse
  control.author.decode
}

FUNCTION {control.pass}
{ type$ "control" = 'control.setup 'skip$ if$
}

FUNCTION {control.presort}
{
}

FUNCTION {control.forward}
{
}

FUNCTION {control.reverse}
{
}

FUNCTION {control.sort}
{
}

FUNCTION {control.longest.label}
{
}

FUNCTION {control.key.bib}
{
  "Control: key "
  control.key
  duplicate$ "(" swap$ int.to.str$ * ") " * swap$
  pop$ *
  diagn.cmntlog
}

FUNCTION {control.author.bib}
{
  "Control: author "
  control.author "(" swap$ int.to.str$ * ")" * *
  control.author.reversed { " reversed" * }{} if$
  control.author.first    { " first"    * }{} if$
  control.author.nocomma  { " nocomma"  * }{} if$
  control.author.initials { " initials" * }{} if$
  control.author.nospace  { " nospace"  * }{} if$
  control.author.dotless  { " dotless"  * }{} if$
  control.author.jnrlst   { " jnrlst"   * }{} if$
  diagn.cmntlog
}

FUNCTION {control.editor.bib}
{
  "Control: editor formatted "
  control.editor
  duplicate$ "(" swap$ int.to.str$ * ") " * swap$
  duplicate$ #0 <  {
    pop$
    "disabled!"
  } {
    #0 > {
      "identically to author"
    } {
      "differently from author"
    } if$
  } if$ * *
  diagn.cmntlog
}

FUNCTION {control.title.bib}
{
  "Control: production of article title "
  control.title
  duplicate$ "(" swap$ int.to.str$ * ") " * swap$
  duplicate$ #0 <  {
    pop$
    "disabled"
  } {
    #0 > {
      "required"
    } {
      "allowed"
    } if$
  } if$ * *
  diagn.cmntlog
}

FUNCTION {control.pages.bib}
{
  "Control: page "
  control.pages
  duplicate$ "(" swap$ int.to.str$ * ") " * swap$
  duplicate$ #0 <  {
    pop$
    "none"
  } {
    #0 > {
      "range"
    } {
      "single"
    } if$
  } if$ * *
  diagn.cmntlog
}

FUNCTION {control.year.bib}
{
  "Control: year "
  control.year
  duplicate$ "(" swap$ int.to.str$ * ") " * swap$
  duplicate$ #0 <  {
    pop$
    "disabled!"
  } {
    #0 > {
      "truncated"
    } {
      "verbatim"
    } if$
  } if$ * *
  diagn.cmntlog
}

FUNCTION {control.eprint.bib}
{
  "Control: production of eprint "
  control.eprint
  duplicate$ "(" swap$ int.to.str$ * ") " * swap$
  #0 < { "disabled" } { "enabled" } if$ * *
  diagn.cmntlog
}

FUNCTION {control.bib}
{
  control.key.bib
  control.author.bib
  control.editor.bib
  control.title.bib
  control.pages.bib
  control.year.bib
  control.eprint.bib
}

FUNCTION {init.state.consts}
{
  #0 'before.all        :=
  #1 'after.word        :=
  #2 'after.punctuation :=
  #3 'after.sentence    :=
  #4 'after.block       :=
  #0 'punctuation.no    :=
  #1 'punctuation.space :=
  #2 'punctuation.yes   :=
  "" 'bibfield          :=
  "" 'output.bibfield   :=
}

STRINGS { s t}
FUNCTION {block.punctuation}
{ ""
  "," *
}

FUNCTION {word.space}
{
  "\ "
}

FUNCTION {show.stackstring.one}{
  "(" *
  output.state int.to.str$ *
    "," * punctuation.state int.to.str$ *
  ")" * top$
  duplicate$ "1(" swap$ * ")" * top$
}

FUNCTION {show.stackstring.two}{
  "(" *
  output.state int.to.str$ *
    "," * punctuation.state int.to.str$ *
  ")" * top$
  swap$
  duplicate$ "1(" swap$ * ")" * top$
  swap$
  duplicate$ "2(" swap$ * ")" * top$
}

FUNCTION {bibfield.command}{ "\bibfield "}

FUNCTION {output.nonnull}
{
  swap$
  output.state after.word =
    {
      block.punctuation *
      word.space *
    }
    {
      output.state after.punctuation =
        {
          word.space *
        }
        {
          output.state after.block = output.state after.sentence = or
            {
              add.period$
              "\EOS\ " *
            }{
            }
          if$
        }
      if$
    }
  if$
  output.bibfield duplicate$ empty$ 'pop$
    {
      bibfield.command
      " {" * swap$ * "} {" * swap$ * "}" *
    }
  if$
  write$
  bibfield 'output.bibfield := "" 'bibfield :=
  output.state after.block =
    {
      newline$
      "\newblock " write$
    }
    'skip$
  if$
  punctuation.state duplicate$
  punctuation.yes 'punctuation.state :=
  punctuation.no =
    { pop$ before.all }
    { punctuation.yes = { after.word }{ after.punctuation } if$ }
  if$
  'output.state :=
}

FUNCTION {output}
{ duplicate$ empty$
    {
      pop$
  "" 'bibfield :=
    }
    'output.nonnull
  if$
}

FUNCTION {output.nopunct}
{
  punctuation.no 'punctuation.state :=
  output.nonnull
}

FUNCTION {output.check}
{ swap$
  duplicate$ empty$
    { pop$ "empty " swap$ * " in " * cite$ * warning$ }
    { swap$ pop$ output.nonnull }
  if$
}

FUNCTION {bbl.open} { "\BibitemOpen " }

FUNCTION {bbl.shut} { "\BibitemShut " }

FUNCTION {bibitem.shut.stop} { bbl.shut "{Stop}%" * }

FUNCTION {bibitem.shut.nostop} { bbl.shut "{NoStop}%" * }

FUNCTION {bibitem.shut}
{
  non.stop
    {
  bibitem.shut.nostop *
    }{
  bibitem.shut.stop *
    }
  if$
}

FUNCTION {html.itag} {
  "p"
}

FUNCTION {html.ltag} {
  ""
}

FUNCTION {output.SLACcitation}
{ SLACcitation empty$
    'skip$
    {
      newline$
      SLACcitation write$
    }
  if$
}

FUNCTION {fin.entry}
{
  bibitem.shut
  write$
    output.SLACcitation
}

FUNCTION {new.block}
{ output.state before.all =
    'skip$
    { after.block 'output.state := }
  if$
}

FUNCTION {new.block.comma}
{
}

FUNCTION {new.sentence}
{ output.state after.block = output.state before.all = or
    'skip$
    { after.sentence 'output.state := }
  if$
}

FUNCTION {new.sentence.comma}
{
}

FUNCTION {sentence.or.colon}
{
  new.sentence
}

FUNCTION {add.blank}
{
  word.space *
  before.all 'output.state :=
}

FUNCTION {no.blank.or.punct}
{
   "\hspace {0pt}" *
   before.all 'output.state :=
}

FUNCTION {date.block}
{
  new.block.comma
  skip$
}

STRINGS {z}
FUNCTION {remove.dots}
{
  control.author.dotless {
    'z :=
    ""
    { z empty$ not }
    { z #1 #1 substring$
      z #2 global.max$ substring$ 'z :=
      duplicate$ "." = 'pop$
        { * }
      if$
    }
    while$
  } 'skip$ if$
}

FUNCTION {new.block.checkb}
{ empty$
  swap$ empty$
  and
    'skip$
    'new.block
  if$
}

FUNCTION {field.or.null}
{ duplicate$ empty$
    { pop$ "" }
    'skip$
  if$
}

FUNCTION {emphasize}
{
  duplicate$ empty$
    { pop$ "" }
    {
      "\emph {" swap$ * "}" *
    }
  if$
}

FUNCTION {bolden}
{ duplicate$ empty$
    { pop$ "" }
    { "\textbf {" swap$ * "}" * }
  if$
}

FUNCTION {bib.name.font}
{
  duplicate$ empty$
    { pop$ "" }
    {
    "\bibnamefont {" swap$ * "}" *
    }
  if$
}

FUNCTION {bib.fname.font}
{
  duplicate$ empty$
    { pop$ "" }
    {
    "\bibfnamefont {" swap$ * "}" *
    }
  if$
}

FUNCTION {cite.name.font}
{
  duplicate$ empty$
    { pop$ "" }
    {
    "\citenamefont {" swap$ * "}" *
    }
  if$
}

FUNCTION {tie.or.space.prefix}
{ duplicate$ text.length$ #3 <
    { "~" }
    { word.space }
  if$
  swap$
}

FUNCTION {capitalize}
{
  "u" change.case$ "t" change.case$
}

FUNCTION {space.word}
{ word.space swap$ * word.space * }

 % Here are the language-specific definitions for explicit words.
 % Each function has a name bbl.xxx where xxx is the English word.
 % The language selected here is ENGLISH

FUNCTION {bbl.and}
{
  "and"
}

FUNCTION {bbl.etal}
{
  "et~al."
}

FUNCTION {bbl.editors}
{
  "eds."
}

FUNCTION {bbl.editor}
{
  "ed."
}

FUNCTION {bbl.edby}
{ "edited by" }

FUNCTION {bbl.edition}
{
  "ed."
}

FUNCTION {bbl.volume}
{
  "vol."
}

FUNCTION {bbl.of}
{ "of" }

FUNCTION {bbl.number}
{
  "no."
}

FUNCTION {bbl.nr}
{ "no." }

FUNCTION {bbl.in}
{ "in" }

FUNCTION {bbl.pages}
{
  "pp."
}

FUNCTION {bbl.page}
{
  "p."
}

FUNCTION {bbl.eidpp}
{ "pages" }

FUNCTION {bbl.chapter}
{
  "chap."
}

FUNCTION {bbl.techrep}
{
  "Tech. Rep."
}

FUNCTION {bbl.mthesis}
{ "Master's thesis" }

FUNCTION {bbl.phdthesis}
{ "Ph.D. thesis" }

FUNCTION {bbl.first}
{
  "1st"
}

FUNCTION {bbl.second}
{
  "2nd"
}

FUNCTION {bbl.third}
{
  "3rd"
}

FUNCTION {bbl.fourth}
{
  "4th"
}

FUNCTION {bbl.fifth}
{
  "5th"
}

FUNCTION {bbl.st}
{ "st" }

FUNCTION {bbl.nd}
{ "nd" }

FUNCTION {bbl.rd}
{ "rd" }

FUNCTION {bbl.th}
{ "th" }

MACRO {jan} {"Jan."}

MACRO {feb} {"Feb."}

MACRO {mar} {"Mar."}

MACRO {apr} {"Apr."}

MACRO {may} {"May"}

MACRO {jun} {"Jun."}

MACRO {jul} {"Jul."}

MACRO {aug} {"Aug."}

MACRO {sep} {"Sep."}

MACRO {oct} {"Oct."}

MACRO {nov} {"Nov."}

MACRO {dec} {"Dec."}

FUNCTION {bbl.url.prefix}
{
  "\urlprefix "
}

FUNCTION {eng.ord}
{ duplicate$ "1" swap$ *
  #-2 #1 substring$ "1" =
     { bbl.th * }
     { duplicate$ #-1 #1 substring$
       duplicate$ "1" =
         { pop$ bbl.st * }
         { duplicate$ "2" =
             { pop$ bbl.nd * }
             { "3" =
                 { bbl.rd * }
                 { bbl.th * }
               if$
             }
           if$
          }
       if$
     }
   if$
}

 %-------------------------------------------------------------------
 % Begin module:
 % \ProvidesFile{physjour.mbs}[2002/01/14 2.2 (PWD)]
MACRO {aa}{"Astron. \& Astrophys."}
MACRO {aasup}{"Astron. \& Astrophys. Suppl. Ser."}
MACRO {aj} {"Astron. J."}
MACRO {aph} {"Acta Phys."}
MACRO {advp} {"Adv. Phys."}
MACRO {ajp} {"Amer. J. Phys."}
MACRO {ajm} {"Amer. J. Math."}
MACRO {amsci} {"Amer. Sci."}
MACRO {anofd} {"Ann. Fluid Dyn."}
MACRO {am} {"Ann. Math."}
MACRO {ap} {"Ann. Phys. (NY)"}
MACRO {adp} {"Ann. Phys. (Leipzig)"}
MACRO {ao} {"Appl. Opt."}
MACRO {apl} {"Appl. Phys. Lett."}
MACRO {app} {"Astroparticle Phys."}
MACRO {apj} {"Astrophys. J."}
MACRO {apjsup} {"Astrophys. J. Suppl."}
MACRO {apss} {"Astrophys. Space Sci."}
MACRO {araa} {"Ann. Rev. Astron. Astrophys."}
MACRO {baas} {"Bull. Amer. Astron. Soc."}
MACRO {baps} {"Bull. Amer. Phys. Soc."}
MACRO {cmp} {"Comm. Math. Phys."}
MACRO {cpam} {"Commun. Pure Appl. Math."}
MACRO {cppcf} {"Comm. Plasma Phys. \& Controlled Fusion"}
MACRO {cpc} {"Comp. Phys. Comm."}
MACRO {cqg} {"Class. Quant. Grav."}
MACRO {cra} {"C. R. Acad. Sci. A"}
MACRO {fed} {"Fusion Eng. \& Design"}
MACRO {ft} {"Fusion Tech."}
MACRO {grg} {"Gen. Relativ. Gravit."}
MACRO {ieeens} {"IEEE Trans. Nucl. Sci."}
MACRO {ieeeps} {"IEEE Trans. Plasma Sci."}
MACRO {ijimw} {"Interntl. J. Infrared \& Millimeter Waves"}
MACRO {ip} {"Infrared Phys."}
MACRO {irp} {"Infrared Phys."}
MACRO {jap} {"J. Appl. Phys."}
MACRO {jasa} {"J. Acoust. Soc. America"}
MACRO {jcp} {"J. Comp. Phys."}
MACRO {jetp} {"Sov. Phys.--JETP"}
MACRO {jfe} {"J. Fusion Energy"}
MACRO {jfm} {"J. Fluid Mech."}
MACRO {jmp} {"J. Math. Phys."}
MACRO {jne} {"J. Nucl. Energy"}
MACRO {jnec} {"J. Nucl. Energy, C: Plasma Phys., Accelerators, Thermonucl. Res."}
MACRO {jnm} {"J. Nucl. Mat."}
MACRO {jpc} {"J. Phys. Chem."}
MACRO {jpp} {"J. Plasma Phys."}
MACRO {jpsj} {"J. Phys. Soc. Japan"}
MACRO {jsi} {"J. Sci. Instrum."}
MACRO {jvst} {"J. Vac. Sci. \& Tech."}
MACRO {nat} {"Nature"}
MACRO {nature} {"Nature"}
MACRO {nedf} {"Nucl. Eng. \& Design/Fusion"}
MACRO {nf} {"Nucl. Fusion"}
MACRO {nim} {"Nucl. Inst. \& Meth."}
MACRO {nimpr} {"Nucl. Inst. \& Meth. in Phys. Res."}
MACRO {np} {"Nucl. Phys."}
MACRO {npb} {"Nucl. Phys. B"}
MACRO {nt/f} {"Nucl. Tech./Fusion"}
MACRO {npbpc} {"Nucl. Phys. B (Proc. Suppl.)"}
MACRO {inc} {"Nuovo Cimento"}
MACRO {nc} {"Nuovo Cimento"}
MACRO {pf} {"Phys. Fluids"}
MACRO {pfa} {"Phys. Fluids A: Fluid Dyn."}
MACRO {pfb} {"Phys. Fluids B: Plasma Phys."}
MACRO {pl} {"Phys. Lett."}
MACRO {pla} {"Phys. Lett. A"}
MACRO {plb} {"Phys. Lett. B"}
MACRO {prep} {"Phys. Rep."}
MACRO {pnas} {"Proc. Nat. Acad. Sci. USA"}
MACRO {pp} {"Phys. Plasmas"}
MACRO {ppcf} {"Plasma Phys. \& Controlled Fusion"}
MACRO {phitrsl} {"Philos. Trans. Roy. Soc. London"}
MACRO {prl} {"Phys. Rev. Lett."}
MACRO {pr} {"Phys. Rev."}
MACRO {physrev} {"Phys. Rev."}
MACRO {pra} {"Phys. Rev. A"}
MACRO {prb} {"Phys. Rev. B"}
MACRO {prc} {"Phys. Rev. C"}
MACRO {prd} {"Phys. Rev. D"}
MACRO {pre} {"Phys. Rev. E"}
MACRO {ps} {"Phys. Scripta"}
MACRO {procrsl} {"Proc. Roy. Soc. London"}
MACRO {rmp} {"Rev. Mod. Phys."}
MACRO {rsi} {"Rev. Sci. Inst."}
MACRO {science} {"Science"}
MACRO {sciam} {"Sci. Am."}
MACRO {sam} {"Stud. Appl. Math."}
MACRO {sjpp} {"Sov. J. Plasma Phys."}
MACRO {spd} {"Sov. Phys.--Doklady"}
MACRO {sptp} {"Sov. Phys.--Tech. Phys."}
MACRO {spu} {"Sov. Phys.--Uspeki"}
MACRO {st} {"Sky and Telesc."}
 % End module: physjour.mbs
 %-------------------------------------------------------------------
 % Begin module:
 % \ProvidesFile{geojour.mbs}[2002/07/10 2.0h (PWD)]
MACRO {aisr} {"Adv. Space Res."}
MACRO {ag} {"Ann. Geophys."}
MACRO {anigeo} {"Ann. Geofis."}
MACRO {angl} {"Ann. Glaciol."}
MACRO {andmet} {"Ann. d. Meteor."}
MACRO {andgeo} {"Ann. d. Geophys."}
MACRO {andphy} {"Ann. Phys.-Paris"}
MACRO {afmgb} {"Arch. Meteor. Geophys. Bioklimatol."}
MACRO {atph} {"Atm\'osphera"}
MACRO {aao} {"Atmos. Ocean"}
MACRO {ass}{"Astrophys. Space Sci."}
MACRO {atenv} {"Atmos. Environ."}
MACRO {aujag} {"Aust. J. Agr. Res."}
MACRO {aumet} {"Aust. Meteorol. Mag."}
MACRO {blmet} {"Bound.-Lay. Meteorol."}
MACRO {bams} {"Bull. Amer. Meteorol. Soc."}
MACRO {cch} {"Clim. Change"}
MACRO {cdyn} {"Clim. Dynam."}
MACRO {cbul} {"Climatol. Bull."}
MACRO {cap} {"Contrib. Atmos. Phys."}
MACRO {dsr} {"Deep-Sea Res."}
MACRO {dhz} {"Dtsch. Hydrogr. Z."}
MACRO {dao} {"Dynam. Atmos. Oceans"}
MACRO {eco} {"Ecology"}
MACRO {empl}{"Earth, Moon and Planets"}
MACRO {envres} {"Environ. Res."}
MACRO {envst} {"Environ. Sci. Technol."}
MACRO {ecms} {"Estuarine Coastal Mar. Sci."}
MACRO {expa}{"Exper. Astron."}
MACRO {geoint} {"Geofis. Int."}
MACRO {geopub} {"Geofys. Publ."}
MACRO {geogeo} {"Geol. Geofiz."}
MACRO {gafd} {"Geophys. Astrophys. Fluid Dyn."}
MACRO {gfd} {"Geophys. Fluid Dyn."}
MACRO {geomag} {"Geophys. Mag."}
MACRO {georl} {"Geophys. Res. Lett."}
MACRO {grl} {"Geophys. Res. Lett."}
MACRO {ga} {"Geophysica"}
MACRO {gs} {"Geophysics"}
MACRO {ieeetap} {"IEEE Trans. Antenn. Propag."}
MACRO {ijawp} {"Int. J. Air Water Pollut."}
MACRO {ijc} {"Int. J. Climatol."}
MACRO {ijrs} {"Int. J. Remote Sens."}
MACRO {jam} {"J. Appl. Meteorol."}
MACRO {jaot} {"J. Atmos. Ocean. Technol."}
MACRO {jatp} {"J. Atmos. Terr. Phys."}
MACRO {jastp} {"J. Atmos. Solar-Terr. Phys."}
MACRO {jce} {"J. Climate"}
MACRO {jcam} {"J. Climate Appl. Meteor."}
MACRO {jcm} {"J. Climate Meteor."}
MACRO {jcy} {"J. Climatol."}
MACRO {jgr} {"J. Geophys. Res."}
MACRO {jga} {"J. Glaciol."}
MACRO {jh} {"J. Hydrol."}
MACRO {jmr} {"J. Mar. Res."}
MACRO {jmrj} {"J. Meteor. Res. Japan"}
MACRO {jm} {"J. Meteor."}
MACRO {jpo} {"J. Phys. Oceanogr."}
MACRO {jra} {"J. Rech. Atmos."}
MACRO {jaes} {"J. Aeronaut. Sci."}
MACRO {japca} {"J. Air Pollut. Control Assoc."}
MACRO {jas} {"J. Atmos. Sci."}
MACRO {jmts} {"J. Mar. Technol. Soc."}
MACRO {jmsj} {"J. Meteorol. Soc. Japan"}
MACRO {josj} {"J. Oceanogr. Soc. Japan"}
MACRO {jwm} {"J. Wea. Mod."}
MACRO {lao} {"Limnol. Oceanogr."}
MACRO {mwl} {"Mar. Wea. Log"}
MACRO {mau} {"Mausam"}
MACRO {meteor} {"``Meteor'' Forschungsergeb."}
MACRO {map} {"Meteorol. Atmos. Phys."}
MACRO {metmag} {"Meteor. Mag."}
MACRO {metmon} {"Meteor. Monogr."}
MACRO {metrun} {"Meteor. Rundsch."}
MACRO {metzeit} {"Meteor. Z."}
MACRO {metgid} {"Meteor. Gidrol."}
MACRO {mwr} {"Mon. Weather Rev."}
MACRO {nwd} {"Natl. Weather Dig."}
MACRO {nzjmfr} {"New Zeal. J. Mar. Freshwater Res."}
MACRO {npg} {"Nonlin. Proc. Geophys."}
MACRO {om} {"Oceanogr. Meteorol."}
MACRO {ocac} {"Oceanol. Acta"}
MACRO {oceanus} {"Oceanus"}
MACRO {paleoc} {"Paleoceanography"}
MACRO {pce} {"Phys. Chem. Earth"}
MACRO {pmg} {"Pap. Meteor. Geophys."}
MACRO {ppom} {"Pap. Phys. Oceanogr. Meteor."}
MACRO {physzeit} {"Phys. Z."}
MACRO {pps} {"Planet. Space Sci."}
MACRO {pss} {"Planet. Space Sci."}
MACRO {pag} {"Pure Appl. Geophys."}
MACRO {qjrms} {"Quart. J. Roy. Meteorol. Soc."}
MACRO {quatres} {"Quat. Res."}
MACRO {rsci} {"Radio Sci."}
MACRO {rse} {"Remote Sens. Environ."}
MACRO {rgeo} {"Rev. Geophys."}
MACRO {rgsp} {"Rev. Geophys. Space Phys."}
MACRO {rdgeo} {"Rev. Geofis."}
MACRO {revmeta} {"Rev. Meteorol."}
MACRO {sgp}{"Surveys in Geophys."}
MACRO {sp} {"Solar Phys."}
MACRO {ssr} {"Space Sci. Rev."}
MACRO {tellus} {"Tellus"}
MACRO {tac} {"Theor. Appl. Climatol."}
MACRO {tagu} {"Trans. Am. Geophys. Union (EOS)"}
MACRO {wrr} {"Water Resour. Res."}
MACRO {weather} {"Weather"}
MACRO {wafc} {"Weather Forecast."}
MACRO {ww} {"Weatherwise"}
MACRO {wmob} {"WMO Bull."}
MACRO {zeitmet} {"Z. Meteorol."}
 % End module: geojour.mbs
 %-------------------------------------------------------------------
 % Begin module:
 % \ProvidesFile{photjour.mbs}[1999/02/24 2.0b (PWD)]

MACRO {appopt} {"Appl. Opt."}
MACRO {bell} {"Bell Syst. Tech. J."}
MACRO {ell} {"Electron. Lett."}
MACRO {jasp} {"J. Appl. Spectr."}
MACRO {jqe} {"IEEE J. Quantum Electron."}
MACRO {jlwt} {"J. Lightwave Technol."}
MACRO {jmo} {"J. Mod. Opt."}
MACRO {josa} {"J. Opt. Soc. America"}
MACRO {josaa} {"J. Opt. Soc. Amer.~A"}
MACRO {josab} {"J. Opt. Soc. Amer.~B"}
MACRO {jdp} {"J. Phys. (Paris)"}
MACRO {oc} {"Opt. Commun."}
MACRO {ol} {"Opt. Lett."}
MACRO {phtl} {"IEEE Photon. Technol. Lett."}
MACRO {pspie} {"Proc. Soc. Photo-Opt. Instrum. Eng."}
MACRO {sse} {"Solid-State Electron."}
MACRO {sjot} {"Sov. J. Opt. Technol."}
MACRO {sjqe} {"Sov. J. Quantum Electron."}
MACRO {sleb} {"Sov. Phys.--Leb. Inst. Rep."}
MACRO {stph} {"Sov. Phys.--Techn. Phys."}
MACRO {stphl} {"Sov. Techn. Phys. Lett."}
MACRO {vr} {"Vision Res."}
MACRO {zph} {"Z. f. Physik"}
MACRO {zphb} {"Z. f. Physik~B"}
MACRO {zphd} {"Z. f. Physik~D"}

MACRO {CLEO} {"CLEO"}
MACRO {ASSL} {"Adv. Sol.-State Lasers"}
MACRO {OSA}  {"OSA"}
 % End module: photjour.mbs
%% Copyright 1994-2007 Patrick W Daly
MACRO {acmcs} {"ACM Comput. Surv."}
MACRO {acta} {"Acta Inf."}
MACRO {cacm} {"Commun. ACM"}
MACRO {ibmjrd} {"IBM J. Res. Dev."}
MACRO {ibmsj} {"IBM Syst.~J."}
MACRO {ieeese} {"IEEE Trans. Software Eng."}
MACRO {ieeetc} {"IEEE Trans. Comput."}
MACRO {ieeetcad}
 {"IEEE Trans. Comput. Aid. Des."}
MACRO {ipl} {"Inf. Process. Lett."}
MACRO {jacm} {"J.~ACM"}
MACRO {jcss} {"J.~Comput. Syst. Sci."}
MACRO {scp} {"Sci. Comput. Program."}
MACRO {sicomp} {"SIAM J. Comput."}
MACRO {tocs} {"ACM Trans. Comput. Syst."}
MACRO {tods} {"ACM Trans. Database Syst."}
MACRO {tog} {"ACM Trans. Graphic."}
MACRO {toms} {"ACM Trans. Math. Software"}
MACRO {toois} {"ACM Trans. Office Inf. Syst."}
MACRO {toplas} {"ACM Trans. Progr. Lang. Syst."}
MACRO {tcs} {"Theor. Comput. Sci."}

MACRO {jhep} {"J. High Energy Phys."}
MACRO {jcap} {"J. Cosmol. Astropart. Phys."}
MACRO {jinst} {"J. Instrum."}
MACRO {jstat} {"J. Stat. Mech.: Theory Exp."}

FUNCTION {bibinfo.command} { "\bibinfo " }

FUNCTION {bibinfo.check}
{ swap$
  duplicate$ missing$
    {
      pop$
      pop$ ""
    }{
      duplicate$ empty$
        {
          swap$ pop$
        }{
          swap$
          bibinfo.command "{" * swap$ * "} {" * swap$ * "}" *
        }
      if$
    }
  if$
}

FUNCTION {bibinfo.warn}
{ swap$
  duplicate$ missing$
    {
      swap$ "missing " swap$ * " in " * cite$ * warning$ pop$
      ""
    }{
      duplicate$ empty$
        {
          swap$ "empty " swap$ * " in " * cite$ * warning$
        }{
          swap$
          bibinfo.command " {" * swap$ * "} {" * swap$ * "}" *
        }
      if$
    }
  if$
}

FUNCTION {archiv.base}
{
  "https://arxiv.org/abs"
}

FUNCTION {archiv.prefix.base}
{
  "arXiv"
}

FUNCTION {eprint.command}
{
  "\Eprint "
}

FUNCTION {format.eprint.controlled}
{
  eprint duplicate$ empty$
  control.eprint #1 =
  or
    { pop$ "" }
    {
      duplicate$
      ""
        archive duplicate$ empty$ { pop$ archiv.base } 'skip$ if$ *
        "/" *
        swap$ *
        "{" swap$ * "} " *
      swap$
      ""
        archivePrefix duplicate$ empty$ { pop$ "" } { ":" * } if$ *
        swap$ *
        primaryClass  duplicate$ empty$ { pop$ "" } { " [" swap$ * "]" * } if$ *
        "{" swap$ * "} " *
      *
      eprint.command swap$ *
    }
  if$
}

FUNCTION {format.eprint}
{
  eprint duplicate$ empty$
    { pop$ "" }
    {
      duplicate$
      ""
        archive duplicate$ empty$ { pop$ archiv.base } 'skip$ if$ *
        "/" *
        swap$ *
        "{" swap$ * "} " *
      swap$
      ""
        archivePrefix duplicate$ empty$ { pop$ "" } { ":" * } if$ *
        swap$ *
        primaryClass  duplicate$ empty$ { pop$ "" } { " [" swap$ * "]" * } if$ *
        "{" swap$ * "} " *
      *
      eprint.command swap$ *
    }
  if$
}

FUNCTION {format.translation}
{ translation duplicate$ empty$
    'skip$
    { ""
      "\translation{" * swap$ * "}" *
      punctuation.space 'punctuation.state :=
    }
  if$
}

FUNCTION {format.url}
{
  url duplicate$ empty$
    { pop$ "" }
    {
      "\url "
      "{" * swap$ * "}" *
    }
  if$
}

INTEGERS { nameptr namesleft numnames }

FUNCTION {check.speaker}
{ key empty$ 'skip$
  { key nameptr int.to.str$ =
    {
      bolden
    }
      'skip$
    if$
  }
  if$
}

STRINGS  { bibinfo}

FUNCTION {format.names.fname}
{
  control.author.initials {
    control.author.dotless {
      control.author.nospace {
        "f{}"
      } {
        "f{~}"
      } if$
    } {
      control.author.nospace {
        "f{.}."
      } {
        "f."
      } if$
    } if$
  } {
    "ff"
  } if$
}

FUNCTION {bracify}
{
  "{" swap$ * "}" *
}

FUNCTION {name.comma}
{
  control.author.nocomma 'skip$ { "," swap$ * } if$
}

FUNCTION {format.names.format.onefont}
{
  "{vv~}{ll}"
  nameptr #1 >
  control.author.first
  and
  control.author.reversed not
  or
    {
      control.author.initials {
        "f"
        control.author.dotless 'skip$ {
          "." *
        } if$
        "~" *
      } {
        "ff"
      } if$
      bracify
      swap$
    } {
      format.names.fname
      " " swap$ *
      name.comma
      bracify
    }
  if$
  "jj"
  " " swap$ *
  name.comma
  bracify
  control.author.jnrlst 'skip$ 'swap$ if$
  * *
}

FUNCTION {format.names.onefont}
{
  s nameptr format.names.format.onefont format.name$
  remove.dots
  bib.name.font
}

FUNCTION {format.names.morfont}
{ s nameptr
  "{vv~}{ll}" format.name$ bib.name.font
  nameptr #1 >
  control.author.first
  and
  control.author.reversed not
  or
  {
    s nameptr
    control.author.initials {
      "f"                        % default: name + surname + comma junior
    } {
      "ff"
    } if$
    control.author.dotless 'skip$ {
      "." *                    % nm-init   % Initials. + surname (J. F. Smith)                                           control.author.initials
    } if$
    bracify
    format.name$ duplicate$ empty$ 'skip$
      { tie.or.space.prefix bib.fname.font swap$ * }
    if$
    swap$
    *
    s nameptr
    "{jj}" format.name$ duplicate$ empty$ 'skip$
      { bib.fname.font ", " swap$ * }
    if$
  } {
    "," *
    s nameptr
    format.names.fname
    "jj"
    " "
    name.comma
    control.author.jnrlst {
      swap$ * skip$
    } {
      skip$ * swap$
    } if$
    bracify swap$ bracify swap$
    *
    format.name$
    remove.dots
    duplicate$ empty$ 'skip$
      { bib.fname.font " " swap$ * }
      if$
  } if$
  *
}

FUNCTION {names.punctuate}
{
  "," *
  " " *
}

FUNCTION {names.comma}
{
  "," *
}

FUNCTION {format.names}
{ 'bibinfo :=
  duplicate$ empty$ { pop$ "" } {
    duplicate$ num.names$
    duplicate$ 'numnames :=
    'namesleft :=
    's :=
    #1 'nameptr :=
    ""
      { namesleft #0 > }
      {
      format.names.morfont
        bibinfo bibinfo.check
        type$ "presentation" =
          'check.speaker
          'skip$
        if$
        't :=
        nameptr #1 > not
          {
            t *
          } {
            namesleft #1 >
              {
                names.punctuate
                t *
              } {
                s nameptr "{ll}" format.name$ duplicate$ "others" =
                  { 't := }
                  { pop$ }
                if$
                numnames #2 >
                  'names.comma
                  'skip$
                if$
                t "others" =
                  {
                    " " *
                    bbl.etal
                    emphasize
                    *
                  } {
                    bbl.and
                    space.word *
                    t *
                  }
                if$
              }
            if$
          }
        if$
        nameptr #1 + 'nameptr :=
        namesleft #1 - 'namesleft :=
      }
    while$
  } if$
}

FUNCTION {format.names.ed.onefont}
{
    s nameptr
    control.author.initials {
      control.author.dotless {
        control.author.nospace {
          "{f{}~}{vv~}{ll}{ jj}"   % nm-rvx|nm-rvcx
        } {
          "{f{~}~}{vv~}{ll}{ jj}"  % nm-rv
        } if$
      } {
        control.author.nospace {
          "{f{.}.~}{vv~}{ll}{ jj}" % nm-rvv|nm-rvvc
        }{
          "{f.~}{vv~}{ll}{, jj}"   % nm-init|nm-rev|nm-rev1
        } if$
      } if$
    } {
      "{ff~}{vv~}{ll}{, jj}"
    } if$
    format.name$
    remove.dots
    bib.name.font
}

FUNCTION {format.names.ed.morfont}
{
  control.author.reversed { %
    control.author.initials { %
      control.author.dotless { %
        s nameptr
        control.author.nospace { % nm-rvx nm-rvcx
          "{f{}}"
        } { % nm-rv
          "{f{~}}"
        } if$
        format.name$ duplicate$ empty$ 'skip$
          { tie.or.space.prefix bib.fname.font swap$ * }
        if$
        s nameptr
        "{vv~}{ll}" format.name$ bib.name.font *
        s nameptr
        "{jj}" format.name$
        remove.dots
        duplicate$ empty$ 'skip$
          { bib.fname.font " " swap$ * }
        if$
      } { % !control.author.dotless
        s nameptr
        control.author.nospace { % nm-rvv
          "{ff}"
        } { % nm-rev nm-rev1
          "{f.}"
        } if$
        format.name$ duplicate$ empty$ 'skip$
          { tie.or.space.prefix bib.fname.font swap$ * }
        if$
        s nameptr
        "{vv~}{ll}" format.name$ bib.name.font *
        s nameptr
        "{jj}" format.name$
        duplicate$ empty$ 'skip$
          { bib.fname.font ", " swap$ * }
        if$
      } if$
    } { % Full names !control.author.initials  nm-revf nm-revv1
      s nameptr
      "{ff}"
      format.name$ duplicate$ empty$ 'skip$
        { tie.or.space.prefix bib.fname.font swap$ * }
      if$
      s nameptr
      "{vv~}{ll}" format.name$ bib.name.font *
      s nameptr
      "{jj}" format.name$
      duplicate$ empty$ 'skip$
        { bib.fname.font ", " swap$ * }
      if$
    } if$
  } { % !control.author.reversed nm-init
    s nameptr
    "{f.}"
    format.name$ duplicate$ empty$ 'skip$
      { tie.or.space.prefix bib.fname.font swap$ * }
    if$
    s nameptr
    "{vv~}{ll}" format.name$ bib.name.font *
    s nameptr
    "{jj}" format.name$
    duplicate$ empty$ 'skip$
      { bib.fname.font ", " swap$ * }
    if$
  } if$
  *
}

FUNCTION {format.names.ed}
{
  control.editor #0 > {
    format.names
  } {
    'bibinfo :=
    duplicate$ empty$ 'skip$ {
    's :=
    "" 't :=
    #1 'nameptr :=
    s num.names$ 'numnames :=
    numnames 'namesleft :=
      { namesleft #0 > }
      {
       format.names.ed.morfont
        bibinfo bibinfo.check
        't :=
        nameptr #1 >
          {
            namesleft #1 >
              {
                names.punctuate
                t *
              }{
                s nameptr "{ll}" format.name$ duplicate$ "others" =
                  { 't := }
                  { pop$ }
                if$
                numnames #2 >
                  'names.comma
                  'skip$
                if$
                t "others" =
                  {
                    " " * bbl.etal emphasize *
                  }{
                   bbl.and
                    space.word * t *
                  }
                if$
              }
            if$
          }
          't
        if$
        nameptr #1 + 'nameptr :=
        namesleft #1 - 'namesleft :=
      }
    while$
    } if$
  } if$
}

FUNCTION {format.key}
{ empty$
    { key field.or.null }
    { "" }
  if$
}

FUNCTION {format.authors}
{ author "author" format.names
  duplicate$ empty$ 'skip$
    { collaboration "collaboration" bibinfo.check
      duplicate$ empty$ 'skip$
        { " (" swap$ * ")" * }
      if$
      *
    }
  if$
  "author" 'bibfield :=
}

FUNCTION {get.bbl.editor}
{ editor num.names$ #1 > 'bbl.editors 'bbl.editor if$
}

FUNCTION {format.editors}
{ editor "editor" format.names duplicate$ empty$ 'skip$
    {
      "," *
      word.space *
      get.bbl.editor
      *
    }
  if$
}

FUNCTION {format.isbn.output}
{
}

FUNCTION {format.issn.output}
{
}

FUNCTION {doi.base}
{
  "https://doi.org/"
}

FUNCTION {doi.base.command}
{
  "\doibase "
}

FUNCTION {noop.command}
{
  "\href@noop "
}

FUNCTION {href.command}
{
  "\href "
}

FUNCTION {link.tag.open}
{
  doi duplicate$ empty$
    {
      pop$
      url duplicate$ empty$
        {
          pop$ "" noop.command
        }{
          href.command
        }
      if$
    }
    {
      "https://doi.org/" swap$ *
      href.command
    }
  if$
  "{" * swap$ * "} {" *
}

FUNCTION {link.tag.shut}
{
  "}"
}

FUNCTION {link.open}
{
  link.tag.open output.nopunct
}

FUNCTION {link.shut}
{
  link.tag.shut *
}

FUNCTION {add.doi}
{
  link.tag.open swap$ * link.tag.shut *
}

FUNCTION {select.language}
{ duplicate$ empty$
    'pop$
    { language empty$
        'skip$
        { "{\selectlanguage {" language * "}" * swap$ * "}" * }
      if$
    }
    if$
}

FUNCTION {format.note}
{
 note empty$
    { "" }
    { note #1 #1 substring$
      duplicate$ "{" =
        'skip$
        {
          output.state after.word = output.state after.punctuation = or
            { "l" }
            { "u" }
          if$ change.case$
        }
      if$
      note #2 global.max$ substring$ * "note" bibinfo.check
    }
  if$
}

FUNCTION {bbl.enquote}
{
  "\enquote "
}

FUNCTION {string.enquote}
{
  punctuation.no 'punctuation.state :=
  non.stop {
    block.punctuation
  } { "" } if$
   swap$ pop$
  *
  bbl.enquote "{" * swap$ * "}" *
  word.space *
}

FUNCTION {format.title}
{ title
  duplicate$ empty$ 'skip$ { "t" change.case$ } if$
  duplicate$ "title" bibinfo.check swap$
  duplicate$ empty$ 'pop$
    {
      punctuation.yes 'punctuation.state :=
      pop$
      select.language
    }
  if$
}

FUNCTION {end.quote.title}
{ title empty$
    'skip$
    { before.all 'output.state := }
  if$
}

FUNCTION {format.name.apply}
{
  s nameptr
  "{vv~}{ll}"
  format.name$
  cite.name.font
}

FUNCTION {format.full.names}
{
  's :=
  "" 't :=
  #1 'nameptr :=
  s num.names$ 'numnames :=
  numnames 'namesleft :=
    { namesleft #0 > }
    { format.name.apply
      't :=
      nameptr #1 >
        {
          namesleft #1 >
            { ", " * t * }
            {
              s nameptr "{ll}" format.name$ duplicate$ "others" =
                {
                  't :=
                }
                'pop$
              if$
              t "others" =
                {
                  " " * bbl.etal
                  emphasize *
                }{
                  numnames #2 > { "," * }{ skip$ } if$
                  bbl.and
                  space.word * t *
                }
              if$
            }
          if$
        }
        't
      if$
      nameptr #1 + 'nameptr :=
      namesleft #1 - 'namesleft :=
    }
  while$
}

FUNCTION {make.full.names}
{
  key editor author
  type$ "proceedings" =
  type$ "book"        =
  type$ "inbook"      =
  or { pop$ }{ { pop$ "" }{ swap$ pop$ "" swap$ } if$ } if$
  duplicate$ empty$
    { pop$
      duplicate$ empty$
        { pop$
          duplicate$ empty$
            { pop$
              cite$ #1 #3 substring$
            }{
              skip$
            }
          if$
        }
        { swap$ pop$ format.full.names }
      if$
    }
    { swap$ pop$ swap$ pop$ format.full.names }
  if$
}

FUNCTION {year.bibitem}
{
  year duplicate$ empty$
  { pop$ ""
  }{
    skip$
  } if$
  extra.label *
}

FUNCTION {output.bibitem}
{
  newline$
  ""
  label
  * ")" *
   make.full.names duplicate$ short.list =
   { pop$ }{ * } if$
   bracify
  "[" swap$ * "]" *
  cite$ bracify "%" *
  *
  "\bibitem "
  swap$ *
  write$ newline$
  "  "
  duplicate$ bbl.open * write$ newline$
  before.all 'output.state :=
  punctuation.yes 'punctuation.state :=
}

FUNCTION {n.dashify}
{
  't :=
  ""
    { t empty$ not }
    { t #1 #1 substring$ "-" =
        { t #1 #2 substring$ "--" = not
            { "--" *
              t #2 global.max$ substring$ 't :=
            }{
                { t #1 #1 substring$ "-" = }
                { "-" *
                  t #2 global.max$ substring$ 't :=
                }
              while$
            }
          if$
        }{
          t #1 #1 substring$ *
          t #2 global.max$ substring$ 't :=
        }
      if$
    }
  while$
}

FUNCTION {word.in}
{
  bbl.in
  word.space *
}

FUNCTION {parenthesize}
{
  duplicate$ empty$
    'skip$
    {
      before.all 'output.state :=
      " (" swap$ * ")" *
    }
  if$
}

FUNCTION {format.date}
{
  year "year" bibinfo.check duplicate$ empty$
    {
    }
    'skip$
  if$
  extra.label *
  parenthesize
}

FUNCTION {format.date.output.check}
{
 format.date
 "year" output.check
}

FUNCTION {format.date.output}
{
  format.date.output.check
}

FUNCTION {format.btitle}
{
  booktitle duplicate$ empty$ { pop$
      title
  } 'skip$ if$
  "title" bibinfo.check
  duplicate$ empty$ 'skip$
    {
      emphasize
      select.language
    }
  if$
}

FUNCTION {either.or.check}
{ empty$
    'pop$
    { "can't use both " swap$ * " fields in " * cite$ * warning$ }
  if$
}

FUNCTION {editor.check.book}
{ editor empty$ 'skip$
    {
      "can't use both author and editor fields in " cite$ *
      ": try using @inbook instead" *
      warning$
    }
  if$
}

FUNCTION {format.bvolume}
{ volume duplicate$ empty$
    { pop$ "" }
    {
      "volume and number" number either.or.check
      bbl.volume
      capitalize
      swap$
      tie.or.space.prefix "volume" bibinfo.check * *
      series "series" bibinfo.check duplicate$ empty$ 'pop$
        {
          ", "
          * swap$ *
        }
      if$
    }
  if$
}

FUNCTION {format.number}
{
  bbl.number
  output.state after.word = output.state after.punctuation = or
  #1 or
  #0 and
    'skip$
    { capitalize }
  if$
  number tie.or.space.prefix "number" bibinfo.check * *
}

FUNCTION {format.number.series}
{ volume empty$
    { number empty$
        { series field.or.null }
        {
          series empty$
            {
              number "number" bibinfo.check
            }{
              format.number
              series "series" bibinfo.check
              word.space * swap$ *
            }
          if$
        }
      if$
    }
    { "" }
  if$
}

FUNCTION {is.num}
{ chr.to.int$
  duplicate$ "0" chr.to.int$ < not
  swap$ "9" chr.to.int$ > not and
}

FUNCTION {extract.num}
{ duplicate$ 't :=
  "" 's :=
  { t empty$ not }
  { t #1 #1 substring$
    t #2 global.max$ substring$ 't :=
    duplicate$ is.num
      { s swap$ * 's := }
      { pop$ "" 't := }
    if$
  }
  while$
  s empty$
    'skip$
    { pop$ s }
  if$
}

FUNCTION {convert.edition}
{ extract.num "l" change.case$ 's :=
  s "first" = s "1" = or
    { bbl.first 't := }
    { s "second" = s "2" = or
        { bbl.second 't := }
        { s "third" = s "3" = or
            { bbl.third 't := }
            { s "fourth" = s "4" = or
                { bbl.fourth 't := }
                { s "fifth" = s "5" = or
                    { bbl.fifth 't := }
                    { s #1 #1 substring$ is.num
                        { s
                            eng.ord
                        't := }
                        { edition 't := }
                      if$
                    }
                  if$
                }
              if$
            }
          if$
        }
      if$
    }
  if$
  t
}

FUNCTION {format.edition}
{ edition duplicate$ empty$ 'skip$
    {
      convert.edition
      output.state after.word = output.state after.punctuation = or
        { "l" }
        { "t" }
      if$ change.case$
      "edition" bibinfo.check
      word.space * bbl.edition *
    }
  if$
}

INTEGERS { multiresult }
FUNCTION {multi.page.check}
{ 't :=
  #0 'multiresult :=
    { multiresult not
      t empty$ not
      and
    }
    { t #1 #1 substring$
      duplicate$ "-" =
      swap$ duplicate$ "," =
      swap$ "+" =
      or or
        { #1 'multiresult := }
        { t #2 global.max$ substring$ 't := }
      if$
    }
  while$
  multiresult
}

FUNCTION {format.pages}
{ pages duplicate$ empty$
    'skip$
    { duplicate$ multi.page.check
        {
          bbl.pages swap$
          n.dashify
        }{
          bbl.page swap$
        }
      if$
      tie.or.space.prefix
      "pages" bibinfo.check
      * *
    }
  if$
}

FUNCTION {first.page}
{ 't :=
  ""
    {  t empty$ not t #1 #1 substring$ "-" = not and }
    { t #1 #1 substring$ *
      t #2 global.max$ substring$ 't :=
    }
  while$
}

FUNCTION {format.book.pages}
{
  pages duplicate$ empty$ 'skip$
    {
      "pages" bibinfo.check word.space bbl.pages * *
    }
  if$
}

FUNCTION {volnum.punct}
{
          ","
  word.space *
}

FUNCTION {format.journal.pages}
{ pages duplicate$ empty$ 'pop$
    { swap$ duplicate$ empty$
        { pop$ pop$ format.pages }
        { volnum.punct *
          swap$
          control.pages duplicate$ #0 < {
            pop$ pop$
          }{
            #0 >
            {
              n.dashify
            }{
              first.page
            } if$
          } if$
          "pages" bibinfo.check
          *
        }
      if$
    }
  if$
}

FUNCTION {format.journal.eid}
{ eid "eid" bibinfo.check
  duplicate$ empty$ 'pop$
    { swap$ duplicate$ empty$ 'skip$
        { volnum.punct * }
      if$
      swap$ *
    }
  if$
}

FUNCTION {eid.or.pages}
{
  eid empty$
    { format.journal.pages }
    { format.journal.eid }
  if$
}

FUNCTION {format.ser.vol.num}
{
  series "series" bibinfo.check output
  volume field.or.null
  duplicate$ empty$ 'skip$
    {
      "volume" bibinfo.check
    }
  if$
  bolden
}

FUNCTION {format.vol.num}
{
  volume field.or.null
  duplicate$ empty$ 'skip$
    {
      "volume" bibinfo.check
    }
  if$
  bolden
}

FUNCTION {format.chapter.pages}
{ chapter empty$
    {
    format.pages
    }
    { type empty$
        {
          bbl.chapter
          capitalize
        }{
          type
          capitalize
          "type" bibinfo.check
        }
      if$
      chapter tie.or.space.prefix
      "chapter" bibinfo.check
      * *
      pages empty$
        'skip$
        { ", " * format.pages * }
      if$
    }
  if$
}

FUNCTION {format.booktitle}
{
  booktitle duplicate$ "booktitle" bibinfo.check swap$
  duplicate$ empty$ 'pop$
    {
      punctuation.yes 'punctuation.state :=
      pop$ emphasize
      select.language
    }
  if$
}

FUNCTION {format.editor.in}
{
  editor "editor" format.names.ed duplicate$ empty$ 'skip$
    {
      bbl.edby
      word.space * swap$ *
    }
  if$
}

FUNCTION {output.article.booktitle}
{
  format.booktitle
      "booktitle" 'bibfield :=
  output
  bookaddress "address" bibinfo.check duplicate$ empty$ 'pop$
    {
      "address" 'bibfield :=
      output.nonnull after.punctuation 'output.state :=
    }
  if$
}

FUNCTION {format.in.ed.booktitle}
{
  format.booktitle duplicate$ empty$ 'pop$
    {
      add.doi
      word.in swap$ * output.nonnull
      bookaddress "address" bibinfo.check output
      format.number.series "series and number" bibinfo.check output
      format.bvolume output
      format.editor.in "editor" bibinfo.check output
    }
  if$
}

FUNCTION {format.in.ed.booktitle.inbook}
{
  format.booktitle duplicate$ empty$ 'pop$
    {
      add.doi
      word.in swap$ * output.nonnull
      bookaddress "address" bibinfo.check output
      format.number.series "series and number" bibinfo.check output
      format.bvolume output
      author empty$ 'skip$
        { format.editor.in "editor" bibinfo.check output }
    if$
    }
  if$
}

FUNCTION {format.thesis.type}
{ type duplicate$ empty$
    'pop$
    { swap$ pop$
      "t" change.case$ "type" bibinfo.check
    }
  if$
}

FUNCTION {format.tr.number}
{ number "number" bibinfo.check
  type duplicate$ empty$
    { pop$ bbl.techrep }
    'skip$
  if$
  "type" bibinfo.check
  swap$ duplicate$ empty$
    { pop$ "t" change.case$ }
    { tie.or.space.prefix * * }
  if$
}

FUNCTION {format.article.crossref}
{
  word.in
  " \cite{" * crossref * "}" *
}

FUNCTION {format.book.crossref}
{ volume duplicate$ empty$
    { "empty volume in " cite$ * "'s crossref of " * crossref * warning$
      pop$ word.in
    }
    { bbl.volume
      swap$ tie.or.space.prefix "volume" bibinfo.check * * bbl.of space.word *
    }
  if$
  " \cite{" * crossref * "}" *
}

FUNCTION {format.incoll.inproc.crossref}
{
  word.in
  " \cite{" * crossref * "}" *
}

FUNCTION {format.org.or.pub}
{ 't :=
  ""
  address "address" bibinfo.check
  duplicate$ empty$
    { pop$ t }
    { t duplicate$ empty$
        { pop$ }
        {
          "," word.space *
          * swap$ *
        }
      if$
    }
  if$
  *
  year duplicate$ empty$
    {
      "empty year in " cite$ *
      warning$
      pop$ ""
    }
    'skip$
  if$
  duplicate$ empty$
    { pop$ }
    {
      "year" bibinfo.check
      swap$
      duplicate$ empty$
        { pop$ }
        {
          "," *
          word.space *
          swap$ *
        }
      if$
    }
  if$
  duplicate$ empty$ 'skip$ {
    ""
    "(" * swap$ * ")" *
    after.punctuation 'output.state :=
    punctuation.space 'punctuation.state :=
  } if$
}

FUNCTION {format.publisher.address}
{ publisher "publisher" bibinfo.warn format.org.or.pub
}

FUNCTION {format.organization.address}
{ organization "organization" bibinfo.check format.org.or.pub
}

FUNCTION {format.organization.publisher.address}
{
  publisher empty$
    { format.organization.address }
    { organization "organization" bibinfo.check output
      format.publisher.address
    }
  if$
}

FUNCTION {format.school.address.output}
{
  school  "school"  bibinfo.warn
  address "address" bibinfo.check
  duplicate$ empty$ 'skip$
    {
      swap$
      duplicate$ empty$ 'skip$
        {
          ", " *
        }
      if$
      swap$
    }
  if$
  *
  output
}

FUNCTION {article.title.produce}
{
  control.title duplicate$ #0 <
  { pop$
  }{
    format.title
    "title" 'bibfield :=
    swap$ #0 >
    {
      "title" output.check
    }{
      output
    } if$
    new.block.comma
  } if$
}

FUNCTION {control}
{
}

FUNCTION {article}
{ output.bibitem
  format.authors
  booktitle empty$ {
      "author" output.check
    }{ output } if$
  author format.key output
  new.block.comma
  article.title.produce
  output.article.booktitle
  crossref missing$
%% Handle special case of SISSA journals which require an issue number for unique citations and use volume/year interchangably
    {
      journal "J. High Energy Phys." =
      journal "J. High Energ. Phys." = or
      journal "JHEP" = or
      journal "Journal of Cosmology and Astroparticle Physics" = or
      journal "J. Cosmol. Astropart. Phys." = or
      journal "JCAP" = or
      journal "Journal of Instrumentation" = or
      journal "J. Instrum." = or
      journal "JINST" = or
      journal "Journal of Statistical Mechanics" = or
      journal "J. of Stat. Mech." = or
      journal "J. Stat. Mech.: Theory Exp." = or
      journal "JSTAT" = or
      {
          link.open
          journal
          "journal" bibinfo.warn
          "journal" 'bibfield :=
          output
          add.blank
          format.vol.num output
          number parenthesize
          "number" bibinfo.warn
          "number" 'bibfield :=
          output
          eid.or.pages
          link.shut
      }
      {
          eid missing$
          pages missing$ and
          doi missing$ not and
          {
            journal
            "journal" bibinfo.warn
            "journal" 'bibfield :=
            output
            add.blank
            format.ser.vol.num
            output
            link.open
            doi output
            link.shut
            format.date.output.check
          }
          {
            link.open
            journal
            "journal" bibinfo.warn
            "journal" 'bibfield :=
            output
            add.blank
            format.ser.vol.num
            output
            eid.or.pages
            format.date.output.check
            link.shut
          } if$
      } if$
    }{
      format.article.crossref output.nonnull
      format.pages output
    }
  if$
  new.block.comma
  new.sentence.comma
  format.note output
  format.eprint.controlled output
  format.translation output
  fin.entry
}

FUNCTION {book}
{ output.bibitem
  author empty$
    {
      format.editors "author and editor" output.check
      editor format.key output
    }{
      format.authors output.nonnull
%%     crossref missing$ { editor.check.book } 'skip$ if$
    }
  if$
  new.block.comma
  link.open
  format.btitle
  "title" output.check
  link.shut
  format.edition output
  author empty$
    {
    }
    {
      format.editor.in output
      editor format.key output
    }
  if$
  format.number.series output
  crossref missing$
    {
      format.bvolume output
      new.block.comma
      format.publisher.address output
    }{
      new.block.comma
      format.book.crossref output.nonnull
      format.date.output.check
    }
  if$
  format.isbn.output
  format.chapter.pages
  output
  new.block.comma
  new.sentence.comma
  format.note output
  format.eprint output
  fin.entry
}

FUNCTION {booklet}
{ output.bibitem
  format.authors output
  author format.key output
  new.block.comma
  link.open
  format.title
  "title" output.check
  link.shut
  new.block.comma
  howpublished "howpublished" bibinfo.check output
  address "address" bibinfo.check output
  format.date.output
  format.isbn.output
  format.book.pages output
  new.block.comma
  new.sentence.comma
  format.note output
  format.eprint output
  fin.entry
}

FUNCTION {footnote}
{ output.bibitem
  format.note output
  fin.entry
}

FUNCTION {inbook}
{ output.bibitem
  author empty$
    {
       format.editors "editor" output.check
       editor format.key output
    }{
       format.authors output.nonnull
       author format.key output
    }
  if$
  new.block.comma

  format.title output
  new.block.comma

  crossref missing$
    {
      format.in.ed.booktitle.inbook
      format.publisher.address output
      format.chapter.pages
      "chapter and pages"
      output.check
      new.block.comma
      format.edition output
      new.block.comma
    }{
      format.chapter.pages
      "chapter and pages"
      output.check
      new.block.comma
      format.book.crossref output.nonnull
      format.date.output.check
    }
  if$
  crossref missing$
    { format.isbn.output }
    'skip$
  if$
  new.block.comma
  new.sentence.comma
  format.note output
  format.eprint output
  fin.entry
}

FUNCTION {incollection}
{ output.bibitem
  format.authors "author" output.check
  author format.key output
  new.block.comma
  article.title.produce
  crossref missing$
    {
      format.in.ed.booktitle
      format.publisher.address output
      format.edition output
      format.chapter.pages output
      format.isbn.output
    }{
      format.incoll.inproc.crossref output.nonnull
      format.chapter.pages output
    }
  if$
  new.block.comma
  new.sentence.comma
  format.note output
  format.eprint output
  fin.entry
}

FUNCTION {inproceedings}
{ output.bibitem
  format.authors "author" output.check
  author format.key output
  new.block.comma
  article.title.produce
  crossref missing$
    {
      format.in.ed.booktitle
      format.organization.publisher.address output
      format.chapter.pages output
      format.isbn.output
      format.issn.output
    }{
      format.incoll.inproc.crossref output.nonnull
      format.chapter.pages output
    }
  if$
  new.block.comma
  new.sentence.comma
  format.note output
  format.eprint output
  fin.entry
}

FUNCTION {conference} { inproceedings }
FUNCTION {manual}
{ output.bibitem
  format.authors output
  author format.key output
  new.block.comma
  link.open
  format.btitle
  "title" output.check
  link.shut
      organization "organization" bibinfo.check output
      address "address" bibinfo.check output
  format.edition output
  format.date.output
  new.block.comma
  new.sentence.comma
  format.note output
  format.eprint output
  fin.entry
}

FUNCTION {mastersthesis}
{ output.bibitem
  format.authors "author" output.check
  author format.key output
  new.block.comma
  format.btitle
  output
  new.block.comma
  link.open
  bbl.mthesis
  format.thesis.type
  output.nonnull
  link.shut
  format.school.address.output
  format.date.output.check
  new.block.comma
  new.sentence.comma
  format.note output
  format.eprint output
  fin.entry
}

FUNCTION {misc}
{ output.bibitem
  format.authors output
  author format.key output
  new.block.comma
  link.open
  format.title
  output
  link.shut
  new.block.comma
  howpublished "howpublished" bibinfo.check output
  format.date.output
  new.block.comma
  new.sentence.comma
  format.note output
  format.eprint output
  fin.entry
}

FUNCTION {phdthesis}
{ output.bibitem
  format.authors "author" output.check
  author format.key output
  new.block.comma
  format.btitle
  output
  new.block.comma
  link.open
  bbl.phdthesis
  format.thesis.type
  output.nonnull
  link.shut
  format.school.address.output
  format.date.output.check
  new.block.comma
  new.sentence.comma
  format.note output
  format.eprint output
  fin.entry
}

FUNCTION {presentation}
{ output.bibitem
  format.authors output
  author format.key output
  new.block.comma
  link.open
  format.title
  output
  link.shut
  new.block.comma
  format.organization.address "organization and address" output.check
  month "month" output.check
  year "year" output.check
  new.block.comma
  new.sentence.comma
  format.note output
  new.sentence
  type missing$ 'skip$
    {"(" type capitalize * ")" * output}
  if$
  fin.entry
}

FUNCTION {proceedings}
{ output.bibitem
  format.editors output
  editor format.key output
  new.block.comma
  link.open
  format.btitle
  "title" output.check
  link.shut
      bookaddress "address" bibinfo.check output
  format.number.series output
  format.bvolume output
  format.organization.publisher.address output
  format.isbn.output
  format.issn.output
  new.block.comma
  new.sentence.comma
  format.note output
  format.eprint output
  fin.entry
}

FUNCTION {techreport}
{ output.bibitem
  format.authors "author" output.check
  author format.key output
  new.block.comma
  link.open
  format.btitle
  "title" output.check
  link.shut
  new.block.comma
  format.tr.number
  output.nonnull
  institution "institution" bibinfo.warn
  format.org.or.pub output
  new.block.comma
  new.sentence.comma
  format.note output
  format.eprint output
  fin.entry
}

FUNCTION {unpublished}
{ output.bibitem
  format.authors "author" output.check
  author format.key output
  article.title.produce
  format.eprint output
  format.date.output
  new.block.comma
  new.sentence.comma
  format.note "note" output.check
  fin.entry
}

FUNCTION {dataset}
{
  output.bibitem
  format.authors "author" output.check
  author format.key output
  article.title.produce
  doi missing$
  url missing$ not and
  {
    link.open
    url "url" output.check
    link.shut
  }
  {
    link.open
    doi "doi" output.check
    link.shut
  } if$
  format.date.output
  format.note output
  fin.entry
}

FUNCTION {default.type} { misc }

READ

EXECUTE {control.init}

ITERATE {control.pass}

EXECUTE {control.check}

FUNCTION {sortify}
{ purify$
  "l" change.case$
}

INTEGERS { len }

FUNCTION {chop.word}
{ 's :=
  'len :=
  s #1 len substring$ =
    { s len #1 + global.max$ substring$ }
    's
  if$
}

FUNCTION {cite.name.font.apply}
{
  word.space * bbl.etal
  emphasize
  *
}

FUNCTION {format.lab.names}
{ 's :=
  "" 't :=
  #1 'nameptr :=
  format.name.apply
  s num.names$ duplicate$
  #2 >
    { pop$
      cite.name.font.apply
    }{
      #2 <
        'skip$
        {
          s #2 "{ff }{vv }{ll}{ jj}" format.name$ "others" =
            {
              cite.name.font.apply
            }{
              bbl.and space.word *
              s #2 "{vv~}{ll}" format.name$
              cite.name.font
              *
            }
          if$
        }
      if$
    }
  if$
}

FUNCTION {author.key.label}
{ author empty$
    { key empty$
        { cite$ #1 #3 substring$ }
        'key
      if$
    }
    { author format.lab.names }
  if$
}

FUNCTION {author.editor.key.label}
{ author empty$
    { editor empty$
        { key empty$
            { cite$ #1 #3 substring$ }
            'key
          if$
        }
        { editor format.lab.names }
      if$
    }
    { author format.lab.names }
  if$
}

FUNCTION {editor.key.label}
{ editor empty$
    { key empty$
        { cite$ #1 #3 substring$ }
        'key
      if$
    }
    { editor format.lab.names }
  if$
}

FUNCTION {calc.short.authors}
{ type$ "book" =
  type$ "inbook" =
  or
    'author.editor.key.label
    { type$ "proceedings" =
        'editor.key.label
        'author.key.label
      if$
    }
  if$
  'short.list :=
}

FUNCTION {calc.label}
{
  calc.short.authors
  short.list
  year duplicate$ empty$
  short.list key field.or.null = or
    {
      pop$ ""
    }{
      control.year #0 > { purify$ #-1 #4 substring$ } 'skip$ if$
    }
  if$
  "(" swap$ *
  * 'label :=
}

FUNCTION {sort.format.names}
{ 's :=
  #1 'nameptr :=
  ""
  s num.names$ 'numnames :=
  numnames 'namesleft :=
    { namesleft #0 > }
    { s nameptr
      "{ll{ }}"
      control.author.initials {
        "{  f{ }}"  *
      }{
        "{  ff{ }}" *
      } if$
      "{  jj{ }}" *
      format.name$ 't :=
      nameptr #1 >
        {
          "   "  *
          namesleft #1 = t "others" = and
            { "zzzzz" * }
            {
              t sortify *
            }
          if$
        }
        { t sortify * }
      if$
      nameptr #1 + 'nameptr :=
      namesleft #1 - 'namesleft :=
    }
  while$
}

FUNCTION {sort.format.title}
{ 't :=
  "A " #2
    "An " #3
      "The " #4 t chop.word
    chop.word
  chop.word
  sortify
  #1 global.max$ substring$
}

FUNCTION {author.sort}
{ author empty$
    { key empty$
        { "to sort, need author or key in " cite$ * warning$
          ""
        }
        { key sortify }
      if$
    }
    { author sort.format.names }
  if$
}

FUNCTION {author.editor.sort}
{ author empty$
    { editor empty$
        { key empty$
            { "to sort, need author, editor, or key in " cite$ * warning$
              ""
            }
            { key sortify }
          if$
        }
        { editor sort.format.names }
      if$
    }
    { author sort.format.names }
  if$
}

FUNCTION {year.sort.key}
{
  year
}

FUNCTION {editor.sort}
{ editor empty$
    { key empty$
        { "to sort, need editor or key in " cite$ * warning$
          ""
        }
        { key sortify }
      if$
    }
    { editor sort.format.names }
  if$
}

INTEGERS { seq.num }

FUNCTION {init.seq}
{ #0 'seq.num :=}

EXECUTE {init.seq}

FUNCTION {int.to.fix}
{ "000000000" swap$ int.to.str$ *
  #-1 #10 substring$
}

FUNCTION {label.presort}
{
  calc.label
  label sortify
  "    "
  *
  seq.num #1 + 'seq.num :=
  seq.num  int.to.fix
  'sort.label :=
  sort.label
  *
  "    "
  *
  title field.or.null sort.format.title
  *
  #1 entry.max$ substring$
  'sort.key$ :=
}

FUNCTION {presort.pass}
{ type$ "control" = 'control.presort 'label.presort if$
}

ITERATE {presort.pass}

SORT

STRINGS { last.label next.extra }

INTEGERS { last.extra.num number.label }

FUNCTION {initialize.extra.label.stuff}
{ #0 int.to.chr$ 'last.label :=
  "" 'next.extra :=
  #0 'last.extra.num :=
  #0 'number.label :=
}

FUNCTION {label.forward}
{
  last.label label =
    { last.extra.num #1 + 'last.extra.num :=
      last.extra.num int.to.chr$ 'extra.label :=
    }
    { "a" chr.to.int$ 'last.extra.num :=
      "" 'extra.label :=
      label 'last.label :=
    }
  if$
  number.label #1 + 'number.label :=
}

FUNCTION {label.reverse}
{ next.extra "b" =
    { "a" 'extra.label := }
    'skip$
  if$
  extra.label 'next.extra :=
  extra.label
  duplicate$ empty$
    'skip$
    { "{\natexlab{" swap$ * "}}" * }
  if$
  'extra.label :=
  label extra.label * 'label :=
}

EXECUTE {initialize.extra.label.stuff}

FUNCTION {forward.pass}
{ type$ "control" = 'control.forward 'label.forward if$
}

ITERATE {forward.pass}

FUNCTION {reverse.pass}
{ type$ "control" = 'control.reverse 'label.reverse if$
}

REVERSE {reverse.pass}

FUNCTION {sortkey.sort}
{ sort.label
  "    "
  *
  year.sort.key
  field.or.null sortify
  *
  "    "
  *
  title field.or.null sort.format.title
  *
  #1 entry.max$ substring$
  'sort.key$ :=
}

FUNCTION {bib.sort.pass}
{ type$ "control" = 'control.sort 'sortkey.sort if$
}

ITERATE {bib.sort.pass}

SORT

FUNCTION {init.bib.eprint}
{
    "\texttt {"
  pop$
  "\providecommand \url  [0]{\begingroup\@sanitize@url \@url }%" write$ newline$
  "\providecommand \@url [1]{\endgroup\@href {#1}{" "}}%" bbl.url.prefix swap$ * * write$ newline$
  "\providecommand " " [0]{URL }%" bbl.url.prefix swap$ * * write$ newline$
  eprint.command "\providecommand " swap$ * "[0]{\href }%" * write$ newline$
}

FUNCTION {init.bib.doi}
{
  "\providecommand \doibase [0]{" doi.base "}%" * * write$ newline$
}

FUNCTION {init.bib.hypertex}
{
  "\providecommand " noop.command "[0]{\@secondoftwo}%" * * write$ newline$
  "\providecommand " href.command "[0]{\begingroup \@sanitize@url \@href}%" * * write$ newline$
  "\providecommand \@href[1]{\@@startlink{#1}\@@href}%" write$ newline$
  "\providecommand \@@href[1]{\endgroup#1\@@endlink}%" write$ newline$
  "\providecommand \@sanitize@url [0]{\catcode `\\12\catcode `\$12\catcode `\&12\catcode `\#12\catcode `\^12\catcode `\_12\catcode `\%12\relax}%" write$ newline$
  "\providecommand \@@startlink[1]{}%" write$ newline$
  "\providecommand \@@endlink[0]{}%"   write$ newline$
}

FUNCTION {init.bib.namefont}
{
  "\providecommand \bibnamefont  [1]{#1}%"  write$ newline$
  "\providecommand \bibfnamefont [1]{#1}%" write$ newline$
  "\providecommand \citenamefont [1]{#1}%"  write$ newline$
}

FUNCTION {init.bib.quote}
{
  "\providecommand " bbl.enquote * " [1]{" *
  "``" "''"
  "#1" swap$ "}%" * * * * write$ newline$
}

FUNCTION {init.bib.ay}
{
  "\providecommand \natexlab [1]{#1}%"
  write$ newline$
}

FUNCTION {init.bib.bibinfo}
{
  bibinfo.command  "\providecommand " swap$ * " [0]{\@secondoftwo}%" * write$ newline$
  bibfield.command "\providecommand " swap$ * " [0]{\@secondoftwo}%" * write$ newline$
}

FUNCTION {init.bib.lang}
{
  "\providecommand \selectlanguage [0]{\@gobble}%" write$ newline$
}

FUNCTION {init.bib.endbibitem}
{
  "\providecommand " bbl.open * "[0]{}%" *           write$ newline$
  "\providecommand \bibitemStop [0]{}%"               write$ newline$
  "\providecommand \bibitemNoStop [0]{.\EOS\space}%"  write$ newline$
  "\providecommand \EOS [0]{\spacefactor3000\relax}%"   write$ newline$
}

FUNCTION {init.bib.translation}
{
  "\providecommand \translation [1]{[#1]}%" write$ newline$
}

FUNCTION {warn.bib}
{
}

FUNCTION {init.bib}
{
  warn.bib
  "\makeatletter" write$ newline$
  "\providecommand \@ifxundefined [1]{%"   write$ newline$
  " \@ifx{#1\undefined}"                   write$ newline$
  "}%"                                     write$ newline$
  "\providecommand \@ifnum [1]{%"          write$ newline$
  " \ifnum #1\expandafter \@firstoftwo"    write$ newline$
  " \else \expandafter \@secondoftwo"      write$ newline$
  " \fi"                                   write$ newline$
  "}%"                                     write$ newline$
  "\providecommand \@ifx [1]{%"            write$ newline$
  " \ifx #1\expandafter \@firstoftwo"      write$ newline$
  " \else \expandafter \@secondoftwo"      write$ newline$
  " \fi"                                   write$ newline$
  "}%"                                     write$ newline$
  init.bib.ay
  init.bib.quote
  init.bib.namefont
  init.bib.hypertex
  init.bib.eprint
  init.bib.doi
  init.bib.lang
  init.bib.bibinfo
  init.bib.translation
  init.bib.endbibitem
  "\providecommand " bbl.shut * " [1]{\csname bibitem#1\endcsname}%" * write$ newline$
  "\let\auto@bib@innerbib\@empty"          write$ newline$
  "%</preamble>" write$
}

FUNCTION {begin.bib}
{
  id.bst diagn.cmntlog
  control.bib
  preamble$ empty$
    'skip$
    { preamble$ write$ newline$ }
  if$
  "\begin{thebibliography}{"
  number.label int.to.str$
  * "}%" *
  write$ newline$
  init.bib
}

EXECUTE {begin.bib}

EXECUTE {init.state.consts}

ITERATE {call.type$}

FUNCTION {end.bib}
{ newline$
  "\end{thebibliography}%"
  write$ newline$
}

EXECUTE {end.bib}

%% End of customized bst file
%%
%% End of file `apsrev4-2.bst'.
