%!PS-Adobe-3.0 EPSF-3.0
%%Title: temp_vs_afd_001.eps
%%Creator: Mat<PERSON>lotlib v3.7.1, https://matplotlib.org/
%%CreationDate: Wed Apr 24 13:21:39 2024
%%Orientation: portrait
%%BoundingBox: 23 178 589 614
%%HiResBoundingBox: 23.553125 178.916875 588.446875 613.083125
%%EndComments
%%BeginProlog
/mpldict 11 dict def
mpldict begin
/_d { bind def } bind def
/m { moveto } _d
/l { lineto } _d
/r { rlineto } _d
/c { curveto } _d
/cl { closepath } _d
/ce { closepath eofill } _d
/box {
      m
      1 index 0 r
      0 exch r
      neg 0 r
      cl
    } _d
/clipbox {
      box
      clip
      newpath
    } _d
/sc { setcachedevice } _d
%!PS-TrueTypeFont-1.0-2.53740
%%Title: unknown
%%Creator: Converted from TrueType to type 42 by PPR
15 dict begin
/FontName /TimesNewRomanPSMT def
/PaintType 0 def
/FontMatrix[1 0 0 1 0 0]def
/FontBBox[-568 -307 2028 1007]def
/FontType 42 def
/Encoding StandardEncoding def
/FontInfo 10 dict dup begin
/FamilyName (unknown) def
/FullName (unknown) def
/Weight (unknown) def
/Version (unknown) def
/ItalicAngle 0.0 def
/isFixedPitch false def
/UnderlinePosition -223 def
/UnderlineThickness 100 def
end readonly def
/sfnts[<00010000000900800003001063767420F34DDA810000009C000007C46670676D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>]def
/CharStrings 30 dict dup begin
/.notdef 0 def
/minus 30 def
/space 3 def
/parenleft 4 def
/parenright 5 def
/comma 6 def
/period 7 def
/slash 8 def
/zero 9 def
/two 10 def
/four 11 def
/six 12 def
/less 13 def
/greater 14 def
/E 15 def
/F 16 def
/omega 31 def
/M 17 def
/V 18 def
/c 19 def
/d 20 def
/e 21 def
/i 22 def
/l 23 def
/m 24 def
/r 25 def
/t 26 def
/x 27 def
/y 28 def
/z 29 def
end readonly def

systemdict/resourcestatus known
 {42 /FontType resourcestatus
   {pop pop false}{true}ifelse}
 {true}ifelse
{/TrueDict where{pop}{(%%[ Error: no TrueType rasterizer ]%%)= flush}ifelse
/FontType 3 def
 /TrueState 271 string def
 TrueDict begin sfnts save
 72 0 matrix defaultmatrix dtransform dup
 mul exch dup mul add sqrt cvi 0 72 matrix
 defaultmatrix dtransform dup mul exch dup
 mul add sqrt cvi 3 -1 roll restore
 TrueState initer end
 /BuildGlyph{exch begin
  CharStrings dup 2 index known
    {exch}{exch pop /.notdef}ifelse
  get dup xcheck
    {currentdict systemdict begin begin exec end end}
    {TrueDict begin /bander load cvlit exch TrueState render end}
    ifelse
 end}bind def
 /BuildChar{
  1 index /Encoding get exch get
  1 index /BuildGlyph get exec
 }bind def
}if

FontName currentdict end definefont pop
end
%%EndProlog
mpldict begin
23.553 178.917 translate
564.894 434.166 0 0 clipbox
gsave
0 -0 m
564.89375 -0 l
564.89375 434.16625 l
0 434.16625 l
cl
1.000 setgray
fill
grestore
gsave
105.04375 66.60625 m
551.44375 66.60625 l
551.44375 426.96625 l
105.04375 426.96625 l
cl
1.000 setgray
fill
grestore
2.500 setlinewidth
1 setlinejoin
0 setlinecap
[] 0 setdash
0.000 setgray
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
105.044 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
105.044 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

91.7469 39.2469 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /four glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
216.644 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
216.644 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

203.347 39.2469 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /two glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
328.244 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
328.244 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

321.994 39.2469 translate
0 rotate
0 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
439.844 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
439.844 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

433.594 39.2469 translate
0 rotate
0 0 m /two glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
551.444 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
551.444 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

545.194 39.2469 translate
0 rotate
0 0 m /four glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
132.944 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
132.944 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
160.844 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
160.844 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
188.744 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
188.744 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
244.544 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
244.544 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
272.444 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
272.444 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
300.344 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
300.344 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
356.144 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
356.144 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
384.044 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
384.044 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
411.944 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
411.944 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
467.744 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
467.744 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
495.644 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
495.644 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
523.544 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
523.544 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

210.197 12.5438 translate
0 rotate
0 0 m /E glyphshow
15.271 0 m /l glyphshow
22.2168 0 m /e glyphshow
33.313 0 m /c glyphshow
44.4092 0 m /t glyphshow
51.355 0 m /r glyphshow
59.6802 0 m /i glyphshow
66.626 0 m /c glyphshow
77.7222 0 m /space glyphshow
83.9722 0 m /F glyphshow
97.876 0 m /i glyphshow
104.822 0 m /e glyphshow
115.918 0 m /l glyphshow
122.864 0 m /d glyphshow
135.364 0 m /space glyphshow
141.614 0 m /parenleft glyphshow
149.939 0 m /M glyphshow
172.168 0 m /V glyphshow
190.222 0 m /slash glyphshow
197.168 0 m /c glyphshow
208.264 0 m /m glyphshow
227.71 0 m /parenright glyphshow
grestore
2.500 setlinewidth
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
105.044 81.0365 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
551.444 81.0365 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

37.2 72.3568 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /zero glyphshow
26.5991 0 m /period glyphshow
32.8491 0 m /zero glyphshow
45.3491 0 m /two glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
105.044 161.147 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
551.444 161.147 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

51.2938 152.467 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
105.044 241.258 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
551.444 241.258 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

51.2938 232.578 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /two glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
105.044 321.368 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
551.444 321.368 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

51.2938 312.689 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /four glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
105.044 401.479 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
551.444 401.479 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

51.2938 392.799 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /six glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
105.044 101.064 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
551.444 101.064 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
105.044 121.092 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
551.444 121.092 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
105.044 141.119 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
551.444 141.119 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
105.044 181.175 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
551.444 181.175 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
105.044 201.202 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
551.444 201.202 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
105.044 221.23 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
551.444 221.23 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
105.044 261.285 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
551.444 261.285 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
105.044 281.313 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
551.444 281.313 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
105.044 301.341 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
551.444 301.341 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
105.044 341.396 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
551.444 341.396 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
105.044 361.424 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
551.444 361.424 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
105.044 381.451 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
551.444 381.451 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
105.044 421.507 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
551.444 421.507 o
grestore
gsave
22.2 203.786 translate
90 rotate
/TimesNewRomanPSMT 25.0 selectfont
0 0.6875 moveto
/less glyphshow
14.0991 0.6875 moveto
/omega glyphshow
/TimesNewRomanPSMT 17.5 selectfont
30.9299 -5.75313 moveto
/x glyphshow
39.6799 -5.75313 moveto
/comma glyphshow
46.7009 -5.75313 moveto
/y glyphshow
55.4509 -5.75313 moveto
/comma glyphshow
62.4719 -5.75313 moveto
/z glyphshow
/TimesNewRomanPSMT 25.0 selectfont
71.3127 0.6875 moveto
/greater glyphshow
grestore
0.500 setlinewidth
[1.85 0.8] 0 setdash
0.031 0.235 0.235 setrgbcolor
gsave
446.4 360.36 105.044 66.606 clipbox
540.28375 375.098937 m
529.12375 375.795859 l
517.96375 376.52134 l
506.80375 377.256435 l
495.64375 377.878694 l
484.48375 378.500553 l
473.32375 379.225033 l
462.16375 379.987766 l
451.00375 380.789513 l
439.84375 381.490361 l
428.68375 382.200661 l
417.52375 382.924821 l
406.36375 383.607604 l
395.20375 384.250051 l
384.04375 385.029687 l
372.88375 385.610088 l
361.72375 386.500598 l
350.56375 387.090332 l
339.40375 387.874214 l
328.24375 388.634784 l
317.08375 389.5154 l
305.92375 390.337815 l
294.76375 391.040946 l
283.60375 391.796269 l
272.44375 392.740052 l
261.28375 393.504427 l
250.12375 394.294838 l
238.96375 395.243107 l
227.80375 396.151642 l
216.64375 397.098469 l
205.48375 398.049662 l
194.32375 399.266943 l
183.16375 400.256068 l
172.00375 401.332434 l
160.84375 402.601266 l
149.68375 404.052149 l
138.52375 405.706793 l
127.36375 407.649035 l
116.20375 410.51391 l
105.04375 374.270193 l
116.20375 375.098696 l
127.36375 375.640725 l
138.52375 376.496266 l
149.68375 377.266089 l
160.84375 377.920752 l
172.00375 378.519539 l
183.16375 379.345399 l
194.32375 380.04108 l
205.48375 380.704796 l
216.64375 381.408247 l
227.80375 382.177629 l
238.96375 382.856086 l
250.12375 383.518801 l
261.28375 384.385117 l
272.44375 385.087847 l
283.60375 385.655231 l
294.76375 386.529157 l
305.92375 387.332106 l
317.08375 388.049616 l
328.24375 388.613635 l
339.40375 389.525093 l
350.56375 390.394013 l
361.72375 391.06566 l
372.88375 391.911468 l
384.04375 392.655135 l
395.20375 393.486803 l
406.36375 394.188411 l
417.52375 395.250237 l
428.68375 396.148357 l
439.84375 397.119778 l
451.00375 397.958456 l
462.16375 399.233497 l
473.32375 400.276416 l
484.48375 401.399607 l
495.64375 402.700723 l
506.80375 404.196388 l
517.96375 405.670303 l
529.12375 407.749333 l
540.28375 410.530333 l
551.44375 374.327712 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
446.4 360.36 105.044 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

0 2 m
-2 -2 l
2 -2 l
cl

gsave
0.031 0.235 0.235 setrgbcolor
fill
grestore
stroke
grestore
} bind def
540.284 375.099 o
529.124 375.796 o
517.964 376.521 o
506.804 377.256 o
495.644 377.879 o
484.484 378.501 o
473.324 379.225 o
462.164 379.988 o
451.004 380.79 o
439.844 381.49 o
428.684 382.201 o
417.524 382.925 o
406.364 383.608 o
395.204 384.25 o
384.044 385.03 o
372.884 385.61 o
361.724 386.501 o
350.564 387.09 o
339.404 387.874 o
328.244 388.635 o
317.084 389.515 o
305.924 390.338 o
294.764 391.041 o
283.604 391.796 o
272.444 392.74 o
261.284 393.504 o
250.124 394.295 o
238.964 395.243 o
227.804 396.152 o
216.644 397.098 o
205.484 398.05 o
194.324 399.267 o
183.164 400.256 o
172.004 401.332 o
160.844 402.601 o
149.684 404.052 o
138.524 405.707 o
127.364 407.649 o
116.204 410.514 o
105.044 374.27 o
116.204 375.099 o
127.364 375.641 o
138.524 376.496 o
149.684 377.266 o
160.844 377.921 o
172.004 378.52 o
183.164 379.345 o
194.324 380.041 o
205.484 380.705 o
216.644 381.408 o
227.804 382.178 o
238.964 382.856 o
250.124 383.519 o
261.284 384.385 o
272.444 385.088 o
283.604 385.655 o
294.764 386.529 o
305.924 387.332 o
317.084 388.05 o
328.244 388.614 o
339.404 389.525 o
350.564 390.394 o
361.724 391.066 o
372.884 391.911 o
384.044 392.655 o
395.204 393.487 o
406.364 394.188 o
417.524 395.25 o
428.684 396.148 o
439.844 397.12 o
451.004 397.958 o
462.164 399.233 o
473.324 400.276 o
484.484 401.4 o
495.644 402.701 o
506.804 404.196 o
517.964 405.67 o
529.124 407.749 o
540.284 410.53 o
551.444 374.328 o
grestore
0.500 setlinewidth
1 setlinejoin
[1.85 0.8] 0 setdash
0.031 0.416 0.416 setrgbcolor
gsave
446.4 360.36 105.044 66.606 clipbox
540.28375 375.139673 m
529.12375 375.867157 l
517.96375 376.462299 l
506.80375 377.121689 l
495.64375 378.146464 l
484.48375 378.618756 l
473.32375 379.375681 l
462.16375 380.041961 l
451.00375 380.744891 l
439.84375 381.348044 l
428.68375 382.053218 l
417.52375 382.813467 l
406.36375 383.561019 l
395.20375 384.305888 l
384.04375 384.950097 l
372.88375 385.767025 l
361.72375 386.483414 l
350.56375 387.332506 l
339.40375 388.114426 l
328.24375 388.760157 l
317.08375 389.309796 l
305.92375 390.29035 l
294.76375 390.95711 l
283.60375 391.832038 l
272.44375 392.602262 l
261.28375 393.569637 l
250.12375 394.467757 l
238.96375 395.379816 l
227.80375 396.071852 l
216.64375 397.138484 l
205.48375 398.054829 l
194.32375 399.256048 l
183.16375 400.299168 l
172.00375 401.35803 l
160.84375 402.726439 l
149.68375 403.997233 l
138.52375 405.882756 l
127.36375 407.84839 l
116.20375 410.290682 l
105.04375 374.34814 l
116.20375 375.069496 l
127.36375 375.672128 l
138.52375 376.411349 l
149.68375 377.202921 l
160.84375 377.835274 l
172.00375 378.684006 l
183.16375 379.332862 l
194.32375 380.010678 l
205.48375 380.783865 l
216.64375 381.461681 l
227.80375 382.0982 l
238.96375 382.960671 l
250.12375 383.740868 l
261.28375 384.405305 l
272.44375 384.925623 l
283.60375 385.824144 l
294.76375 386.247809 l
305.92375 387.12498 l
317.08375 387.738907 l
328.24375 388.723947 l
339.40375 389.550248 l
350.56375 390.361688 l
361.72375 391.019556 l
372.88375 392.067283 l
384.04375 392.605626 l
395.20375 393.414984 l
406.36375 394.475247 l
417.52375 395.108161 l
428.68375 396.131534 l
439.84375 397.083048 l
451.00375 397.996309 l
462.16375 399.068308 l
473.32375 400.418092 l
484.48375 401.464376 l
495.64375 402.711498 l
506.80375 404.32777 l
517.96375 405.578977 l
529.12375 407.618833 l
540.28375 410.58625 l
551.44375 374.262902 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
446.4 360.36 105.044 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-5 -5 m
5 -5 l
5 5 l
-5 5 l
cl

gsave
grestore
stroke
grestore
} bind def
540.284 375.14 o
529.124 375.867 o
517.964 376.462 o
506.804 377.122 o
495.644 378.146 o
484.484 378.619 o
473.324 379.376 o
462.164 380.042 o
451.004 380.745 o
439.844 381.348 o
428.684 382.053 o
417.524 382.813 o
406.364 383.561 o
395.204 384.306 o
384.044 384.95 o
372.884 385.767 o
361.724 386.483 o
350.564 387.333 o
339.404 388.114 o
328.244 388.76 o
317.084 389.31 o
305.924 390.29 o
294.764 390.957 o
283.604 391.832 o
272.444 392.602 o
261.284 393.57 o
250.124 394.468 o
238.964 395.38 o
227.804 396.072 o
216.644 397.138 o
205.484 398.055 o
194.324 399.256 o
183.164 400.299 o
172.004 401.358 o
160.844 402.726 o
149.684 403.997 o
138.524 405.883 o
127.364 407.848 o
116.204 410.291 o
105.044 374.348 o
116.204 375.069 o
127.364 375.672 o
138.524 376.411 o
149.684 377.203 o
160.844 377.835 o
172.004 378.684 o
183.164 379.333 o
194.324 380.011 o
205.484 380.784 o
216.644 381.462 o
227.804 382.098 o
238.964 382.961 o
250.124 383.741 o
261.284 384.405 o
272.444 384.926 o
283.604 385.824 o
294.764 386.248 o
305.924 387.125 o
317.084 387.739 o
328.244 388.724 o
339.404 389.55 o
350.564 390.362 o
361.724 391.02 o
372.884 392.067 o
384.044 392.606 o
395.204 393.415 o
406.364 394.475 o
417.524 395.108 o
428.684 396.132 o
439.844 397.083 o
451.004 397.996 o
462.164 399.068 o
473.324 400.418 o
484.484 401.464 o
495.644 402.711 o
506.804 404.328 o
517.964 405.579 o
529.124 407.619 o
540.284 410.586 o
551.444 374.263 o
grestore
0.500 setlinewidth
1 setlinejoin
[1.85 0.8] 0 setdash
0.031 0.659 0.659 setrgbcolor
gsave
446.4 360.36 105.044 66.606 clipbox
540.28375 83.016612 m
529.12375 83.141665 l
517.96375 83.18873 l
506.80375 83.317107 l
495.64375 83.440757 l
484.48375 83.51478 l
473.32375 83.786915 l
462.16375 83.778183 l
451.00375 83.995483 l
439.84375 84.104954 l
428.68375 84.233973 l
417.52375 84.411217 l
406.36375 84.635247 l
395.20375 84.937824 l
384.04375 85.147193 l
372.88375 85.361289 l
361.72375 85.522672 l
350.56375 85.782911 l
339.40375 86.136199 l
328.24375 86.324298 l
317.08375 86.811611 l
305.92375 87.136219 l
294.76375 87.432829 l
283.60375 87.739572 l
272.44375 88.168485 l
261.28375 88.539997 l
250.12375 89.080824 l
238.96375 89.559205 l
227.80375 90.092661 l
216.64375 90.802601 l
205.48375 91.468881 l
194.32375 92.285849 l
183.16375 92.932141 l
172.00375 93.854495 l
160.84375 95.14752 l
149.68375 96.159276 l
138.52375 97.753397 l
127.36375 99.918026 l
116.20375 102.779977 l
105.04375 239.352727 l
116.20375 239.275381 l
127.36375 239.113397 l
138.52375 239.228316 l
149.68375 239.034688 l
160.84375 238.851636 l
172.00375 238.708919 l
183.16375 238.648275 l
194.32375 238.527067 l
205.48375 238.322745 l
216.64375 238.147904 l
227.80375 237.968176 l
238.96375 237.814804 l
250.12375 237.576315 l
261.28375 237.405679 l
272.44375 237.31231 l
283.60375 236.9714 l
294.76375 236.849111 l
305.92375 236.568564 l
317.08375 236.296388 l
328.24375 235.866394 l
339.40375 235.687708 l
350.56375 235.230436 l
361.72375 234.95806 l
372.88375 234.61034 l
384.04375 234.196088 l
395.20375 233.748751 l
406.36375 233.24874 l
417.52375 232.689168 l
428.68375 232.233859 l
439.84375 231.660508 l
451.00375 231.008728 l
462.16375 230.207982 l
473.32375 229.484503 l
484.48375 228.455803 l
495.64375 227.400466 l
506.80375 226.135199 l
517.96375 224.62928 l
529.12375 222.480434 l
540.28375 219.358283 l
551.44375 82.98625 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
446.4 360.36 105.044 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -5 m
1.326016 -5 2.597899 -4.473168 3.535534 -3.535534 c
4.473168 -2.597899 5 -1.326016 5 0 c
5 1.326016 4.473168 2.597899 3.535534 3.535534 c
2.597899 4.473168 1.326016 5 0 5 c
-1.326016 5 -2.597899 4.473168 -3.535534 3.535534 c
-4.473168 2.597899 -5 1.326016 -5 0 c
-5 -1.326016 -4.473168 -2.597899 -3.535534 -3.535534 c
-2.597899 -4.473168 -1.326016 -5 0 -5 c
cl

gsave
grestore
stroke
grestore
} bind def
540.284 83.0166 o
529.124 83.1417 o
517.964 83.1887 o
506.804 83.3171 o
495.644 83.4408 o
484.484 83.5148 o
473.324 83.7869 o
462.164 83.7782 o
451.004 83.9955 o
439.844 84.105 o
428.684 84.234 o
417.524 84.4112 o
406.364 84.6352 o
395.204 84.9378 o
384.044 85.1472 o
372.884 85.3613 o
361.724 85.5227 o
350.564 85.7829 o
339.404 86.1362 o
328.244 86.3243 o
317.084 86.8116 o
305.924 87.1362 o
294.764 87.4328 o
283.604 87.7396 o
272.444 88.1685 o
261.284 88.54 o
250.124 89.0808 o
238.964 89.5592 o
227.804 90.0927 o
216.644 90.8026 o
205.484 91.4689 o
194.324 92.2858 o
183.164 92.9321 o
172.004 93.8545 o
160.844 95.1475 o
149.684 96.1593 o
138.524 97.7534 o
127.364 99.918 o
116.204 102.78 o
105.044 239.353 o
116.204 239.275 o
127.364 239.113 o
138.524 239.228 o
149.684 239.035 o
160.844 238.852 o
172.004 238.709 o
183.164 238.648 o
194.324 238.527 o
205.484 238.323 o
216.644 238.148 o
227.804 237.968 o
238.964 237.815 o
250.124 237.576 o
261.284 237.406 o
272.444 237.312 o
283.604 236.971 o
294.764 236.849 o
305.924 236.569 o
317.084 236.296 o
328.244 235.866 o
339.404 235.688 o
350.564 235.23 o
361.724 234.958 o
372.884 234.61 o
384.044 234.196 o
395.204 233.749 o
406.364 233.249 o
417.524 232.689 o
428.684 232.234 o
439.844 231.661 o
451.004 231.009 o
462.164 230.208 o
473.324 229.485 o
484.484 228.456 o
495.644 227.4 o
506.804 226.135 o
517.964 224.629 o
529.124 222.48 o
540.284 219.358 o
551.444 82.9862 o
grestore
2.500 setlinewidth
0 setlinejoin
2 setlinecap
0.000 setgray
gsave
105.04375 66.60625 m
105.04375 426.96625 l
stroke
grestore
gsave
551.44375 66.60625 m
551.44375 426.96625 l
stroke
grestore
gsave
105.04375 66.60625 m
551.44375 66.60625 l
stroke
grestore
gsave
105.04375 426.96625 m
551.44375 426.96625 l
stroke
grestore
0.500 setlinewidth
1 setlinejoin
0 setlinecap
[1.85 0.8] 0 setdash
0.031 0.235 0.235 setrgbcolor
gsave
289.24375 160.60625 m
309.24375 160.60625 l
329.24375 160.60625 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

0 2 m
-2 -2 l
2 -2 l
cl

gsave
0.031 0.235 0.235 setrgbcolor
fill
grestore
stroke
grestore
} bind def
309.244 160.606 o
grestore
0.000 setgray
gsave
345.244 153.606 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.796875 moveto
/omega glyphshow
/TimesNewRomanPSMT 14.0 selectfont
13.4646 -4.35562 moveto
/x glyphshow
grestore
0.500 setlinewidth
1 setlinejoin
[1.85 0.8] 0 setdash
0.031 0.416 0.416 setrgbcolor
gsave
289.24375 130.60625 m
309.24375 130.60625 l
329.24375 130.60625 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-5 -5 m
5 -5 l
5 5 l
-5 5 l
cl

gsave
grestore
stroke
grestore
} bind def
309.244 130.606 o
grestore
0.000 setgray
gsave
345.244 123.606 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.796875 moveto
/omega glyphshow
/TimesNewRomanPSMT 14.0 selectfont
13.4646 -4.35562 moveto
/y glyphshow
grestore
0.500 setlinewidth
1 setlinejoin
[1.85 0.8] 0 setdash
0.031 0.659 0.659 setrgbcolor
gsave
289.24375 97.60625 m
309.24375 97.60625 l
329.24375 97.60625 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -5 m
1.326016 -5 2.597899 -4.473168 3.535534 -3.535534 c
4.473168 -2.597899 5 -1.326016 5 0 c
5 1.326016 4.473168 2.597899 3.535534 3.535534 c
2.597899 4.473168 1.326016 5 0 5 c
-1.326016 5 -2.597899 4.473168 -3.535534 3.535534 c
-4.473168 2.597899 -5 1.326016 -5 0 c
-5 -1.326016 -4.473168 -2.597899 -3.535534 -3.535534 c
-2.597899 -4.473168 -1.326016 -5 0 -5 c
cl

gsave
grestore
stroke
grestore
} bind def
309.244 97.6062 o
grestore
0.000 setgray
gsave
345.244 90.6062 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.796875 moveto
/omega glyphshow
/TimesNewRomanPSMT 14.0 selectfont
13.4646 -4.35562 moveto
/z glyphshow
grestore

end
showpage
