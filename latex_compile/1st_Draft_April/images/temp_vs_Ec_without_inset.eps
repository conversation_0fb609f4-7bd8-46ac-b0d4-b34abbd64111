%!PS-Adobe-3.0 EPSF-3.0
%%Title: temp_vs_Ec_without_inset.eps
%%Creator: Matplotlib v3.7.1, https://matplotlib.org/
%%CreationDate: Wed Apr 24 19:26:38 2024
%%Orientation: portrait
%%BoundingBox: 41 178 571 614
%%HiResBoundingBox: 41.623438 178.916875 570.376562 613.083125
%%EndComments
%%BeginProlog
/mpldict 11 dict def
mpldict begin
/_d { bind def } bind def
/m { moveto } _d
/l { lineto } _d
/r { rlineto } _d
/c { curveto } _d
/cl { closepath } _d
/ce { closepath eofill } _d
/box {
      m
      1 index 0 r
      0 exch r
      neg 0 r
      cl
    } _d
/clipbox {
      box
      clip
      newpath
    } _d
/sc { setcachedevice } _d
%!PS-TrueTypeFont-1.0-2.53740
%%Title: unknown
%%Creator: Converted from TrueType to type 42 by PPR
15 dict begin
/FontName /TimesNewRomanPSMT def
/PaintType 0 def
/FontMatrix[1 0 0 1 0 0]def
/FontBBox[-568 -307 2028 1007]def
/FontType 42 def
/Encoding StandardEncoding def
/FontInfo 10 dict dup begin
/FamilyName (unknown) def
/FullName (unknown) def
/Weight (unknown) def
/Version (unknown) def
/ItalicAngle 0.0 def
/isFixedPitch false def
/UnderlinePosition -223 def
/UnderlineThickness 100 def
end readonly def
/sfnts[<00010000000900800003001063767420F34DDA810000009C000007C46670676D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>]def
/CharStrings 33 dict dup begin
/.notdef 0 def
/space 3 def
/parenleft 4 def
/parenright 5 def
/period 6 def
/slash 7 def
/zero 8 def
/one 9 def
/two 10 def
/three 11 def
/five 12 def
/six 13 def
/nine 14 def
/C 15 def
/E 16 def
/F 17 def
/K 18 def
/M 19 def
/T 20 def
/V 21 def
/a 22 def
/c 23 def
/d 24 def
/e 25 def
/i 26 def
/l 27 def
/m 28 def
/o 29 def
/p 30 def
/r 31 def
/t 32 def
/u 33 def
/v 34 def
end readonly def

systemdict/resourcestatus known
 {42 /FontType resourcestatus
   {pop pop false}{true}ifelse}
 {true}ifelse
{/TrueDict where{pop}{(%%[ Error: no TrueType rasterizer ]%%)= flush}ifelse
/FontType 3 def
 /TrueState 271 string def
 TrueDict begin sfnts save
 72 0 matrix defaultmatrix dtransform dup
 mul exch dup mul add sqrt cvi 0 72 matrix
 defaultmatrix dtransform dup mul exch dup
 mul add sqrt cvi 3 -1 roll restore
 TrueState initer end
 /BuildGlyph{exch begin
  CharStrings dup 2 index known
    {exch}{exch pop /.notdef}ifelse
  get dup xcheck
    {currentdict systemdict begin begin exec end end}
    {TrueDict begin /bander load cvlit exch TrueState render end}
    ifelse
 end}bind def
 /BuildChar{
  1 index /Encoding get exch get
  1 index /BuildGlyph get exec
 }bind def
}if

FontName currentdict end definefont pop
end
%%EndProlog
mpldict begin
41.623 178.917 translate
528.753 434.166 0 0 clipbox
gsave
0 -0 m
528.753125 -0 l
528.753125 434.16625 l
0 434.16625 l
cl
1.000 setgray
fill
grestore
gsave
75.153125 66.60625 m
521.553125 66.60625 l
521.553125 426.96625 l
75.153125 426.96625 l
cl
1.000 setgray
fill
grestore
2.500 setlinewidth
1 setlinejoin
0 setlinecap
[] 0 setdash
0.000 setgray
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
75.1531 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
75.1531 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

68.9031 39.2469 translate
0 rotate
0 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
203 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
203 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

184.25 39.2469 translate
0 rotate
0 0 m /three glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
330.848 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
330.848 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

312.098 39.2469 translate
0 rotate
0 0 m /six glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
458.695 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
458.695 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

439.945 39.2469 translate
0 rotate
0 0 m /nine glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
107.115 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
107.115 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
139.077 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
139.077 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
171.039 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
171.039 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
234.962 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
234.962 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
266.924 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
266.924 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
298.886 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
298.886 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
362.809 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
362.809 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
394.771 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
394.771 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
426.733 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
426.733 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
490.657 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
490.657 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

214.892 12.5438 translate
0 rotate
0 0 m /T glyphshow
13.521 0 m /e glyphshow
24.6172 0 m /m glyphshow
44.063 0 m /p glyphshow
56.563 0 m /e glyphshow
67.6592 0 m /r glyphshow
75.9844 0 m /a glyphshow
87.0806 0 m /t glyphshow
94.0264 0 m /u glyphshow
106.526 0 m /r glyphshow
114.852 0 m /e glyphshow
125.948 0 m /space glyphshow
132.198 0 m /parenleft glyphshow
140.523 0 m /K glyphshow
158.577 0 m /parenright glyphshow
grestore
2.500 setlinewidth
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
75.1531 70.3863 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
521.553 70.3863 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

33.9031 61.7066 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /five glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
75.1531 133.386 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
521.553 133.386 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

33.9031 124.707 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
75.1531 196.386 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
521.553 196.386 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

33.9031 187.707 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /period glyphshow
18.75 0 m /five glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
75.1531 259.386 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
521.553 259.386 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

33.9031 250.707 translate
0 rotate
0 0 m /two glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
75.1531 322.386 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
521.553 322.386 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

33.9031 313.707 translate
0 rotate
0 0 m /two glyphshow
12.5 0 m /period glyphshow
18.75 0 m /five glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
75.1531 385.386 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
521.553 385.386 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

33.9031 376.707 translate
0 rotate
0 0 m /three glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
75.1531 82.9862 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
521.553 82.9862 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
75.1531 95.5862 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
521.553 95.5862 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
75.1531 108.186 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
521.553 108.186 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
75.1531 120.786 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
521.553 120.786 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
75.1531 145.986 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
521.553 145.986 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
75.1531 158.586 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
521.553 158.586 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
75.1531 171.186 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
521.553 171.186 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
75.1531 183.786 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
521.553 183.786 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
75.1531 208.986 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
521.553 208.986 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
75.1531 221.586 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
521.553 221.586 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
75.1531 234.186 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
521.553 234.186 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
75.1531 246.786 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
521.553 246.786 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
75.1531 271.986 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
521.553 271.986 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
75.1531 284.586 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
521.553 284.586 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
75.1531 297.186 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
521.553 297.186 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
75.1531 309.786 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
521.553 309.786 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
75.1531 334.986 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
521.553 334.986 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
75.1531 347.586 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
521.553 347.586 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
75.1531 360.186 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
521.553 360.186 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
75.1531 372.786 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
521.553 372.786 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
75.1531 397.986 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
521.553 397.986 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
75.1531 410.586 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
521.553 410.586 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
75.1531 423.186 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
521.553 423.186 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

24.5594 122.489 translate
90 rotate
0 0 m /C glyphshow
16.6748 0 m /o glyphshow
29.1748 0 m /e glyphshow
40.271 0 m /r glyphshow
48.5962 0 m /c glyphshow
59.6924 0 m /i glyphshow
66.6382 0 m /v glyphshow
79.1382 0 m /e glyphshow
90.2344 0 m /space glyphshow
96.4844 0 m /F glyphshow
110.388 0 m /i glyphshow
117.334 0 m /e glyphshow
128.43 0 m /l glyphshow
135.376 0 m /d glyphshow
147.876 0 m /space glyphshow
154.126 0 m /parenleft glyphshow
162.451 0 m /M glyphshow
184.68 0 m /V glyphshow
202.734 0 m /slash glyphshow
209.68 0 m /c glyphshow
220.776 0 m /m glyphshow
240.222 0 m /parenright glyphshow
grestore
[9.6 2.4 1.5 2.4] 0 setdash
0.031 0.659 0.255 setrgbcolor
gsave
446.4 360.36 75.153 66.606 clipbox
96.461001 410.58625 m
117.768877 385.38625 l
160.384629 360.18625 l
203.00038 309.78625 l
245.616132 284.58625 l
288.231884 234.18625 l
330.847636 208.98625 l
373.463388 183.78625 l
416.079139 158.58625 l
501.310643 82.98625 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
446.4 360.36 75.153 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
96.461 410.586 o
117.769 385.386 o
160.385 360.186 o
203 309.786 o
245.616 284.586 o
288.232 234.186 o
330.848 208.986 o
373.463 183.786 o
416.079 158.586 o
501.311 82.9862 o
grestore
2.500 setlinewidth
0 setlinejoin
2 setlinecap
0.000 setgray
gsave
75.153125 66.60625 m
75.153125 426.96625 l
stroke
grestore
gsave
521.553125 66.60625 m
521.553125 426.96625 l
stroke
grestore
gsave
75.153125 66.60625 m
521.553125 66.60625 l
stroke
grestore
gsave
75.153125 426.96625 m
521.553125 426.96625 l
stroke
grestore
1.500 setlinewidth
1 setlinejoin
0 setlinecap
[9.6 2.4 1.5 2.4] 0 setdash
0.031 0.659 0.255 setrgbcolor
gsave
427.553125 401.96625 m
447.553125 401.96625 l
467.553125 401.96625 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
447.553 401.966 o
grestore
0.000 setgray
gsave
483.553 394.966 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.75 moveto
/E glyphshow
/TimesNewRomanPSMT 14.0 selectfont
12.5174 -4.4025 moveto
/c glyphshow
grestore

end
showpage
