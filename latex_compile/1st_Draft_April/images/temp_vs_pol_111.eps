%!PS-Adobe-3.0 EPSF-3.0
%%Title: temp_vs_pol_111.eps
%%Creator: Mat<PERSON>lotlib v3.7.1, https://matplotlib.org/
%%CreationDate: Wed Apr 24 13:15:26 2024
%%Orientation: portrait
%%BoundingBox: 29 178 583 614
%%HiResBoundingBox: 29.428125 178.916875 582.571875 613.083125
%%EndComments
%%BeginProlog
/mpldict 11 dict def
mpldict begin
/_d { bind def } bind def
/m { moveto } _d
/l { lineto } _d
/r { rlineto } _d
/c { curveto } _d
/cl { closepath } _d
/ce { closepath eofill } _d
/box {
      m
      1 index 0 r
      0 exch r
      neg 0 r
      cl
    } _d
/clipbox {
      box
      clip
      newpath
    } _d
/sc { setcachedevice } _d
%!PS-TrueTypeFont-1.0-2.53740
%%Title: unknown
%%Creator: Converted from TrueType to type 42 by PPR
15 dict begin
/FontName /TimesNewRomanPSMT def
/PaintType 0 def
/FontMatrix[1 0 0 1 0 0]def
/FontBBox[-568 -307 2028 1007]def
/FontType 42 def
/Encoding StandardEncoding def
/FontInfo 10 dict dup begin
/FamilyName (unknown) def
/FullName (unknown) def
/Weight (unknown) def
/Version (unknown) def
/ItalicAngle 0.0 def
/isFixedPitch false def
/UnderlinePosition -223 def
/UnderlineThickness 100 def
end readonly def
/sfnts[<00010000000900800003001063767420F34DDA810000009C000007C46670676D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>]def
/CharStrings 31 dict dup begin
/.notdef 0 def
/minus 31 def
/space 3 def
/parenleft 4 def
/parenright 5 def
/slash 6 def
/zero 7 def
/two 8 def
/four 9 def
/six 10 def
/mu 32 def
/C 11 def
/E 12 def
/F 13 def
/M 14 def
/P 15 def
/V 16 def
/a 17 def
/c 18 def
/d 19 def
/e 20 def
/i 21 def
/l 22 def
/m 23 def
/n 24 def
/o 25 def
/r 26 def
/t 27 def
/x 28 def
/y 29 def
/z 30 def
end readonly def

systemdict/resourcestatus known
 {42 /FontType resourcestatus
   {pop pop false}{true}ifelse}
 {true}ifelse
{/TrueDict where{pop}{(%%[ Error: no TrueType rasterizer ]%%)= flush}ifelse
/FontType 3 def
 /TrueState 271 string def
 TrueDict begin sfnts save
 72 0 matrix defaultmatrix dtransform dup
 mul exch dup mul add sqrt cvi 0 72 matrix
 defaultmatrix dtransform dup mul exch dup
 mul add sqrt cvi 3 -1 roll restore
 TrueState initer end
 /BuildGlyph{exch begin
  CharStrings dup 2 index known
    {exch}{exch pop /.notdef}ifelse
  get dup xcheck
    {currentdict systemdict begin begin exec end end}
    {TrueDict begin /bander load cvlit exch TrueState render end}
    ifelse
 end}bind def
 /BuildChar{
  1 index /Encoding get exch get
  1 index /BuildGlyph get exec
 }bind def
}if

FontName currentdict end definefont pop
end
%%EndProlog
mpldict begin
29.428 178.917 translate
553.144 434.166 0 0 clipbox
gsave
0 -0 m
553.14375 -0 l
553.14375 434.16625 l
0 434.16625 l
cl
1.000 setgray
fill
grestore
gsave
93.29375 66.60625 m
539.69375 66.60625 l
539.69375 426.96625 l
93.29375 426.96625 l
cl
1.000 setgray
fill
grestore
2.500 setlinewidth
1 setlinejoin
0 setlinecap
[] 0 setdash
0.000 setgray
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

79.9969 39.2469 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /four glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
204.894 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
204.894 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

191.597 39.2469 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /two glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
316.494 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
316.494 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

310.244 39.2469 translate
0 rotate
0 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
428.094 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
428.094 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

421.844 39.2469 translate
0 rotate
0 0 m /two glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
539.694 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
539.694 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

533.444 39.2469 translate
0 rotate
0 0 m /four glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
121.194 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
121.194 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
149.094 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
149.094 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
176.994 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
176.994 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
232.794 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
232.794 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
260.694 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
260.694 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
288.594 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
288.594 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
344.394 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
344.394 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
372.294 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
372.294 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
400.194 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
400.194 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
455.994 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
455.994 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
483.894 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
483.894 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
511.794 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
511.794 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

198.447 12.5438 translate
0 rotate
0 0 m /E glyphshow
15.271 0 m /l glyphshow
22.2168 0 m /e glyphshow
33.313 0 m /c glyphshow
44.4092 0 m /t glyphshow
51.355 0 m /r glyphshow
59.6802 0 m /i glyphshow
66.626 0 m /c glyphshow
77.7222 0 m /space glyphshow
83.9722 0 m /F glyphshow
97.876 0 m /i glyphshow
104.822 0 m /e glyphshow
115.918 0 m /l glyphshow
122.864 0 m /d glyphshow
135.364 0 m /space glyphshow
141.614 0 m /parenleft glyphshow
149.939 0 m /M glyphshow
172.168 0 m /V glyphshow
190.222 0 m /slash glyphshow
197.168 0 m /c glyphshow
208.264 0 m /m glyphshow
227.71 0 m /parenright glyphshow
grestore
2.500 setlinewidth
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 76.8651 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
539.694 76.8651 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

44.2 68.1854 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /six glyphshow
26.5991 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 133.496 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
539.694 133.496 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

44.2 124.816 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /four glyphshow
26.5991 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 190.126 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
539.694 190.126 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

44.2 181.446 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /two glyphshow
26.5991 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 246.757 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
539.694 246.757 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

70.7938 238.077 translate
0 rotate
0 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 303.387 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
539.694 303.387 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

58.2938 294.707 translate
0 rotate
0 0 m /two glyphshow
12.5 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 360.018 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
539.694 360.018 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

58.2938 351.338 translate
0 rotate
0 0 m /four glyphshow
12.5 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 416.648 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
539.694 416.648 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

58.2938 407.968 translate
0 rotate
0 0 m /six glyphshow
12.5 0 m /zero glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 91.0227 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
539.694 91.0227 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 105.18 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
539.694 105.18 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 119.338 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
539.694 119.338 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 147.653 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
539.694 147.653 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 161.811 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
539.694 161.811 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 175.969 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
539.694 175.969 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 204.284 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
539.694 204.284 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 218.441 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
539.694 218.441 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 232.599 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
539.694 232.599 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 260.914 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
539.694 260.914 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 275.072 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
539.694 275.072 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 289.23 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
539.694 289.23 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 317.545 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
539.694 317.545 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 331.702 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
539.694 331.702 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 345.86 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
539.694 345.86 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 374.175 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
539.694 374.175 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 388.333 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
539.694 388.333 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 402.491 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
539.694 402.491 o
grestore
gsave
34.2 135.786 translate
90 rotate
/TimesNewRomanPSMT 25.0 selectfont
0 0.148438 moveto
/P glyphshow
13.9038 0.148438 moveto
/o glyphshow
26.4038 0.148438 moveto
/l glyphshow
33.3496 0.148438 moveto
/a glyphshow
44.4458 0.148438 moveto
/r glyphshow
52.771 0.148438 moveto
/i glyphshow
59.7168 0.148438 moveto
/z glyphshow
70.813 0.148438 moveto
/a glyphshow
81.9092 0.148438 moveto
/t glyphshow
88.855 0.148438 moveto
/i glyphshow
95.8008 0.148438 moveto
/o glyphshow
108.301 0.148438 moveto
/n glyphshow
120.801 0.148438 moveto
/space glyphshow
127.051 0.148438 moveto
/parenleft glyphshow
135.376 0.148438 moveto
/mu glyphshow
148.779 0.148438 moveto
/C glyphshow
165.454 0.148438 moveto
/slash glyphshow
172.4 0.148438 moveto
/c glyphshow
183.496 0.148438 moveto
/m glyphshow
/TimesNewRomanPSMT 17.5 selectfont
203.318 15.1766 moveto
/two glyphshow
/TimesNewRomanPSMT 25.0 selectfont
213.141 0.148438 moveto
/parenright glyphshow
grestore
[5.55 2.4] 0 setdash
0.031 0.235 0.235 setrgbcolor
gsave
446.4 360.36 93.294 66.606 clipbox
528.53375 409.437928 m
517.37375 408.242891 l
506.21375 407.156876 l
495.05375 405.965088 l
483.89375 404.757652 l
472.73375 403.538301 l
461.57375 402.276282 l
450.41375 401.058755 l
439.25375 399.742923 l
428.09375 398.373277 l
416.93375 397.005341 l
405.77375 395.571193 l
394.61375 394.129805 l
383.45375 392.570815 l
372.29375 391.199117 l
361.13375 389.532842 l
349.97375 387.885037 l
338.81375 386.198953 l
327.65375 384.376511 l
316.49375 382.509121 l
305.33375 380.585494 l
294.17375 378.534317 l
283.01375 376.40547 l
271.85375 374.175666 l
260.69375 371.843451 l
249.53375 369.277086 l
238.37375 366.413237 l
227.21375 363.416336 l
216.05375 359.882785 l
204.89375 355.763301 l
193.73375 350.962628 l
182.57375 344.401878 l
171.41375 91.26686 l
160.25375 89.998371 l
149.09375 88.759382 l
137.93375 87.474361 l
126.77375 86.343711 l
115.61375 85.218648 l
104.45375 84.106496 l
93.29375 82.98625 l
104.45375 84.06149 l
115.61375 85.253906 l
126.77375 86.400745 l
137.93375 87.474104 l
149.09375 88.716998 l
160.25375 89.922497 l
171.41375 91.218292 l
182.57375 92.525117 l
193.73375 93.866375 l
204.89375 95.137657 l
216.05375 96.475124 l
227.21375 97.914601 l
238.37375 99.345072 l
249.53375 100.784066 l
260.69375 102.365203 l
271.85375 103.973018 l
283.01375 105.588415 l
294.17375 107.282224 l
305.33375 109.13351 l
316.49375 110.905958 l
327.65375 112.874705 l
338.81375 114.806968 l
349.97375 116.967111 l
361.13375 119.223024 l
372.29375 121.654229 l
383.45375 124.289457 l
394.61375 127.112091 l
405.77375 130.214908 l
416.93375 133.611589 l
428.09375 137.674523 l
439.25375 142.60839 l
450.41375 149.096629 l
461.57375 402.287882 l
472.73375 403.574899 l
483.89375 404.751894 l
495.05375 405.921393 l
506.21375 407.143851 l
517.37375 408.210882 l
528.53375 409.422451 l
539.69375 410.58625 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
446.4 360.36 93.294 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

0 2.5 m
-2.5 -2.5 l
2.5 -2.5 l
cl

gsave
0.031 0.235 0.235 setrgbcolor
fill
grestore
stroke
grestore
} bind def
528.534 409.438 o
517.374 408.243 o
506.214 407.157 o
495.054 405.965 o
483.894 404.758 o
472.734 403.538 o
461.574 402.276 o
450.414 401.059 o
439.254 399.743 o
428.094 398.373 o
416.934 397.005 o
405.774 395.571 o
394.614 394.13 o
383.454 392.571 o
372.294 391.199 o
361.134 389.533 o
349.974 387.885 o
338.814 386.199 o
327.654 384.377 o
316.494 382.509 o
305.334 380.585 o
294.174 378.534 o
283.014 376.405 o
271.854 374.176 o
260.694 371.843 o
249.534 369.277 o
238.374 366.413 o
227.214 363.416 o
216.054 359.883 o
204.894 355.763 o
193.734 350.963 o
182.574 344.402 o
171.414 91.2669 o
160.254 89.9984 o
149.094 88.7594 o
137.934 87.4744 o
126.774 86.3437 o
115.614 85.2186 o
104.454 84.1065 o
93.2938 82.9862 o
104.454 84.0615 o
115.614 85.2539 o
126.774 86.4007 o
137.934 87.4741 o
149.094 88.717 o
160.254 89.9225 o
171.414 91.2183 o
182.574 92.5251 o
193.734 93.8664 o
204.894 95.1377 o
216.054 96.4751 o
227.214 97.9146 o
238.374 99.3451 o
249.534 100.784 o
260.694 102.365 o
271.854 103.973 o
283.014 105.588 o
294.174 107.282 o
305.334 109.134 o
316.494 110.906 o
327.654 112.875 o
338.814 114.807 o
349.974 116.967 o
361.134 119.223 o
372.294 121.654 o
383.454 124.289 o
394.614 127.112 o
405.774 130.215 o
416.934 133.612 o
428.094 137.675 o
439.254 142.608 o
450.414 149.097 o
461.574 402.288 o
472.734 403.575 o
483.894 404.752 o
495.054 405.921 o
506.214 407.144 o
517.374 408.211 o
528.534 409.422 o
539.694 410.586 o
grestore
1.500 setlinewidth
1 setlinejoin
[5.55 2.4] 0 setdash
0.031 0.416 0.416 setrgbcolor
gsave
446.4 360.36 93.294 66.606 clipbox
528.53375 409.438869 m
517.37375 408.339515 l
506.21375 407.111414 l
495.05375 405.9721 l
483.89375 404.705434 l
472.73375 403.527042 l
461.57375 402.394768 l
450.41375 400.991574 l
439.25375 399.768775 l
428.09375 398.362132 l
416.93375 397.00916 l
405.77375 395.625234 l
394.61375 394.137587 l
383.45375 392.687876 l
372.29375 391.144107 l
361.13375 389.570266 l
349.97375 387.835271 l
338.81375 386.248862 l
327.65375 384.370868 l
316.49375 382.569803 l
305.33375 380.678271 l
294.17375 378.630828 l
283.01375 376.472252 l
271.85375 374.119886 l
260.69375 371.763928 l
249.53375 369.182599 l
238.37375 366.446443 l
227.21375 363.307171 l
216.05375 359.916333 l
204.89375 355.937596 l
193.73375 351.091746 l
182.57375 344.405241 l
171.41375 91.209057 l
160.25375 89.966505 l
149.09375 88.736409 l
137.93375 87.598633 l
126.77375 86.385268 l
115.61375 85.255331 l
104.45375 84.122999 l
93.29375 83.004748 l
104.45375 84.156063 l
115.61375 85.259464 l
126.77375 86.409154 l
137.93375 87.564572 l
149.09375 88.735554 l
160.25375 89.935095 l
171.41375 91.229066 l
182.57375 92.510068 l
193.73375 93.773997 l
204.89375 95.139282 l
216.05375 96.474097 l
227.21375 97.980956 l
238.37375 99.397375 l
249.53375 100.853527 l
260.69375 102.339522 l
271.85375 103.990604 l
283.01375 105.601128 l
294.17375 107.249047 l
305.33375 109.104894 l
316.49375 110.834587 l
327.65375 112.844948 l
338.81375 114.905331 l
349.97375 117.099307 l
361.13375 119.339971 l
372.29375 121.519154 l
383.45375 124.283186 l
394.61375 127.042459 l
405.77375 130.216219 l
416.93375 133.663607 l
428.09375 137.532237 l
439.25375 142.531432 l
450.41375 149.093494 l
461.57375 402.23367 l
472.73375 403.550785 l
483.89375 404.802772 l
495.05375 405.987548 l
506.21375 407.167992 l
517.37375 408.266434 l
528.53375 409.390072 l
539.69375 410.487858 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
446.4 360.36 93.294 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-4 -4 m
4 -4 l
4 4 l
-4 4 l
cl

gsave
grestore
stroke
grestore
} bind def
528.534 409.439 o
517.374 408.34 o
506.214 407.111 o
495.054 405.972 o
483.894 404.705 o
472.734 403.527 o
461.574 402.395 o
450.414 400.992 o
439.254 399.769 o
428.094 398.362 o
416.934 397.009 o
405.774 395.625 o
394.614 394.138 o
383.454 392.688 o
372.294 391.144 o
361.134 389.57 o
349.974 387.835 o
338.814 386.249 o
327.654 384.371 o
316.494 382.57 o
305.334 380.678 o
294.174 378.631 o
283.014 376.472 o
271.854 374.12 o
260.694 371.764 o
249.534 369.183 o
238.374 366.446 o
227.214 363.307 o
216.054 359.916 o
204.894 355.938 o
193.734 351.092 o
182.574 344.405 o
171.414 91.2091 o
160.254 89.9665 o
149.094 88.7364 o
137.934 87.5986 o
126.774 86.3853 o
115.614 85.2553 o
104.454 84.123 o
93.2938 83.0047 o
104.454 84.1561 o
115.614 85.2595 o
126.774 86.4092 o
137.934 87.5646 o
149.094 88.7356 o
160.254 89.9351 o
171.414 91.2291 o
182.574 92.5101 o
193.734 93.774 o
204.894 95.1393 o
216.054 96.4741 o
227.214 97.981 o
238.374 99.3974 o
249.534 100.854 o
260.694 102.34 o
271.854 103.991 o
283.014 105.601 o
294.174 107.249 o
305.334 109.105 o
316.494 110.835 o
327.654 112.845 o
338.814 114.905 o
349.974 117.099 o
361.134 119.34 o
372.294 121.519 o
383.454 124.283 o
394.614 127.042 o
405.774 130.216 o
416.934 133.664 o
428.094 137.532 o
439.254 142.531 o
450.414 149.093 o
461.574 402.234 o
472.734 403.551 o
483.894 404.803 o
495.054 405.988 o
506.214 407.168 o
517.374 408.266 o
528.534 409.39 o
539.694 410.488 o
grestore
1.500 setlinewidth
1 setlinejoin
[5.55 2.4] 0 setdash
0.031 0.659 0.659 setrgbcolor
gsave
446.4 360.36 93.294 66.606 clipbox
528.53375 409.377246 m
517.37375 408.268201 l
506.21375 407.153228 l
495.05375 405.930828 l
483.89375 404.788919 l
472.73375 403.541351 l
461.57375 402.241394 l
450.41375 401.065938 l
439.25375 399.774019 l
428.09375 398.485435 l
416.93375 397.008391 l
405.77375 395.589235 l
394.61375 394.163439 l
383.45375 392.672599 l
372.29375 391.067406 l
361.13375 389.552737 l
349.97375 387.931269 l
338.81375 386.157367 l
327.65375 384.398886 l
316.49375 382.530954 l
305.33375 380.615251 l
294.17375 378.543666 l
283.01375 376.443008 l
271.85375 374.265535 l
260.69375 371.769201 l
249.53375 369.210475 l
238.37375 366.381827 l
227.21375 363.334961 l
216.05375 359.938679 l
204.89375 355.892077 l
193.73375 350.98982 l
182.57375 344.416215 l
171.41375 91.198881 l
160.25375 89.991274 l
149.09375 88.74191 l
137.93375 87.626195 l
126.77375 86.428706 l
115.61375 85.29552 l
104.45375 84.051343 l
93.29375 83.057507 l
104.45375 84.108406 l
115.61375 85.238087 l
126.77375 86.355996 l
137.93375 87.552772 l
149.09375 88.731592 l
160.25375 89.954135 l
171.41375 91.164393 l
182.57375 92.458649 l
193.73375 93.774567 l
204.89375 95.108869 l
216.05375 96.488463 l
227.21375 97.916711 l
238.37375 99.370639 l
249.53375 100.855893 l
260.69375 102.344738 l
271.85375 103.988609 l
283.01375 105.690256 l
294.17375 107.30591 l
305.33375 109.097312 l
316.49375 110.951135 l
327.65375 112.859912 l
338.81375 114.913255 l
349.97375 117.062254 l
361.13375 119.258795 l
372.29375 121.705619 l
383.45375 124.30844 l
394.61375 127.062639 l
405.77375 130.223402 l
416.93375 133.718132 l
428.09375 137.695701 l
439.25375 142.451368 l
450.41375 149.101189 l
461.57375 402.268671 l
472.73375 403.499395 l
483.89375 404.779599 l
495.05375 405.940775 l
506.21375 407.090778 l
517.37375 408.315886 l
528.53375 409.469937 l
539.69375 410.352498 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
446.4 360.36 93.294 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
528.534 409.377 o
517.374 408.268 o
506.214 407.153 o
495.054 405.931 o
483.894 404.789 o
472.734 403.541 o
461.574 402.241 o
450.414 401.066 o
439.254 399.774 o
428.094 398.485 o
416.934 397.008 o
405.774 395.589 o
394.614 394.163 o
383.454 392.673 o
372.294 391.067 o
361.134 389.553 o
349.974 387.931 o
338.814 386.157 o
327.654 384.399 o
316.494 382.531 o
305.334 380.615 o
294.174 378.544 o
283.014 376.443 o
271.854 374.266 o
260.694 371.769 o
249.534 369.21 o
238.374 366.382 o
227.214 363.335 o
216.054 359.939 o
204.894 355.892 o
193.734 350.99 o
182.574 344.416 o
171.414 91.1989 o
160.254 89.9913 o
149.094 88.7419 o
137.934 87.6262 o
126.774 86.4287 o
115.614 85.2955 o
104.454 84.0513 o
93.2938 83.0575 o
104.454 84.1084 o
115.614 85.2381 o
126.774 86.356 o
137.934 87.5528 o
149.094 88.7316 o
160.254 89.9541 o
171.414 91.1644 o
182.574 92.4586 o
193.734 93.7746 o
204.894 95.1089 o
216.054 96.4885 o
227.214 97.9167 o
238.374 99.3706 o
249.534 100.856 o
260.694 102.345 o
271.854 103.989 o
283.014 105.69 o
294.174 107.306 o
305.334 109.097 o
316.494 110.951 o
327.654 112.86 o
338.814 114.913 o
349.974 117.062 o
361.134 119.259 o
372.294 121.706 o
383.454 124.308 o
394.614 127.063 o
405.774 130.223 o
416.934 133.718 o
428.094 137.696 o
439.254 142.451 o
450.414 149.101 o
461.574 402.269 o
472.734 403.499 o
483.894 404.78 o
495.054 405.941 o
506.214 407.091 o
517.374 408.316 o
528.534 409.47 o
539.694 410.352 o
grestore
2.500 setlinewidth
0 setlinejoin
2 setlinecap
0.000 setgray
gsave
93.29375 66.60625 m
93.29375 426.96625 l
stroke
grestore
gsave
539.69375 66.60625 m
539.69375 426.96625 l
stroke
grestore
gsave
93.29375 66.60625 m
539.69375 66.60625 l
stroke
grestore
gsave
93.29375 426.96625 m
539.69375 426.96625 l
stroke
grestore
1.500 setlinewidth
1 setlinejoin
0 setlinecap
[5.55 2.4] 0 setdash
0.031 0.235 0.235 setrgbcolor
gsave
278.49375 281.28625 m
298.49375 281.28625 l
318.49375 281.28625 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

0 2.5 m
-2.5 -2.5 l
2.5 -2.5 l
cl

gsave
0.031 0.235 0.235 setrgbcolor
fill
grestore
stroke
grestore
} bind def
298.494 281.286 o
grestore
0.000 setgray
gsave
334.494 274.286 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.75 moveto
/P glyphshow
/TimesNewRomanPSMT 14.0 selectfont
11.4236 -4.4025 moveto
/x glyphshow
grestore
1.500 setlinewidth
1 setlinejoin
[5.55 2.4] 0 setdash
0.031 0.416 0.416 setrgbcolor
gsave
278.49375 251.28625 m
298.49375 251.28625 l
318.49375 251.28625 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-4 -4 m
4 -4 l
4 4 l
-4 4 l
cl

gsave
grestore
stroke
grestore
} bind def
298.494 251.286 o
grestore
0.000 setgray
gsave
334.494 244.286 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.75 moveto
/P glyphshow
/TimesNewRomanPSMT 14.0 selectfont
11.4236 -4.4025 moveto
/y glyphshow
grestore
1.500 setlinewidth
1 setlinejoin
[5.55 2.4] 0 setdash
0.031 0.659 0.659 setrgbcolor
gsave
278.49375 218.28625 m
298.49375 218.28625 l
318.49375 218.28625 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
298.494 218.286 o
grestore
0.000 setgray
gsave
334.494 211.286 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.75 moveto
/P glyphshow
/TimesNewRomanPSMT 14.0 selectfont
11.4236 -4.4025 moveto
/z glyphshow
grestore

end
showpage
