%!PS-Adobe-3.0 EPSF-3.0
%%Title: energy_bar.eps
%%Creator: Matplotlib v3.7.1, https://matplotlib.org/
%%CreationDate: Fri Mar 22 12:37:47 2024
%%Orientation: portrait
%%BoundingBox: 27 173 585 619
%%HiResBoundingBox: 27.943750 173.795781 584.056250 618.204219
%%EndComments
%%BeginProlog
/mpldict 11 dict def
mpldict begin
/_d { bind def } bind def
/m { moveto } _d
/l { lineto } _d
/r { rlineto } _d
/c { curveto } _d
/cl { closepath } _d
/ce { closepath eofill } _d
/box {
      m
      1 index 0 r
      0 exch r
      neg 0 r
      cl
    } _d
/clipbox {
      box
      clip
      newpath
    } _d
/sc { setcachedevice } _d
%!PS-TrueTypeFont-1.0-2.0
%%Title: unknown
%%Creator: Converted from TrueType to type 42 by PPR
15 dict begin
/FontName /LiberationSans def
/PaintType 0 def
/FontMatrix[1 0 0 1 0 0]def
/FontBBox[-544 -303 1302 980]def
/FontType 42 def
/Encoding StandardEncoding def
/FontInfo 10 dict dup begin
/FamilyName (unknown) def
/FullName (unknown) def
/Weight (unknown) def
/Version (unknown) def
/ItalicAngle 0.0 def
/isFixedPitch false def
/UnderlinePosition -217 def
/UnderlineThickness 150 def
end readonly def
/sfnts[<000100000009008000030010637674204ADA4BFA0000009C000002886670676D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>]def
/CharStrings 31 dict dup begin
/.notdef 0 def
/minus 32 def
/Gamma 30 def
/Delta 31 def
/space 3 def
/parenleft 4 def
/parenright 5 def
/plus 6 def
/period 7 def
/zero 8 def
/one 9 def
/two 10 def
/five 11 def
/C 12 def
/D 13 def
/E 14 def
/M 15 def
/R 16 def
/V 17 def
/d 18 def
/e 19 def
/f 20 def
/i 21 def
/m 22 def
/n 23 def
/o 24 def
/p 25 def
/r 26 def
/s 27 def
/t 28 def
/u 29 def
end readonly def

systemdict/resourcestatus known
 {42 /FontType resourcestatus
   {pop pop false}{true}ifelse}
 {true}ifelse
{/TrueDict where{pop}{(%%[ Error: no TrueType rasterizer ]%%)= flush}ifelse
/FontType 3 def
 /TrueState 271 string def
 TrueDict begin sfnts save
 72 0 matrix defaultmatrix dtransform dup
 mul exch dup mul add sqrt cvi 0 72 matrix
 defaultmatrix dtransform dup mul exch dup
 mul add sqrt cvi 3 -1 roll restore
 TrueState initer end
 /BuildGlyph{exch begin
  CharStrings dup 2 index known
    {exch}{exch pop /.notdef}ifelse
  get dup xcheck
    {currentdict systemdict begin begin exec end end}
    {TrueDict begin /bander load cvlit exch TrueState render end}
    ifelse
 end}bind def
 /BuildChar{
  1 index /Encoding get exch get
  1 index /BuildGlyph get exec
 }bind def
}if

FontName currentdict end definefont pop
end
%%EndProlog
mpldict begin
27.944 173.796 translate
556.112 444.408 0 0 clipbox
gsave
0 0 m
556.1125 0 l
556.1125 444.408437 l
0 444.408437 l
cl
1.000 setgray
fill
grestore
gsave
102.5125 67.79375 m
548.9125 67.79375 l
548.9125 428.15375 l
102.5125 428.15375 l
cl
1.000 setgray
fill
grestore
gsave
446.4 360.36 102.513 67.794 clipbox
122.803409 428.15375 m
154.192661 428.15375 l
154.192661 145.267258 l
122.803409 145.267258 l
cl
0.000 0.318 0.318 setrgbcolor
fill
grestore
0.000 setgray
gsave
446.4 360.36 102.513 67.794 clipbox
154.192661 428.15375 m
185.581912 428.15375 l
185.581912 170.417485 l
154.192661 170.417485 l
cl
gsave
0.973 0.451 0.024 setrgbcolor
fill
grestore
  << /PatternType 1
     /PaintType 2
     /TilingType 2
     /BBox[0 0 72 72]
     /XStep 72
     /YStep 72

     /PaintProc {
        pop
        1 setlinewidth
-36 36 m
36 108 l
-24 24 m
48 96 l
-12 12 m
60 84 l
0 0 m
72 72 l
12 -12 m
84 60 l
24 -24 m
96 48 l
36 -36 m
108 36 l

        gsave
        fill
        grestore
        stroke
     } bind
   >>
   matrix
   0 444.408 translate
   makepattern
   /H0 exch def
gsave
0.000000 0.000000 0.000000 H0 setpattern fill grestore
grestore
gsave
446.4 360.36 102.513 67.794 clipbox
212.486985 428.15375 m
243.876237 428.15375 l
243.876237 169.109473 l
212.486985 169.109473 l
cl
0.016 0.459 0.459 setrgbcolor
fill
grestore
gsave
446.4 360.36 102.513 67.794 clipbox
243.876237 428.15375 m
275.265488 428.15375 l
275.265488 196.191111 l
243.876237 196.191111 l
cl
gsave
0.973 0.451 0.024 setrgbcolor
fill
grestore
gsave
0.000000 0.000000 0.000000 H0 setpattern fill grestore
grestore
gsave
446.4 360.36 102.513 67.794 clipbox
317.865187 428.15375 m
349.254439 428.15375 l
349.254439 281.516313 l
317.865187 281.516313 l
cl
0.031 0.659 0.659 setrgbcolor
fill
grestore
gsave
446.4 360.36 102.513 67.794 clipbox
391.854137 428.15375 m
423.243389 428.15375 l
423.243389 84.95375 l
391.854137 84.95375 l
cl
0.000 0.196 0.196 setrgbcolor
fill
grestore
gsave
446.4 360.36 102.513 67.794 clipbox
423.243389 428.15375 m
454.632641 428.15375 l
454.632641 105.983418 l
423.243389 105.983418 l
cl
gsave
0.973 0.451 0.024 setrgbcolor
fill
grestore
gsave
0.000000 0.000000 0.000000 H0 setpattern fill grestore
grestore
gsave
446.4 360.36 102.513 67.794 clipbox
497.232339 428.15375 m
528.621591 428.15375 l
528.621591 135.325081 l
497.232339 135.325081 l
cl
0.000 0.318 0.271 setrgbcolor
fill
grestore
2.500 setlinewidth
1 setlinejoin
0 setlinecap
[] 0 setdash
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
154.193 67.7938 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
154.193 428.154 o
grestore
gsave
147.193 39.6844 translate
0 rotate
/LiberationSans 25.0 selectfont
0 0.78125 moveto
/Gamma glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
243.876 67.7938 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
243.876 428.154 o
grestore
gsave
224.376 39.6844 translate
0 rotate
/LiberationSans 25.0 selectfont
0 0.796875 moveto
/R glyphshow
/LiberationSans 17.5 selectfont
18.2853 -3.16406 moveto
/two glyphshow
28.0179 -3.16406 moveto
/five glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
333.56 67.7938 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
333.56 428.154 o
grestore
/LiberationSans 25.000 selectfont
gsave

323.146 39.6844 translate
0 rotate
0 0 m /M glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
423.243 67.7938 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
423.243 428.154 o
grestore
gsave
389.743 39.6844 translate
0 rotate
/LiberationSans 25.0 selectfont
0 0.78125 moveto
/Gamma glyphshow
13.7695 0.78125 moveto
/plus glyphshow
28.3691 0.78125 moveto
/R glyphshow
/LiberationSans 17.5 selectfont
46.6544 -3.17969 moveto
/two glyphshow
56.3871 -3.17969 moveto
/five glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
512.927 67.7938 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
512.927 428.154 o
grestore
gsave
487.927 39.6844 translate
0 rotate
/LiberationSans 25.0 selectfont
0 0.78125 moveto
/Gamma glyphshow
13.7695 0.78125 moveto
/plus glyphshow
28.3691 0.78125 moveto
/M glyphshow
grestore
/LiberationSans 25.000 selectfont
gsave

231.892 12.3875 translate
0 rotate
0 0 m /D glyphshow
18.0542 0 m /i glyphshow
23.6084 0 m /s glyphshow
36.1084 0 m /t glyphshow
43.0542 0 m /o glyphshow
56.958 0 m /r glyphshow
65.2832 0 m /t glyphshow
72.229 0 m /i glyphshow
77.7832 0 m /o glyphshow
91.687 0 m /n glyphshow
105.591 0 m /space glyphshow
112.537 0 m /m glyphshow
133.362 0 m /o glyphshow
147.266 0 m /d glyphshow
161.169 0 m /e glyphshow
175.073 0 m /s glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
102.513 105.983 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
548.913 105.983 o
grestore
/LiberationSans 25.000 selectfont
gsave

36.2 96.9287 translate
0 rotate
0 0 m /minus glyphshow
14.5996 0 m /two glyphshow
28.5034 0 m /five glyphshow
42.4072 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
102.513 170.417 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
548.913 170.417 o
grestore
/LiberationSans 25.000 selectfont
gsave

36.2 161.363 translate
0 rotate
0 0 m /minus glyphshow
14.5996 0 m /two glyphshow
28.5034 0 m /zero glyphshow
42.4072 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
102.513 234.852 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
548.913 234.852 o
grestore
/LiberationSans 25.000 selectfont
gsave

36.2 225.797 translate
0 rotate
0 0 m /minus glyphshow
14.5996 0 m /one glyphshow
28.5034 0 m /five glyphshow
42.4072 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
102.513 299.286 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
548.913 299.286 o
grestore
/LiberationSans 25.000 selectfont
gsave

36.2 290.231 translate
0 rotate
0 0 m /minus glyphshow
14.5996 0 m /one glyphshow
28.5034 0 m /zero glyphshow
42.4072 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
102.513 363.72 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
548.913 363.72 o
grestore
/LiberationSans 25.000 selectfont
gsave

50.1063 354.665 translate
0 rotate
0 0 m /minus glyphshow
14.5996 0 m /five glyphshow
28.5034 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
102.513 428.154 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
548.913 428.154 o
grestore
/LiberationSans 25.000 selectfont
gsave

78.6063 419.099 translate
0 rotate
0 0 m /zero glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
102.513 80.2098 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
548.913 80.2098 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
102.513 93.0966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
548.913 93.0966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
102.513 118.87 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
548.913 118.87 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
102.513 131.757 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
548.913 131.757 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
102.513 144.644 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
548.913 144.644 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
102.513 157.531 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
548.913 157.531 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
102.513 183.304 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
548.913 183.304 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
102.513 196.191 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
548.913 196.191 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
102.513 209.078 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
548.913 209.078 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
102.513 221.965 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
548.913 221.965 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
102.513 247.738 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
548.913 247.738 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
102.513 260.625 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
548.913 260.625 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
102.513 273.512 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
548.913 273.512 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
102.513 286.399 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
548.913 286.399 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
102.513 312.172 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
548.913 312.172 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
102.513 325.059 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
548.913 325.059 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
102.513 337.946 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
548.913 337.946 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
102.513 350.833 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
548.913 350.833 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
102.513 376.606 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
548.913 376.606 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
102.513 389.493 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
548.913 389.493 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
102.513 402.38 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
548.913 402.38 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
102.513 415.267 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
548.913 415.267 o
grestore
gsave
26.2 196.974 translate
90 rotate
/LiberationSans 25.0 selectfont
0 0.890625 moveto
/Delta glyphshow
16.6992 0.890625 moveto
/E glyphshow
33.374 0.890625 moveto
/parenleft glyphshow
41.6992 0.890625 moveto
/m glyphshow
62.5244 0.890625 moveto
/e glyphshow
76.4282 0.890625 moveto
/V glyphshow
93.103 0.890625 moveto
/parenright glyphshow
grestore
2.500 setlinewidth
0 setlinejoin
2 setlinecap
gsave
102.5125 67.79375 m
102.5125 428.15375 l
stroke
grestore
gsave
548.9125 67.79375 m
548.9125 428.15375 l
stroke
grestore
gsave
102.5125 67.79375 m
548.9125 67.79375 l
stroke
grestore
gsave
102.5125 428.15375 m
548.9125 428.15375 l
stroke
grestore
gsave
120.5125 118.60625 m
160.5125 118.60625 l
160.5125 132.60625 l
120.5125 132.60625 l
cl
0.000 0.318 0.318 setrgbcolor
fill
grestore
/LiberationSans 20.000 selectfont
gsave

176.512 118.606 translate
0 rotate
0 0 m /C glyphshow
14.4434 0 m /o glyphshow
25.5664 0 m /m glyphshow
42.2266 0 m /p glyphshow
53.3496 0 m /u glyphshow
64.4727 0 m /period glyphshow
grestore
gsave
120.5125 89.95 m
160.5125 89.95 l
160.5125 103.95 l
120.5125 103.95 l
cl
gsave
0.973 0.451 0.024 setrgbcolor
fill
grestore
gsave
0.000000 0.000000 0.000000 H0 setpattern fill grestore
grestore
/LiberationSans 20.000 selectfont
gsave

176.512 89.95 translate
0 rotate
0 0 m /R glyphshow
14.4434 0 m /e glyphshow
25.5664 0 m /f glyphshow
31.123 0 m /period glyphshow
grestore

end
showpage
