%!PS-Adobe-3.0 EPSF-3.0
%%Title: double_well.eps
%%Creator: Matplotlib v3.7.1, https://matplotlib.org/
%%CreationDate: Wed Apr 24 13:09:57 2024
%%Orientation: portrait
%%BoundingBox: 30 173 582 619
%%HiResBoundingBox: 30.803125 173.428594 581.196875 618.571406
%%EndComments
%%BeginProlog
/mpldict 11 dict def
mpldict begin
/_d { bind def } bind def
/m { moveto } _d
/l { lineto } _d
/r { rlineto } _d
/c { curveto } _d
/cl { closepath } _d
/ce { closepath eofill } _d
/box {
      m
      1 index 0 r
      0 exch r
      neg 0 r
      cl
    } _d
/clipbox {
      box
      clip
      newpath
    } _d
/sc { setcachedevice } _d
%!PS-TrueTypeFont-1.0-2.53740
%%Title: unknown
%%Creator: Converted from TrueType to type 42 by PPR
15 dict begin
/FontName /TimesNewRomanPSMT def
/PaintType 0 def
/FontMatrix[1 0 0 1 0 0]def
/FontBBox[-568 -307 2028 1007]def
/FontType 42 def
/Encoding StandardEncoding def
/FontInfo 10 dict dup begin
/FamilyName (unknown) def
/FullName (unknown) def
/Weight (unknown) def
/Version (unknown) def
/ItalicAngle 0.0 def
/isFixedPitch false def
/UnderlinePosition -223 def
/UnderlineThickness 100 def
end readonly def
/sfnts[<00010000000900800003001063767420F34DDA810000009C000007C46670676D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>]def
/CharStrings 25 dict dup begin
/.notdef 0 def
/minus 24 def
/Gamma 25 def
/Delta 26 def
/space 3 def
/parenleft 4 def
/parenright 5 def
/period 6 def
/zero 7 def
/one 8 def
/two 9 def
/five 10 def
/E 11 def
/M 12 def
/R 13 def
/V 14 def
/a 15 def
/e 16 def
/f 17 def
/i 18 def
/m 19 def
/n 20 def
/o 21 def
/t 22 def
/u 23 def
end readonly def

systemdict/resourcestatus known
 {42 /FontType resourcestatus
   {pop pop false}{true}ifelse}
 {true}ifelse
{/TrueDict where{pop}{(%%[ Error: no TrueType rasterizer ]%%)= flush}ifelse
/FontType 3 def
 /TrueState 271 string def
 TrueDict begin sfnts save
 72 0 matrix defaultmatrix dtransform dup
 mul exch dup mul add sqrt cvi 0 72 matrix
 defaultmatrix dtransform dup mul exch dup
 mul add sqrt cvi 3 -1 roll restore
 TrueState initer end
 /BuildGlyph{exch begin
  CharStrings dup 2 index known
    {exch}{exch pop /.notdef}ifelse
  get dup xcheck
    {currentdict systemdict begin begin exec end end}
    {TrueDict begin /bander load cvlit exch TrueState render end}
    ifelse
 end}bind def
 /BuildChar{
  1 index /Encoding get exch get
  1 index /BuildGlyph get exec
 }bind def
}if

FontName currentdict end definefont pop
end
%%EndProlog
mpldict begin
30.803 173.429 translate
550.394 445.143 0 0 clipbox
gsave
0 0 m
550.39375 0 l
550.39375 445.142812 l
0 445.142812 l
cl
1.000 setgray
fill
grestore
gsave
96.79375 68.903125 m
543.19375 68.903125 l
543.19375 429.263125 l
96.79375 429.263125 l
cl
1.000 setgray
fill
grestore
2.500 setlinewidth
1 setlinejoin
0 setlinecap
[] 0 setdash
0.000 setgray
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
118.757 68.9031 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
118.757 429.263 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

89.8351 41.5438 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /zero glyphshow
26.5991 0 m /period glyphshow
32.8491 0 m /one glyphshow
45.3491 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
216.514 68.9031 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
216.514 429.263 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

187.592 41.5438 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /zero glyphshow
26.5991 0 m /period glyphshow
32.8491 0 m /zero glyphshow
45.3491 0 m /five glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
314.271 68.9031 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
314.271 429.263 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

292.396 41.5438 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
412.028 68.9031 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
412.028 429.263 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

390.153 41.5438 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /five glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
509.784 68.9031 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
509.784 429.263 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

487.909 41.5438 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /one glyphshow
31.25 0 m /zero glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
99.2056 68.9031 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
99.2056 429.263 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
138.308 68.9031 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
138.308 429.263 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
157.86 68.9031 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
157.86 429.263 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
177.411 68.9031 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
177.411 429.263 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
196.962 68.9031 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
196.962 429.263 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
236.065 68.9031 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
236.065 429.263 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
255.617 68.9031 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
255.617 429.263 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
275.168 68.9031 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
275.168 429.263 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
294.719 68.9031 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
294.719 429.263 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
333.822 68.9031 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
333.822 429.263 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
353.373 68.9031 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
353.373 429.263 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
372.925 68.9031 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
372.925 429.263 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
392.476 68.9031 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
392.476 429.263 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
431.579 68.9031 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
431.579 429.263 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
451.13 68.9031 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
451.13 429.263 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
470.682 68.9031 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
470.682 429.263 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
490.233 68.9031 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
490.233 429.263 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
529.336 68.9031 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
529.336 429.263 o
grestore
gsave
255.494 14.2 translate
0 rotate
/TimesNewRomanPSMT 25.0 selectfont
0 0.640625 moveto
/u glyphshow
12.5 0.640625 moveto
/space glyphshow
18.75 0.640625 moveto
/parenleft glyphshow
27.0752 0.640625 moveto
/u glyphshow
39.5752 0.640625 moveto
/n glyphshow
52.0752 0.640625 moveto
/i glyphshow
59.021 0.640625 moveto
/t glyphshow
65.9668 0.640625 moveto
/space glyphshow
72.2168 0.640625 moveto
/o glyphshow
84.7168 0.640625 moveto
/f glyphshow
93.042 0.640625 moveto
/space glyphshow
99.292 0.640625 moveto
/a glyphshow
/TimesNewRomanPSMT 17.5 selectfont
110.764 -5.8 moveto
/zero glyphshow
/TimesNewRomanPSMT 25.0 selectfont
120.587 0.640625 moveto
/parenright glyphshow
grestore
2.500 setlinewidth
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
96.7938 68.9031 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
543.194 68.9031 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

35.2 60.2234 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /one glyphshow
26.5991 0 m /five glyphshow
39.0991 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
96.7938 158.993 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
543.194 158.993 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

35.2 150.313 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /one glyphshow
26.5991 0 m /zero glyphshow
39.0991 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
96.7938 249.083 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
543.194 249.083 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

47.7 240.403 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /five glyphshow
26.5991 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
96.7938 339.173 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
543.194 339.173 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

74.2938 330.493 translate
0 rotate
0 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
96.7938 429.263 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
543.194 429.263 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

61.7938 420.583 translate
0 rotate
0 0 m /five glyphshow
12.5 0 m /zero glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
96.7938 86.9211 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
543.194 86.9211 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
96.7938 104.939 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
543.194 104.939 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
96.7938 122.957 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
543.194 122.957 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
96.7938 140.975 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
543.194 140.975 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
96.7938 177.011 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
543.194 177.011 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
96.7938 195.029 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
543.194 195.029 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
96.7938 213.047 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
543.194 213.047 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
96.7938 231.065 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
543.194 231.065 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
96.7938 267.101 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
543.194 267.101 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
96.7938 285.119 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
543.194 285.119 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
96.7938 303.137 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
543.194 303.137 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
96.7938 321.155 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
543.194 321.155 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
96.7938 357.191 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
543.194 357.191 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
96.7938 375.209 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
543.194 375.209 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
96.7938 393.227 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
543.194 393.227 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
96.7938 411.245 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
543.194 411.245 o
grestore
gsave
25.2 200.583 translate
90 rotate
/TimesNewRomanPSMT 25.0 selectfont
0 0.640625 moveto
/Delta glyphshow
16.0767 0.640625 moveto
/E glyphshow
31.3477 0.640625 moveto
/parenleft glyphshow
39.6729 0.640625 moveto
/m glyphshow
59.1187 0.640625 moveto
/e glyphshow
70.2148 0.640625 moveto
/V glyphshow
88.269 0.640625 moveto
/parenright glyphshow
grestore
2.500 setlinewidth
2 setlinecap
0.031 0.416 0.416 setrgbcolor
gsave
446.4 360.36 96.794 68.903 clipbox
96.79375 201.434524 m
108.239904 163.276904 l
119.686058 137.031435 l
131.132212 121.351271 l
142.578365 114.909836 l
154.024519 116.391816 l
165.470673 124.526943 l
176.916827 138.053957 l
188.362981 155.761146 l
199.809135 176.472837 l
211.255288 199.069661 l
222.701442 222.477296 l
234.147596 245.707002 l
245.59375 267.833106 l
257.039904 288.04705 l
268.486058 305.612348 l
279.932212 319.923144 l
291.378365 330.492953 l
302.824519 336.98619 l
314.270673 339.173125 l
325.716827 336.98619 l
337.162981 330.492953 l
348.609135 319.923144 l
360.055288 305.612348 l
371.501442 288.04705 l
382.947596 267.833106 l
394.39375 245.707002 l
405.839904 222.477296 l
417.286058 199.069661 l
428.732212 176.472837 l
440.178365 155.761146 l
451.624519 138.053957 l
463.070673 124.526943 l
474.516827 116.391816 l
485.962981 114.909836 l
497.409135 121.351271 l
508.855288 137.031435 l
520.301442 163.276904 l
531.747596 201.434524 l
543.19375 252.864653 l
stroke
grestore
1.000 setlinewidth
0 setlinecap
gsave
446.4 360.36 96.794 68.903 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -4 m
1.060812 -4 2.078319 -3.578535 2.828427 -2.828427 c
3.578535 -2.078319 4 -1.060812 4 0 c
4 1.060812 3.578535 2.078319 2.828427 2.828427 c
2.078319 3.578535 1.060812 4 0 4 c
-1.060812 4 -2.078319 3.578535 -2.828427 2.828427 c
-3.578535 2.078319 -4 1.060812 -4 0 c
-4 -1.060812 -3.578535 -2.078319 -2.828427 -2.828427 c
-2.078319 -3.578535 -1.060812 -4 0 -4 c
cl

gsave
0.031 0.416 0.416 setrgbcolor
fill
grestore
stroke
grestore
} bind def
96.7938 201.435 o
108.24 163.277 o
119.686 137.031 o
131.132 121.351 o
142.578 114.91 o
154.025 116.392 o
165.471 124.527 o
176.917 138.054 o
188.363 155.761 o
199.809 176.473 o
211.255 199.07 o
222.701 222.477 o
234.148 245.707 o
245.594 267.833 o
257.04 288.047 o
268.486 305.612 o
279.932 319.923 o
291.378 330.493 o
302.825 336.986 o
314.271 339.173 o
325.717 336.986 o
337.163 330.493 o
348.609 319.923 o
360.055 305.612 o
371.501 288.047 o
382.948 267.833 o
394.394 245.707 o
405.84 222.477 o
417.286 199.07 o
428.732 176.473 o
440.178 155.761 o
451.625 138.054 o
463.071 124.527 o
474.517 116.392 o
485.963 114.91 o
497.409 121.351 o
508.855 137.031 o
520.301 163.277 o
531.748 201.435 o
543.194 252.865 o
grestore
2.500 setlinewidth
2 setlinecap
0.031 0.235 0.235 setrgbcolor
gsave
446.4 360.36 96.794 68.903 clipbox
112.686701 446.142812 m
119.686058 367.337511 l
131.132212 266.161937 l
142.578365 189.957058 l
154.024519 136.108013 l
165.470673 102.026966 l
176.916827 85.177883 l
188.362981 83.054012 l
199.809135 93.150848 l
211.255288 112.98191 l
222.701442 140.067468 l
234.147596 171.943562 l
245.59375 206.177762 l
257.039904 240.396197 l
268.486058 272.346615 l
279.932212 299.893885 l
291.378365 321.143864 l
302.824519 334.571778 l
314.270673 339.173125 l
325.716827 334.571778 l
337.162981 321.143864 l
348.609135 299.893885 l
360.055288 272.346615 l
371.501442 240.396197 l
382.947596 206.177762 l
394.39375 171.943562 l
405.839904 140.067468 l
417.286058 112.98191 l
428.732212 93.150848 l
440.178365 83.054012 l
451.624519 85.177883 l
463.070673 102.026966 l
474.516827 136.108013 l
485.962981 189.957058 l
497.409135 266.161937 l
508.855288 367.337511 l
515.854646 446.142812 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
0 setlinecap
gsave
446.4 360.36 96.794 68.903 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-4 -4 m
4 -4 l
4 4 l
-4 4 l
cl

gsave
0.031 0.235 0.235 setrgbcolor
fill
grestore
stroke
grestore
} bind def
112.687 446.143 o
119.686 367.338 o
131.132 266.162 o
142.578 189.957 o
154.025 136.108 o
165.471 102.027 o
176.917 85.1779 o
188.363 83.054 o
199.809 93.1508 o
211.255 112.982 o
222.701 140.067 o
234.148 171.944 o
245.594 206.178 o
257.04 240.396 o
268.486 272.347 o
279.932 299.894 o
291.378 321.144 o
302.825 334.572 o
314.271 339.173 o
325.717 334.572 o
337.163 321.144 o
348.609 299.894 o
360.055 272.347 o
371.501 240.396 o
382.948 206.178 o
394.394 171.944 o
405.84 140.067 o
417.286 112.982 o
428.732 93.1508 o
440.178 83.054 o
451.625 85.1779 o
463.071 102.027 o
474.517 136.108 o
485.963 189.957 o
497.409 266.162 o
508.855 367.338 o
515.855 446.143 o
grestore
2.500 setlinewidth
1 setlinejoin
2 setlinecap
0.031 0.659 0.659 setrgbcolor
gsave
446.4 360.36 96.794 68.903 clipbox
128.595271 446.142812 m
131.132212 420.202323 l
142.578365 328.051514 l
154.024519 258.398431 l
165.470673 208.884967 l
176.916827 177.180044 l
188.362981 160.954835 l
199.809135 157.867 l
211.255288 165.571947 l
222.701442 181.680039 l
234.147596 203.810648 l
245.59375 229.585397 l
257.039904 256.634919 l
268.486058 282.738497 l
279.932212 305.736221 l
291.378365 323.74296 l
302.824519 335.222678 l
314.270673 339.173125 l
325.716827 335.222678 l
337.162981 323.74296 l
348.609135 305.736221 l
360.055288 282.738497 l
371.501442 256.634919 l
382.947596 229.585397 l
394.39375 203.810648 l
405.839904 181.680039 l
417.286058 165.571947 l
428.732212 157.867 l
440.178365 160.954835 l
451.624519 177.180044 l
463.070673 208.884967 l
474.516827 258.398431 l
485.962981 328.051514 l
497.409135 420.202323 l
499.946075 446.142812 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
0 setlinecap
gsave
446.4 360.36 96.794 68.903 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

0 4 m
-4 -4 l
4 -4 l
cl

gsave
0.031 0.659 0.659 setrgbcolor
fill
grestore
stroke
grestore
} bind def
128.595 446.143 o
131.132 420.202 o
142.578 328.052 o
154.025 258.398 o
165.471 208.885 o
176.917 177.18 o
188.363 160.955 o
199.809 157.867 o
211.255 165.572 o
222.701 181.68 o
234.148 203.811 o
245.594 229.585 o
257.04 256.635 o
268.486 282.738 o
279.932 305.736 o
291.378 323.743 o
302.825 335.223 o
314.271 339.173 o
325.717 335.223 o
337.163 323.743 o
348.609 305.736 o
360.055 282.738 o
371.501 256.635 o
382.948 229.585 o
394.394 203.811 o
405.84 181.68 o
417.286 165.572 o
428.732 157.867 o
440.178 160.955 o
451.625 177.18 o
463.071 208.885 o
474.517 258.398 o
485.963 328.052 o
497.409 420.202 o
499.946 446.143 o
grestore
2.500 setlinewidth
2 setlinecap
0.000 setgray
gsave
96.79375 68.903125 m
96.79375 429.263125 l
stroke
grestore
gsave
543.19375 68.903125 m
543.19375 429.263125 l
stroke
grestore
gsave
96.79375 68.903125 m
543.19375 68.903125 l
stroke
grestore
gsave
96.79375 429.263125 m
543.19375 429.263125 l
stroke
grestore
1 setlinejoin
0.031 0.416 0.416 setrgbcolor
gsave
181.89375 406.763125 m
199.89375 406.763125 l
217.89375 406.763125 l
stroke
grestore
1.000 setlinewidth
0 setlinecap
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -4 m
1.060812 -4 2.078319 -3.578535 2.828427 -2.828427 c
3.578535 -2.078319 4 -1.060812 4 0 c
4 1.060812 3.578535 2.078319 2.828427 2.828427 c
2.078319 3.578535 1.060812 4 0 4 c
-1.060812 4 -2.078319 3.578535 -2.828427 2.828427 c
-3.578535 2.078319 -4 1.060812 -4 0 c
-4 -1.060812 -3.578535 -2.078319 -2.828427 -2.828427 c
-2.078319 -3.578535 -1.060812 -4 0 -4 c
cl

gsave
0.031 0.416 0.416 setrgbcolor
fill
grestore
stroke
grestore
} bind def
199.894 406.763 o
grestore
0.000 setgray
gsave
232.294 400.463 translate
0 rotate
/TimesNewRomanPSMT 18.0 selectfont
0 0.078125 moveto
/Gamma glyphshow
grestore
2.500 setlinewidth
2 setlinecap
0.031 0.235 0.235 setrgbcolor
gsave
279.29375 406.763125 m
297.29375 406.763125 l
315.29375 406.763125 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
0 setlinecap
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-4 -4 m
4 -4 l
4 4 l
-4 4 l
cl

gsave
0.031 0.235 0.235 setrgbcolor
fill
grestore
stroke
grestore
} bind def
297.294 406.763 o
grestore
0.000 setgray
gsave
329.694 400.463 translate
0 rotate
/TimesNewRomanPSMT 18.0 selectfont
0 0.078125 moveto
/R glyphshow
/TimesNewRomanPSMT 12.6 selectfont
12.2764 -4.55913 moveto
/two glyphshow
18.5764 -4.55913 moveto
/five glyphshow
grestore
2.500 setlinewidth
1 setlinejoin
2 setlinecap
0.031 0.659 0.659 setrgbcolor
gsave
391.69375 406.763125 m
409.69375 406.763125 l
427.69375 406.763125 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
0 setlinecap
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

0 4 m
-4 -4 l
4 -4 l
cl

gsave
0.031 0.659 0.659 setrgbcolor
fill
grestore
stroke
grestore
} bind def
409.694 406.763 o
grestore
0.000 setgray
/TimesNewRomanPSMT 18.000 selectfont
gsave

442.094 400.463 translate
0 rotate
0 0 m /M glyphshow
grestore

end
showpage
