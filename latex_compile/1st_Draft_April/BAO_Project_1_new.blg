This is BibTeX, Version 0.99d (TeX Live 2017/Debian)
Capacity: max_strings=100000, hash_size=100000, hash_prime=85009
The top-level auxiliary file: BAO_Project_1_new.aux
The style file: apsrev4-2.bst
Reallocated singl_function (elt_size=4) to 100 items from 50.
Reallocated singl_function (elt_size=4) to 100 items from 50.
Reallocated singl_function (elt_size=4) to 100 items from 50.
Reallocated singl_function (elt_size=4) to 100 items from 50.
Reallocated singl_function (elt_size=4) to 100 items from 50.
Reallocated singl_function (elt_size=4) to 100 items from 50.
Reallocated singl_function (elt_size=4) to 100 items from 50.
Reallocated wiz_functions (elt_size=4) to 6000 items from 3000.
Reallocated singl_function (elt_size=4) to 100 items from 50.
Database file #1: BAO_Project_1_newNotes.bib
Database file #2: References.bib
Repeated entry---line 1463 of file References.bib
 : @article{mani_atomistic_2013
 :                             ,
I'm skipping whatever remains of this entry
Reallocated singl_function (elt_size=4) to 100 items from 50.
Reallocated wiz_functions (elt_size=4) to 9000 items from 6000.
apsrev4-2.bst 2019-01-14 (MD) hand-edited version of apsrev4-1.bst
Control: key (0) 
Control: author (72) initials jnrlst
Control: editor formatted (1) identically to author
Control: production of article title (-1) disabled
Control: page (0) single
Control: year (1) truncated
Control: production of eprint (0) enabled
You've used 27 entries,
            6079 wiz_defined-function locations,
            1879 strings with 22141 characters,
and the built_in function-call counts, 28328 in all, are:
= -- 2134
> -- 720
< -- 156
+ -- 227
- -- 168
* -- 4226
:= -- 2666
add.period$ -- 27
call.type$ -- 27
change.case$ -- 108
chr.to.int$ -- 26
cite$ -- 27
duplicate$ -- 2491
empty$ -- 1960
format.name$ -- 431
if$ -- 5839
int.to.chr$ -- 2
int.to.str$ -- 35
missing$ -- 408
newline$ -- 133
num.names$ -- 81
pop$ -- 1053
preamble$ -- 1
purify$ -- 135
quote$ -- 0
skip$ -- 1309
stack$ -- 0
substring$ -- 704
swap$ -- 2412
text.length$ -- 84
text.prefix$ -- 0
top$ -- 8
type$ -- 381
warning$ -- 0
while$ -- 81
width$ -- 0
write$ -- 268
(There was 1 error message)
