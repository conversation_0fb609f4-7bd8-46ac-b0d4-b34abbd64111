%%
%% This is file `ltxfront.sty',
%% generated with the docstrip utility.
%%
%% The original source files were:
%%
%% ltxfront.dtx  (with options: `package,options,kernel')
%% 
%% This is a generated file;
%% altering it directly is inadvisable;
%% instead, modify the original source file.
%% See the URL in the file README-LTXFRONT.tex.
%% 
%% License
%%    You may distribute this file under the conditions of the
%%    LaTeX Project Public License 1.3c or later
%%    (http://www.latex-project.org/lppl.txt).
%% 
%%    This file is distributed WITHOUT ANY WARRANTY;
%%    without even the implied warranty of MERCHANTABILITY
%%    or FITNESS FOR A PARTICULAR PURPOSE.
%% 
%%%  @LaTeX-file{
%%%     filename        = "ltxfront.dtx",
%%%     version         = "4.2f",
%%%     date            = "2022/06/05",
%%%     author          = "<PERSON> (mailto:arthur_ogawa at sbcglobal.net),
%%%                        commissioned by the American Physical Society.
%%%                        ",
%%%     copyright       = "Copyright (C) 1999, 2009 <PERSON>,
%%%                        distributed under the terms of the
%%%                        LaTeX Project Public License 1.3c, see
%%%                        ftp://ctan.tug.org/macros/latex/base/lppl.txt
%%%                        ",
%%%     address         = "Arthur Ogawa,
%%%                        USA",
%%%     telephone       = "",
%%%     FAX             = "",
%%%     email           = "mailto colon arthur_ogawa at sbcglobal.net",
%%%     codetable       = "ISO/ASCII",
%%%     keywords        = "latex, page grid, main vertical list",
%%%     supported       = "yes",
%%%     abstract        = "package to change page grid, MVL",
%%%  }
\NeedsTeXFormat{LaTeX2e}[1995/12/01]%
\ProvidesFile{%
ltxfront%
.sty%
}%
 [2022/06/05 4.2f frontmatter package (AO,DPC,MD)]% \fileversion
\def\package@name{ltxfront}%
\expandafter\PackageInfo\expandafter{\package@name}{%
 Title page macros for \protect\LaTeXe,
 by A. Ogawa (arthur_ogawa at sbcglobal.net)%
}%
\let\class@name\package@name
\RequirePackage{ltxutil}%
\DeclareOption{frontmatterverbose}{\@booleantrue\frontmatterverbose@sw}%
\@booleanfalse\frontmatterverbose@sw
\DeclareOption{inactive}{\@booleanfalse\frontmatter@syntax@sw}%
\@booleantrue\frontmatter@syntax@sw
\@booleanfalse\runinaddress@sw
\@booleantrue\@affils@sw
\@booleanfalse\groupauthors@sw
\DeclareOption{groupedaddress}{\clo@groupedaddress}%
\def\clo@groupedaddress{%
 \@booleantrue\groupauthors@sw
 \@booleantrue\@affils@sw
 \@booleanfalse\runinaddress@sw
}%
\DeclareOption{unsortedaddress}{\clo@unsortedaddress}%
\def\clo@unsortedaddress{%
 \@booleantrue\groupauthors@sw
 \@booleanfalse\@affils@sw
 \@booleanfalse\runinaddress@sw
}%
\DeclareOption{runinaddress}{\clo@runinaddress}%
\def\clo@runinaddress{%
 \@booleantrue\groupauthors@sw
 \@booleantrue\@affils@sw
 \@booleantrue\runinaddress@sw
}%
\DeclareOption{superscriptaddress}{\clo@superscriptaddress}%
\def\clo@superscriptaddress{%
 \@booleanfalse\groupauthors@sw
 \@booleantrue\@affils@sw
 \@booleanfalse\runinaddress@sw
}%
\DeclareOption*{\OptionNotUsed}%
\ProcessOptions*
\appdef\class@documenthook{\frontmatter@init}%
\let\frontmatter@init\@empty
\newcommand\frontmatter@title[2][]{%
 \def\@title{#2}%
 \def\@shorttitle{#1}%
 \let\@AF@join\@title@join
}%
\appdef\frontmatter@init{%
 \def\@title{\class@warn{No title}}%
 \let\@shorttitle\@empty
 \let\@title@aux\@title@aux@cleared
}%
\def\@title@join{\expandafter\@title@join@\@title@aux}%
\def\@title@join@#1#2{%
 \def\@title@aux{{\@join{\@separator}{#1}{#2}}}%
}%
\def\@title@aux@cleared{{}}%
\newcounter{affil}%
\newcounter{collab}%
\appdef\frontmatter@init{%
 \c@affil\z@
 \c@collab\z@
}%
\newcommand\frontmatter@author{% implicit #1
 \@author@def{}% implicit #2
}%
\def\collaboration{% implicit #1
 \@author@def{\@booleantrue\collaboration@sw}% implicit #2
}%
\appdef\frontmatter@init{%
 \@booleanfalse\collaboration@sw
}%
\def\@author@cleared{{}{}{}}%
\def\@author@gobble#1#2#3{}%
\def\@author@init{%
 \let\@author\@author@cleared
 \@booleanfalse\collaboration@sw
}%
\def\@authorclear@sw{\@ifx{\@author\@author@cleared}}%
\appdef\frontmatter@init{%
 \@author@init
}%
\def\@author@def#1#2{%
 \frontmatterverbose@sw{\typeout{\string\author\space\string\collaboration}}{}%
 \move@AU\move@AF\move@AUAF
 \let\@AF@join\@author@join
 #1%
 \def\@author{{#2}{}}%
}%
\def\@author@join@#1#2#3{%
  \def\@author{{#1}{\@join{\@separator}{#2}{#3}}}%
}%
\def\@author@join{\expandafter\@author@join@\@author}%
\def\move@AU{%
 \@authorclear@sw{}{%
  \collaboration@sw{%
   \advance\c@collab\@ne
   \@argswap{\CO@grp\CO@opr}%
  }{%
   \@argswap{\AU@grp\AU@opr}%
  }%
   {%
    \expandafter\@argswap@val
    \expandafter{\@author}%
     {\expandafter\@argswap@val\expandafter{\the\c@collab}{\add@AUCO@grp}}%
   }%
 }%
 \@author@init
}%
\def\add@AUCO@grp#1#2#3#4{%
 \appdef#3{#4{#1}#2}%
 \frontmatterverbose@sw{\say#3}{}%
}%
\def\@author@finish{%
 \frontmatterverbose@sw{\typeout{\string\@author@finish}}{}%
 \move@AU\move@AF
 \@ifx{\AU@grp\@empty}{%
  \@ifx{\CO@grp\@empty}%
 }{%
  \false@sw
 }%
 {}{%
  \@ifx{\AF@grp\@empty}{%
   \begingroup
    \let\href\@secondoftwo
    \let\AU@opr\@secondofthree
    \let\CO@opr\@secondofthree
    \let\footnote\@gobble
    \@ifx{\CO@grp\@empty}{%
     \class@warn{Assuming \string\noaffiliation\space for authors}%
     \frontmatterverbose@sw{\say\AU@grp}%
    }{%
     \class@warn{Assuming \string\noaffiliation\space for collaboration}%
     \frontmatterverbose@sw{\say\CO@grp}{}%
    }%
   \endgroup
   \@affil@none\move@AF
  }{}%
 }%
 \move@AUAF
}%
\def\@secondofthree#1#2#3{#2}%
\def\@join#1#2#3{%
  \@if@empty{#2}{#3}{#2#1#3}%
}%
\def\@separator{;\space}%
\let\surname\@firstofone
\let\firstname\@firstofone
\newcommand\frontmatter@and{\class@err{\protect\and\space is not supported}}
\def\cat@comma@active{\catcode`\,\active}%
{\cat@comma@active\gdef,{\active@comma}}%
\def\active@comma{,\penalty-300\relax}%
\newcommand\affiliation{%
 \frontmatterverbose@sw{\typeout{\string\affiliation}}{}%
 \move@AU\move@AF
 \begingroup
  \cat@comma@active
  \@affiliation
}%
\def\@affiliation#1{%
 \endgroup
 \let\@AF@join\@affil@join
 \@affil@def{#1}%
}%
\newcommand\frontmatter@noaffiliation{%
 \frontmatterverbose@sw{\typeout{\string\noaffiliation}}{}%
 \move@AU\move@AF
 \@affil@none\move@AF
 \move@AUAF
}%
\def\blankaffiliation{{}}%
\def\@affil@cleared{{{}}{}}%
\def\@affil@nil{{\relax}{}}%
\appdef\frontmatter@init{%
 \@affil@init
}%
\def\@affil@none{%
 \let\@affil\@affil@nil
}%
\def\@affil@init{%
 \let\@affil\@affil@cleared
}%
\def\@affilclear@sw{\@ifx{\@affil\@affil@cleared}}%
\def\@affil@def#1{%
 \def\@affil{{#1}{}}%
}%
\def\@affil@join@#1#2#3{%
  \def\@affil{{#1}{\@join{\@separator}{#2}{#3}}}%
}%
\def\@affil@join{\expandafter\@affil@join@\@affil}%
\def\move@AF{%
 \@affilclear@sw{}{%
  \@booleanfalse\temp@sw
  \let\@tempd\@empty
  \@affils@sw{%
    \expandafter\@affil@addr@def\expandafter\@tempa\@affil
    \def\AFF@opr{\@affil@match\@tempa}%
    \@AFF@list
  }{}\temp@sw
  {%
   \expandafter\@affil@aux@def\expandafter\@tempb\@affil
   \@ifx{\@tempb\@empty}{}{%
    \@ifx{\@tempb\@tempd}{}{%
     \class@warn{%
      Ancillary information for \@tempa\space must not be different!
      Please put all of it on the first instance%
     }%
    }%
   }%
  }%
  {%
   \@ifx{\@affil\@affil@nil}{%
    \def\@tempc{0}%
    \@argswap@val{0}%
   }{%
    \advance\c@affil\@ne
    \expandafter\def\expandafter\@tempc\expandafter{\the\c@affil}%
    \expandafter\@argswap@val\expandafter{\the\c@affil}%
   }%
   {%
    \expandafter\@argswap@val\expandafter{\the\c@collab}{%
     \expandafter\@argswap@val\expandafter{\@affil}{%
      \add@list@val@val@val\@AFF@list\AFF@opr
     }%
    }%
   }%
  }%
  \appdef@eval\AF@grp\@tempc
  \frontmatterverbose@sw{\say\AF@grp}{}%
  \@affil@init
 }%
}%
\def\@affil@addr@def#1#2#3{%
 \def#1{#2}%
}%
\def\@affil@aux@def#1#2#3{%
 \def#1{#3}%
}%
\def\add@list@val@val@val#1#2#3#4#5{%
 \appdef#1{#2{#5}{#4}#3}%
 \frontmatterverbose@sw{\say#1}{}%
}%
\def\@affil@match#1#2#3#4#5{%
 \temp@sw{}{%
  \def\@tempifx{#4}%
  \@ifx{\@tempifx#1}{%
   \groupauthors@sw{%
    \@ifnum{#3=\c@collab}{%
     \true@sw
    }{%
     \false@sw
    }%
   }{%
    \true@sw
   }%
  }{%
   \false@sw
  }%
  {%
   \@booleantrue\temp@sw
   \def\@tempc{#2}%
   \def\@tempd{#5}%
  }{%
  }%
 }%
}%
\def\move@AUAF{%
 \frontmatterverbose@sw{\say\AU@grp\say\AF@grp\say\CO@grp}{}%
 \@ifx{\AF@grp\@empty}{%
    \@ifx{\@empty\CO@grp}{%
    }{%
     \appdef     \@AAC@list{\AF@opr{{0}}}%
     \appdef@e   \@AAC@list{\CO@grp}%
     \appdef@e   \@AFG@list{\CO@grp}%
     \let\CO@grp\@empty
    }%
 }{%
      \appdef     \@AAC@list{\AF@opr}%
      \appdef@eval\@AAC@list{\AF@grp}%
      \appdef@e   \@AAC@list{\AU@grp}%
    \@ifx{\@empty\AU@grp}{%
     \@ifx{\@empty\CO@grp}%
    }{%
     \false@sw
    }%
    {%
    }{%
      \@booleanfalse\temp@sw
      \def\AFG@opr{\x@match\AF@grp}%
      \let\CO@opr\@author@gobble
      \@AFG@list
      \temp@sw{}{%
        \appdef     \@AFG@list{\AFG@opr}%
        \appdef@eval\@AFG@list{\AF@grp}%
      }%
     \@ifx{\@empty\CO@grp}{}{%
       \appdef@e   \@AAC@list{\CO@grp}%
       \appdef@e   \@AFG@list{\CO@grp}%
       \let\CO@grp\@empty
     }%
    }%
    \let\CO@grp\@empty
    \let\AU@grp\@empty
    \let\AF@grp\@empty
 }%
 \frontmatterverbose@sw{\say\@AAC@list\say\@AFG@list}{}%
}%
\appdef\frontmatter@init{%
 \let\AU@grp\@empty
 \let\CO@grp\@empty
 \let\AF@grp\@empty
 \let\@AAC@list\@empty
 \let\@AFG@list\@empty
 \let\@AFF@list\@empty
}%
\appdef\frontmatter@init{%
 \let\@AF@join\@AF@join@error
}%
\def\@AF@join@error#1{%
 \class@warn{%
  \string\email, \string\homepage, \string\thanks, or \string\altaffiliation\space
  appears in wrong context.
 }%
}%
\def\sanitize@url{%
 \@makeother\%%
 \@makeother\~%
 \@makeother\_%
}%
\newcommand*\email[1][]{\begingroup\sanitize@url\@email{#1}}%
\def\@email#1#2{%
 \endgroup
 \@AF@join{#1\href{mailto:#2}{#2}}%
}%
\newcommand*\homepage[1][]{\begingroup\sanitize@url\@homepage{#1}}%
\def\@homepage#1#2{%
 \endgroup
 \@AF@join{#1\href{#2}{#2}}%
}%
\appdef\class@documenthook{%
 \providecommand\href[1]{}%
}%
\def\frontmatter@thanks{% implicit #1
  \@AF@join
}%
\newcommand*\altaffiliation[2][]{%
  \@AF@join{#1#2}%
}%
\def\set@listcomma@list#1{%
  \expandafter\@reset@ac\expandafter#1#1{0}\@reset@ac{%
   \let\@listcomma\relax
  }{%
   \let\@listcomma\@listcomma@comma
  }%
}%
\def\set@listcomma@count#1{%
  \@ifnum{#1=\tw@}{%
    \let\@listcomma\relax
  }{%
    \let\@listcomma\@listcomma@comma
  }%
}%
\def\@reset@ac#1#2#3\@reset@ac{%
  \def#1{#3}%
  \@tempcnta#2\relax
  \@ifnum{#2=\tw@}%
}%
\def\@listand{\@ifnum{\@tempcnta=\tw@}{\andname\space}{}}%
\def\@listcomma@comma{\@ifnum{\@tempcnta>\@ne}{,}{}}%
\def\@listcomma@comma@UK{\@ifnum{\@tempcnta>\tw@}{,}{}}%
\def\@collaboration@gobble#1#2#3{}%
\def\doauthor#1#2#3{%
  \ignorespaces#1\unskip\@listcomma
  \begingroup
   #3%
  \@if@empty{#2}{\endgroup{}{}}{\endgroup{\comma@space}{}\frontmatter@footnote{#2}}%
  \space \@listand
}%
\def\x@match#1#2{%
 \temp@sw{}{%
  \def\@tempifx{#2}%
  \@ifx{\@tempifx#1}{%
    \@booleantrue\temp@sw
  }{%
  }%
 }%
}%
\def\y@match#1#2#3{%
 \temp@sw{}{%
  \def\@tempifx{#3}%
  \@ifx{\@tempifx#1}{%
    \@booleantrue\temp@sw
    \def\@tempb{#2}%
  }{%
  }%
 }%
}%
\def\frontmatter@footnote#1{%
 \begingroup
  \@booleanfalse\temp@sw
  \def\@tempa{#1}%
  \let\@tempb\@empty
  \def\@TBN@opr{\y@match\@tempa}%
  \@FMN@list
  \temp@sw{%
   \expandafter\frontmatter@footnotemark
   \expandafter{\@tempb}%
  }{%
   \stepcounter\@mpfn
   \expandafter\expandafter
   \expandafter\frontmatter@foot@mark
   \expandafter\expandafter
   \expandafter{%
   \expandafter \the\csname c@\@mpfn\endcsname
               }{#1}%
  }%
 \endgroup
}%
\def\frontmatter@foot@mark#1#2{%
 \frontmatter@footnotemark{#1}%
 \g@addto@macro\@FMN@list{\@TBN@opr{#1}{#2}}%
}%
\appdef\frontmatter@init{%
 \global\let\@FMN@list\@empty
}%
\def\frontmatter@footnotemark#1{%
 \leavevmode
 \ifhmode\edef\@x@sf{\the\spacefactor}\nobreak\fi
  \begingroup
   \hyper@linkstart {link}{frontmatter.#1}%
    \csname c@\@mpfn\endcsname#1\relax
    \def\@thefnmark{\frontmatter@thefootnote}%
    \@makefnmark
   \hyper@linkend
  \endgroup
 \ifhmode\spacefactor\@x@sf\fi
 \relax
}%
\def\keywords#1{%
  \aftermaketitle@chk{\keywords}%
  \gdef\@keywords{#1}%
}%
\appdef\frontmatter@init{%
 \let\@keywords\@empty
}%
\newcommand*\frontmatter@date[2][\Dated@name]{\def\@date{#1#2}}%
\def\@date{}%
\newcommand*\received[2][\Received@name]{\def\@received{#1#2}}%
\def\@received{}%
\newcommand*\revised[2][\Revised@name]{\def\@revised{#1#2}}%
\def\@revised{}%
\newcommand*\accepted[2][\Accepted@name]{\def\@accepted{#1#2}}%
\def\@accepted{}%
\newcommand*\published[2][\Published@name]{\def\@published{#1#2}}%
\def\@published{}%
\def\pacs#1{%
  \aftermaketitle@chk{\pacs}%
  \gdef\@pacs{#1}%
}%
\appdef\frontmatter@init{%
 \let\@pacs\@empty
}%
\def\preprint#1{\gappdef\@preprint{\preprint{#1}}}%
\appdef\frontmatter@init{%
 \let\@preprint\@empty
}%
\newbox\absbox
\def\toclevel@abstract{1}%
\def\addcontents@abstract{%
 \phantomsection
 \expandafter\def\csname Parent0\endcsname{section*.2}%
 \expandafter\@argswap@val\expandafter{\abstractname}{\addcontentsline{toc}{abstract}}%
}%
\newenvironment{frontmatter@abstract}{%
  \aftermaketitle@chk{\begin{abstract}}%
  \global\setbox\absbox\vbox\bgroup
   \color@begingroup
   \columnwidth\textwidth
   \hsize\columnwidth
   \@parboxrestore
   \def\@mpfn{mpfootnote}\def\thempfn{\thempfootnote}\c@mpfootnote\z@
   \let\@footnotetext\frontmatter@footnotetext
   \minipagefootnote@init
   \let\set@listindent\set@listindent@
   \let\@listdepth\@mplistdepth \@mplistdepth\z@
   \let@environment{description}{frontmatter@description}%
   \@minipagerestore
   \@setminipage
    \frontmatter@abstractheading
    \frontmatter@abstractfont
    \let\footnote\mini@note
    \expandafter\everypar\expandafter{\the\everypar\addcontents@abstract\everypar{}}%
}{%
    \par
    \unskip
    \minipagefootnote@here
    \@minipagefalse   %% added 24 May 89
    \color@endgroup
  \egroup
}%
\long\def\frontmatter@footnotetext#1{%
  \minipagefootnote@pick
    \set@footnotefont
    \set@footnotewidth
    \@parboxrestore
    \protected@edef\@currentlabel{\csname p@\@mpfn\endcsname\@thefnmark}%
    \color@begingroup
      \frontmatter@makefntext{%
        \rule\z@\footnotesep\ignorespaces#1\@finalstrut\strutbox\vadjust{\vskip\z@skip}%
      }%
    \color@endgroup
  \minipagefootnote@drop
}%
\def\ltx@no@footnote{%
 \let\ltx@xfootnote\ltx@no@xfootnote\let\ltx@yfootnote\ltx@no@yfootnote
 \let\ltx@xfootmark\ltx@no@xfootmark\let\ltx@yfootmark\ltx@no@yfootmark
 \let\ltx@xfoottext\ltx@no@xfoottext\let\ltx@yfoottext\ltx@no@yfoottext
}%
\def\ltx@no@xfootnote[#1]#2{\ltx@no@footwarn\footnote}%
\def\ltx@no@yfootnote#1{\ltx@no@footwarn\footnote}%
\def\ltx@no@xfootmark[#1]{\ltx@no@footwarn\footnotemark}%
\def\ltx@no@yfootmark{\ltx@no@footwarn\footnotemark}%
\def\ltx@no@xfoottext[#1]#2{\ltx@no@footwarn\footnotetext}%
\def\ltx@no@yfoottext#1{\ltx@no@footwarn\footnotetext}%
\def\ltx@no@footwarn#1{%
 \class@warn{%
  The \string#1\space command is not legal on the title page;
  using \string\thanks\space instead might suit you: consult the manual for details%
 }%
}%
\def\frontmatter@abstractheading{%
 \begingroup
  \centering\large
  \abstractname
  \par
 \endgroup
}%
\def\frontmatter@abstractfont{}%
\newenvironment{frontmatter@description}{%
 \list{}{%
  \leftmargin\z@
  \labelwidth\z@
  \itemindent\z@
  \let\makelabel\frontmatter@descriptionlabel
 }%
}{%
 \endlist
}%
\def\frontmatter@descriptionlabel#1{%
 \hspace\labelsep
 \normalfont\bfseries
 #1:%
}%
\def\frontmatter@abstractwidth{\textwidth}
\def\frontmatter@abstract@produce{%
  \par
  \preprintsty@sw{%
   \do@output@MVL{%
    \vskip\frontmatter@preabstractspace
    \vskip200\p@\@plus1fil
    \penalty-200\relax
    \vskip-200\p@\@plus-1fil
   }%
  }{%
   \addvspace{\frontmatter@preabstractspace}%
  }%
   \begingroup
    \dimen@\baselineskip
    \setbox\z@\vtop{\unvcopy\absbox}%
    \advance\dimen@-\ht\z@\advance\dimen@-\prevdepth
    \@ifdim{\dimen@>\z@}{\vskip\dimen@}{}%
   \endgroup
   \begingroup
    \prep@absbox
    \unvbox\absbox
    \post@absbox
   \endgroup
  \@ifx{\@empty\mini@notes}{}{\mini@notes\par}%
  \addvspace\frontmatter@postabstractspace
}%
\appdef\frontmatter@init{\let\mini@notes\@empty}%
\let\prep@absbox\@empty
\let\post@absbox\@empty
\def\frontmatter@preabstractspace{.5\baselineskip}
\def\frontmatter@postabstractspace{.5\baselineskip}
\newenvironment{frontmatter@titlepage}{%
      \twocolumn@sw{\onecolumngrid}{\newpage}%
      \thispagestyle{titlepage}%
      \setcounter{page}\@ne
}{%
     \twocolumn@sw{\twocolumngrid}{\newpage}%
     \twoside@sw{}{%
        \setcounter{page}\@ne
     }%
}%
\def\frontmatter@maketitle{%
  \@author@finish
  \title@column\titleblock@produce
  \suppressfloats[t]%
  \let\and\relax
  \let\affiliation\@gobble
  \let\author\@gobble
  \let\@AAC@list\@empty
  \let\@AFF@list\@empty
  \let\@AFG@list\@empty
  \let\@AF@join\@AF@join@error
  \let\email\@gobble
  \let\@address\@empty
  \let\maketitle\relax
  \let\thanks\@gobble
  \let\abstract\@undefined\let\endabstract\@undefined
  \titlepage@sw{%
   \vfil
   \clearpage
  }{}%
}%
\def\maketitle@Hy{%
  \let\Hy@saved@footnotemark\@footnotemark
  \let\Hy@saved@footnotetext\@footnotetext
  \let\@footnotemark\H@@footnotemark
  \let\@footnotetext\H@@footnotetext
  \@ifnextchar[%]
   \Hy@maketitle@optarg
   {%
    \HyOrg@maketitle
    \Hy@maketitle@end
   }%
}%
\appdef\class@documenthook{%
  \@ifx{\maketitle\maketitle@Hy}{%
   \class@info{Taking \string\maketitle\space back from hyperref}%
   \let\maketitle\frontmatter@maketitle
  }{%
  }%
}%
\def\titleblock@produce{%
 \begingroup
  \ltx@footnote@pop
  \def\@mpfn{mpfootnote}%
  \def\thempfn{\thempfootnote}%
  \c@mpfootnote\z@
  \let\@makefnmark\frontmatter@makefnmark
  \frontmatter@setup
  \thispagestyle{titlepage}\label{FirstPage}%
  \frontmatter@title@produce
  \groupauthors@sw{%
   \frontmatter@author@produce@group
  }{%
   \frontmatter@author@produce@script
  }%
  \frontmatter@RRAPformat{%
   \expandafter\produce@RRAP\expandafter{\@date}%
   \expandafter\produce@RRAP\expandafter{\@received}%
   \expandafter\produce@RRAP\expandafter{\@revised}%
   \expandafter\produce@RRAP\expandafter{\@accepted}%
   \expandafter\produce@RRAP\expandafter{\@published}%
  }%
  \frontmatter@abstract@produce
  \@ifx@empty\@pacs{}{%
   \@pacs@produce\@pacs
  }%
  \@ifx@empty\@keywords{}{%
   \@keywords@produce\@keywords
  }%
  \par
  \frontmatter@finalspace
 \endgroup
}%
\def\toclevel@title{0}%
\def\frontmatter@title@produce{%
 \begingroup
  \frontmatter@title@above
  \frontmatter@title@format
  \@title
  \unskip
  \phantomsection\expandafter\@argswap@val\expandafter{\@title}{\addcontentsline{toc}{title}}%
  \@ifx{\@title@aux\@title@aux@cleared}{}{%
   \expandafter\frontmatter@footnote\expandafter{\@title@aux}%
  }%
  \par
  \frontmatter@title@below
 \endgroup
}%
\appdef\let@mark{\let\\\relax}%
\def\frontmatter@title@above{}%
\def\frontmatter@title@format{}%
\def\frontmatter@title@below{\addvspace{\baselineskip}}%
\def\frontmatter@author@produce@script{%
  \begingroup
    \let\@author@present\@author@present@script
    \frontmatterverbose@sw{\typeout{\string\frontmatter@author@produce@script:}\say\@AAC@list\say\@AFF@list\say\@AFG@list}{}%
    \let\AU@temp\@empty
    \@tempcnta\z@
    \let\AF@opr \@gobble
    \def\AU@opr{\@author@count\@tempcnta}%
    \def\CO@opr{\@collaboration@count\AU@temp\@tempcnta}%
    \@AAC@list
    \expandafter\CO@opr\@author@cleared
    \begingroup
     \frontmatter@authorformat
     \let\AF@opr \@affilID@def
     \let\AU@opr \@author@present
     \def\CO@opr{\@collaboration@present\AU@temp}%
     \set@listcomma@list\AU@temp
     \@AAC@list
     \unskip\unskip
     \par
    \endgroup
    \begingroup
     \frontmatter@above@affiliation@script
     \let\AFF@opr \@affil@script
     \@AFF@list
     \frontmatter@footnote@produce
     \par
    \endgroup
  \endgroup
}%
\def\@author@count#1{%
 \advance#1\@ne
 \@author@gobble
}%
\def\@collaboration@present#1#2#3#4{%
 \par
 \begingroup
  \frontmatter@collaboration@above
  \@affilID@def{}%
  \@tempcnta\z@
  \@author@present{}{(\ignorespaces#3\unskip)}{#4}%
  \par
 \endgroup
 \set@listcomma@list#1%
}%
\def\frontmatter@collaboration@above{}%
\def\@collaboration@count#1#2{%
 \appdef@eval#1{\the#2}#2\z@
 \@author@gobble
}%
\def\@affilID@def{\def\@affilID@temp}%
\let\@affilID@temp\@empty
\def\affil@script#1#2#3{%
 \def\@tempifx{#1}\@ifx{\@tempifx\@tempa}{%
  \@if@empty{#2}{}{%
   \par
   \begingroup
    \def\@thefnmark{#1}\@makefnmark\ignorespaces
    #2%
    \@if@empty{#3}{}{\frontmatter@footnote{#3}}%
    \par
   \endgroup
  }%
 }{}%
}%
\def\@affil@script#1#2#3#4{%
 \@ifnum{#1=\z@}{}{%
  \par
  \begingroup
   \frontmatter@affiliationfont
   \@ifnum{\c@affil<\affil@cutoff}{}{%
    \def\@thefnmark{#1}\@makefnmark
   }%
   \ignorespaces#3%
   \@if@empty{#4}{}{\frontmatter@footnote{#4}}%
   \par
  \endgroup
 }%
}%
\let\affil@cutoff\@ne
\def\@author@present@script#1#2#3{%
 \begingroup
  \gdef\comma@space{\textsuperscript{,\,}}%
  \doauthor{#2}{#3}{\@affil@present@script}%
 \endgroup
 \advance\@tempcnta\m@ne
}%
\def\@affilcomma#1#2{%
 \@ifx{\z@#1}{%
  \@ifx{\relax#2}{}{%
   \@affilcomma{#2}%
  }%
 }{%
  #1%
  \@ifx{\relax#2}{}{%
   \@ifx{\z@#2}{%
    \@affilcomma
   }{%
    ,\,\@affilcomma{#2}%
   }%
  }%
 }%
}%
\def\@affil@present@script{%
 \let\@tempa\@empty
 \expandafter\@affil@present@script@\@affilID@temp\relax
}%
\def\@affil@present@script@#1{%
 \@ifx{\relax#1}{%
  \@ifx{\@tempa\@empty}{%
   \aftergroup\false@sw
  }{%
   \textsuperscript{\expandafter\@affilcomma\@tempa\relax\relax}%
   \aftergroup\true@sw
  }%
 }{%
  \@ifnum{#1=\z@}{}{\appdef\@tempa{{#1}}}%
  \@affil@present@script@
 }%
}%
\@provide\@author@parskip{\z@skip}%
\def\frontmatter@author@produce@group{%
  \begingroup
    \let\@author@present\@author@present@group
    \frontmatter@authorformat
    \frontmatterverbose@sw{\typeout{\string\frontmatter@author@produce@group:}\say\@AAC@list\say\@AFF@list\say\@AFG@list}{}%
    \let\AU@temp\@empty
    \set@listcomma@list\AU@temp
    \def\CO@opr{\@collaboration@present\AU@temp}%
    \let\AFG@opr \affils@present@group
    \let\@listcomma\relax
    \@AFG@list
    \frontmatter@footnote@produce
    \par
  \endgroup
  \frontmatter@authorbelow
}%
\@provide\frontmatter@authorbelow{}%
\def\affils@present@group#1{%
 \begingroup
   \def\AF@temp{#1}%
   \@tempcnta\z@
   \let\AU@opr \@undefined
   \let\CO@opr \@undefined
   \def\AF@opr{\@affilID@count\AF@temp\@tempcnta}%
   \@AAC@list
   \@ifnum{\@tempcnta=\z@}{}{%
    \begingroup
     \frontmatter@above@affilgroup
     \set@listcomma@count\@tempcnta
     \let\AU@opr \@undefined
     \let\CO@opr \@undefined
     \def\AF@opr{\@affilID@match\AF@temp}%
     \@AAC@list
    \endgroup
    \begingroup
     \par
     \frontmatter@above@affiliation
     \frontmatter@affiliationfont
     \let\\\frontmatter@addressnewline
     \@tempcnta\z@
     \@tfor\AF@temp:=#1\do{%
      \expandafter\@ifx\expandafter{\expandafter\z@\AF@temp}{}{%
       \advance\@tempcnta\@ne
      }%
     }%
     \@ifnum{\@tempcnta=\tw@}{%
      \let\@listcomma\relax
     }{}%
     \def@after@address
     \runinaddress@sw{%
     }{%
      \tightenlines@sw{}{%
       \parskip\z@
      }%
      \appdef\after@address\par
     }%
     \let\AFF@opr \@affil@group
     \do@affil@fromgroup\@AFF@list#1\relax
    \endgroup
   }%
   \par
 \endgroup
}%
\def\def@after@address{\def\after@address{\@listcomma\ \@listand}}%
\def\def@after@address@empty{\let\after@address\@empty}%
\def\@affilID@count#1#2#3{%
  \def\@tempifx{#3}%
  \@ifx{\@tempifx#1}{%
    \def\AU@opr{\@author@count#2}%
  }{%
    \let\AU@opr \@author@gobble
  }%
  \let\CO@opr \@collaboration@gobble
}%
\def\@affilID@match#1#2{%
 \def\@tempifx{#2}%
 \@ifx{\@tempifx#1}{%
   \let\AU@opr \@author@present
 }{%
   \let\AU@opr \@author@gobble
 }%
  \let\CO@opr \@collaboration@gobble
}%
\def\do@affil@fromgroup#1#2{%
  \@ifx{\relax#2}{}{%
    \count@#2\relax
    \@ifnum{\z@=\count@}{}{#1}%
    \do@affil@fromgroup#1%
  }%
}%
\def\@affil@group#1#2#3#4{%
  \@ifnum{#1=\count@}{%
   \def\@tempa{#3}%
   \@ifx{\@tempa\blankaffiliation}{}{%
    #3%
    \@if@empty{#4}{}{%
     \frontmatter@footnote{#4}%
    }%
    \after@address
   }%
   \advance\@tempcnta\m@ne
  }{}%
}%
\def\@author@present@group#1#2#3{%
  \gdef\comma@space{\gdef\comma@space{\textsuperscript{,\,}}}%
  \doauthor{#2}{#3}{\@affil@present@group}%
  \advance\@tempcnta\m@ne
}%
\def\@affil@present@group{%
 \aftergroup\false@sw
}%
\def\@pacs@produce#1{%
 \showPACS@sw{%
  \begingroup
   \frontmatter@PACS@format
   \@pacs@name#1\par
  \endgroup
 }{%
  \@if@empty{#1}{}{%
   \class@warn{\PACS@warn}%
  }%
 }%
}%
\def\PACS@warn{If you want your PACS to appear in your output, use document class option showpacs}%
\def\@keywords@produce#1{%
 \showKEYS@sw{%
  \begingroup
   \frontmatter@keys@format
   \@keys@name#1\par
  \endgroup
 }{%
  \@if@empty{#1}{}{%
   \class@warn{If you want your keywords to appear in your output, use document class option showkeys}%
  }%
 }%
}%
\def\frontmatter@footnote@produce@footnote{%
 \let\@TBN@opr\present@FM@footnote
 \@FMN@list
 \global\let\@FMN@list\@empty
}%
\def\present@FM@footnote#1#2{%
 \begingroup
  \csname c@\@mpfn\endcsname#1\relax
  \def\@thefnmark{\frontmatter@thefootnote}%
  \frontmatter@footnotetext{#2}%
 \endgroup
}%
\def\frontmatter@footnote@produce@endnote{%
}%
\appdef\frontmatter@init{%
 \@ifxundefined\title@column         {\let\title@column\@empty}{}%
 \@ifxundefined\preprintsty@sw       {\@booleanfalse\preprintsty@sw}{}%
 \@ifxundefined\frontmatter@footnote@produce{\let\frontmatter@footnote@produce\frontmatter@footnote@produce@footnote}{}%
 \@ifxundefined\do@output@MVL        {\let\do@output@MVL\@firstofone}{}%
 \@ifxundefined\comma@space          {\let\comma@space\@empty}{}%
}%
\def\frontmatter@thefootnote{%
 \altaffilletter@sw{\@alph}{\@fnsymbol}{\csname c@\@mpfn\endcsname}%
}%
\@ifx{\altaffilletter@sw\@undefined}{\@booleantrue\altaffilletter@sw}{}%
\def\frontmatter@makefnmark{%
 \@textsuperscript{%
  \normalfont\@thefnmark
 }%
}%
\long\def\frontmatter@makefntext#1{%
 \parindent 1em
 \noindent
 \Hy@raisedlink{\hyper@anchorstart{frontmatter.\expandafter\the\csname c@\@mpfn\endcsname}\hyper@anchorend}%
 \@makefnmark
 #1%
}%
\def\frontmatter@setup{}%
\def\frontmatter@RRAPformat#1{%
 \removelastskip
 \begingroup
  \frontmatter@RRAP@format
  #1\par
 \endgroup
}%
\def\punct@RRAP{; }%
\def\produce@RRAP#1{%
  \@if@empty{#1}{}{%
   \@ifvmode{\leavevmode}{\unskip\punct@RRAP\ignorespaces}%
   #1%
  }%
}%
\def\frontmatter@authorformat{}%
\def\frontmatter@above@affilgroup{}%
\def\frontmatter@above@affiliation{}%
\def\frontmatter@above@affiliation@script{}%
\def\frontmatter@affiliationfont{\itshape\selectfont}%
\def\frontmatter@RRAP@format{}%
\def\frontmatter@PACS@format{}%
\def\frontmatter@keys@format{}%
\def\frontmatter@finalspace{\addvspace{18\p@}}
\def\frontmatter@addressnewline{%
  \@ifhmode{\skip@\lastskip\unskip\unpenalty\break\hskip\skip@}{}%
  % was: \vskip-.5ex
}%
\def\frontmatter@preabstractspace{5.5\p@}
\def\frontmatter@postabstractspace{6.5\p@}
\def\aftermaketitle@chk#1{%
  \@ifx{\maketitle\relax}{%
    \class@err{\protect#1 must be used before \protect\maketitle}%
  }{}%
}%
\def\ps@titlepage{\ps@empty}%
\def\volumeyear#1{\gdef\@volumeyear{#1}}%
\def\@volumeyear{}%
\def\volumenumber#1{\gdef\@volumenumber{#1}}%
\def\@volumenumber{}%
\def\issuenumber#1{\gdef\@issuenumber{#1}}%
\def\@issuenumber{}%
\def\eid#1{\gdef\@eid{#1}}%
\def\@eid{}%
\def\startpage#1{\gdef\@startpage{#1}\c@page#1\relax}%
\def\@startpage{\pageref{FirstPage}}%
\def\endpage#1{\gdef\@endpage{#1}}%
\def\@endpage{\pageref{LastPage}}%
\def\print@toc#1{%
 \begingroup
  \expandafter\section
  \expandafter*%
  \expandafter{%
              \csname#1name\endcsname
              }%
  \let\appendix\appendix@toc
  \@starttoc{#1}%
 \endgroup
}%
\def\appendix@toc{}%
\def\Dated@name{Dated }%
\def\Received@name{Received }%
\def\Revised@name{Revised }%
\def\Accepted@name{Accepted }%
\def\Published@name{Published }%
\appdef\robustify@contents{%
 \let\thanks\@gobble\let\class@warn\@gobble
 \def\begin{\string\begin}\def\end{\string\end}%
}%
\@ifxundefined\frontmatter@syntax@sw{\@booleantrue\frontmatter@syntax@sw}{}%
\frontmatter@syntax@sw{%
 \let\title         \frontmatter@title
 \let\author        \frontmatter<AUTHOR>          \frontmatter@date
 \@ifxundefined\@maketitle{%
  \let\maketitle    \frontmatter@maketitle
  \@booleantrue     \titlepage@sw
 }{%
  \let\@maketitle   \frontmatter@maketitle
  \prepdef\maketitle\@author@finish
 }%
 \let\noaffiliation \frontmatter@noaffiliation
 \let\thanks@latex  \thanks
 \let\thanks        \frontmatter@thanks
 \let\and@latex     \and
 \let\and           \frontmatter@and
 \let@environment{titlepage}{frontmatter@titlepage}%
 \let@environment{abstract}{frontmatter@abstract}%
}{%
 \let\noaffiliation\@empty
}%
\def\thanks@latex#1{%
 \footnotemark
 \expandafter\expandafter
 \expandafter\gappdef
 \expandafter\expandafter
 \expandafter\@thanks
 \expandafter\expandafter
 \expandafter{%
 \expandafter\expandafter
 \expandafter\footnotetext
 \expandafter\expandafter
 \expandafter[%
 \expandafter\the\csname c@\@mpfn\endcsname]{#1}}%
}%
\@booleanfalse\altaffilletter@sw
\@if@sw\if@titlepage\fi{\@booleantrue}{\@booleanfalse}\titlepage@sw
\def\frontmatter@title@above{\newpage\null\vskip2em\relax}%
\def\frontmatter@title@format{\centering\LARGE\let\thanks\thanks@latex}%
\def\frontmatter@title@below{\vskip1.5em\relax}%
\def\frontmatter@authorformat{\centering\large\advance\baselineskip\p@\parskip11.5\p@\let\thanks\thanks@latex\let\and\and@space}%
\def\frontmatter@authorbelow{\vskip 1em\relax}%
\def\frontmatter@above@affiliation{}%
\def\frontmatter@above@affiliation@script{}%
\def\frontmatter@affiliationfont{\centering\itshape}%
\def\frontmatter@RRAP@format{\centering\large}%
\def\frontmatter@preabstractspace{1.5em}%
\long\def\frontmatter@footnotetext{%
 \expandafter\expandafter
 \expandafter\footnotetext
 \expandafter\expandafter
 \expandafter[%
 \expandafter\the\csname c@\@mpfn\endcsname]%
}%
\def\and@space{\\}%
\def\andname{and}%
\endinput
%%
%% End of file `ltxfront.sty'.
