%!PS-Adobe-3.0 EPSF-3.0
%%Title: Fig2_with_exp.eps
%%Creator: Mat<PERSON>lotlib v3.7.1, https://matplotlib.org/
%%CreationDate: Mon Nov 11 12:04:58 2024
%%Orientation: portrait
%%BoundingBox: -203 -18 816 811
%%HiResBoundingBox: -203.475000 -18.442812 815.475000 810.442812
%%EndComments
%%BeginProlog
/mpldict 11 dict def
mpldict begin
/_d { bind def } bind def
/m { moveto } _d
/l { lineto } _d
/r { rlineto } _d
/c { curveto } _d
/cl { closepath } _d
/ce { closepath eofill } _d
/box {
      m
      1 index 0 r
      0 exch r
      neg 0 r
      cl
    } _d
/clipbox {
      box
      clip
      newpath
    } _d
/sc { setcachedevice } _d
%!PS-TrueTypeFont-1.0-2.53740
%%Title: unknown
%%Creator: Converted from TrueType to type 42 by PPR
15 dict begin
/FontName /TimesNewRomanPSMT def
/PaintType 0 def
/FontMatrix[1 0 0 1 0 0]def
/FontBBox[-568 -307 2028 1007]def
/FontType 42 def
/Encoding StandardEncoding def
/FontInfo 10 dict dup begin
/FamilyName (unknown) def
/FullName (unknown) def
/Weight (unknown) def
/Version (unknown) def
/ItalicAngle 0.0 def
/isFixedPitch false def
/UnderlinePosition -223 def
/UnderlineThickness 100 def
end readonly def
/sfnts[<00010000000900800003001063767420F34DDA810000009C000007C46670676D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>]def
/CharStrings 49 dict dup begin
/.notdef 0 def
/Gamma 48 def
/space 3 def
/parenleft 4 def
/parenright 5 def
/comma 6 def
/period 7 def
/slash 8 def
/zero 9 def
/one 10 def
/two 11 def
/three 12 def
/four 13 def
/five 14 def
/six 15 def
/seven 16 def
/eight 17 def
/nine 18 def
/less 19 def
/mu 49 def
/greater 20 def
/C 21 def
/omega 50 def
/K 22 def
/L 23 def
/P 24 def
/R 25 def
/T 26 def
/bracketleft 27 def
/bracketright 28 def
/a 29 def
/b 30 def
/c 31 def
/d 32 def
/e 33 def
/f 34 def
/i 35 def
/l 36 def
/m 37 def
/n 38 def
/o 39 def
/p 40 def
/r 41 def
/s 42 def
/t 43 def
/u 44 def
/x 45 def
/y 46 def
/z 47 def
end readonly def

systemdict/resourcestatus known
 {42 /FontType resourcestatus
   {pop pop false}{true}ifelse}
 {true}ifelse
{/TrueDict where{pop}{(%%[ Error: no TrueType rasterizer ]%%)= flush}ifelse
/FontType 3 def
 /TrueState 271 string def
 TrueDict begin sfnts save
 72 0 matrix defaultmatrix dtransform dup
 mul exch dup mul add sqrt cvi 0 72 matrix
 defaultmatrix dtransform dup mul exch dup
 mul add sqrt cvi 3 -1 roll restore
 TrueState initer end
 /BuildGlyph{exch begin
  CharStrings dup 2 index known
    {exch}{exch pop /.notdef}ifelse
  get dup xcheck
    {currentdict systemdict begin begin exec end end}
    {TrueDict begin /bander load cvlit exch TrueState render end}
    ifelse
 end}bind def
 /BuildChar{
  1 index /Encoding get exch get
  1 index /BuildGlyph get exec
 }bind def
}if

FontName currentdict end definefont pop
end
%%EndProlog
mpldict begin
-203.475 -18.443 translate
1018.95 828.886 0 0 clipbox
gsave
0 0 m
1018.95 0 l
1018.95 828.885625 l
0 828.885625 l
cl
1.000 setgray
fill
grestore
gsave
93.95 487.02625 m
482.123913 487.02625 l
482.123913 787.32625 l
93.95 787.32625 l
cl
1.000 setgray
fill
grestore
2.500 setlinewidth
1 setlinejoin
0 setlinecap
[] 0 setdash
0.000 setgray
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
106.472 487.026 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
106.472 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

100.222 459.667 translate
0 rotate
0 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
181.602 487.026 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
181.602 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

162.852 459.667 translate
0 rotate
0 0 m /three glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
256.733 487.026 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
256.733 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

237.983 459.667 translate
0 rotate
0 0 m /six glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
331.863 487.026 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
331.863 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

313.113 459.667 translate
0 rotate
0 0 m /nine glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
406.993 487.026 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
406.993 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

381.993 459.667 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /two glyphshow
25 0 m /zero glyphshow
37.5 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 487.026 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

457.124 459.667 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /five glyphshow
25 0 m /zero glyphshow
37.5 0 m /zero glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
125.254 487.026 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
125.254 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
144.037 487.026 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
144.037 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
162.82 487.026 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
162.82 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
200.385 487.026 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
200.385 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
219.167 487.026 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
219.167 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
237.95 487.026 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
237.95 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
275.515 487.026 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
275.515 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
294.298 487.026 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
294.298 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
313.08 487.026 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
313.08 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
350.646 487.026 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
350.646 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
369.428 487.026 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
369.428 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
388.211 487.026 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
388.211 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
425.776 487.026 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
425.776 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
444.559 487.026 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
444.559 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
463.341 487.026 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
463.341 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

204.576 432.964 translate
0 rotate
0 0 m /T glyphshow
13.521 0 m /e glyphshow
24.6172 0 m /m glyphshow
44.063 0 m /p glyphshow
56.563 0 m /e glyphshow
67.6592 0 m /r glyphshow
75.9844 0 m /a glyphshow
87.0806 0 m /t glyphshow
94.0264 0 m /u glyphshow
106.526 0 m /r glyphshow
114.852 0 m /e glyphshow
125.948 0 m /space glyphshow
132.198 0 m /parenleft glyphshow
140.523 0 m /K glyphshow
158.577 0 m /parenright glyphshow
grestore
2.500 setlinewidth
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 500.67 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 500.67 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

40.2 491.99 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 555.644 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 555.644 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

40.2 546.964 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /one glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 610.619 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 610.619 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

40.2 601.939 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /two glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 665.593 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 665.593 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

40.2 656.913 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /three glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 720.568 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 720.568 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

40.2 711.888 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /four glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 775.542 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 775.542 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

40.2 766.862 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /five glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 489.675 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 489.675 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 511.665 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 511.665 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 522.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 522.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 533.654 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 533.654 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 544.649 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 544.649 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 566.639 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 566.639 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 577.634 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 577.634 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 588.629 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 588.629 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 599.624 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 599.624 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 621.614 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 621.614 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 632.608 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 632.608 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 643.603 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 643.603 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 654.598 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 654.598 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 676.588 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 676.588 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 687.583 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 687.583 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 698.578 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 698.578 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 709.573 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 709.573 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 731.563 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 731.563 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 742.557 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 742.557 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 753.552 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 753.552 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 764.547 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 764.547 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 786.537 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 786.537 o
grestore
gsave
25.2 580.676 translate
90 rotate
/TimesNewRomanPSMT 25.0 selectfont
0 0.640625 moveto
/less glyphshow
14.0991 0.640625 moveto
/u glyphshow
/TimesNewRomanPSMT 17.5 selectfont
26.9748 -5.8 moveto
/x glyphshow
35.7248 -5.8 moveto
/comma glyphshow
42.7458 -5.8 moveto
/y glyphshow
51.4958 -5.8 moveto
/comma glyphshow
58.5168 -5.8 moveto
/z glyphshow
/TimesNewRomanPSMT 25.0 selectfont
67.3576 0.640625 moveto
/parenleft glyphshow
75.6828 0.640625 moveto
/Gamma glyphshow
90.1359 0.640625 moveto
/parenright glyphshow
98.4611 0.640625 moveto
/greater glyphshow
grestore
0.050 setlinewidth
[0.185 0.08] 0 setdash
0.722 0.224 0.055 setrgbcolor
gsave
388.174 300.3 93.95 487.026 clipbox
477.115217 501.025613 m
472.106522 501.615709 l
467.097826 502.498929 l
462.08913 501.223246 l
457.080435 500.67625 l
452.071739 501.576128 l
447.063043 504.368722 l
442.054348 505.534236 l
437.045652 503.435475 l
432.036957 503.971806 l
427.028261 501.842754 l
422.019565 502.914042 l
417.01087 517.883103 l
412.002174 508.928032 l
406.993478 501.992834 l
401.984783 504.337497 l
396.976087 682.580265 l
391.967391 690.187251 l
386.958696 695.303837 l
381.95 700.139284 l
376.941304 703.951765 l
371.932609 707.130666 l
366.923913 710.207753 l
361.915217 712.924373 l
356.906522 715.416422 l
351.897826 717.895277 l
346.88913 720.090299 l
341.880435 722.099452 l
336.871739 724.0323 l
331.863043 726.151952 l
326.854348 727.864187 l
321.845652 729.637555 l
316.836957 731.376893 l
311.828261 733.195229 l
306.819565 734.462336 l
301.81087 736.074244 l
296.802174 737.532222 l
291.793478 738.87327 l
286.784783 740.201509 l
281.776087 741.593298 l
276.767391 742.800428 l
271.758696 744.067096 l
266.75 745.302648 l
261.741304 746.530448 l
256.732609 747.779633 l
251.723913 748.854385 l
246.715217 749.946783 l
241.706522 751.100863 l
236.697826 752.134493 l
231.68913 753.108421 l
226.680435 754.123361 l
221.671739 755.153693 l
216.663043 756.1611 l
211.654348 757.117986 l
206.645652 758.135344 l
201.636957 759.063809 l
196.628261 759.982762 l
191.619565 760.902651 l
186.61087 761.782682 l
181.602174 762.564365 l
176.593478 763.505748 l
171.584783 764.32157 l
166.576087 765.170376 l
161.567391 765.985593 l
156.558696 766.850122 l
151.55 767.631639 l
146.541304 768.404526 l
141.532609 769.197423 l
136.523913 770.011485 l
131.515217 770.729507 l
126.506522 771.448738 l
121.497826 772.195567 l
116.48913 772.946628 l
111.480435 773.67625 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
388.174 300.3 93.95 487.026 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

0 2 m
-2 -2 l
2 -2 l
cl

gsave
0.722 0.224 0.055 setrgbcolor
fill
grestore
stroke
grestore
} bind def
477.115 501.026 o
472.107 501.616 o
467.098 502.499 o
462.089 501.223 o
457.08 500.676 o
452.072 501.576 o
447.063 504.369 o
442.054 505.534 o
437.046 503.435 o
432.037 503.972 o
427.028 501.843 o
422.02 502.914 o
417.011 517.883 o
412.002 508.928 o
406.993 501.993 o
401.985 504.337 o
396.976 682.58 o
391.967 690.187 o
386.959 695.304 o
381.95 700.139 o
376.941 703.952 o
371.933 707.131 o
366.924 710.208 o
361.915 712.924 o
356.907 715.416 o
351.898 717.895 o
346.889 720.09 o
341.88 722.099 o
336.872 724.032 o
331.863 726.152 o
326.854 727.864 o
321.846 729.638 o
316.837 731.377 o
311.828 733.195 o
306.82 734.462 o
301.811 736.074 o
296.802 737.532 o
291.793 738.873 o
286.785 740.202 o
281.776 741.593 o
276.767 742.8 o
271.759 744.067 o
266.75 745.303 o
261.741 746.53 o
256.733 747.78 o
251.724 748.854 o
246.715 749.947 o
241.707 751.101 o
236.698 752.134 o
231.689 753.108 o
226.68 754.123 o
221.672 755.154 o
216.663 756.161 o
211.654 757.118 o
206.646 758.135 o
201.637 759.064 o
196.628 759.983 o
191.62 760.903 o
186.611 761.783 o
181.602 762.564 o
176.593 763.506 o
171.585 764.322 o
166.576 765.17 o
161.567 765.986 o
156.559 766.85 o
151.55 767.632 o
146.541 768.405 o
141.533 769.197 o
136.524 770.011 o
131.515 770.73 o
126.507 771.449 o
121.498 772.196 o
116.489 772.947 o
111.48 773.676 o
grestore
0.050 setlinewidth
1 setlinejoin
[0.185 0.08] 0 setdash
1.000 0.271 0.000 setrgbcolor
gsave
388.174 300.3 93.95 487.026 clipbox
477.115217 502.540435 m
472.106522 500.990044 l
467.097826 500.895818 l
462.08913 500.887737 l
457.080435 502.098275 l
452.071739 501.636654 l
447.063043 501.299826 l
442.054348 502.011526 l
437.045652 502.06749 l
432.036957 505.655235 l
427.028261 510.448352 l
422.019565 502.828007 l
417.01087 509.967655 l
412.002174 506.535432 l
406.993478 504.594282 l
401.984783 508.7387 l
396.976087 682.595163 l
391.967391 690.238487 l
386.958696 695.509167 l
381.95 700.112731 l
376.941304 703.827963 l
371.932609 707.312301 l
366.923913 710.215505 l
361.915217 712.993916 l
356.906522 715.457543 l
351.897826 717.83365 l
346.88913 720.124438 l
341.880435 722.241341 l
336.871739 724.285567 l
331.863043 726.056296 l
326.854348 727.930322 l
321.845652 729.656686 l
316.836957 731.291352 l
311.828261 732.958399 l
306.819565 734.592186 l
301.81087 736.067977 l
296.802174 737.427771 l
291.793478 738.846882 l
286.784783 740.217671 l
281.776087 741.650582 l
276.767391 742.932147 l
271.758696 744.099586 l
266.75 745.347727 l
261.741304 746.512416 l
256.732609 747.63637 l
251.723913 748.810955 l
246.715217 750.005606 l
241.706522 751.030056 l
236.697826 752.081553 l
231.68913 753.143935 l
226.680435 754.143481 l
221.671739 755.177552 l
216.663043 756.212831 l
211.654348 757.129916 l
206.645652 758.119237 l
201.636957 758.978653 l
196.628261 759.985511 l
191.619565 760.85059 l
186.61087 761.793018 l
181.602174 762.559527 l
176.593478 763.549893 l
171.584783 764.333884 l
166.576087 765.181756 l
161.567391 766.007637 l
156.558696 766.791959 l
151.55 767.631694 l
146.541304 768.435311 l
141.532609 769.196598 l
136.523913 769.984713 l
131.515217 770.719722 l
126.506522 771.459348 l
121.497826 772.226133 l
116.48913 772.947563 l
111.480435 773.669488 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
388.174 300.3 93.95 487.026 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-4 -4 m
4 -4 l
4 4 l
-4 4 l
cl

gsave
grestore
stroke
grestore
} bind def
477.115 502.54 o
472.107 500.99 o
467.098 500.896 o
462.089 500.888 o
457.08 502.098 o
452.072 501.637 o
447.063 501.3 o
442.054 502.012 o
437.046 502.067 o
432.037 505.655 o
427.028 510.448 o
422.02 502.828 o
417.011 509.968 o
412.002 506.535 o
406.993 504.594 o
401.985 508.739 o
396.976 682.595 o
391.967 690.238 o
386.959 695.509 o
381.95 700.113 o
376.941 703.828 o
371.933 707.312 o
366.924 710.216 o
361.915 712.994 o
356.907 715.458 o
351.898 717.834 o
346.889 720.124 o
341.88 722.241 o
336.872 724.286 o
331.863 726.056 o
326.854 727.93 o
321.846 729.657 o
316.837 731.291 o
311.828 732.958 o
306.82 734.592 o
301.811 736.068 o
296.802 737.428 o
291.793 738.847 o
286.785 740.218 o
281.776 741.651 o
276.767 742.932 o
271.759 744.1 o
266.75 745.348 o
261.741 746.512 o
256.733 747.636 o
251.724 748.811 o
246.715 750.006 o
241.707 751.03 o
236.698 752.082 o
231.689 753.144 o
226.68 754.143 o
221.672 755.178 o
216.663 756.213 o
211.654 757.13 o
206.646 758.119 o
201.637 758.979 o
196.628 759.986 o
191.62 760.851 o
186.611 761.793 o
181.602 762.56 o
176.593 763.55 o
171.585 764.334 o
166.576 765.182 o
161.567 766.008 o
156.559 766.792 o
151.55 767.632 o
146.541 768.435 o
141.533 769.197 o
136.524 769.985 o
131.515 770.72 o
126.507 771.459 o
121.498 772.226 o
116.489 772.948 o
111.48 773.669 o
grestore
0.050 setlinewidth
1 setlinejoin
[0.185 0.08] 0 setdash
1.000 0.514 0.000 setrgbcolor
gsave
388.174 300.3 93.95 487.026 clipbox
477.115217 500.721989 m
472.106522 502.19569 l
467.097826 500.984712 l
462.08913 503.171432 l
457.080435 503.205572 l
452.071739 501.259584 l
447.063043 501.12902 l
442.054348 502.54027 l
437.045652 501.450511 l
432.036957 502.458963 l
427.028261 512.211384 l
422.019565 501.447707 l
417.01087 507.815568 l
412.002174 501.742865 l
406.993478 518.832512 l
401.984783 505.723019 l
396.976087 682.484334 l
391.967391 690.236563 l
386.958696 695.290643 l
381.95 700.013942 l
376.941304 703.917626 l
371.932609 707.302901 l
366.923913 710.071526 l
361.915217 712.824484 l
356.906522 715.438467 l
351.897826 717.984445 l
346.88913 720.0334 l
341.880435 722.287079 l
336.871739 724.218883 l
331.863043 726.119572 l
326.854348 727.797283 l
321.845652 729.660094 l
316.836957 731.298334 l
311.828261 732.888142 l
306.819565 734.572615 l
301.81087 736.121247 l
296.802174 737.421559 l
291.793478 738.876019 l
286.784783 740.321848 l
281.776087 741.566581 l
276.767391 742.838856 l
271.758696 744.083203 l
266.75 745.255974 l
261.741304 746.55744 l
256.732609 747.637854 l
251.723913 748.824809 l
246.715217 749.935568 l
241.706522 751.093056 l
236.697826 752.164289 l
231.68913 753.040198 l
226.680435 754.144031 l
221.671739 755.222906 l
216.663043 756.266761 l
211.654348 757.154544 l
206.645652 758.109122 l
201.636957 759.024722 l
196.628261 759.923995 l
191.619565 760.86087 l
186.61087 761.77625 l
181.602174 762.604496 l
176.593478 763.543845 l
171.584783 764.403702 l
166.576087 765.153169 l
161.567391 766.037104 l
156.558696 766.822909 l
151.55 767.629825 l
146.541304 768.418984 l
141.532609 769.176587 l
136.523913 769.931992 l
131.515217 770.713125 l
126.506522 771.457479 l
121.497826 772.222944 l
116.48913 772.923814 l
111.480435 773.66597 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
388.174 300.3 93.95 487.026 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
477.115 500.722 o
472.107 502.196 o
467.098 500.985 o
462.089 503.171 o
457.08 503.206 o
452.072 501.26 o
447.063 501.129 o
442.054 502.54 o
437.046 501.451 o
432.037 502.459 o
427.028 512.211 o
422.02 501.448 o
417.011 507.816 o
412.002 501.743 o
406.993 518.833 o
401.985 505.723 o
396.976 682.484 o
391.967 690.237 o
386.959 695.291 o
381.95 700.014 o
376.941 703.918 o
371.933 707.303 o
366.924 710.072 o
361.915 712.824 o
356.907 715.438 o
351.898 717.984 o
346.889 720.033 o
341.88 722.287 o
336.872 724.219 o
331.863 726.12 o
326.854 727.797 o
321.846 729.66 o
316.837 731.298 o
311.828 732.888 o
306.82 734.573 o
301.811 736.121 o
296.802 737.422 o
291.793 738.876 o
286.785 740.322 o
281.776 741.567 o
276.767 742.839 o
271.759 744.083 o
266.75 745.256 o
261.741 746.557 o
256.733 747.638 o
251.724 748.825 o
246.715 749.936 o
241.707 751.093 o
236.698 752.164 o
231.689 753.04 o
226.68 754.144 o
221.672 755.223 o
216.663 756.267 o
211.654 757.155 o
206.646 758.109 o
201.637 759.025 o
196.628 759.924 o
191.62 760.861 o
186.611 761.776 o
181.602 762.604 o
176.593 763.544 o
171.585 764.404 o
166.576 765.153 o
161.567 766.037 o
156.559 766.823 o
151.55 767.63 o
146.541 768.419 o
141.533 769.177 o
136.524 769.932 o
131.515 770.713 o
126.507 771.457 o
121.498 772.223 o
116.489 772.924 o
111.48 773.666 o
grestore
2.500 setlinewidth
0 setlinejoin
2 setlinecap
0.000 setgray
gsave
93.95 487.02625 m
93.95 787.32625 l
stroke
grestore
gsave
482.123913 487.02625 m
482.123913 787.32625 l
stroke
grestore
gsave
93.95 487.02625 m
482.123913 487.02625 l
stroke
grestore
gsave
93.95 787.32625 m
482.123913 787.32625 l
stroke
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

274.162 804.326 translate
0 rotate
0 0 m /parenleft glyphshow
8.3252 0 m /a glyphshow
19.4214 0 m /parenright glyphshow
grestore
0.050 setlinewidth
1 setlinejoin
0 setlinecap
[0.185 0.08] 0 setdash
0.722 0.224 0.055 setrgbcolor
gsave
111.95 581.02625 m
131.95 581.02625 l
151.95 581.02625 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

0 2 m
-2 -2 l
2 -2 l
cl

gsave
0.722 0.224 0.055 setrgbcolor
fill
grestore
stroke
grestore
} bind def
131.95 581.026 o
grestore
0.000 setgray
gsave
167.95 574.026 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.546875 moveto
/less glyphshow
11.2793 0.546875 moveto
/u glyphshow
/TimesNewRomanPSMT 14.0 selectfont
21.5799 -4.60562 moveto
/x glyphshow
/TimesNewRomanPSMT 20.0 selectfont
29.4386 0.546875 moveto
/greater glyphshow
grestore
0.050 setlinewidth
1 setlinejoin
[0.185 0.08] 0 setdash
1.000 0.271 0.000 setrgbcolor
gsave
111.95 551.02625 m
131.95 551.02625 l
151.95 551.02625 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-4 -4 m
4 -4 l
4 4 l
-4 4 l
cl

gsave
grestore
stroke
grestore
} bind def
131.95 551.026 o
grestore
0.000 setgray
gsave
167.95 544.026 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.546875 moveto
/less glyphshow
11.2793 0.546875 moveto
/u glyphshow
/TimesNewRomanPSMT 14.0 selectfont
21.5799 -4.60562 moveto
/y glyphshow
/TimesNewRomanPSMT 20.0 selectfont
29.4386 0.546875 moveto
/greater glyphshow
grestore
0.050 setlinewidth
1 setlinejoin
[0.185 0.08] 0 setdash
1.000 0.514 0.000 setrgbcolor
gsave
111.95 518.02625 m
131.95 518.02625 l
151.95 518.02625 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
131.95 518.026 o
grestore
0.000 setgray
gsave
167.95 511.026 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.546875 moveto
/less glyphshow
11.2793 0.546875 moveto
/u glyphshow
/TimesNewRomanPSMT 14.0 selectfont
21.5799 -4.60562 moveto
/z glyphshow
/TimesNewRomanPSMT 20.0 selectfont
28.6525 0.546875 moveto
/greater glyphshow
grestore
gsave
598.576087 487.02625 m
986.75 487.02625 l
986.75 787.32625 l
598.576087 787.32625 l
cl
1.000 setgray
fill
grestore
0.031 0.659 0.659 setrgbcolor
gsave
388.174 300.3 598.576 487.026 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

-0 -12.247449 m
7.348469 0 l
0 12.247449 l
-7.348469 0 l
cl

gsave
0.031 0.659 0.659 setrgbcolor
fill
grestore
stroke
grestore
} bind def
654.423 506.828 o
685.727 555.012 o
710.771 626.714 o
735.814 653.674 o
grestore
0.031 0.416 0.416 setrgbcolor
gsave
388.174 300.3 598.576 487.026 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

8.660254 -0 m
-8.660254 8.660254 l
-8.660254 -8.660254 l
cl

gsave
0.031 0.416 0.416 setrgbcolor
fill
grestore
stroke
grestore
} bind def
686.228 666.867 o
grestore
0.031 0.235 0.235 setrgbcolor
gsave
388.174 300.3 598.576 487.026 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 8.660254 m
-1.944348 2.676166 l
-8.236391 2.676166 l
-3.146021 -1.022204 l
-5.09037 -7.006293 l
-0 -3.307923 l
5.09037 -7.006293 l
3.146021 -1.022204 l
8.236391 2.676166 l
1.944348 2.676166 l
cl

gsave
0.031 0.235 0.235 setrgbcolor
fill
grestore
stroke
grestore
} bind def
686.228 566.484 o
grestore
2.500 setlinewidth
0.000 setgray
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
611.098 487.026 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
611.098 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

604.848 459.667 translate
0 rotate
0 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
686.228 487.026 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
686.228 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

667.478 459.667 translate
0 rotate
0 0 m /three glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
761.359 487.026 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
761.359 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

742.609 459.667 translate
0 rotate
0 0 m /six glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
836.489 487.026 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
836.489 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

817.739 459.667 translate
0 rotate
0 0 m /nine glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
911.62 487.026 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
911.62 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

886.62 459.667 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /two glyphshow
25 0 m /zero glyphshow
37.5 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 487.026 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

961.75 459.667 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /five glyphshow
25 0 m /zero glyphshow
37.5 0 m /zero glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
629.88 487.026 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
629.88 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
648.663 487.026 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
648.663 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
667.446 487.026 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
667.446 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
705.011 487.026 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
705.011 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
723.793 487.026 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
723.793 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
742.576 487.026 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
742.576 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
780.141 487.026 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
780.141 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
798.924 487.026 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
798.924 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
817.707 487.026 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
817.707 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
855.272 487.026 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
855.272 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
874.054 487.026 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
874.054 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
892.837 487.026 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
892.837 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
930.402 487.026 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
930.402 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
949.185 487.026 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
949.185 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
967.967 487.026 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
967.967 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

709.202 432.964 translate
0 rotate
0 0 m /T glyphshow
13.521 0 m /e glyphshow
24.6172 0 m /m glyphshow
44.063 0 m /p glyphshow
56.563 0 m /e glyphshow
67.6592 0 m /r glyphshow
75.9844 0 m /a glyphshow
87.0806 0 m /t glyphshow
94.0264 0 m /u glyphshow
106.526 0 m /r glyphshow
114.852 0 m /e glyphshow
125.948 0 m /space glyphshow
132.198 0 m /parenleft glyphshow
140.523 0 m /K glyphshow
158.577 0 m /parenright glyphshow
grestore
2.500 setlinewidth
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 500.519 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 500.519 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

576.076 491.839 translate
0 rotate
0 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 557.88 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 557.88 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

563.576 549.2 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 615.242 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 615.242 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

563.576 606.562 translate
0 rotate
0 0 m /two glyphshow
12.5 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 672.603 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 672.603 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

563.576 663.923 translate
0 rotate
0 0 m /three glyphshow
12.5 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 729.965 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 729.965 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

563.576 721.285 translate
0 rotate
0 0 m /four glyphshow
12.5 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

563.576 778.647 translate
0 rotate
0 0 m /five glyphshow
12.5 0 m /zero glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 489.046 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 489.046 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 511.991 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 511.991 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 523.463 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 523.463 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 534.935 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 534.935 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 546.408 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 546.408 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 569.352 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 569.352 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 580.825 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 580.825 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 592.297 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 592.297 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 603.769 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 603.769 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 626.714 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 626.714 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 638.186 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 638.186 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 649.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 649.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 661.131 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 661.131 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 684.075 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 684.075 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 695.548 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 695.548 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 707.02 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 707.02 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 718.492 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 718.492 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 741.437 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 741.437 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 752.909 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 752.909 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 764.382 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 764.382 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 775.854 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 775.854 o
grestore
gsave
553.576 526.176 translate
90 rotate
/TimesNewRomanPSMT 25.0 selectfont
0 0.148438 moveto
/P glyphshow
13.9038 0.148438 moveto
/o glyphshow
26.4038 0.148438 moveto
/l glyphshow
33.3496 0.148438 moveto
/a glyphshow
44.4458 0.148438 moveto
/r glyphshow
52.771 0.148438 moveto
/i glyphshow
59.7168 0.148438 moveto
/z glyphshow
70.813 0.148438 moveto
/a glyphshow
81.9092 0.148438 moveto
/t glyphshow
88.855 0.148438 moveto
/i glyphshow
95.8008 0.148438 moveto
/o glyphshow
108.301 0.148438 moveto
/n glyphshow
120.801 0.148438 moveto
/space glyphshow
127.051 0.148438 moveto
/parenleft glyphshow
135.376 0.148438 moveto
/mu glyphshow
148.779 0.148438 moveto
/C glyphshow
165.454 0.148438 moveto
/slash glyphshow
172.4 0.148438 moveto
/c glyphshow
183.496 0.148438 moveto
/m glyphshow
/TimesNewRomanPSMT 17.5 selectfont
203.318 15.1766 moveto
/two glyphshow
/TimesNewRomanPSMT 25.0 selectfont
213.141 0.148438 moveto
/parenright glyphshow
grestore
0.050 setlinewidth
[0.185 0.08] 0 setdash
0.722 0.224 0.055 setrgbcolor
gsave
388.174 300.3 598.576 487.026 clipbox
981.741304 500.883026 m
976.732609 501.487303 l
971.723913 502.391852 l
966.715217 501.085518 l
961.706522 500.525314 l
956.697826 501.447038 l
951.68913 504.307649 l
946.680435 505.501792 l
941.671739 503.352024 l
936.663043 503.901597 l
931.654348 501.720407 l
926.645652 502.818014 l
921.636957 518.153498 l
916.628261 508.978045 l
911.619565 501.873621 l
906.61087 504.273684 l
901.602174 685.277102 l
896.593478 692.669685 l
891.584783 697.611306 l
886.576087 702.279883 l
881.567391 705.94182 l
876.558696 708.975597 l
871.55 711.91327 l
866.541304 714.494679 l
861.532609 716.856268 l
856.523913 719.208365 l
851.515217 721.282747 l
846.506522 723.166377 l
841.497826 724.980869 l
836.48913 726.98426 l
831.480435 728.583618 l
826.471739 730.244725 l
821.463043 731.876081 l
816.454348 733.583259 l
811.445652 734.741389 l
806.436957 736.250402 l
801.428261 737.607308 l
796.419565 738.843012 l
791.41087 740.072247 l
786.402174 741.359322 l
781.393478 742.473265 l
776.384783 743.640462 l
771.376087 744.782262 l
766.367391 745.911722 l
761.358696 747.066797 l
756.35 748.045604 l
751.341304 749.045885 l
746.332609 750.105619 l
741.323913 751.049367 l
736.315217 751.93463 l
731.306522 752.860192 l
726.297826 753.8001 l
721.28913 754.717524 l
716.280435 755.588363 l
711.271739 756.518228 l
706.263043 757.360698 l
701.254348 758.192966 l
696.245652 759.026163 l
691.236957 759.822199 l
686.228261 760.521797 l
681.219565 761.376994 l
676.21087 762.110701 l
671.202174 762.875986 l
666.193478 763.610213 l
661.184783 764.393838 l
656.176087 765.093345 l
651.167391 765.786978 l
646.158696 766.499291 l
641.15 767.233387 l
636.141304 767.872434 l
631.132609 768.513847 l
626.123913 769.181646 l
621.115217 769.854572 l
616.106522 770.506596 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
388.174 300.3 598.576 487.026 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

0 2 m
-2 -2 l
2 -2 l
cl

gsave
0.722 0.224 0.055 setrgbcolor
fill
grestore
stroke
grestore
} bind def
981.741 500.883 o
976.733 501.487 o
971.724 502.392 o
966.715 501.086 o
961.707 500.525 o
956.698 501.447 o
951.689 504.308 o
946.68 505.502 o
941.672 503.352 o
936.663 503.902 o
931.654 501.72 o
926.646 502.818 o
921.637 518.153 o
916.628 508.978 o
911.62 501.874 o
906.611 504.274 o
901.602 685.277 o
896.593 692.67 o
891.585 697.611 o
886.576 702.28 o
881.567 705.942 o
876.559 708.976 o
871.55 711.913 o
866.541 714.495 o
861.533 716.856 o
856.524 719.208 o
851.515 721.283 o
846.507 723.166 o
841.498 724.981 o
836.489 726.984 o
831.48 728.584 o
826.472 730.245 o
821.463 731.876 o
816.454 733.583 o
811.446 734.741 o
806.437 736.25 o
801.428 737.607 o
796.42 738.843 o
791.411 740.072 o
786.402 741.359 o
781.393 742.473 o
776.385 743.64 o
771.376 744.782 o
766.367 745.912 o
761.359 747.067 o
756.35 748.046 o
751.341 749.046 o
746.333 750.106 o
741.324 751.049 o
736.315 751.935 o
731.307 752.86 o
726.298 753.8 o
721.289 754.718 o
716.28 755.588 o
711.272 756.518 o
706.263 757.361 o
701.254 758.193 o
696.246 759.026 o
691.237 759.822 o
686.228 760.522 o
681.22 761.377 o
676.211 762.111 o
671.202 762.876 o
666.193 763.61 o
661.185 764.394 o
656.176 765.093 o
651.167 765.787 o
646.159 766.499 o
641.15 767.233 o
636.141 767.872 o
631.133 768.514 o
626.124 769.182 o
621.115 769.855 o
616.107 770.507 o
grestore
0.050 setlinewidth
1 setlinejoin
[0.185 0.08] 0 setdash
1.000 0.271 0.000 setrgbcolor
gsave
388.174 300.3 598.576 487.026 clipbox
981.741304 502.434056 m
976.732609 500.846633 l
971.723913 500.750165 l
966.715217 500.741907 l
961.706522 501.981761 l
956.697826 501.509034 l
951.68913 501.164066 l
946.680435 501.893159 l
941.671739 501.950582 l
936.663043 505.626261 l
931.654348 510.536903 l
926.645652 502.729868 l
921.636957 510.044231 l
916.628261 506.52718 l
911.619565 504.537752 l
906.61087 508.779623 l
901.602174 685.292233 l
896.593478 692.721633 l
891.584783 697.819229 l
886.576087 702.253025 l
881.567391 705.816713 l
876.558696 709.158988 l
871.55 711.921091 l
866.541304 714.564785 l
861.532609 716.897694 l
856.523913 719.146324 l
851.515217 721.317095 l
846.506522 723.309047 l
841.497826 725.235383 l
836.48913 726.888187 l
831.480435 728.650005 l
826.471739 730.263919 l
821.463043 731.790299 l
816.454348 733.345879 l
811.445652 734.871478 l
806.436957 736.244126 l
801.428261 737.502756 l
796.419565 738.816611 l
791.41087 740.088411 l
786.402174 741.416586 l
781.393478 742.604888 l
776.384783 743.672915 l
771.376087 744.827273 l
766.367391 745.893725 l
761.358696 746.92386 l
756.35 748.002289 l
751.341304 749.10453 l
746.332609 750.035051 l
741.323913 750.996624 l
736.315217 751.969999 l
731.306522 752.880225 l
726.297826 753.823846 l
721.28913 754.768994 l
716.280435 755.600228 l
711.271739 756.502212 l
706.263043 757.276054 l
701.254348 758.195697 l
696.245652 758.974447 l
691.236957 759.832463 l
686.228261 760.516994 l
681.219565 761.420806 l
676.21087 762.122919 l
671.202174 762.887274 l
666.193478 763.632073 l
661.184783 764.336179 l
656.176087 765.0934 l
651.167391 765.81748 l
646.158696 766.498474 l
641.15 767.206876 l
636.141304 767.862746 l
631.132609 768.524348 l
626.123913 769.21189 l
621.115217 769.855496 l
616.106522 770.499909 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
388.174 300.3 598.576 487.026 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-4 -4 m
4 -4 l
4 4 l
-4 4 l
cl

gsave
grestore
stroke
grestore
} bind def
981.741 502.434 o
976.733 500.847 o
971.724 500.75 o
966.715 500.742 o
961.707 501.982 o
956.698 501.509 o
951.689 501.164 o
946.68 501.893 o
941.672 501.951 o
936.663 505.626 o
931.654 510.537 o
926.646 502.73 o
921.637 510.044 o
916.628 506.527 o
911.62 504.538 o
906.611 508.78 o
901.602 685.292 o
896.593 692.722 o
891.585 697.819 o
886.576 702.253 o
881.567 705.817 o
876.559 709.159 o
871.55 711.921 o
866.541 714.565 o
861.533 716.898 o
856.524 719.146 o
851.515 721.317 o
846.507 723.309 o
841.498 725.235 o
836.489 726.888 o
831.48 728.65 o
826.472 730.264 o
821.463 731.79 o
816.454 733.346 o
811.446 734.871 o
806.437 736.244 o
801.428 737.503 o
796.42 738.817 o
791.411 740.088 o
786.402 741.417 o
781.393 742.605 o
776.385 743.673 o
771.376 744.827 o
766.367 745.894 o
761.359 746.924 o
756.35 748.002 o
751.341 749.105 o
746.333 750.035 o
741.324 750.997 o
736.315 751.97 o
731.307 752.88 o
726.298 753.824 o
721.289 754.769 o
716.28 755.6 o
711.272 756.502 o
706.263 757.276 o
701.254 758.196 o
696.246 758.974 o
691.237 759.832 o
686.228 760.517 o
681.22 761.421 o
676.211 762.123 o
671.202 762.887 o
666.193 763.632 o
661.185 764.336 o
656.176 765.093 o
651.167 765.817 o
646.159 766.498 o
641.15 767.207 o
636.141 767.863 o
631.133 768.524 o
626.124 769.212 o
621.115 769.855 o
616.107 770.5 o
grestore
0.050 setlinewidth
1 setlinejoin
[0.185 0.08] 0 setdash
1.000 0.514 0.000 setrgbcolor
gsave
388.174 300.3 598.576 487.026 clipbox
981.741304 500.572144 m
976.732609 502.081194 l
971.723913 500.841198 l
966.715217 503.080746 l
961.706522 503.115861 l
956.697826 501.12281 l
951.68913 500.989104 l
946.680435 502.4348 l
941.671739 501.318513 l
936.663043 502.351697 l
931.654348 512.343142 l
926.645652 501.315702 l
921.636957 507.839448 l
916.628261 501.617904 l
911.619565 519.119058 l
906.61087 505.692178 l
901.602174 685.17967 l
896.593478 692.719682 l
891.584783 697.597946 l
886.576087 702.153101 l
881.567391 705.907321 l
876.558696 709.149497 l
871.55 711.775837 l
866.541304 714.39398 l
861.532609 716.878477 l
856.523913 719.298135 l
851.515217 721.2255 l
846.506522 723.355037 l
841.497826 725.168371 l
836.48913 726.951739 l
831.480435 728.516457 l
826.471739 730.267339 l
821.463043 731.797301 l
816.454348 733.275459 l
811.445652 734.851871 l
806.436957 736.297471 l
801.428261 737.496538 l
796.419565 738.845762 l
791.41087 740.192598 l
786.402174 741.332614 l
781.393478 742.511664 l
776.384783 743.656551 l
771.376087 744.735659 l
766.367391 745.938663 l
761.358696 746.925341 l
756.35 748.016106 l
751.341304 749.034704 l
746.332609 750.097839 l
741.323913 751.079053 l
736.315217 751.866683 l
731.306522 752.880772 l
726.297826 753.868985 l
721.28913 754.822651 l
716.280435 755.624724 l
711.271739 756.492155 l
706.263043 757.321846 l
701.254348 758.13457 l
696.245652 758.984659 l
691.236957 759.815812 l
686.228261 760.561638 l
681.219565 761.414805 l
676.21087 762.192191 l
671.202174 762.858919 l
666.193478 763.661293 l
661.184783 764.366862 l
656.176087 765.091547 l
651.167391 765.801303 l
646.158696 766.478653 l
641.15 767.15467 l
636.141304 767.856215 l
631.132609 768.522498 l
626.123913 769.208735 l
621.115217 769.832004 l
616.106522 770.496429 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
388.174 300.3 598.576 487.026 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
981.741 500.572 o
976.733 502.081 o
971.724 500.841 o
966.715 503.081 o
961.707 503.116 o
956.698 501.123 o
951.689 500.989 o
946.68 502.435 o
941.672 501.319 o
936.663 502.352 o
931.654 512.343 o
926.646 501.316 o
921.637 507.839 o
916.628 501.618 o
911.62 519.119 o
906.611 505.692 o
901.602 685.18 o
896.593 692.72 o
891.585 697.598 o
886.576 702.153 o
881.567 705.907 o
876.559 709.149 o
871.55 711.776 o
866.541 714.394 o
861.533 716.878 o
856.524 719.298 o
851.515 721.225 o
846.507 723.355 o
841.498 725.168 o
836.489 726.952 o
831.48 728.516 o
826.472 730.267 o
821.463 731.797 o
816.454 733.275 o
811.446 734.852 o
806.437 736.297 o
801.428 737.497 o
796.42 738.846 o
791.411 740.193 o
786.402 741.333 o
781.393 742.512 o
776.385 743.657 o
771.376 744.736 o
766.367 745.939 o
761.359 746.925 o
756.35 748.016 o
751.341 749.035 o
746.333 750.098 o
741.324 751.079 o
736.315 751.867 o
731.307 752.881 o
726.298 753.869 o
721.289 754.823 o
716.28 755.625 o
711.272 756.492 o
706.263 757.322 o
701.254 758.135 o
696.246 758.985 o
691.237 759.816 o
686.228 760.562 o
681.22 761.415 o
676.211 762.192 o
671.202 762.859 o
666.193 763.661 o
661.185 764.367 o
656.176 765.092 o
651.167 765.801 o
646.159 766.479 o
641.15 767.155 o
636.141 767.856 o
631.133 768.522 o
626.124 769.209 o
621.115 769.832 o
616.107 770.496 o
grestore
2.500 setlinewidth
0 setlinejoin
2 setlinecap
0.000 setgray
gsave
598.576087 487.02625 m
598.576087 787.32625 l
stroke
grestore
gsave
986.75 487.02625 m
986.75 787.32625 l
stroke
grestore
gsave
598.576087 487.02625 m
986.75 487.02625 l
stroke
grestore
gsave
598.576087 787.32625 m
986.75 787.32625 l
stroke
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

778.085 804.326 translate
0 rotate
0 0 m /parenleft glyphshow
8.3252 0 m /b glyphshow
20.8252 0 m /parenright glyphshow
grestore
0.050 setlinewidth
1 setlinejoin
0 setlinecap
[0.185 0.08] 0 setdash
0.722 0.224 0.055 setrgbcolor
gsave
700.770856 570.72625 m
718.770856 570.72625 l
736.770856 570.72625 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

0 2 m
-2 -2 l
2 -2 l
cl

gsave
0.722 0.224 0.055 setrgbcolor
fill
grestore
stroke
grestore
} bind def
718.771 570.726 o
grestore
0.000 setgray
gsave
751.171 564.426 translate
0 rotate
/TimesNewRomanPSMT 18.0 selectfont
0 0.078125 moveto
/P glyphshow
/TimesNewRomanPSMT 12.6 selectfont
10.2812 -4.55913 moveto
/x glyphshow
grestore
0.050 setlinewidth
1 setlinejoin
[0.185 0.08] 0 setdash
1.000 0.271 0.000 setrgbcolor
gsave
700.770856 544.12625 m
718.770856 544.12625 l
736.770856 544.12625 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-4 -4 m
4 -4 l
4 4 l
-4 4 l
cl

gsave
grestore
stroke
grestore
} bind def
718.771 544.126 o
grestore
0.000 setgray
gsave
751.171 537.826 translate
0 rotate
/TimesNewRomanPSMT 18.0 selectfont
0 0.078125 moveto
/P glyphshow
/TimesNewRomanPSMT 12.6 selectfont
10.2812 -4.55913 moveto
/y glyphshow
grestore
0.050 setlinewidth
1 setlinejoin
[0.185 0.08] 0 setdash
1.000 0.514 0.000 setrgbcolor
gsave
700.770856 514.52625 m
718.770856 514.52625 l
736.770856 514.52625 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
718.771 514.526 o
grestore
0.000 setgray
gsave
751.171 508.226 translate
0 rotate
/TimesNewRomanPSMT 18.0 selectfont
0 0.078125 moveto
/P glyphshow
/TimesNewRomanPSMT 12.6 selectfont
10.2812 -4.55913 moveto
/z glyphshow
grestore
0.031 0.659 0.659 setrgbcolor
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

-0 -12.247449 m
7.348469 0 l
0 12.247449 l
-7.348469 0 l
cl

gsave
0.031 0.659 0.659 setrgbcolor
fill
grestore
stroke
grestore
} bind def
796.171 569.151 o
grestore
0.000 setgray
/TimesNewRomanPSMT 18.000 selectfont
gsave

828.571 564.426 translate
0 rotate
0 0 m /R glyphshow
12.0059 0 m /e glyphshow
19.9951 0 m /f glyphshow
25.9893 0 m /period glyphshow
30.4893 0 m /space glyphshow
34.9893 0 m /bracketleft glyphshow
40.9834 0 m /a glyphshow
48.9727 0 m /bracketright glyphshow
grestore
0.031 0.416 0.416 setrgbcolor
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

8.660254 -0 m
-8.660254 8.660254 l
-8.660254 -8.660254 l
cl

gsave
0.031 0.416 0.416 setrgbcolor
fill
grestore
stroke
grestore
} bind def
796.171 543.707 o
grestore
0.000 setgray
/TimesNewRomanPSMT 18.000 selectfont
gsave

828.571 538.982 translate
0 rotate
0 0 m /R glyphshow
12.0059 0 m /e glyphshow
19.9951 0 m /f glyphshow
25.9893 0 m /period glyphshow
30.4893 0 m /space glyphshow
34.9893 0 m /bracketleft glyphshow
40.9834 0 m /b glyphshow
49.9834 0 m /bracketright glyphshow
grestore
0.031 0.235 0.235 setrgbcolor
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 8.660254 m
-1.944348 2.676166 l
-8.236391 2.676166 l
-3.146021 -1.022204 l
-5.09037 -7.006293 l
-0 -3.307923 l
5.09037 -7.006293 l
3.146021 -1.022204 l
8.236391 2.676166 l
1.944348 2.676166 l
cl

gsave
0.031 0.235 0.235 setrgbcolor
fill
grestore
stroke
grestore
} bind def
796.171 518.264 o
grestore
0.000 setgray
/TimesNewRomanPSMT 18.000 selectfont
gsave

828.571 513.539 translate
0 rotate
0 0 m /R glyphshow
12.0059 0 m /e glyphshow
19.9951 0 m /f glyphshow
25.9893 0 m /period glyphshow
30.4893 0 m /space glyphshow
34.9893 0 m /bracketleft glyphshow
40.9834 0 m /c glyphshow
48.9727 0 m /bracketright glyphshow
grestore
gsave
93.95 66.60625 m
482.123913 66.60625 l
482.123913 366.90625 l
93.95 366.90625 l
cl
1.000 setgray
fill
grestore
2.500 setlinewidth
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
106.472 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
106.472 366.906 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

100.222 39.2469 translate
0 rotate
0 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
181.602 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
181.602 366.906 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

162.852 39.2469 translate
0 rotate
0 0 m /three glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
256.733 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
256.733 366.906 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

237.983 39.2469 translate
0 rotate
0 0 m /six glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
331.863 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
331.863 366.906 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

313.113 39.2469 translate
0 rotate
0 0 m /nine glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
406.993 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
406.993 366.906 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

381.993 39.2469 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /two glyphshow
25 0 m /zero glyphshow
37.5 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 366.906 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

457.124 39.2469 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /five glyphshow
25 0 m /zero glyphshow
37.5 0 m /zero glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
125.254 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
125.254 366.906 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
144.037 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
144.037 366.906 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
162.82 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
162.82 366.906 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
200.385 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
200.385 366.906 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
219.167 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
219.167 366.906 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
237.95 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
237.95 366.906 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
275.515 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
275.515 366.906 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
294.298 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
294.298 366.906 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
313.08 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
313.08 366.906 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
350.646 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
350.646 366.906 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
369.428 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
369.428 366.906 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
388.211 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
388.211 366.906 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
425.776 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
425.776 366.906 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
444.559 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
444.559 366.906 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
463.341 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
463.341 366.906 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

204.576 12.5437 translate
0 rotate
0 0 m /T glyphshow
13.521 0 m /e glyphshow
24.6172 0 m /m glyphshow
44.063 0 m /p glyphshow
56.563 0 m /e glyphshow
67.6592 0 m /r glyphshow
75.9844 0 m /a glyphshow
87.0806 0 m /t glyphshow
94.0264 0 m /u glyphshow
106.526 0 m /r glyphshow
114.852 0 m /e glyphshow
125.948 0 m /space glyphshow
132.198 0 m /parenleft glyphshow
140.523 0 m /K glyphshow
158.577 0 m /parenright glyphshow
grestore
2.500 setlinewidth
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 80.2352 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 80.2352 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

40.2 71.5555 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 171.405 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 171.405 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

40.2 162.725 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /two glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 262.575 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 262.575 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

40.2 253.895 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /four glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 353.744 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 353.744 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

40.2 345.065 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /six glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 103.028 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 103.028 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 125.82 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 125.82 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 148.612 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 148.612 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 194.197 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 194.197 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 216.99 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 216.99 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 239.782 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 239.782 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 285.367 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 285.367 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 308.16 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 308.16 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 330.952 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 330.952 o
grestore
gsave
25.2 173.756 translate
90 rotate
/TimesNewRomanPSMT 25.0 selectfont
0 0.6875 moveto
/less glyphshow
14.0991 0.6875 moveto
/omega glyphshow
/TimesNewRomanPSMT 17.5 selectfont
30.9299 -5.75313 moveto
/x glyphshow
39.6799 -5.75313 moveto
/comma glyphshow
46.7009 -5.75313 moveto
/y glyphshow
55.4509 -5.75313 moveto
/comma glyphshow
62.4719 -5.75313 moveto
/z glyphshow
/TimesNewRomanPSMT 25.0 selectfont
71.3127 0.6875 moveto
/greater glyphshow
grestore
0.050 setlinewidth
[0.185 0.08] 0 setdash
0.722 0.224 0.055 setrgbcolor
gsave
388.174 300.3 93.95 66.606 clipbox
477.115217 80.438909 m
472.106522 80.581817 l
467.097826 80.343272 l
462.08913 80.352616 l
457.080435 80.524882 l
452.071739 80.373266 l
447.063043 80.412241 l
442.054348 80.465712 l
437.045652 80.548723 l
432.036957 80.96514 l
427.028261 80.491286 l
422.019565 81.115251 l
417.01087 80.770128 l
412.002174 80.39109 l
406.993478 81.045871 l
401.984783 80.288843 l
396.976087 215.519555 l
391.967391 229.446326 l
386.958696 238.785937 l
381.95 246.423956 l
376.941304 252.569207 l
371.932609 257.781928 l
366.923913 262.774975 l
361.915217 267.221369 l
356.906522 271.117508 l
351.897826 274.705585 l
346.88913 278.209284 l
341.880435 281.417137 l
336.871739 284.297462 l
331.863043 287.273653 l
326.854348 290.013577 l
321.845652 292.604804 l
316.836957 295.010135 l
311.828261 297.37991 l
306.819565 299.594104 l
301.81087 301.784047 l
296.802174 303.784767 l
291.793478 305.874469 l
286.784783 307.786982 l
281.776087 309.661204 l
276.767391 311.402181 l
271.758696 313.233462 l
266.75 314.968742 l
261.741304 316.572873 l
256.732609 318.329349 l
251.723913 319.827223 l
246.715217 321.315022 l
241.706522 322.890572 l
236.697826 324.363464 l
231.68913 325.818534 l
226.680435 327.178968 l
221.671739 328.520167 l
216.663043 329.835655 l
211.654348 331.188021 l
206.645652 332.503418 l
201.636957 333.74474 l
196.628261 335.028227 l
191.619565 336.2747 l
186.61087 337.422983 l
181.602174 338.650948 l
176.593478 339.773111 l
171.584783 340.885291 l
166.576087 342.054999 l
161.567391 343.095519 l
156.558696 344.192291 l
151.55 345.284778 l
146.541304 346.274106 l
141.532609 347.332769 l
136.523913 348.397632 l
131.515217 349.375929 l
126.506522 350.363662 l
121.497826 351.319941 l
116.48913 352.307857 l
111.480435 353.25625 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
388.174 300.3 93.95 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

0 2 m
-2 -2 l
2 -2 l
cl

gsave
0.722 0.224 0.055 setrgbcolor
fill
grestore
stroke
grestore
} bind def
477.115 80.4389 o
472.107 80.5818 o
467.098 80.3433 o
462.089 80.3526 o
457.08 80.5249 o
452.072 80.3733 o
447.063 80.4122 o
442.054 80.4657 o
437.046 80.5487 o
432.037 80.9651 o
427.028 80.4913 o
422.02 81.1153 o
417.011 80.7701 o
412.002 80.3911 o
406.993 81.0459 o
401.985 80.2888 o
396.976 215.52 o
391.967 229.446 o
386.959 238.786 o
381.95 246.424 o
376.941 252.569 o
371.933 257.782 o
366.924 262.775 o
361.915 267.221 o
356.907 271.118 o
351.898 274.706 o
346.889 278.209 o
341.88 281.417 o
336.872 284.297 o
331.863 287.274 o
326.854 290.014 o
321.846 292.605 o
316.837 295.01 o
311.828 297.38 o
306.82 299.594 o
301.811 301.784 o
296.802 303.785 o
291.793 305.874 o
286.785 307.787 o
281.776 309.661 o
276.767 311.402 o
271.759 313.233 o
266.75 314.969 o
261.741 316.573 o
256.733 318.329 o
251.724 319.827 o
246.715 321.315 o
241.707 322.891 o
236.698 324.363 o
231.689 325.819 o
226.68 327.179 o
221.672 328.52 o
216.663 329.836 o
211.654 331.188 o
206.646 332.503 o
201.637 333.745 o
196.628 335.028 o
191.62 336.275 o
186.611 337.423 o
181.602 338.651 o
176.593 339.773 o
171.585 340.885 o
166.576 342.055 o
161.567 343.096 o
156.559 344.192 o
151.55 345.285 o
146.541 346.274 o
141.533 347.333 o
136.524 348.398 o
131.515 349.376 o
126.507 350.364 o
121.498 351.32 o
116.489 352.308 o
111.48 353.256 o
grestore
0.050 setlinewidth
1 setlinejoin
[0.185 0.08] 0 setdash
1.000 0.271 0.000 setrgbcolor
gsave
388.174 300.3 93.95 66.606 clipbox
477.115217 80.519867 m
472.106522 80.616872 l
467.097826 80.561532 l
462.08913 80.336571 l
457.080435 80.654753 l
452.071739 80.339761 l
447.063043 80.601008 l
442.054348 80.885093 l
437.045652 80.350292 l
432.036957 80.431341 l
427.028261 80.509383 l
422.019565 80.427877 l
417.01087 80.266005 l
412.002174 80.730789 l
406.993478 80.844158 l
401.984783 81.321477 l
396.976087 215.483133 l
391.967391 229.492959 l
386.958696 238.804718 l
381.95 246.429289 l
376.941304 252.500647 l
371.932609 257.989612 l
366.923913 262.81974 l
361.915217 267.179386 l
356.906522 271.09271 l
351.897826 274.735625 l
346.88913 278.146696 l
341.880435 281.438789 l
336.871739 284.486001 l
331.863043 287.221914 l
326.854348 289.959514 l
321.845652 292.614559 l
316.836957 294.927763 l
311.828261 297.339887 l
306.819565 299.666675 l
301.81087 301.724513 l
296.802174 303.76945 l
291.793478 305.849716 l
286.784783 307.698 l
281.776087 309.784009 l
276.767391 311.45041 l
271.758696 313.23287 l
266.75 314.941163 l
261.741304 316.60068 l
256.732609 318.185666 l
251.723913 319.865013 l
246.715217 321.419639 l
241.706522 322.848542 l
236.697826 324.318518 l
231.68913 325.815343 l
226.680435 327.207869 l
221.671739 328.572954 l
216.663043 329.894414 l
211.654348 331.191212 l
206.645652 332.457286 l
201.636957 333.744877 l
196.628261 335.008717 l
191.619565 336.200078 l
186.61087 337.43479 l
181.602174 338.560827 l
176.593478 339.733224 l
171.584783 340.897462 l
166.576087 342.079614 l
161.567391 343.143657 l
156.558696 344.186547 l
151.55 345.266043 l
146.541304 346.326392 l
141.532609 347.372519 l
136.523913 348.347625 l
131.515217 349.37014 l
126.506522 350.365121 l
121.497826 351.353993 l
116.48913 352.319481 l
111.480435 353.250552 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
388.174 300.3 93.95 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-4 -4 m
4 -4 l
4 4 l
-4 4 l
cl

gsave
grestore
stroke
grestore
} bind def
477.115 80.5199 o
472.107 80.6169 o
467.098 80.5615 o
462.089 80.3366 o
457.08 80.6548 o
452.072 80.3398 o
447.063 80.601 o
442.054 80.8851 o
437.046 80.3503 o
432.037 80.4313 o
427.028 80.5094 o
422.02 80.4279 o
417.011 80.266 o
412.002 80.7308 o
406.993 80.8442 o
401.985 81.3215 o
396.976 215.483 o
391.967 229.493 o
386.959 238.805 o
381.95 246.429 o
376.941 252.501 o
371.933 257.99 o
366.924 262.82 o
361.915 267.179 o
356.907 271.093 o
351.898 274.736 o
346.889 278.147 o
341.88 281.439 o
336.872 284.486 o
331.863 287.222 o
326.854 289.96 o
321.846 292.615 o
316.837 294.928 o
311.828 297.34 o
306.82 299.667 o
301.811 301.725 o
296.802 303.769 o
291.793 305.85 o
286.785 307.698 o
281.776 309.784 o
276.767 311.45 o
271.759 313.233 o
266.75 314.941 o
261.741 316.601 o
256.733 318.186 o
251.724 319.865 o
246.715 321.42 o
241.707 322.849 o
236.698 324.319 o
231.689 325.815 o
226.68 327.208 o
221.672 328.573 o
216.663 329.894 o
211.654 331.191 o
206.646 332.457 o
201.637 333.745 o
196.628 335.009 o
191.62 336.2 o
186.611 337.435 o
181.602 338.561 o
176.593 339.733 o
171.585 340.897 o
166.576 342.08 o
161.567 343.144 o
156.559 344.187 o
151.55 345.266 o
146.541 346.326 o
141.533 347.373 o
136.524 348.348 o
131.515 349.37 o
126.507 350.365 o
121.498 351.354 o
116.489 352.319 o
111.48 353.251 o
grestore
0.050 setlinewidth
1 setlinejoin
[0.185 0.08] 0 setdash
1.000 0.514 0.000 setrgbcolor
gsave
388.174 300.3 93.95 66.606 clipbox
477.115217 80.263635 m
472.106522 80.425552 l
467.097826 80.347876 l
462.08913 80.451034 l
457.080435 80.511252 l
452.071739 80.41329 l
447.063043 80.334109 l
442.054348 80.917459 l
437.045652 80.740908 l
432.036957 80.353802 l
427.028261 80.323214 l
422.019565 80.25625 l
417.01087 81.124004 l
412.002174 80.576985 l
406.993478 80.786493 l
401.984783 81.383108 l
396.976087 215.44858 l
391.967391 229.588642 l
386.958696 238.648954 l
381.95 246.406269 l
376.941304 252.485467 l
371.932609 257.907651 l
366.923913 262.721459 l
361.915217 267.100569 l
356.906522 271.152381 l
351.897826 274.851593 l
346.88913 278.097737 l
341.880435 281.442801 l
336.871739 284.478571 l
331.863043 287.336241 l
326.854348 289.990147 l
321.845652 292.483366 l
316.836957 294.972983 l
311.828261 297.22442 l
306.819565 299.570628 l
301.81087 301.696022 l
296.802174 303.771684 l
291.793478 305.846388 l
286.784783 307.761591 l
281.776087 309.660885 l
276.767391 311.438923 l
271.758696 313.277862 l
266.75 314.878939 l
261.741304 316.620099 l
256.732609 318.174634 l
251.723913 319.810539 l
246.715217 321.333712 l
241.706522 322.879312 l
236.697826 324.3409 l
231.68913 325.734156 l
226.680435 327.166934 l
221.671739 328.522218 l
216.663043 329.941822 l
211.654348 331.23625 l
206.645652 332.483042 l
201.636957 333.785174 l
196.628261 334.958072 l
191.619565 336.248307 l
186.61087 337.382823 l
181.602174 338.63431 l
176.593478 339.757111 l
171.584783 340.930192 l
166.576087 341.998337 l
161.567391 343.106277 l
156.558696 344.199584 l
151.55 345.249222 l
146.541304 346.310073 l
141.532609 347.344029 l
136.523913 348.328343 l
131.515217 349.374607 l
126.506522 350.361337 l
121.497826 351.347748 l
116.48913 352.29295 l
111.480435 353.247999 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
388.174 300.3 93.95 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
477.115 80.2636 o
472.107 80.4256 o
467.098 80.3479 o
462.089 80.451 o
457.08 80.5113 o
452.072 80.4133 o
447.063 80.3341 o
442.054 80.9175 o
437.046 80.7409 o
432.037 80.3538 o
427.028 80.3232 o
422.02 80.2562 o
417.011 81.124 o
412.002 80.577 o
406.993 80.7865 o
401.985 81.3831 o
396.976 215.449 o
391.967 229.589 o
386.959 238.649 o
381.95 246.406 o
376.941 252.485 o
371.933 257.908 o
366.924 262.721 o
361.915 267.101 o
356.907 271.152 o
351.898 274.852 o
346.889 278.098 o
341.88 281.443 o
336.872 284.479 o
331.863 287.336 o
326.854 289.99 o
321.846 292.483 o
316.837 294.973 o
311.828 297.224 o
306.82 299.571 o
301.811 301.696 o
296.802 303.772 o
291.793 305.846 o
286.785 307.762 o
281.776 309.661 o
276.767 311.439 o
271.759 313.278 o
266.75 314.879 o
261.741 316.62 o
256.733 318.175 o
251.724 319.811 o
246.715 321.334 o
241.707 322.879 o
236.698 324.341 o
231.689 325.734 o
226.68 327.167 o
221.672 328.522 o
216.663 329.942 o
211.654 331.236 o
206.646 332.483 o
201.637 333.785 o
196.628 334.958 o
191.62 336.248 o
186.611 337.383 o
181.602 338.634 o
176.593 339.757 o
171.585 340.93 o
166.576 341.998 o
161.567 343.106 o
156.559 344.2 o
151.55 345.249 o
146.541 346.31 o
141.533 347.344 o
136.524 348.328 o
131.515 349.375 o
126.507 350.361 o
121.498 351.348 o
116.489 352.293 o
111.48 353.248 o
grestore
2.500 setlinewidth
0 setlinejoin
2 setlinecap
0.000 setgray
gsave
93.95 66.60625 m
93.95 366.90625 l
stroke
grestore
gsave
482.123913 66.60625 m
482.123913 366.90625 l
stroke
grestore
gsave
93.95 66.60625 m
482.123913 66.60625 l
stroke
grestore
gsave
93.95 366.90625 m
482.123913 366.90625 l
stroke
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

274.162 383.906 translate
0 rotate
0 0 m /parenleft glyphshow
8.3252 0 m /c glyphshow
19.4214 0 m /parenright glyphshow
grestore
0.050 setlinewidth
1 setlinejoin
0 setlinecap
[0.185 0.08] 0 setdash
0.722 0.224 0.055 setrgbcolor
gsave
111.95 160.60625 m
131.95 160.60625 l
151.95 160.60625 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

0 2 m
-2 -2 l
2 -2 l
cl

gsave
0.722 0.224 0.055 setrgbcolor
fill
grestore
stroke
grestore
} bind def
131.95 160.606 o
grestore
0.000 setgray
gsave
167.95 153.606 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
3.024 0.546875 moveto
/less glyphshow
17.3273 0.546875 moveto
/omega glyphshow
/TimesNewRomanPSMT 14.0 selectfont
30.7919 -4.60562 moveto
/x glyphshow
/TimesNewRomanPSMT 20.0 selectfont
41.6747 0.546875 moveto
/greater glyphshow
grestore
0.050 setlinewidth
1 setlinejoin
[0.185 0.08] 0 setdash
1.000 0.271 0.000 setrgbcolor
gsave
111.95 130.60625 m
131.95 130.60625 l
151.95 130.60625 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-4 -4 m
4 -4 l
4 4 l
-4 4 l
cl

gsave
grestore
stroke
grestore
} bind def
131.95 130.606 o
grestore
0.000 setgray
gsave
167.95 123.606 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
3.024 0.546875 moveto
/less glyphshow
17.3273 0.546875 moveto
/omega glyphshow
/TimesNewRomanPSMT 14.0 selectfont
30.7919 -4.60562 moveto
/y glyphshow
/TimesNewRomanPSMT 20.0 selectfont
38.6507 0.546875 moveto
/greater glyphshow
grestore
0.050 setlinewidth
1 setlinejoin
[0.185 0.08] 0 setdash
1.000 0.514 0.000 setrgbcolor
gsave
111.95 97.60625 m
131.95 97.60625 l
151.95 97.60625 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
131.95 97.6062 o
grestore
0.000 setgray
gsave
167.95 90.6062 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
3.024 0.546875 moveto
/less glyphshow
17.3273 0.546875 moveto
/omega glyphshow
/TimesNewRomanPSMT 14.0 selectfont
30.7919 -4.60562 moveto
/z glyphshow
/TimesNewRomanPSMT 20.0 selectfont
40.8885 0.546875 moveto
/greater glyphshow
grestore
gsave
598.576087 66.60625 m
986.75 66.60625 l
986.75 366.90625 l
598.576087 366.90625 l
cl
1.000 setgray
fill
grestore
0 setlinejoin
0.902 0.902 0.980 setrgbcolor
gsave
388.174 300.3 598.576 66.606 clipbox
598.576087 66.60625 m
598.576087 366.90625 l
906.61087 366.90625 l
906.61087 66.60625 l
cl
gsave
fill
grestore
stroke
grestore
0.800 setgray
gsave
388.174 300.3 598.576 66.606 clipbox
906.61087 66.60625 m
906.61087 366.90625 l
986.75 366.90625 l
986.75 66.60625 l
cl
gsave
fill
grestore
stroke
grestore
2.500 setlinewidth
1 setlinejoin
0.000 setgray
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
611.098 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
611.098 366.906 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

604.848 39.2469 translate
0 rotate
0 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
686.228 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
686.228 366.906 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

667.478 39.2469 translate
0 rotate
0 0 m /three glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
761.359 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
761.359 366.906 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

742.609 39.2469 translate
0 rotate
0 0 m /six glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
836.489 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
836.489 366.906 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

817.739 39.2469 translate
0 rotate
0 0 m /nine glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
911.62 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
911.62 366.906 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

886.62 39.2469 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /two glyphshow
25 0 m /zero glyphshow
37.5 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 366.906 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

961.75 39.2469 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /five glyphshow
25 0 m /zero glyphshow
37.5 0 m /zero glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
629.88 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
629.88 366.906 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
648.663 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
648.663 366.906 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
667.446 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
667.446 366.906 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
705.011 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
705.011 366.906 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
723.793 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
723.793 366.906 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
742.576 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
742.576 366.906 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
780.141 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
780.141 366.906 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
798.924 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
798.924 366.906 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
817.707 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
817.707 366.906 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
855.272 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
855.272 366.906 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
874.054 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
874.054 366.906 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
892.837 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
892.837 366.906 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
930.402 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
930.402 366.906 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
949.185 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
949.185 366.906 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
967.967 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
967.967 366.906 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

709.202 12.5437 translate
0 rotate
0 0 m /T glyphshow
13.521 0 m /e glyphshow
24.6172 0 m /m glyphshow
44.063 0 m /p glyphshow
56.563 0 m /e glyphshow
67.6592 0 m /r glyphshow
75.9844 0 m /a glyphshow
87.0806 0 m /t glyphshow
94.0264 0 m /u glyphshow
106.526 0 m /r glyphshow
114.852 0 m /e glyphshow
125.948 0 m /space glyphshow
132.198 0 m /parenleft glyphshow
140.523 0 m /K glyphshow
158.577 0 m /parenright glyphshow
grestore
2.500 setlinewidth
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 66.6063 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

544.826 57.9266 translate
0 rotate
0 0 m /seven glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /eight glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 126.666 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 126.666 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

544.826 117.987 translate
0 rotate
0 0 m /seven glyphshow
12.5 0 m /period glyphshow
18.75 0 m /one glyphshow
31.25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 186.726 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 186.726 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

544.826 178.047 translate
0 rotate
0 0 m /seven glyphshow
12.5 0 m /period glyphshow
18.75 0 m /one glyphshow
31.25 0 m /two glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 246.786 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 246.786 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

544.826 238.107 translate
0 rotate
0 0 m /seven glyphshow
12.5 0 m /period glyphshow
18.75 0 m /one glyphshow
31.25 0 m /four glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 306.846 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 306.846 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

544.826 298.167 translate
0 rotate
0 0 m /seven glyphshow
12.5 0 m /period glyphshow
18.75 0 m /one glyphshow
31.25 0 m /six glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 366.906 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 366.906 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

544.826 358.227 translate
0 rotate
0 0 m /seven glyphshow
12.5 0 m /period glyphshow
18.75 0 m /one glyphshow
31.25 0 m /eight glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 81.6213 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 81.6213 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 96.6363 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 96.6363 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 111.651 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 111.651 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 141.681 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 141.681 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 156.696 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 156.696 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 171.711 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 171.711 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 201.741 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 201.741 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 216.756 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 216.756 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 231.771 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 231.771 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 261.801 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 261.801 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 276.816 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 276.816 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 291.831 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 291.831 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 321.861 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 321.861 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 336.876 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 336.876 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 351.891 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 351.891 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

535.482 93.8891 translate
90 rotate
0 0 m /L glyphshow
15.271 0 m /a glyphshow
26.3672 0 m /t glyphshow
33.313 0 m /t glyphshow
40.2588 0 m /i glyphshow
47.2046 0 m /c glyphshow
58.3008 0 m /e glyphshow
69.397 0 m /space glyphshow
75.647 0 m /P glyphshow
89.5508 0 m /a glyphshow
100.647 0 m /r glyphshow
108.972 0 m /a glyphshow
120.068 0 m /m glyphshow
139.514 0 m /e glyphshow
150.61 0 m /t glyphshow
157.556 0 m /e glyphshow
168.652 0 m /r glyphshow
176.978 0 m /s glyphshow
186.707 0 m /space glyphshow
192.957 0 m /parenleft glyphshow
201.282 0 m /a glyphshow
212.378 0 m /period glyphshow
218.628 0 m /u glyphshow
231.128 0 m /period glyphshow
237.378 0 m /parenright glyphshow
grestore
0.050 setlinewidth
[0.185 0.08] 0 setdash
0.722 0.224 0.055 setrgbcolor
gsave
388.174 300.3 598.576 66.606 clipbox
981.741304 103.374982 m
976.732609 102.813421 l
971.723913 102.260869 l
966.715217 101.68129 l
961.706522 101.269879 l
956.697826 100.78039 l
951.68913 100.365976 l
946.680435 99.990601 l
941.671739 99.528139 l
936.663043 99.266878 l
931.654348 99.182794 l
926.645652 98.984596 l
921.636957 99.329941 l
916.628261 100.257868 l
911.619565 102.035644 l
906.61087 104.104711 l
901.602174 160.864414 l
896.593478 173.209747 l
891.584783 182.098627 l
886.576087 190.146667 l
881.567391 196.888402 l
876.558696 202.996504 l
871.55 208.702204 l
866.541304 214.011508 l
861.532609 218.981473 l
856.523913 223.717204 l
851.515217 228.095578 l
846.506522 232.515994 l
841.497826 236.618092 l
836.48913 240.615085 l
831.480435 244.38385 l
826.471739 248.086549 l
821.463043 251.600059 l
816.454348 255.167623 l
811.445652 258.582034 l
806.436957 261.792241 l
801.428261 264.903349 l
796.419565 268.101544 l
791.41087 271.08052 l
786.402174 274.194631 l
781.393478 276.93637 l
776.384783 279.837268 l
771.376087 282.542971 l
766.367391 285.359785 l
761.358696 288.023446 l
756.35 290.726146 l
751.341304 293.296714 l
746.332609 295.906321 l
741.323913 298.374787 l
736.315217 300.798208 l
731.306522 303.218626 l
726.297826 305.648053 l
721.28913 308.050453 l
716.280435 310.332733 l
711.271739 312.630028 l
706.263043 314.882278 l
701.254348 317.140534 l
696.245652 319.383775 l
691.236957 321.545935 l
686.228261 323.660047 l
681.219565 325.843228 l
676.21087 327.92731 l
671.202174 330.02941 l
666.193478 332.056435 l
661.184783 334.059436 l
656.176087 336.101476 l
651.167391 338.056429 l
646.158696 340.035406 l
641.15 341.978347 l
636.141304 343.912279 l
631.132609 345.801166 l
626.123913 347.708071 l
621.115217 349.581943 l
616.106522 351.434794 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
388.174 300.3 598.576 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

0 2 m
-2 -2 l
2 -2 l
cl

gsave
0.722 0.224 0.055 setrgbcolor
fill
grestore
stroke
grestore
} bind def
981.741 103.375 o
976.733 102.813 o
971.724 102.261 o
966.715 101.681 o
961.707 101.27 o
956.698 100.78 o
951.689 100.366 o
946.68 99.9906 o
941.672 99.5281 o
936.663 99.2669 o
931.654 99.1828 o
926.646 98.9846 o
921.637 99.3299 o
916.628 100.258 o
911.62 102.036 o
906.611 104.105 o
901.602 160.864 o
896.593 173.21 o
891.585 182.099 o
886.576 190.147 o
881.567 196.888 o
876.559 202.997 o
871.55 208.702 o
866.541 214.012 o
861.533 218.981 o
856.524 223.717 o
851.515 228.096 o
846.507 232.516 o
841.498 236.618 o
836.489 240.615 o
831.48 244.384 o
826.472 248.087 o
821.463 251.6 o
816.454 255.168 o
811.446 258.582 o
806.437 261.792 o
801.428 264.903 o
796.42 268.102 o
791.411 271.081 o
786.402 274.195 o
781.393 276.936 o
776.385 279.837 o
771.376 282.543 o
766.367 285.36 o
761.359 288.023 o
756.35 290.726 o
751.341 293.297 o
746.333 295.906 o
741.324 298.375 o
736.315 300.798 o
731.307 303.219 o
726.298 305.648 o
721.289 308.05 o
716.28 310.333 o
711.272 312.63 o
706.263 314.882 o
701.254 317.141 o
696.246 319.384 o
691.237 321.546 o
686.228 323.66 o
681.22 325.843 o
676.211 327.927 o
671.202 330.029 o
666.193 332.056 o
661.185 334.059 o
656.176 336.101 o
651.167 338.056 o
646.159 340.035 o
641.15 341.978 o
636.141 343.912 o
631.133 345.801 o
626.124 347.708 o
621.115 349.582 o
616.107 351.435 o
grestore
0.050 setlinewidth
1 setlinejoin
[0.185 0.08] 0 setdash
1.000 0.271 0.000 setrgbcolor
gsave
388.174 300.3 598.576 66.606 clipbox
981.741304 103.374982 m
976.732609 102.813421 l
971.723913 102.260869 l
966.715217 101.68129 l
961.706522 101.269879 l
956.697826 100.78039 l
951.68913 100.365976 l
946.680435 99.990601 l
941.671739 99.528139 l
936.663043 99.266878 l
931.654348 99.182794 l
926.645652 98.984596 l
921.636957 99.329941 l
916.628261 100.257868 l
911.619565 102.035644 l
906.61087 104.104711 l
901.602174 160.864414 l
896.593478 173.209747 l
891.584783 182.098627 l
886.576087 190.146667 l
881.567391 196.888402 l
876.558696 202.996504 l
871.55 208.702204 l
866.541304 214.011508 l
861.532609 218.981473 l
856.523913 223.717204 l
851.515217 228.095578 l
846.506522 232.515994 l
841.497826 236.618092 l
836.48913 240.615085 l
831.480435 244.38385 l
826.471739 248.086549 l
821.463043 251.600059 l
816.454348 255.167623 l
811.445652 258.582034 l
806.436957 261.792241 l
801.428261 264.903349 l
796.419565 268.101544 l
791.41087 271.08052 l
786.402174 274.194631 l
781.393478 276.93637 l
776.384783 279.837268 l
771.376087 282.542971 l
766.367391 285.359785 l
761.358696 288.023446 l
756.35 290.726146 l
751.341304 293.296714 l
746.332609 295.906321 l
741.323913 298.374787 l
736.315217 300.798208 l
731.306522 303.218626 l
726.297826 305.648053 l
721.28913 308.050453 l
716.280435 310.332733 l
711.271739 312.630028 l
706.263043 314.882278 l
701.254348 317.140534 l
696.245652 319.383775 l
691.236957 321.545935 l
686.228261 323.660047 l
681.219565 325.843228 l
676.21087 327.92731 l
671.202174 330.02941 l
666.193478 332.056435 l
661.184783 334.059436 l
656.176087 336.101476 l
651.167391 338.056429 l
646.158696 340.035406 l
641.15 341.978347 l
636.141304 343.912279 l
631.132609 345.801166 l
626.123913 347.708071 l
621.115217 349.581943 l
616.106522 351.434794 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
388.174 300.3 598.576 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-4 -4 m
4 -4 l
4 4 l
-4 4 l
cl

gsave
grestore
stroke
grestore
} bind def
981.741 103.375 o
976.733 102.813 o
971.724 102.261 o
966.715 101.681 o
961.707 101.27 o
956.698 100.78 o
951.689 100.366 o
946.68 99.9906 o
941.672 99.5281 o
936.663 99.2669 o
931.654 99.1828 o
926.646 98.9846 o
921.637 99.3299 o
916.628 100.258 o
911.62 102.036 o
906.611 104.105 o
901.602 160.864 o
896.593 173.21 o
891.585 182.099 o
886.576 190.147 o
881.567 196.888 o
876.559 202.997 o
871.55 208.702 o
866.541 214.012 o
861.533 218.981 o
856.524 223.717 o
851.515 228.096 o
846.507 232.516 o
841.498 236.618 o
836.489 240.615 o
831.48 244.384 o
826.472 248.087 o
821.463 251.6 o
816.454 255.168 o
811.446 258.582 o
806.437 261.792 o
801.428 264.903 o
796.42 268.102 o
791.411 271.081 o
786.402 274.195 o
781.393 276.936 o
776.385 279.837 o
771.376 282.543 o
766.367 285.36 o
761.359 288.023 o
756.35 290.726 o
751.341 293.297 o
746.333 295.906 o
741.324 298.375 o
736.315 300.798 o
731.307 303.219 o
726.298 305.648 o
721.289 308.05 o
716.28 310.333 o
711.272 312.63 o
706.263 314.882 o
701.254 317.141 o
696.246 319.384 o
691.237 321.546 o
686.228 323.66 o
681.22 325.843 o
676.211 327.927 o
671.202 330.029 o
666.193 332.056 o
661.185 334.059 o
656.176 336.101 o
651.167 338.056 o
646.159 340.035 o
641.15 341.978 o
636.141 343.912 o
631.133 345.801 o
626.124 347.708 o
621.115 349.582 o
616.107 351.435 o
grestore
0.050 setlinewidth
1 setlinejoin
[0.185 0.08] 0 setdash
1.000 0.514 0.000 setrgbcolor
gsave
388.174 300.3 598.576 66.606 clipbox
981.741304 103.374982 m
976.732609 102.813421 l
971.723913 102.260869 l
966.715217 101.68129 l
961.706522 101.269879 l
956.697826 100.78039 l
951.68913 100.365976 l
946.680435 99.990601 l
941.671739 99.528139 l
936.663043 99.266878 l
931.654348 99.182794 l
926.645652 98.984596 l
921.636957 99.329941 l
916.628261 100.257868 l
911.619565 102.035644 l
906.61087 104.104711 l
901.602174 160.864414 l
896.593478 173.209747 l
891.584783 182.098627 l
886.576087 190.146667 l
881.567391 196.888402 l
876.558696 202.996504 l
871.55 208.702204 l
866.541304 214.011508 l
861.532609 218.981473 l
856.523913 223.717204 l
851.515217 228.095578 l
846.506522 232.515994 l
841.497826 236.618092 l
836.48913 240.615085 l
831.480435 244.38385 l
826.471739 248.086549 l
821.463043 251.600059 l
816.454348 255.167623 l
811.445652 258.582034 l
806.436957 261.792241 l
801.428261 264.903349 l
796.419565 268.101544 l
791.41087 271.08052 l
786.402174 274.194631 l
781.393478 276.93637 l
776.384783 279.837268 l
771.376087 282.542971 l
766.367391 285.359785 l
761.358696 288.023446 l
756.35 290.726146 l
751.341304 293.296714 l
746.332609 295.906321 l
741.323913 298.374787 l
736.315217 300.798208 l
731.306522 303.218626 l
726.297826 305.648053 l
721.28913 308.050453 l
716.280435 310.332733 l
711.271739 312.630028 l
706.263043 314.882278 l
701.254348 317.140534 l
696.245652 319.383775 l
691.236957 321.545935 l
686.228261 323.660047 l
681.219565 325.843228 l
676.21087 327.92731 l
671.202174 330.02941 l
666.193478 332.056435 l
661.184783 334.059436 l
656.176087 336.101476 l
651.167391 338.056429 l
646.158696 340.035406 l
641.15 341.978347 l
636.141304 343.912279 l
631.132609 345.801166 l
626.123913 347.708071 l
621.115217 349.581943 l
616.106522 351.434794 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
388.174 300.3 598.576 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
981.741 103.375 o
976.733 102.813 o
971.724 102.261 o
966.715 101.681 o
961.707 101.27 o
956.698 100.78 o
951.689 100.366 o
946.68 99.9906 o
941.672 99.5281 o
936.663 99.2669 o
931.654 99.1828 o
926.646 98.9846 o
921.637 99.3299 o
916.628 100.258 o
911.62 102.036 o
906.611 104.105 o
901.602 160.864 o
896.593 173.21 o
891.585 182.099 o
886.576 190.147 o
881.567 196.888 o
876.559 202.997 o
871.55 208.702 o
866.541 214.012 o
861.533 218.981 o
856.524 223.717 o
851.515 228.096 o
846.507 232.516 o
841.498 236.618 o
836.489 240.615 o
831.48 244.384 o
826.472 248.087 o
821.463 251.6 o
816.454 255.168 o
811.446 258.582 o
806.437 261.792 o
801.428 264.903 o
796.42 268.102 o
791.411 271.081 o
786.402 274.195 o
781.393 276.936 o
776.385 279.837 o
771.376 282.543 o
766.367 285.36 o
761.359 288.023 o
756.35 290.726 o
751.341 293.297 o
746.333 295.906 o
741.324 298.375 o
736.315 300.798 o
731.307 303.219 o
726.298 305.648 o
721.289 308.05 o
716.28 310.333 o
711.272 312.63 o
706.263 314.882 o
701.254 317.141 o
696.246 319.384 o
691.237 321.546 o
686.228 323.66 o
681.22 325.843 o
676.211 327.927 o
671.202 330.029 o
666.193 332.056 o
661.185 334.059 o
656.176 336.101 o
651.167 338.056 o
646.159 340.035 o
641.15 341.978 o
636.141 343.912 o
631.133 345.801 o
626.124 347.708 o
621.115 349.582 o
616.107 351.435 o
grestore
2.500 setlinewidth
0 setlinejoin
2 setlinecap
0.000 setgray
gsave
598.576087 66.60625 m
598.576087 366.90625 l
stroke
grestore
gsave
986.75 66.60625 m
986.75 366.90625 l
stroke
grestore
gsave
598.576087 66.60625 m
986.75 66.60625 l
stroke
grestore
gsave
598.576087 366.90625 m
986.75 366.90625 l
stroke
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

713.393 216.756 translate
0 rotate
0 0 m /R glyphshow
16.6748 0 m /three glyphshow
29.1748 0 m /C glyphshow
grestore
gsave
908.672 216.756 translate
0 rotate
/TimesNewRomanPSMT 25.0 selectfont
0 0.921875 moveto
/P glyphshow
13.9038 0.921875 moveto
/m glyphshow
33.3496 0.546875 moveto
/three glyphshow
45.8496 0.921875 moveto
/m glyphshow
33.349609375 20.875 12.5 1.5625 rectfill
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

778.085 383.906 translate
0 rotate
0 0 m /parenleft glyphshow
8.3252 0 m /d glyphshow
20.8252 0 m /parenright glyphshow
grestore
0.050 setlinewidth
1 setlinejoin
0 setlinecap
[0.185 0.08] 0 setdash
0.722 0.224 0.055 setrgbcolor
gsave
616.576087 152.45 m
636.576087 152.45 l
656.576087 152.45 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

0 2 m
-2 -2 l
2 -2 l
cl

gsave
0.722 0.224 0.055 setrgbcolor
fill
grestore
stroke
grestore
} bind def
636.576 152.45 o
grestore
0.000 setgray
gsave
672.576 145.45 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.796875 moveto
/a glyphshow
grestore
0.050 setlinewidth
1 setlinejoin
[0.185 0.08] 0 setdash
1.000 0.271 0.000 setrgbcolor
gsave
616.576087 124.16875 m
636.576087 124.16875 l
656.576087 124.16875 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-4 -4 m
4 -4 l
4 4 l
-4 4 l
cl

gsave
grestore
stroke
grestore
} bind def
636.576 124.169 o
grestore
0.000 setgray
gsave
672.576 117.169 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.109375 moveto
/b glyphshow
grestore
0.050 setlinewidth
1 setlinejoin
[0.185 0.08] 0 setdash
1.000 0.514 0.000 setrgbcolor
gsave
616.576087 95.8875 m
636.576087 95.8875 l
656.576087 95.8875 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
636.576 95.8875 o
grestore
0.000 setgray
gsave
672.576 88.8875 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.796875 moveto
/c glyphshow
grestore

end
showpage
