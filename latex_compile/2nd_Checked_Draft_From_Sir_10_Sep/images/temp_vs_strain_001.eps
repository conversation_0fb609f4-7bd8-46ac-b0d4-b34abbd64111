%!PS-Adobe-3.0 EPSF-3.0
%%Title: temp_vs_strain_001.eps
%%Creator: Mat<PERSON>lotlib v3.7.1, https://matplotlib.org/
%%CreationDate: Wed Apr 24 13:25:22 2024
%%Orientation: portrait
%%BoundingBox: 25 177 587 615
%%HiResBoundingBox: 25.998438 177.993168 586.001562 614.006832
%%EndComments
%%BeginProlog
/mpldict 11 dict def
mpldict begin
/_d { bind def } bind def
/m { moveto } _d
/l { lineto } _d
/r { rlineto } _d
/c { curveto } _d
/cl { closepath } _d
/ce { closepath eofill } _d
/box {
      m
      1 index 0 r
      0 exch r
      neg 0 r
      cl
    } _d
/clipbox {
      box
      clip
      newpath
    } _d
/sc { setcachedevice } _d
%!PS-TrueTypeFont-1.0-2.53740
%%Title: unknown
%%Creator: Converted from TrueType to type 42 by PPR
15 dict begin
/FontName /TimesNewRomanPSMT def
/PaintType 0 def
/FontMatrix[1 0 0 1 0 0]def
/FontBBox[-568 -307 2028 1007]def
/FontType 42 def
/Encoding StandardEncoding def
/FontInfo 10 dict dup begin
/FamilyName (unknown) def
/FullName (unknown) def
/Weight (unknown) def
/Version (unknown) def
/ItalicAngle 0.0 def
/isFixedPitch false def
/UnderlinePosition -223 def
/UnderlineThickness 100 def
end readonly def
/sfnts[<00010000000900800003001063767420F34DDA810000009C000007C46670676D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>]def
/CharStrings 32 dict dup begin
/.notdef 0 def
/minus 32 def
/space 3 def
/parenleft 4 def
/parenright 5 def
/period 6 def
/slash 7 def
/zero 8 def
/one 9 def
/two 10 def
/three 11 def
/four 12 def
/six 13 def
/seven 14 def
/eight 15 def
/nine 16 def
/eta 33 def
/E 17 def
/F 18 def
/M 19 def
/S 20 def
/V 21 def
/a 22 def
/c 23 def
/d 24 def
/e 25 def
/i 26 def
/l 27 def
/m 28 def
/n 29 def
/r 30 def
/t 31 def
end readonly def

systemdict/resourcestatus known
 {42 /FontType resourcestatus
   {pop pop false}{true}ifelse}
 {true}ifelse
{/TrueDict where{pop}{(%%[ Error: no TrueType rasterizer ]%%)= flush}ifelse
/FontType 3 def
 /TrueState 271 string def
 TrueDict begin sfnts save
 72 0 matrix defaultmatrix dtransform dup
 mul exch dup mul add sqrt cvi 0 72 matrix
 defaultmatrix dtransform dup mul exch dup
 mul add sqrt cvi 3 -1 roll restore
 TrueState initer end
 /BuildGlyph{exch begin
  CharStrings dup 2 index known
    {exch}{exch pop /.notdef}ifelse
  get dup xcheck
    {currentdict systemdict begin begin exec end end}
    {TrueDict begin /bander load cvlit exch TrueState render end}
    ifelse
 end}bind def
 /BuildChar{
  1 index /Encoding get exch get
  1 index /BuildGlyph get exec
 }bind def
}if

FontName currentdict end definefont pop
end
%%EndProlog
mpldict begin
25.998 177.993 translate
560.003 436.014 0 0 clipbox
gsave
0 -0 m
560.003125 -0 l
560.003125 436.013664 l
0 436.013664 l
cl
1.000 setgray
fill
grestore
gsave
100.153125 66.60625 m
546.553125 66.60625 l
546.553125 426.96625 l
100.153125 426.96625 l
cl
1.000 setgray
fill
grestore
2.500 setlinewidth
1 setlinejoin
0 setlinecap
[] 0 setdash
0.000 setgray
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

86.8563 39.2469 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /four glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
211.753 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
211.753 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

198.456 39.2469 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /two glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
323.353 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
323.353 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

317.103 39.2469 translate
0 rotate
0 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
434.953 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
434.953 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

428.703 39.2469 translate
0 rotate
0 0 m /two glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

540.303 39.2469 translate
0 rotate
0 0 m /four glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
128.053 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
128.053 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
155.953 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
155.953 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
183.853 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
183.853 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
239.653 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
239.653 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
267.553 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
267.553 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
295.453 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
295.453 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
351.253 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
351.253 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
379.153 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
379.153 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
407.053 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
407.053 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
462.853 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
462.853 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
490.753 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
490.753 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
518.653 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
518.653 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

205.306 12.5438 translate
0 rotate
0 0 m /E glyphshow
15.271 0 m /l glyphshow
22.2168 0 m /e glyphshow
33.313 0 m /c glyphshow
44.4092 0 m /t glyphshow
51.355 0 m /r glyphshow
59.6802 0 m /i glyphshow
66.626 0 m /c glyphshow
77.7222 0 m /space glyphshow
83.9722 0 m /F glyphshow
97.876 0 m /i glyphshow
104.822 0 m /e glyphshow
115.918 0 m /l glyphshow
122.864 0 m /d glyphshow
135.364 0 m /space glyphshow
141.614 0 m /parenleft glyphshow
149.939 0 m /M glyphshow
172.168 0 m /V glyphshow
190.222 0 m /slash glyphshow
197.168 0 m /c glyphshow
208.264 0 m /m glyphshow
227.71 0 m /parenright glyphshow
grestore
2.500 setlinewidth
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 112.242 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 112.242 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

33.9031 103.562 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /one glyphshow
43.75 0 m /six glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 173.821 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 173.821 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

33.9031 165.141 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /one glyphshow
43.75 0 m /seven glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 235.399 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 235.399 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

33.9031 226.719 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /one glyphshow
43.75 0 m /eight glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 296.977 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 296.977 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

33.9031 288.298 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /one glyphshow
43.75 0 m /nine glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 358.556 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 358.556 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

33.9031 349.876 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /two glyphshow
43.75 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 420.134 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 420.134 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

33.9031 411.454 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /two glyphshow
43.75 0 m /one glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 75.2951 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 75.2951 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 87.6108 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 87.6108 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 99.9265 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 99.9265 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 124.558 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 124.558 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 136.873 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 136.873 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 149.189 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 149.189 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 161.505 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 161.505 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 186.136 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 186.136 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 198.452 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 198.452 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 210.768 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 210.768 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 223.083 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 223.083 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 247.715 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 247.715 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 260.03 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 260.03 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 272.346 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 272.346 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 284.662 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 284.662 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 309.293 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 309.293 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 321.609 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 321.609 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 333.924 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 333.924 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 346.24 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 346.24 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 370.871 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 370.871 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 383.187 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 383.187 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 395.503 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 395.503 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 407.818 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 407.818 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

24.5594 216.919 translate
90 rotate
0 0 m /S glyphshow
13.9038 0 m /t glyphshow
20.8496 0 m /r glyphshow
29.1748 0 m /a glyphshow
40.271 0 m /i glyphshow
47.2168 0 m /n glyphshow
grestore
0.500 setlinewidth
[1.85 0.8] 0 setdash
0.031 0.235 0.235 setrgbcolor
gsave
446.4 360.36 100.153 66.606 clipbox
535.393125 238.271501 m
524.233125 239.865149 l
513.073125 241.009891 l
501.913125 241.698337 l
490.753125 244.236598 l
479.593125 244.117751 l
468.433125 245.891824 l
457.273125 246.35243 l
446.113125 246.449724 l
434.953125 246.178779 l
423.793125 246.213879 l
412.633125 246.164001 l
401.473125 246.135675 l
390.313125 246.266221 l
379.153125 244.32096 l
367.993125 244.950907 l
356.833125 243.065993 l
345.673125 242.711917 l
334.513125 241.243889 l
323.353125 239.320181 l
312.193125 236.972198 l
301.033125 234.482584 l
289.873125 231.536059 l
278.713125 228.893731 l
267.553125 224.778449 l
256.393125 222.123805 l
245.233125 217.78992 l
234.073125 212.925229 l
222.913125 207.531579 l
211.753125 201.977211 l
200.593125 195.109991 l
189.433125 186.408352 l
178.273125 180.102111 l
167.113125 171.625233 l
155.953125 160.895202 l
144.793125 147.928645 l
133.633125 132.764356 l
122.473125 113.659051 l
111.313125 84.922274 l
100.153125 236.459865 l
111.313125 237.764095 l
122.473125 239.181014 l
133.633125 240.981565 l
144.793125 242.135544 l
155.953125 243.303685 l
167.113125 244.386849 l
178.273125 244.811124 l
189.433125 245.76251 l
200.593125 246.571034 l
211.753125 247.177581 l
222.913125 246.991614 l
234.073125 246.808726 l
245.233125 246.466966 l
256.393125 245.602406 l
267.553125 244.643631 l
278.713125 245.405355 l
289.873125 242.092439 l
301.033125 242.148475 l
312.193125 240.762962 l
323.353125 239.02091 l
334.513125 236.167984 l
345.673125 234.073704 l
356.833125 231.356866 l
367.993125 228.534114 l
379.153125 225.191024 l
390.313125 221.6164 l
401.473125 218.943283 l
412.633125 212.890129 l
423.793125 207.29635 l
434.953125 202.060957 l
446.113125 196.719034 l
457.273125 187.939806 l
468.433125 180.840436 l
479.593125 171.369067 l
490.753125 160.635341 l
501.913125 147.274067 l
513.073125 133.055622 l
524.233125 113.033415 l
535.393125 83.085391 l
546.553125 235.788045 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
446.4 360.36 100.153 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

0 2 m
-2 -2 l
2 -2 l
cl

gsave
0.031 0.235 0.235 setrgbcolor
fill
grestore
stroke
grestore
} bind def
535.393 238.272 o
524.233 239.865 o
513.073 241.01 o
501.913 241.698 o
490.753 244.237 o
479.593 244.118 o
468.433 245.892 o
457.273 246.352 o
446.113 246.45 o
434.953 246.179 o
423.793 246.214 o
412.633 246.164 o
401.473 246.136 o
390.313 246.266 o
379.153 244.321 o
367.993 244.951 o
356.833 243.066 o
345.673 242.712 o
334.513 241.244 o
323.353 239.32 o
312.193 236.972 o
301.033 234.483 o
289.873 231.536 o
278.713 228.894 o
267.553 224.778 o
256.393 222.124 o
245.233 217.79 o
234.073 212.925 o
222.913 207.532 o
211.753 201.977 o
200.593 195.11 o
189.433 186.408 o
178.273 180.102 o
167.113 171.625 o
155.953 160.895 o
144.793 147.929 o
133.633 132.764 o
122.473 113.659 o
111.313 84.9223 o
100.153 236.46 o
111.313 237.764 o
122.473 239.181 o
133.633 240.982 o
144.793 242.136 o
155.953 243.304 o
167.113 244.387 o
178.273 244.811 o
189.433 245.763 o
200.593 246.571 o
211.753 247.178 o
222.913 246.992 o
234.073 246.809 o
245.233 246.467 o
256.393 245.602 o
267.553 244.644 o
278.713 245.405 o
289.873 242.092 o
301.033 242.148 o
312.193 240.763 o
323.353 239.021 o
334.513 236.168 o
345.673 234.074 o
356.833 231.357 o
367.993 228.534 o
379.153 225.191 o
390.313 221.616 o
401.473 218.943 o
412.633 212.89 o
423.793 207.296 o
434.953 202.061 o
446.113 196.719 o
457.273 187.94 o
468.433 180.84 o
479.593 171.369 o
490.753 160.635 o
501.913 147.274 o
513.073 133.056 o
524.233 113.033 o
535.393 83.0854 o
546.553 235.788 o
grestore
0.500 setlinewidth
1 setlinejoin
[1.85 0.8] 0 setdash
0.031 0.416 0.416 setrgbcolor
gsave
446.4 360.36 100.153 66.606 clipbox
535.393125 238.327537 m
524.233125 239.168082 l
513.073125 241.619517 l
501.913125 242.176185 l
490.753125 242.450209 l
479.593125 243.822175 l
468.433125 243.806781 l
457.273125 246.012518 l
446.113125 245.208304 l
434.953125 246.97314 l
423.793125 246.592586 l
412.633125 246.89001 l
401.473125 245.965102 l
390.313125 245.298825 l
379.153125 245.308061 l
367.993125 243.769218 l
356.833125 243.649756 l
345.673125 240.919371 l
334.513125 240.19952 l
323.353125 238.640356 l
312.193125 237.748085 l
301.033125 233.745491 l
289.873125 232.11859 l
278.713125 229.114798 l
267.553125 225.998932 l
256.393125 221.16072 l
245.233125 216.097746 l
234.073125 211.831597 l
222.913125 208.340103 l
211.753125 202.061573 l
200.593125 195.72639 l
189.433125 187.185471 l
178.273125 180.504833 l
167.113125 171.317957 l
155.953125 160.106383 l
144.793125 148.827073 l
133.633125 131.574662 l
122.473125 112.405316 l
111.313125 86.326261 l
100.153125 236.087932 l
111.313125 238.280738 l
122.473125 239.338038 l
133.633125 241.477887 l
144.793125 242.961925 l
155.953125 243.215013 l
167.113125 243.61404 l
178.273125 245.392424 l
189.433125 245.614106 l
200.593125 246.160306 l
211.753125 246.365978 l
222.913125 247.103071 l
234.073125 246.383835 l
245.233125 245.81362 l
256.393125 246.156611 l
267.553125 245.633811 l
278.713125 244.10482 l
289.873125 244.490301 l
301.033125 242.54935 l
312.193125 241.12689 l
323.353125 238.344164 l
334.513125 236.029433 l
345.673125 234.778776 l
356.833125 231.555764 l
367.993125 228.098139 l
379.153125 225.172551 l
390.313125 222.003728 l
401.473125 216.906886 l
412.633125 213.835973 l
423.793125 207.668283 l
434.953125 202.447669 l
446.113125 196.255964 l
457.273125 189.191694 l
468.433125 179.791756 l
479.593125 171.390619 l
490.753125 161.079937 l
501.913125 146.457538 l
513.073125 133.571648 l
524.233125 113.817308 l
535.393125 82.98625 l
546.553125 236.471565 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
446.4 360.36 100.153 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-5 -5 m
5 -5 l
5 5 l
-5 5 l
cl

gsave
grestore
stroke
grestore
} bind def
535.393 238.328 o
524.233 239.168 o
513.073 241.62 o
501.913 242.176 o
490.753 242.45 o
479.593 243.822 o
468.433 243.807 o
457.273 246.013 o
446.113 245.208 o
434.953 246.973 o
423.793 246.593 o
412.633 246.89 o
401.473 245.965 o
390.313 245.299 o
379.153 245.308 o
367.993 243.769 o
356.833 243.65 o
345.673 240.919 o
334.513 240.2 o
323.353 238.64 o
312.193 237.748 o
301.033 233.745 o
289.873 232.119 o
278.713 229.115 o
267.553 225.999 o
256.393 221.161 o
245.233 216.098 o
234.073 211.832 o
222.913 208.34 o
211.753 202.062 o
200.593 195.726 o
189.433 187.185 o
178.273 180.505 o
167.113 171.318 o
155.953 160.106 o
144.793 148.827 o
133.633 131.575 o
122.473 112.405 o
111.313 86.3263 o
100.153 236.088 o
111.313 238.281 o
122.473 239.338 o
133.633 241.478 o
144.793 242.962 o
155.953 243.215 o
167.113 243.614 o
178.273 245.392 o
189.433 245.614 o
200.593 246.16 o
211.753 246.366 o
222.913 247.103 o
234.073 246.384 o
245.233 245.814 o
256.393 246.157 o
267.553 245.634 o
278.713 244.105 o
289.873 244.49 o
301.033 242.549 o
312.193 241.127 o
323.353 238.344 o
334.513 236.029 o
345.673 234.779 o
356.833 231.556 o
367.993 228.098 o
379.153 225.173 o
390.313 222.004 o
401.473 216.907 o
412.633 213.836 o
423.793 207.668 o
434.953 202.448 o
446.113 196.256 o
457.273 189.192 o
468.433 179.792 o
479.593 171.391 o
490.753 161.08 o
501.913 146.458 o
513.073 133.572 o
524.233 113.817 o
535.393 82.9862 o
546.553 236.472 o
grestore
0.500 setlinewidth
1 setlinejoin
[1.85 0.8] 0 setdash
0.031 0.659 0.659 setrgbcolor
gsave
446.4 360.36 100.153 66.606 clipbox
535.393125 403.881597 m
524.233125 397.651714 l
513.073125 390.884867 l
501.913125 384.892676 l
490.753125 379.099383 l
479.593125 372.877504 l
468.433125 366.872382 l
457.273125 360.343227 l
446.113125 355.347374 l
434.953125 349.532529 l
423.793125 344.566849 l
412.633125 338.869003 l
401.473125 334.108995 l
390.313125 328.452406 l
379.153125 323.893144 l
367.993125 318.349243 l
356.833125 314.471653 l
345.673125 310.79173 l
334.513125 306.51696 l
323.353125 301.729857 l
312.193125 298.088113 l
301.033125 296.118221 l
289.873125 292.051585 l
278.713125 289.182033 l
267.553125 287.467076 l
256.393125 285.385111 l
245.233125 283.672001 l
234.073125 282.708915 l
222.913125 280.846785 l
211.753125 280.814149 l
200.593125 281.34557 l
189.433125 284.08396 l
178.273125 285.282275 l
167.113125 288.061923 l
155.953125 293.263447 l
144.793125 299.288275 l
133.633125 310.670421 l
122.473125 325.210305 l
111.313125 349.111949 l
100.153125 410.545608 l
111.313125 403.866203 l
122.473125 397.290864 l
133.633125 390.285709 l
144.793125 384.826171 l
155.953125 378.515004 l
167.113125 372.778363 l
178.273125 365.948091 l
189.433125 361.069236 l
200.593125 354.692181 l
211.753125 349.179685 l
222.913125 343.460902 l
234.073125 339.284041 l
245.233125 333.778319 l
256.393125 329.262777 l
267.553125 323.656683 l
278.713125 318.549373 l
289.873125 314.281992 l
301.033125 310.179641 l
312.193125 305.160388 l
323.353125 302.179379 l
334.513125 299.428058 l
345.673125 296.043711 l
356.833125 292.455539 l
367.993125 290.766445 l
379.153125 286.522463 l
390.313125 284.64186 l
401.473125 282.575906 l
412.633125 281.091867 l
423.793125 281.175614 l
434.953125 280.389874 l
446.113125 279.647239 l
457.273125 282.875177 l
468.433125 285.605561 l
479.593125 288.241116 l
490.753125 293.412467 l
501.913125 302.34133 l
513.073125 309.00411 l
524.233125 323.915928 l
535.393125 350.814591 l
546.553125 410.58625 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
446.4 360.36 100.153 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -5 m
1.326016 -5 2.597899 -4.473168 3.535534 -3.535534 c
4.473168 -2.597899 5 -1.326016 5 0 c
5 1.326016 4.473168 2.597899 3.535534 3.535534 c
2.597899 4.473168 1.326016 5 0 5 c
-1.326016 5 -2.597899 4.473168 -3.535534 3.535534 c
-4.473168 2.597899 -5 1.326016 -5 0 c
-5 -1.326016 -4.473168 -2.597899 -3.535534 -3.535534 c
-2.597899 -4.473168 -1.326016 -5 0 -5 c
cl

gsave
grestore
stroke
grestore
} bind def
535.393 403.882 o
524.233 397.652 o
513.073 390.885 o
501.913 384.893 o
490.753 379.099 o
479.593 372.878 o
468.433 366.872 o
457.273 360.343 o
446.113 355.347 o
434.953 349.533 o
423.793 344.567 o
412.633 338.869 o
401.473 334.109 o
390.313 328.452 o
379.153 323.893 o
367.993 318.349 o
356.833 314.472 o
345.673 310.792 o
334.513 306.517 o
323.353 301.73 o
312.193 298.088 o
301.033 296.118 o
289.873 292.052 o
278.713 289.182 o
267.553 287.467 o
256.393 285.385 o
245.233 283.672 o
234.073 282.709 o
222.913 280.847 o
211.753 280.814 o
200.593 281.346 o
189.433 284.084 o
178.273 285.282 o
167.113 288.062 o
155.953 293.263 o
144.793 299.288 o
133.633 310.67 o
122.473 325.21 o
111.313 349.112 o
100.153 410.546 o
111.313 403.866 o
122.473 397.291 o
133.633 390.286 o
144.793 384.826 o
155.953 378.515 o
167.113 372.778 o
178.273 365.948 o
189.433 361.069 o
200.593 354.692 o
211.753 349.18 o
222.913 343.461 o
234.073 339.284 o
245.233 333.778 o
256.393 329.263 o
267.553 323.657 o
278.713 318.549 o
289.873 314.282 o
301.033 310.18 o
312.193 305.16 o
323.353 302.179 o
334.513 299.428 o
345.673 296.044 o
356.833 292.456 o
367.993 290.766 o
379.153 286.522 o
390.313 284.642 o
401.473 282.576 o
412.633 281.092 o
423.793 281.176 o
434.953 280.39 o
446.113 279.647 o
457.273 282.875 o
468.433 285.606 o
479.593 288.241 o
490.753 293.412 o
501.913 302.341 o
513.073 309.004 o
524.233 323.916 o
535.393 350.815 o
546.553 410.586 o
grestore
2.500 setlinewidth
0 setlinejoin
2 setlinecap
0.000 setgray
gsave
100.153125 66.60625 m
100.153125 426.96625 l
stroke
grestore
gsave
546.553125 66.60625 m
546.553125 426.96625 l
stroke
grestore
gsave
100.153125 66.60625 m
546.553125 66.60625 l
stroke
grestore
gsave
100.153125 426.96625 m
546.553125 426.96625 l
stroke
grestore
0.500 setlinewidth
1 setlinejoin
0 setlinecap
[1.85 0.8] 0 setdash
0.031 0.235 0.235 setrgbcolor
gsave
285.853125 157.60625 m
305.853125 157.60625 l
325.853125 157.60625 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

0 2 m
-2 -2 l
2 -2 l
cl

gsave
0.031 0.235 0.235 setrgbcolor
fill
grestore
stroke
grestore
} bind def
305.853 157.606 o
grestore
0.000 setgray
gsave
341.853 150.606 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.796875 moveto
/eta glyphshow
/TimesNewRomanPSMT 14.0 selectfont
10.7595 -4.35562 moveto
/one glyphshow
grestore
0.500 setlinewidth
1 setlinejoin
[1.85 0.8] 0 setdash
0.031 0.416 0.416 setrgbcolor
gsave
285.853125 127.60625 m
305.853125 127.60625 l
325.853125 127.60625 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-5 -5 m
5 -5 l
5 5 l
-5 5 l
cl

gsave
grestore
stroke
grestore
} bind def
305.853 127.606 o
grestore
0.000 setgray
gsave
341.853 120.606 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.796875 moveto
/eta glyphshow
/TimesNewRomanPSMT 14.0 selectfont
10.7595 -4.35562 moveto
/two glyphshow
grestore
0.500 setlinewidth
1 setlinejoin
[1.85 0.8] 0 setdash
0.031 0.659 0.659 setrgbcolor
gsave
285.853125 97.60625 m
305.853125 97.60625 l
325.853125 97.60625 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -5 m
1.326016 -5 2.597899 -4.473168 3.535534 -3.535534 c
4.473168 -2.597899 5 -1.326016 5 0 c
5 1.326016 4.473168 2.597899 3.535534 3.535534 c
2.597899 4.473168 1.326016 5 0 5 c
-1.326016 5 -2.597899 4.473168 -3.535534 3.535534 c
-4.473168 2.597899 -5 1.326016 -5 0 c
-5 -1.326016 -4.473168 -2.597899 -3.535534 -3.535534 c
-2.597899 -4.473168 -1.326016 -5 0 -5 c
cl

gsave
grestore
stroke
grestore
} bind def
305.853 97.6062 o
grestore
0.000 setgray
gsave
341.853 90.6062 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.796875 moveto
/eta glyphshow
/TimesNewRomanPSMT 14.0 selectfont
10.7595 -4.35562 moveto
/three glyphshow
grestore

end
showpage
