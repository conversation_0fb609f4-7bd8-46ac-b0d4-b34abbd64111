%!PS-Adobe-3.0 EPSF-3.0
%%Title: temp_vs_Smax_Srem.eps
%%Creator: Mat<PERSON>lotlib v3.7.1, https://matplotlib.org/
%%CreationDate: Wed Apr 24 13:33:06 2024
%%Orientation: portrait
%%BoundingBox: 29 178 583 614
%%HiResBoundingBox: 29.123438 178.916875 582.876562 613.083125
%%EndComments
%%BeginProlog
/mpldict 11 dict def
mpldict begin
/_d { bind def } bind def
/m { moveto } _d
/l { lineto } _d
/r { rlineto } _d
/c { curveto } _d
/cl { closepath } _d
/ce { closepath eofill } _d
/box {
      m
      1 index 0 r
      0 exch r
      neg 0 r
      cl
    } _d
/clipbox {
      box
      clip
      newpath
    } _d
/sc { setcachedevice } _d
%!PS-TrueTypeFont-1.0-2.53740
%%Title: unknown
%%Creator: Converted from TrueType to type 42 by PPR
15 dict begin
/FontName /TimesNewRomanPSMT def
/PaintType 0 def
/FontMatrix[1 0 0 1 0 0]def
/FontBBox[-568 -307 2028 1007]def
/FontType 42 def
/Encoding StandardEncoding def
/FontInfo 10 dict dup begin
/FamilyName (unknown) def
/FullName (unknown) def
/Weight (unknown) def
/Version (unknown) def
/ItalicAngle 0.0 def
/isFixedPitch false def
/UnderlinePosition -223 def
/UnderlineThickness 100 def
end readonly def
/sfnts[<00010000000900800003001063767420F34DDA810000009C000007C46670676D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>]def
/CharStrings 26 dict dup begin
/.notdef 0 def
/space 3 def
/parenleft 4 def
/parenright 5 def
/period 6 def
/zero 7 def
/one 8 def
/two 9 def
/three 10 def
/four 11 def
/six 12 def
/eight 13 def
/nine 14 def
/K 15 def
/S 16 def
/T 17 def
/a 18 def
/e 19 def
/i 20 def
/m 21 def
/n 22 def
/p 23 def
/r 24 def
/t 25 def
/u 26 def
/x 27 def
end readonly def

systemdict/resourcestatus known
 {42 /FontType resourcestatus
   {pop pop false}{true}ifelse}
 {true}ifelse
{/TrueDict where{pop}{(%%[ Error: no TrueType rasterizer ]%%)= flush}ifelse
/FontType 3 def
 /TrueState 271 string def
 TrueDict begin sfnts save
 72 0 matrix defaultmatrix dtransform dup
 mul exch dup mul add sqrt cvi 0 72 matrix
 defaultmatrix dtransform dup mul exch dup
 mul add sqrt cvi 3 -1 roll restore
 TrueState initer end
 /BuildGlyph{exch begin
  CharStrings dup 2 index known
    {exch}{exch pop /.notdef}ifelse
  get dup xcheck
    {currentdict systemdict begin begin exec end end}
    {TrueDict begin /bander load cvlit exch TrueState render end}
    ifelse
 end}bind def
 /BuildChar{
  1 index /Encoding get exch get
  1 index /BuildGlyph get exec
 }bind def
}if

FontName currentdict end definefont pop
end
%%EndProlog
mpldict begin
29.123 178.917 translate
553.753 434.166 0 0 clipbox
gsave
0 -0 m
553.753125 -0 l
553.753125 434.16625 l
0 434.16625 l
cl
1.000 setgray
fill
grestore
gsave
100.153125 66.60625 m
546.553125 66.60625 l
546.553125 426.96625 l
100.153125 426.96625 l
cl
1.000 setgray
fill
grestore
2.500 setlinewidth
1 setlinejoin
0 setlinecap
[] 0 setdash
0.000 setgray
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

93.9031 39.2469 translate
0 rotate
0 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
228 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
228 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

209.25 39.2469 translate
0 rotate
0 0 m /three glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
355.848 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
355.848 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

337.098 39.2469 translate
0 rotate
0 0 m /six glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
483.695 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
483.695 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

464.945 39.2469 translate
0 rotate
0 0 m /nine glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
132.115 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
132.115 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
164.077 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
164.077 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
196.039 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
196.039 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
259.962 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
259.962 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
291.924 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
291.924 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
323.886 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
323.886 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
387.809 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
387.809 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
419.771 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
419.771 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
451.733 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
451.733 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
515.657 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
515.657 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

239.892 12.5438 translate
0 rotate
0 0 m /T glyphshow
13.521 0 m /e glyphshow
24.6172 0 m /m glyphshow
44.063 0 m /p glyphshow
56.563 0 m /e glyphshow
67.6592 0 m /r glyphshow
75.9844 0 m /a glyphshow
87.0806 0 m /t glyphshow
94.0264 0 m /u glyphshow
106.526 0 m /r glyphshow
114.852 0 m /e glyphshow
125.948 0 m /space glyphshow
132.198 0 m /parenleft glyphshow
140.523 0 m /K glyphshow
158.577 0 m /parenright glyphshow
grestore
2.500 setlinewidth
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 84.9473 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 84.9473 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

33.9031 76.2676 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /one glyphshow
43.75 0 m /four glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 166.049 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 166.049 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

33.9031 157.369 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /one glyphshow
43.75 0 m /six glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 247.151 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 247.151 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

33.9031 238.471 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /one glyphshow
43.75 0 m /eight glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 328.252 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 328.252 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

33.9031 319.573 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /two glyphshow
43.75 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 409.354 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 409.354 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

33.9031 400.674 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /two glyphshow
43.75 0 m /two glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 105.223 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 105.223 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 125.498 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 125.498 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 145.774 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 145.774 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 186.324 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 186.324 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 206.6 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 206.6 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 226.875 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 226.875 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 267.426 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 267.426 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 287.701 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 287.701 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 307.977 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 307.977 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 348.528 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 348.528 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 368.803 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 368.803 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 389.078 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 389.078 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

24.5594 216.919 translate
90 rotate
0 0 m /S glyphshow
13.9038 0 m /t glyphshow
20.8496 0 m /r glyphshow
29.1748 0 m /a glyphshow
40.271 0 m /i glyphshow
47.2168 0 m /n glyphshow
grestore
[9.6 2.4 1.5 2.4] 0 setdash
0.031 0.659 0.255 setrgbcolor
gsave
446.4 360.36 100.153 66.606 clipbox
121.461001 334.097251 m
142.768877 325.355304 l
185.384629 305.950922 l
228.00038 286.264711 l
270.616132 264.694915 l
313.231884 242.304775 l
355.847636 218.419121 l
398.463388 191.471474 l
441.079139 161.109853 l
526.310643 82.98625 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
446.4 360.36 100.153 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
121.461 334.097 o
142.769 325.355 o
185.385 305.951 o
228 286.265 o
270.616 264.695 o
313.232 242.305 o
355.848 218.419 o
398.463 191.471 o
441.079 161.11 o
526.311 82.9862 o
grestore
1.500 setlinewidth
[5.55 2.4] 0 setdash
0.031 0.416 0.416 setrgbcolor
gsave
446.4 360.36 100.153 66.606 clipbox
121.461001 410.58625 m
142.768877 402.001234 l
185.384629 385.002733 l
228.00038 367.251608 l
270.616132 347.648121 l
313.231884 328.497182 l
355.847636 306.792757 l
398.463388 283.588357 l
441.079139 258.039307 l
526.310643 198.522858 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
446.4 360.36 100.153 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-8 -8 m
8 -8 l
8 8 l
-8 8 l
cl

gsave
grestore
stroke
grestore
} bind def
121.461 410.586 o
142.769 402.001 o
185.385 385.003 o
228 367.252 o
270.616 347.648 o
313.232 328.497 o
355.848 306.793 o
398.463 283.588 o
441.079 258.039 o
526.311 198.523 o
grestore
2.500 setlinewidth
2 setlinecap
0.000 setgray
gsave
100.153125 66.60625 m
100.153125 426.96625 l
stroke
grestore
gsave
546.553125 66.60625 m
546.553125 426.96625 l
stroke
grestore
gsave
100.153125 66.60625 m
546.553125 66.60625 l
stroke
grestore
gsave
100.153125 426.96625 m
546.553125 426.96625 l
stroke
grestore
1.500 setlinewidth
1 setlinejoin
0 setlinecap
[9.6 2.4 1.5 2.4] 0 setdash
0.031 0.659 0.255 setrgbcolor
gsave
435.553125 401.96625 m
455.553125 401.96625 l
475.553125 401.96625 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
455.553 401.966 o
grestore
0.000 setgray
gsave
491.553 394.966 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.453125 moveto
/S glyphshow
/TimesNewRomanPSMT 14.0 selectfont
11.4236 -4.69937 moveto
/r glyphshow
16.0857 -4.69937 moveto
/e glyphshow
22.2996 -4.69937 moveto
/m glyphshow
grestore
1.500 setlinewidth
[5.55 2.4] 0 setdash
0.031 0.416 0.416 setrgbcolor
gsave
435.553125 371.96625 m
455.553125 371.96625 l
475.553125 371.96625 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-8 -8 m
8 -8 l
8 8 l
-8 8 l
cl

gsave
grestore
stroke
grestore
} bind def
455.553 371.966 o
grestore
0.000 setgray
gsave
491.553 364.966 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.453125 moveto
/S glyphshow
/TimesNewRomanPSMT 14.0 selectfont
11.4236 -4.69937 moveto
/m glyphshow
22.3133 -4.69937 moveto
/a glyphshow
28.5271 -4.69937 moveto
/x glyphshow
grestore

end
showpage
