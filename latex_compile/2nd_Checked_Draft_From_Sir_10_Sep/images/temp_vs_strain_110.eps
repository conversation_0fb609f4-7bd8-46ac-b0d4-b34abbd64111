%!PS-Adobe-3.0 EPSF-3.0
%%Title: temp_vs_strain_110.eps
%%Creator: Mat<PERSON>lotlib v3.7.1, https://matplotlib.org/
%%CreationDate: Wed Apr 24 13:23:34 2024
%%Orientation: portrait
%%BoundingBox: 25 174 587 618
%%HiResBoundingBox: 25.998438 174.577031 586.001562 617.422969
%%EndComments
%%BeginProlog
/mpldict 11 dict def
mpldict begin
/_d { bind def } bind def
/m { moveto } _d
/l { lineto } _d
/r { rlineto } _d
/c { curveto } _d
/cl { closepath } _d
/ce { closepath eofill } _d
/box {
      m
      1 index 0 r
      0 exch r
      neg 0 r
      cl
    } _d
/clipbox {
      box
      clip
      newpath
    } _d
/sc { setcachedevice } _d
%!PS-TrueTypeFont-1.0-2.53740
%%Title: unknown
%%Creator: Converted from TrueType to type 42 by PPR
15 dict begin
/FontName /TimesNewRomanPSMT def
/PaintType 0 def
/FontMatrix[1 0 0 1 0 0]def
/FontBBox[-568 -307 2028 1007]def
/FontType 42 def
/Encoding StandardEncoding def
/FontInfo 10 dict dup begin
/FamilyName (unknown) def
/FullName (unknown) def
/Weight (unknown) def
/Version (unknown) def
/ItalicAngle 0.0 def
/isFixedPitch false def
/UnderlinePosition -223 def
/UnderlineThickness 100 def
end readonly def
/sfnts[<00010000000900800003001063767420F34DDA810000009C000007C46670676D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>]def
/CharStrings 33 dict dup begin
/.notdef 0 def
/minus 33 def
/space 3 def
/parenleft 4 def
/parenright 5 def
/period 6 def
/slash 7 def
/zero 8 def
/one 9 def
/two 10 def
/three 11 def
/four 12 def
/five 13 def
/six 14 def
/seven 15 def
/eight 16 def
/nine 17 def
/eta 34 def
/E 18 def
/F 19 def
/M 20 def
/S 21 def
/V 22 def
/a 23 def
/c 24 def
/d 25 def
/e 26 def
/i 27 def
/l 28 def
/m 29 def
/n 30 def
/r 31 def
/t 32 def
end readonly def

systemdict/resourcestatus known
 {42 /FontType resourcestatus
   {pop pop false}{true}ifelse}
 {true}ifelse
{/TrueDict where{pop}{(%%[ Error: no TrueType rasterizer ]%%)= flush}ifelse
/FontType 3 def
 /TrueState 271 string def
 TrueDict begin sfnts save
 72 0 matrix defaultmatrix dtransform dup
 mul exch dup mul add sqrt cvi 0 72 matrix
 defaultmatrix dtransform dup mul exch dup
 mul add sqrt cvi 3 -1 roll restore
 TrueState initer end
 /BuildGlyph{exch begin
  CharStrings dup 2 index known
    {exch}{exch pop /.notdef}ifelse
  get dup xcheck
    {currentdict systemdict begin begin exec end end}
    {TrueDict begin /bander load cvlit exch TrueState render end}
    ifelse
 end}bind def
 /BuildChar{
  1 index /Encoding get exch get
  1 index /BuildGlyph get exec
 }bind def
}if

FontName currentdict end definefont pop
end
%%EndProlog
mpldict begin
25.998 174.577 translate
560.003 442.846 0 0 clipbox
gsave
0 -0 m
560.003125 -0 l
560.003125 442.845938 l
0 442.845938 l
cl
1.000 setgray
fill
grestore
gsave
100.153125 66.60625 m
546.553125 66.60625 l
546.553125 426.96625 l
100.153125 426.96625 l
cl
1.000 setgray
fill
grestore
2.500 setlinewidth
1 setlinejoin
0 setlinecap
[] 0 setdash
0.000 setgray
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

86.8563 39.2469 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /four glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
211.753 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
211.753 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

198.456 39.2469 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /two glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
323.353 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
323.353 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

317.103 39.2469 translate
0 rotate
0 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
434.953 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
434.953 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

428.703 39.2469 translate
0 rotate
0 0 m /two glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

540.303 39.2469 translate
0 rotate
0 0 m /four glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
128.053 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
128.053 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
155.953 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
155.953 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
183.853 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
183.853 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
239.653 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
239.653 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
267.553 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
267.553 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
295.453 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
295.453 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
351.253 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
351.253 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
379.153 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
379.153 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
407.053 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
407.053 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
462.853 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
462.853 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
490.753 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
490.753 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
518.653 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
518.653 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

205.306 12.5438 translate
0 rotate
0 0 m /E glyphshow
15.271 0 m /l glyphshow
22.2168 0 m /e glyphshow
33.313 0 m /c glyphshow
44.4092 0 m /t glyphshow
51.355 0 m /r glyphshow
59.6802 0 m /i glyphshow
66.626 0 m /c glyphshow
77.7222 0 m /space glyphshow
83.9722 0 m /F glyphshow
97.876 0 m /i glyphshow
104.822 0 m /e glyphshow
115.918 0 m /l glyphshow
122.864 0 m /d glyphshow
135.364 0 m /space glyphshow
141.614 0 m /parenleft glyphshow
149.939 0 m /M glyphshow
172.168 0 m /V glyphshow
190.222 0 m /slash glyphshow
197.168 0 m /c glyphshow
208.264 0 m /m glyphshow
227.71 0 m /parenright glyphshow
grestore
2.500 setlinewidth
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 66.6063 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

33.9031 57.9266 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /one glyphshow
43.75 0 m /five glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 126.666 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 126.666 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

33.9031 117.987 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /one glyphshow
43.75 0 m /six glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 186.726 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 186.726 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

33.9031 178.047 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /one glyphshow
43.75 0 m /seven glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 246.786 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 246.786 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

33.9031 238.107 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /one glyphshow
43.75 0 m /eight glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 306.846 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 306.846 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

33.9031 298.167 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /one glyphshow
43.75 0 m /nine glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 366.906 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 366.906 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

33.9031 358.227 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /two glyphshow
43.75 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

33.9031 418.287 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /two glyphshow
43.75 0 m /one glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 78.6183 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 78.6183 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 90.6303 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 90.6303 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 102.642 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 102.642 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 114.654 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 114.654 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 138.678 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 138.678 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 150.69 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 150.69 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 162.702 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 162.702 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 174.714 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 174.714 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 198.738 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 198.738 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 210.75 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 210.75 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 222.762 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 222.762 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 234.774 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 234.774 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 258.798 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 258.798 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 270.81 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 270.81 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 282.822 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 282.822 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 294.834 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 294.834 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 318.858 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 318.858 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 330.87 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 330.87 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 342.882 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 342.882 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 354.894 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 354.894 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 378.918 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 378.918 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 390.93 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 390.93 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 402.942 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 402.942 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 414.954 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 414.954 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

24.5594 216.919 translate
90 rotate
0 0 m /S glyphshow
13.9038 0 m /t glyphshow
20.8496 0 m /r glyphshow
29.1748 0 m /a glyphshow
40.271 0 m /i glyphshow
47.2168 0 m /n glyphshow
grestore
0.500 setlinewidth
[1.85 0.8] 0 setdash
0.031 0.235 0.235 setrgbcolor
gsave
446.4 360.36 100.153 66.606 clipbox
535.393125 405.610716 m
524.233125 399.705016 l
513.073125 394.674991 l
501.913125 390.197518 l
490.753125 384.942268 l
479.593125 379.772904 l
468.433125 374.688224 l
457.273125 369.58913 l
446.113125 364.099046 l
434.953125 359.302053 l
423.793125 353.749506 l
412.633125 349.169931 l
401.473125 342.910478 l
390.313125 338.126699 l
379.153125 332.867846 l
367.993125 327.365148 l
356.833125 321.743532 l
345.673125 316.144739 l
334.513125 311.109309 l
323.353125 305.45466 l
312.193125 298.9886 l
301.033125 293.046264 l
289.873125 287.414438 l
278.713125 281.735765 l
267.553125 275.579615 l
256.393125 270.04989 l
245.233125 263.722569 l
234.073125 257.885338 l
222.913125 251.553212 l
211.753125 245.580846 l
200.593125 237.91719 l
189.433125 232.65233 l
178.273125 225.393479 l
167.113125 220.09979 l
155.953125 214.42352 l
144.793125 206.923227 l
133.633125 199.153265 l
122.473125 191.32084 l
111.313125 405.177683 l
100.153125 410.071973 l
111.313125 405.155461 l
122.473125 399.430542 l
133.633125 395.530846 l
144.793125 390.273194 l
155.953125 384.589115 l
167.113125 380.321252 l
178.273125 375.126662 l
189.433125 369.64919 l
200.593125 364.369916 l
211.753125 359.459411 l
222.913125 353.95371 l
234.073125 348.541103 l
245.233125 343.415583 l
256.393125 337.682856 l
267.553125 332.100879 l
278.713125 327.405989 l
289.873125 321.531521 l
301.033125 315.950745 l
312.193125 309.403605 l
323.353125 304.860666 l
334.513125 299.103315 l
345.673125 292.695513 l
356.833125 287.301525 l
367.993125 281.434864 l
379.153125 274.425261 l
390.313125 269.848089 l
401.473125 263.768816 l
412.633125 257.634287 l
423.793125 251.51057 l
434.953125 244.601868 l
446.113125 239.931602 l
457.273125 234.045122 l
468.433125 225.060746 l
479.593125 219.701592 l
490.753125 212.09139 l
501.913125 206.170074 l
513.073125 199.297409 l
524.233125 191.975494 l
535.393125 404.668975 l
546.553125 410.404705 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
446.4 360.36 100.153 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

0 2 m
-2 -2 l
2 -2 l
cl

gsave
0.031 0.235 0.235 setrgbcolor
fill
grestore
stroke
grestore
} bind def
535.393 405.611 o
524.233 399.705 o
513.073 394.675 o
501.913 390.198 o
490.753 384.942 o
479.593 379.773 o
468.433 374.688 o
457.273 369.589 o
446.113 364.099 o
434.953 359.302 o
423.793 353.75 o
412.633 349.17 o
401.473 342.91 o
390.313 338.127 o
379.153 332.868 o
367.993 327.365 o
356.833 321.744 o
345.673 316.145 o
334.513 311.109 o
323.353 305.455 o
312.193 298.989 o
301.033 293.046 o
289.873 287.414 o
278.713 281.736 o
267.553 275.58 o
256.393 270.05 o
245.233 263.723 o
234.073 257.885 o
222.913 251.553 o
211.753 245.581 o
200.593 237.917 o
189.433 232.652 o
178.273 225.393 o
167.113 220.1 o
155.953 214.424 o
144.793 206.923 o
133.633 199.153 o
122.473 191.321 o
111.313 405.178 o
100.153 410.072 o
111.313 405.155 o
122.473 399.431 o
133.633 395.531 o
144.793 390.273 o
155.953 384.589 o
167.113 380.321 o
178.273 375.127 o
189.433 369.649 o
200.593 364.37 o
211.753 359.459 o
222.913 353.954 o
234.073 348.541 o
245.233 343.416 o
256.393 337.683 o
267.553 332.101 o
278.713 327.406 o
289.873 321.532 o
301.033 315.951 o
312.193 309.404 o
323.353 304.861 o
334.513 299.103 o
345.673 292.696 o
356.833 287.302 o
367.993 281.435 o
379.153 274.425 o
390.313 269.848 o
401.473 263.769 o
412.633 257.634 o
423.793 251.511 o
434.953 244.602 o
446.113 239.932 o
457.273 234.045 o
468.433 225.061 o
479.593 219.702 o
490.753 212.091 o
501.913 206.17 o
513.073 199.297 o
524.233 191.975 o
535.393 404.669 o
546.553 410.405 o
grestore
0.500 setlinewidth
1 setlinejoin
[1.85 0.8] 0 setdash
0.031 0.416 0.416 setrgbcolor
gsave
446.4 360.36 100.153 66.606 clipbox
535.393125 405.105611 m
524.233125 400.328439 l
513.073125 395.33445 l
501.913125 390.237158 l
490.753125 384.356683 l
479.593125 379.649781 l
468.433125 374.739876 l
457.273125 369.205947 l
446.113125 364.261208 l
434.953125 358.63839 l
423.793125 353.354312 l
412.633125 348.947109 l
401.473125 342.735103 l
390.313125 337.412586 l
379.153125 332.7225 l
367.993125 327.342326 l
356.833125 321.151941 l
345.673125 316.499093 l
334.513125 310.025226 l
323.353125 304.155562 l
312.193125 299.443855 l
301.033125 293.284702 l
289.873125 286.988612 l
278.713125 281.959188 l
267.553125 276.100935 l
256.393125 269.906948 l
245.233125 264.456503 l
234.073125 257.46672 l
222.913125 250.904564 l
211.753125 245.779644 l
200.593125 239.207879 l
189.433125 232.264943 l
178.273125 226.376661 l
167.113125 218.452344 l
155.953125 211.646946 l
144.793125 207.291995 l
133.633125 197.953266 l
122.473125 190.712432 l
111.313125 404.843149 l
100.153125 410.345846 l
111.313125 405.089395 l
122.473125 400.61012 l
133.633125 395.186702 l
144.793125 390.162683 l
155.953125 385.215541 l
167.113125 379.339871 l
178.273125 374.725461 l
189.433125 369.511052 l
200.593125 364.753099 l
211.753125 359.059411 l
222.913125 353.88284 l
234.073125 348.230593 l
245.233125 343.006574 l
256.393125 337.201775 l
267.553125 332.48166 l
278.713125 327.434217 l
289.873125 321.272061 l
301.033125 315.536932 l
312.193125 310.100901 l
323.353125 304.971177 l
334.513125 299.711723 l
345.673125 293.064882 l
356.833125 287.66729 l
367.993125 282.054083 l
379.153125 276.077512 l
390.313125 269.782623 l
401.473125 263.879927 l
412.633125 257.91717 l
423.793125 250.502162 l
434.953125 244.877543 l
446.113125 238.882354 l
457.273125 231.201881 l
468.433125 227.162246 l
479.593125 218.676969 l
490.753125 214.53403 l
501.913125 205.470375 l
513.073125 197.788701 l
524.233125 190.945465 l
535.393125 404.822128 l
546.553125 409.313415 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
446.4 360.36 100.153 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-5 -5 m
5 -5 l
5 5 l
-5 5 l
cl

gsave
grestore
stroke
grestore
} bind def
535.393 405.106 o
524.233 400.328 o
513.073 395.334 o
501.913 390.237 o
490.753 384.357 o
479.593 379.65 o
468.433 374.74 o
457.273 369.206 o
446.113 364.261 o
434.953 358.638 o
423.793 353.354 o
412.633 348.947 o
401.473 342.735 o
390.313 337.413 o
379.153 332.723 o
367.993 327.342 o
356.833 321.152 o
345.673 316.499 o
334.513 310.025 o
323.353 304.156 o
312.193 299.444 o
301.033 293.285 o
289.873 286.989 o
278.713 281.959 o
267.553 276.101 o
256.393 269.907 o
245.233 264.457 o
234.073 257.467 o
222.913 250.905 o
211.753 245.78 o
200.593 239.208 o
189.433 232.265 o
178.273 226.377 o
167.113 218.452 o
155.953 211.647 o
144.793 207.292 o
133.633 197.953 o
122.473 190.712 o
111.313 404.843 o
100.153 410.346 o
111.313 405.089 o
122.473 400.61 o
133.633 395.187 o
144.793 390.163 o
155.953 385.216 o
167.113 379.34 o
178.273 374.725 o
189.433 369.511 o
200.593 364.753 o
211.753 359.059 o
222.913 353.883 o
234.073 348.231 o
245.233 343.007 o
256.393 337.202 o
267.553 332.482 o
278.713 327.434 o
289.873 321.272 o
301.033 315.537 o
312.193 310.101 o
323.353 304.971 o
334.513 299.712 o
345.673 293.065 o
356.833 287.667 o
367.993 282.054 o
379.153 276.078 o
390.313 269.783 o
401.473 263.88 o
412.633 257.917 o
423.793 250.502 o
434.953 244.878 o
446.113 238.882 o
457.273 231.202 o
468.433 227.162 o
479.593 218.677 o
490.753 214.534 o
501.913 205.47 o
513.073 197.789 o
524.233 190.945 o
535.393 404.822 o
546.553 409.313 o
grestore
0.500 setlinewidth
1 setlinejoin
[1.85 0.8] 0 setdash
0.031 0.659 0.659 setrgbcolor
gsave
446.4 360.36 100.153 66.606 clipbox
535.393125 318.009002 m
524.233125 319.374165 l
513.073125 321.137527 l
501.913125 321.910499 l
490.753125 322.484072 l
479.593125 322.100889 l
468.433125 323.797584 l
457.273125 322.786775 l
446.113125 322.243232 l
434.953125 322.354943 l
423.793125 321.584373 l
412.633125 321.008398 l
401.473125 319.414406 l
390.313125 319.020412 l
379.153125 317.151345 l
367.993125 314.975972 l
356.833125 313.161559 l
345.673125 309.342344 l
334.513125 307.42643 l
323.353125 304.863069 l
312.193125 300.864875 l
301.033125 298.132145 l
289.873125 292.694913 l
278.713125 287.950173 l
267.553125 283.308135 l
256.393125 275.79523 l
245.233125 269.900942 l
234.073125 263.185633 l
222.913125 254.919575 l
211.753125 245.917182 l
200.593125 236.382056 l
189.433125 223.867354 l
178.273125 212.019318 l
167.113125 196.908222 l
155.953125 178.668 l
144.793125 156.707661 l
133.633125 131.771951 l
122.473125 92.775593 l
111.313125 317.687681 l
100.153125 317.449843 l
111.313125 318.386779 l
122.473125 320.273264 l
133.633125 320.530921 l
144.793125 321.044434 l
155.953125 321.729719 l
167.113125 322.497285 l
178.273125 323.151339 l
189.433125 323.507495 l
200.593125 322.356745 l
211.753125 322.47206 l
222.913125 321.442031 l
234.073125 321.303293 l
245.233125 319.563354 l
256.393125 318.837229 l
267.553125 316.359153 l
278.713125 314.672669 l
289.873125 313.179577 l
301.033125 311.467867 l
312.193125 308.289492 l
323.353125 304.508114 l
334.513125 300.378989 l
345.673125 297.666079 l
356.833125 293.574792 l
367.993125 287.55678 l
379.153125 282.736364 l
390.313125 277.079313 l
401.473125 270.113554 l
412.633125 262.264313 l
423.793125 256.13519 l
434.953125 246.0355 l
446.113125 235.39467 l
457.273125 224.749635 l
468.433125 211.854153 l
479.593125 197.296209 l
490.753125 178.496228 l
501.913125 158.960512 l
513.073125 132.403782 l
524.233125 95.231447 l
535.393125 318.419211 l
546.553125 317.062456 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
446.4 360.36 100.153 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -5 m
1.326016 -5 2.597899 -4.473168 3.535534 -3.535534 c
4.473168 -2.597899 5 -1.326016 5 0 c
5 1.326016 4.473168 2.597899 3.535534 3.535534 c
2.597899 4.473168 1.326016 5 0 5 c
-1.326016 5 -2.597899 4.473168 -3.535534 3.535534 c
-4.473168 2.597899 -5 1.326016 -5 0 c
-5 -1.326016 -4.473168 -2.597899 -3.535534 -3.535534 c
-2.597899 -4.473168 -1.326016 -5 0 -5 c
cl

gsave
grestore
stroke
grestore
} bind def
535.393 318.009 o
524.233 319.374 o
513.073 321.138 o
501.913 321.91 o
490.753 322.484 o
479.593 322.101 o
468.433 323.798 o
457.273 322.787 o
446.113 322.243 o
434.953 322.355 o
423.793 321.584 o
412.633 321.008 o
401.473 319.414 o
390.313 319.02 o
379.153 317.151 o
367.993 314.976 o
356.833 313.162 o
345.673 309.342 o
334.513 307.426 o
323.353 304.863 o
312.193 300.865 o
301.033 298.132 o
289.873 292.695 o
278.713 287.95 o
267.553 283.308 o
256.393 275.795 o
245.233 269.901 o
234.073 263.186 o
222.913 254.92 o
211.753 245.917 o
200.593 236.382 o
189.433 223.867 o
178.273 212.019 o
167.113 196.908 o
155.953 178.668 o
144.793 156.708 o
133.633 131.772 o
122.473 92.7756 o
111.313 317.688 o
100.153 317.45 o
111.313 318.387 o
122.473 320.273 o
133.633 320.531 o
144.793 321.044 o
155.953 321.73 o
167.113 322.497 o
178.273 323.151 o
189.433 323.507 o
200.593 322.357 o
211.753 322.472 o
222.913 321.442 o
234.073 321.303 o
245.233 319.563 o
256.393 318.837 o
267.553 316.359 o
278.713 314.673 o
289.873 313.18 o
301.033 311.468 o
312.193 308.289 o
323.353 304.508 o
334.513 300.379 o
345.673 297.666 o
356.833 293.575 o
367.993 287.557 o
379.153 282.736 o
390.313 277.079 o
401.473 270.114 o
412.633 262.264 o
423.793 256.135 o
434.953 246.035 o
446.113 235.395 o
457.273 224.75 o
468.433 211.854 o
479.593 197.296 o
490.753 178.496 o
501.913 158.961 o
513.073 132.404 o
524.233 95.2314 o
535.393 318.419 o
546.553 317.062 o
grestore
2.500 setlinewidth
0 setlinejoin
2 setlinecap
0.000 setgray
gsave
100.153125 66.60625 m
100.153125 426.96625 l
stroke
grestore
gsave
546.553125 66.60625 m
546.553125 426.96625 l
stroke
grestore
gsave
100.153125 66.60625 m
546.553125 66.60625 l
stroke
grestore
gsave
100.153125 426.96625 m
546.553125 426.96625 l
stroke
grestore
0.500 setlinewidth
1 setlinejoin
0 setlinecap
[1.85 0.8] 0 setdash
0.031 0.235 0.235 setrgbcolor
gsave
285.853125 157.60625 m
305.853125 157.60625 l
325.853125 157.60625 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

0 2 m
-2 -2 l
2 -2 l
cl

gsave
0.031 0.235 0.235 setrgbcolor
fill
grestore
stroke
grestore
} bind def
305.853 157.606 o
grestore
0.000 setgray
gsave
341.853 150.606 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.796875 moveto
/eta glyphshow
/TimesNewRomanPSMT 14.0 selectfont
10.7595 -4.35562 moveto
/one glyphshow
grestore
0.500 setlinewidth
1 setlinejoin
[1.85 0.8] 0 setdash
0.031 0.416 0.416 setrgbcolor
gsave
285.853125 127.60625 m
305.853125 127.60625 l
325.853125 127.60625 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-5 -5 m
5 -5 l
5 5 l
-5 5 l
cl

gsave
grestore
stroke
grestore
} bind def
305.853 127.606 o
grestore
0.000 setgray
gsave
341.853 120.606 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.796875 moveto
/eta glyphshow
/TimesNewRomanPSMT 14.0 selectfont
10.7595 -4.35562 moveto
/two glyphshow
grestore
0.500 setlinewidth
1 setlinejoin
[1.85 0.8] 0 setdash
0.031 0.659 0.659 setrgbcolor
gsave
285.853125 97.60625 m
305.853125 97.60625 l
325.853125 97.60625 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -5 m
1.326016 -5 2.597899 -4.473168 3.535534 -3.535534 c
4.473168 -2.597899 5 -1.326016 5 0 c
5 1.326016 4.473168 2.597899 3.535534 3.535534 c
2.597899 4.473168 1.326016 5 0 5 c
-1.326016 5 -2.597899 4.473168 -3.535534 3.535534 c
-4.473168 2.597899 -5 1.326016 -5 0 c
-5 -1.326016 -4.473168 -2.597899 -3.535534 -3.535534 c
-2.597899 -4.473168 -1.326016 -5 0 -5 c
cl

gsave
grestore
stroke
grestore
} bind def
305.853 97.6062 o
grestore
0.000 setgray
gsave
341.853 90.6062 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.796875 moveto
/eta glyphshow
/TimesNewRomanPSMT 14.0 selectfont
10.7595 -4.35562 moveto
/three glyphshow
grestore

end
showpage
