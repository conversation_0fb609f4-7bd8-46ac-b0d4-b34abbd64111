%!PS-Adobe-3.0 EPSF-3.0
%%Title: Fig_5.eps
%%Creator: Matplotlib v3.7.1, https://matplotlib.org/
%%CreationDate: Thu Oct 17 15:35:10 2024
%%Orientation: portrait
%%BoundingBox: -363 7 976 785
%%HiResBoundingBox: -363.500000 7.976875 975.500000 784.023125
%%EndComments
%%BeginProlog
/mpldict 11 dict def
mpldict begin
/_d { bind def } bind def
/m { moveto } _d
/l { lineto } _d
/r { rlineto } _d
/c { curveto } _d
/cl { closepath } _d
/ce { closepath eofill } _d
/box {
      m
      1 index 0 r
      0 exch r
      neg 0 r
      cl
    } _d
/clipbox {
      box
      clip
      newpath
    } _d
/sc { setcachedevice } _d
%!PS-TrueTypeFont-1.0-2.53740
%%Title: unknown
%%Creator: Converted from TrueType to type 42 by PPR
15 dict begin
/FontName /TimesNewRomanPSMT def
/PaintType 0 def
/FontMatrix[1 0 0 1 0 0]def
/FontBBox[-568 -307 2028 1007]def
/FontType 42 def
/Encoding StandardEncoding def
/FontInfo 10 dict dup begin
/FamilyName (unknown) def
/FullName (unknown) def
/Weight (unknown) def
/Version (unknown) def
/ItalicAngle 0.0 def
/isFixedPitch false def
/UnderlinePosition -223 def
/UnderlineThickness 100 def
end readonly def
/sfnts[<00010000000900800003001063767420F34DDA810000009C000007C46670676D
3775A72F0000086000000573676C7966DEE31B7000000DD4000048BC68656164CAFCED46
00005690000000366868656113290CC9000056C800000024686D7478BA590BC2000056EC
000000C46C6F63616F588152000057B0000000646D617870069410AC0000581400000020
70726570D2662BF40000583400000F18058E0000054C001F054C001C0394001B0000FFE1
0000FFE40000FFE8FE4AFFFC056B0023FE6AFFE00313000000AD000000AD000000000025
0096009F002400F0013100C200C0004A00A6004100500094004700CF00AF000E007901CB
00040023004400A80025011F0002004600170105009900D9005C007200E500E00028004B
00DE011200240045007000160039FFE90016004B0088FFB900D9000A004300AE00BA016C
0153002F00430048022C012B0025008FFFC000170028FFCDFFD80025009D00E50124FFB1
0048009D00E600110027007F00910012006A00CAFFFC00000024006200A7017C01E90021
0060008B0434048AFF6B003B00B500D5014BFF6B004D007905D809B5006C009100A30117
01C0FFDFFFE700BE04010065007F00820088009900B200C0022E034305A000200026003D
004E00610065007B00D9011301310340FF27FF42FF99004E00A700F2022B02C603070011
002B0049005F008D00A100AF00D600E400F5010B0135019D01AB01AB01D101EE05D80000
004B0075007A0080009D00A600A700AC00B9013101310217021700020017002900550080
008F00A500B200B300D0014B015901C001C103A50530FE3FFF14FF15FFE7FFFF002A0058
0099009F00C100E400F40130015901AB01AB03220374041E04740532FD81004D0064009C
00D000D100D600DE00E500F500F8012A012A01E1027E027FFF57FFA8FFE500000008001F
00380051005A006F0076007700A200C000C200C400F101FB0209027E02CF04C5057A05F0
FF92001200260042004B004F005100530064008B00AE00B200B800B800D600F501110120
01310138014E01520167018F019601B801D901D902060221027102EA03B003CB03DC0436
0505FF3A00120016001E001F002300570068006C007E0088009200A500A800C500C90115
0126012D013001D601D901F6023B0244024402A302CF02DE0385038F04FC0586FEE0FEEB
FEFBFF8A0007004400470058007500AA00E400EF011601200129016A017301E3027E0290
02B4030E0310032303350341035403590388039403C803CE047204AB04DA0549056105AB
0761FE6EFED1FF4BFF84000000010006001E0027002C0034003700620066006A006B006C
007000700072007C0081008A008E0091009200A000AB00B800BF00C900D500DD00EC00F4
0100012101300169016A016D017C0185018E018E019901AC01C101C501C901E101F601F6
01F60222022202280236023F02430246026702850285029402D002D602E8031C0363037F
03800380039E03B603D90400040404FF053205320548058B05A706CB07280748076208CC
FCEDFD2AFD59FDDEFE00FE1AFE5BFE96FEC1FEE7FF56FF7900010025002D002E007C0087
0091009900A100A500A500AA00AF00B600C600CC00D700DD00EC00F20102010501170118
0123012A012C0131013F014701490149014D01510151015501550157015A015A01610162
01680168017F0180018201830184018D0195019501950198019901A501A901B601B601B7
01BA01BA01D501DF01E601EA01F2020002000203021702250227022F0239024302430247
024F025202520267026F026F027002720276027E02A702B302B902D603130325032D0361
0371039903AE03C203D403F90402042C042F043C04560467048304CF04D104D804FB051F
05450568059E05C2061B06340655066A069806AF06E806FC070607500762077C07D407FF
082500AD00C700AA00B5000000000000000000000000002F06CF01730514047802DF009C
0018037005870155002500060254036C038E03D2056601F0032001DA018A0369036BFFA3
034602F8036F015602BF0122031F053A0366008C00FF01AB02E102F402E70415015402E9
0128049101B7026F034302060000000005D30415048305E8000002D7003A027D01C002C5
03830383FFBD003A059E01DF059E02D1002004E0021300DF01C001870297000000CE0269
028B0058043405FB0069015A01A905780182013E0288012A03D4049E00E5032302F301F0
0196007A00CD014A0424025E023901AB00CF00FD011E00ED017100700195004001BB01DD
01B8000101A803A7014C020C018D01B0020D0137010000CD032101D4030A005900000000
01260215015002F0025503BC06D00335010100D000D2007A01030130007C000000000000
000000FE006E006600940227002B0045004D00D3013200180097004100F4FEBCFFE90016
05D8058B009100A1032C00520030005D02CB003A009200E500E500580086003200BA0099
008800300298007CFF8001640028004D0065000200B8016A002F010B001100170100007F
00040016022200A6005F000000F8000A00CA0043004B01EE0077012000F401C00028045F
0000008C044500C20060007B008B008B0064005D00C2009C009206B505D3004F01170000
0420FE9E00CC00DC005E004600E30032001A003C0091005A00A1042C0041002000490071
009C009CFE4800400040008600CB0102007D003A003E006A0050044800290096FF6A0097
006900E0004C001B00C90069FF970043FFBD0052FF83FF8B005FFFA1FF5C00670053FFA8
002A0076FFB20036008705590256052B043400DE00C901C4004800DB018B00B3004800DA
01160125011800EA00EA00AE0046003E05BB008A04D70053003FFF8CFFD5001500280022
00990062004A00E4006D00EE00E5004803C00033FE4E02B1FF460370007905DF0051FFA7
FF1F010A0068FF6C004F00BC00A5070500AB0080001E05A54040403F3E3D3C3B3A393837
363534333231302F2E2D2C2B2A292827262524232221201F1E1D1C1B1A19181716141312
11100F0E0D0C0B0A090807060504030201002C4523466020B02660B004262348482D2C45
2346236120B02661B004262348482D2C45234660B0206120B04660B004262348482D2C45
23462361B0206020B02661B02061B004262348482D2C45234660B0406120B06660B00426
2348482D2C4523462361B0406020B02661B04061B004262348482D2C0110203C003C2D2C
20452320B0CD442320B8015A51582320B08D44235920B0ED51582320B04D44235920B090
51582320B00D44235921212D2C20204518684420B001602045B04676688A4560442D2C01
B10B0A432343650A2D2C00B10A0B4323430B2D2C00B0172370B101173E01B0172370B102
17453AB10200080D2D2C45B01A234445B01923442D2C2045B00325456164B05051584544
1B2121592D2CB00143632362B0002342B00F2B2D2C2045B0004360442D2C01B00643B007
43650A2D2C2069B04061B0008B20B12CC08A8CB8100062602B0C642364615C58B0036159
2D2C45B0112BB0172344B0177AE4182D2C45B0112BB01723442D2CB01243588745B0112B
B0172344B0177AE41B038A45186920B01723448A8A8720B0A05158B0112BB0172344B017
7AE41B21B0177AE45959182D2CB0022546608A46B040618C482D2C4B53205C58B0028559
58B00185592D2C20B0032545B019234445B01A23444565234520B00325606A20B0092342
23688A6A606120B01A8AB000527921B21A1A40B9FFE0001A45208A54582321B03F1B2359
61441CB114008A5279B31940201945208A54582321B03F1B235961442D2CB11011432343
0B2D2CB10E0F4323430B2D2CB10C0D4323430B2D2CB10C0D432343650B2D2CB10E0F4323
43650B2D2CB11011432343650B2D2C4B525845441B2121592D2C0120B003252349B04060
B0206320B000525823B002253823B002256538008A63381B212121212159012D2C4BB064
51584569B00943608A103A1B212110592D2C01B005251023208AF500B0016023EDEC2D2C
01B005251023208AF500B0016123EDEC2D2C01B0062510F500EDEC2D2C20B00160011020
3C003C2D2C20B001610110203C003C2D2CB02B2BB02A2A2D2C00B00743B006430B2D2C3E
B02A2A2D2C352D2C76B802B023701020B802B04520B0005058B00161593A2F182D2C2121
0C6423648BB84000622D2C21B08051580C6423648BB82000621BB200402F2B59B002602D
2C21B0C051580C6423648BB81555621BB200802F2B59B002602D2C0C6423648BB8400062
6023212D2CB4000100000015B00826B00826B00826B008260F10161345683AB001162D2C
B4000100000015B00826B00826B00826B008260F1016134568653AB001162D2C4B53234B
515A5820458A60441B2121592D2C4B545820458A60441B2121592D2C4B53234B515A5838
1B2121592D2C4B5458381B2121592D2C014B53234B515AB00225B00425B0062549234518
69525A58B00225B00225B00525462345696048592121212D2CB0134358031B02592D2CB0
134358021B03592D2C4B54B012435C5A58381B2121592D2CB012435C580CB00425B00425
060C6423646164B807085158B00425B00425012046B01060482046B0106048590A21211B
2121592D2CB012435C580CB00425B00425060C6423646164B807085158B00425B0042501
2046B8FFF060482046B8FFF06048590A21211B2121592D2C4B53234B515A58B03A2B1B21
21592D2C4B53234B515A58B03B2B1B2121592D2C4B53234B515AB012435C5A58381B2121
592D2C0C8A034B54B00426024B545A8A8A0AB012435C5A58381B2121592D2C462346608A
8A462320468A608A61B8FF8062232010238AB9035803588A70456020B0005058B00161B8
FFBA8B1BB0468C59B0106068013A2D0000010054FE4A027C058E0013003A40239611A711
02860C8911020A980911009801130100000A09A80E22500601068014545E182B10F65DED
FD3C3C103C003FED3FED3130005D015D01152627260235100037150606021514171E0202
7C9765909C0132F67B9E4E211A4A7DFE6F254C6691018AD4013601FF6E2A44ECFE96C5D6
AF8AA79A0001002EFE4A0256058E00130039402429042A08480503009801110A98091300
0101090AA80E222006300640060306801558A4182B10F65DEDFD3C3C103C003FED3FED31
30015D13351617161215100007353636123534272E022E98658F9CFECFF77B9F4D21194B
7C05642A4B6692FE77D5FECAFE016E2545EB016BC5D5B08AA69A00010053018002580217
0003003F40200205801D64367F050101000200B0030300021001500160019001A001D001
0601B80134B5008004545A182B10F6FD5D3C103C002FED3C103C3130015D2B1321152153
0205FDFB0217970000010091FFE4016F00C2000B002B401C0040060B034009403A350940
3F355F09019F09AF090209850C6A7A182B10F671722B2BED003FED313025321615140623
222635343601002F40412E2E4141C2412E2E41412E2F400000010003FFE4023E058E0003
005240090005CB1F6736000101B80327400D02031402020303000002010B00B8011F4014
2003300360037003A003E0030603BB01AD02CB04B8016FB1DF182B10F6EDF45DED003F3C
3F3C87052E2B7D10C431302B01012301023EFE155001EB058EFA5605AA000002004AFFE8
03B705680010002400BAB2610820B80106B2050515B80106B20D0D1AB8010F4012091A00
2640260240266026A026E026042611B8010F400E5F006F007F008F00A00005001925BA01
1E010100182B4E10F45D4DED4E105D71F64DED003FED3FED31304379404C012423242224
020602010301020607251C1B1D1B1E1B030613260F250B2618191719020621041162001F
061A6201140E116200160C1A620124012062011B082062011210156200190A1562002B2B
2B2B012B2B2B2B2A2B2B2B2A2B2A2A811334123736333217161114020623222726371017
16333236373611342726272623220706024A8C745A609C7C9B88D362C2816DC445397136
741E2E302439293A44354834029EE8014F52419FC5FEAFECFEB695E5C1F7FEE8B1956172
AC0139E89B7330213D53FE9C000100F000000306056800160097401440186018A018E018
04001840180276008600020E411301690141000901A00022000301690141000801A00023
000001F8000F016900160141000001F2400E010F0F020902010509080C020300BA01F700
03014940120E0E0F401135300F7F0F900FA00F040F1917BA022401E400182B4E10F45D2B
3C4D10EDE4103C003F3C3F3C11123901113900F5EDFC01F52B2B3130005D01715D132533
11141616171521353E02351134272626232207F0014A21133C5CFE026038160A07251A25
4204C7A1FB8772381E022525021D317A02DC942A201E1F000001002C000003AB0568001E
0146408207180B3917181C3D3418401C3D3419401C3D340F1E161629073C074907A90706
40205B045A085B175A186B08741174129C0B9D0E9911AC0BAC0EC905C917C818D917D918
E020F904F9171515011D0419051B15191619171D180709170B180B1D3419471989178F20
0718190202171A190C19060D031902050618171615140713040DB8016840090940140C3F
80090109B80333400C10051A8F19019F19AF190219BA03330003018DB301020C1EB8018D
400D0006E24F135F136F137F130413B801074013400001001A002040208020036020A020
022019BB01F90003000D014040145F026F027F028F02BF02CF02DF02EF020802191FBA01
8E010100182B4E10F45D4DE43CED4E105D71F65D4DF45DED10ED003F3CEDFD5D713C3FFD
712BE411121739111239011112393902100E3C8710057DC40EC431300171725D005D012B
2B2B002B0103213500003534262322060723363633321615140706070207213236363703
AB5FFCE0016101209E6E649F262519CF9BA5DD304AA6F93E01626C57461A0105FEFB2501
420198A981A67571B9C6D4906767A2B5FEF03810312D00010053FFE8035605680032014C
B9000AFFE0B20C3909B8FFC0403C0C394109450A460B4B2204CF09012929382940346034
CF34E034F70A0700340141097F237A2EAA24B924BA2EC92EDF23DF25DB2EEA22E9250C49
0829B8018CB328281000B802E4B3D0300130B80334B5030510160116B8019FB51D402B2F
341DB80143B3100D2928BB01680014000902E3400B50208020029020A0200220B80333B3
B00C010CB80190400B502D802D02902DA02D022DB80333B55F077F070207B802E5400A40
3401A034C034023400B8013EB74013BF1302131933BA011E01E800182B4E10F45D4DE410
5D71F65DED5D71F45DED5D71E410F43C003FED2BED723FED5DED12392FED313043794036
2E2F1E260A0F04062221232124212521040605250E26260A2062012F042D62011E0F2062
01210B2762010A092E063062011F0D1D62002B2B103C2B012B2B2B2B2B2A81818181005D
01715D7200712B2B13363633321716151407161615140706212226353436333217161617
16333236353427262726262323353E02353426232207683AB184A35742BA7D807092FEEB
89632F21191A117817252A6697231A1F2B964E204F9F4881609B68044A89956A4F5A949E
31B67BB081A844271D2C08053F060B9E6C4F4B381D28411E0A5E844F677FA60000020020
000003B90568000A000D00D74028160D010F00450D400F03400F012A0C01150701020601
0309050601040C0B0D00040C0D04060C0D0DB8011C401207081407070808030407060C08
001F0D010DBB019C0006000B019F400D050106090806060805040C0C04BB014900030008
0166400B09090F03019F03AF030203B801F74018013F0001001A000F01600FA00FE00F03
0F0640070107190EBA018E010100182B4E10F4713C105D71F65D3C4DF45D713C10E610FD
3C003F3F392F103C103C10ED10FD723C1139113901111239872E2B047D10C4011112390F
0F0F313000725D015D7172011523112311213501331123110103B9B6A5FDC202756EA5FE
2401F48EFE9A0166800382FC8C02A1FD5F0000010062FFE80379054C002101064043A404
0112591E691E7F057F067F1D8C048D1D07350235215503552057216B1C7605791C87028A
1A8A1CA303A809A023E0230F0023010F1011121315161708140D020303B8011C40112021
1420030420211A1B1C030718040503B8019FB320202113BA01F900180313400C0D0D01E2
0002E22121000400BA013E001B0147B58007A0070207B801F5400A402301402360230223
16B801F9B61002A021012103B8019F4009204010A01002101922BA019501E900182B4E10
E45D4D10ED105D3C10ED105D71F65DEDE4003F3C10ED10ED3FEDED12392FED0111391112
173987082E2B057D10C40011121739313001715D005D435C58400B6B046F12641E700570
1D055D59015D010721070417161514060607062322263534363332161716333236353426
2726270103794EFE685901099B8557845173797A6F2E231A272F4B4D75B19E8B6DBC0104
054CAAB6279E88B86BB680273753321C2B102134B17F7BD53A2D07020F0000020058FFE8
03B105680018002800ED402A7509760A770E8209D925E925060603017D037A047A168517
043C0828060503231928190603205F080108B80143B620260126260F01B8018DB3180005
20B80106B30F0D0100BA01400023010F40120B1A002A402A02402A602AA02AE02A042A19
BA013E001B010F4012001310132013301340139013A01307131929BA011E010100182B4E
10FC5D4DFDE44E105D71F64DEDF43C003FED3F3CED12392F5DED72121739011112173931
304379402C1C2509121D1C1E1C020611250D2625092362011F101B6200210E236201240A
2662011C12206200220C206200002B2B2B012B2B2B2B2B2A8181005D01715D01150E0307
363332161514070623222726113412243633010615141617163332363534262322060396
84A7A36B2490918BCC677CCC8B61BE92010FF86BFDCC12474633495789887D2657056825
0D4FA2C78963E0B0AA8CAA5CB3011DB60148FE58FD44875360E1422FA498ABFA20000001
004CFFE403A5054C000B00ADB90004FFE0B3131B3E05B8FFE0402B131B3E19080105032A
094200400D600DA00DCA01C902D801D802E00D0B1A00010B01000D400D0302050404B801
93401A03021403030204030D0540473505E2010640473506E201000400B80199B6A00B01
0B190C05BA02E8000302E7400C0004100440045004A0040504BC02E6000C011E01010018
2B10F65DEDE44E10F45D4DF4003F3CED2B10ED2B3F3C87052E2B870E7DC431300171725D
0072012B2B132115012301212207060727CE02D7FE3C700195FE8B713054331D054C26FA
BE04C51B2E600B000003007CFFE8038A0568001900260033017A40BA59010109331F332A
276F337A22800B800C801A801B822580268A33A918A51AA725B30CB41AB726BB27C50AC5
0BD70D1607000A01060D021A0927160D171A250D250E4B018C01830D850EA9000E3B003A
014B004B0149285F015B275C336A006A01690267266A2768337B017C27762C7C338F048F
0580078008821182128F148F1698049608941196129B16A626AD27AD33B804B608E90BEA
0CE90EE927E93229070D09273A0039013832054408000C1A2704000C1A2704101720B801
06B206052DB80106B2130D1D4109010F00090140000901400030010F0010016640100035
40350240356035A035E035043523BC010F0003013E002A010F400A301740179017031719
34BA01EE01E900182B4E10F45D4DEDF4ED105D71F6EDE4F4ED003FED3FED111217390117
393130437940322B2F1E2211160408152521052362001F071D62012C142A62002E123062
0122042062011E082062012B162D62002F112D6200002B2B2B2B012B2B2B2B2B81818181
01715D00715D007201262635343633321615140607161716151406232227263534362536
3635342623220615141617130606151416333236353427260189A15DCCA9A4C86CABB039
4CDAB1C16C5679013178407666668035313653508D6D6C82264702AB84A05684BFB2724C
9E6B884E66718FCB7961735AB1D66C7D4F6977764F34682FFEE746A560819B7A5748396A
00020051FFE403A8056800170027010C40337B27D905D722D92704680479057D087A0977
0C780D791377208B0883130A09088F29023B0805271821271805031E2504000705BA0163
00180140B42750070107B8014340092F256F25022525001EB80106B20E0501B8018CB417
17000D18BA013E001A010FB70012101220120312B8016540120029402980290340296029
A029E029042901BA01400021010FB7400ABF0A020A1928BA011E01E800182B4E10F45D4D
EDE4105D71F65DFDE4003F3C10ED3FED12392F5DED7210F4ED1112391112173901111239
3931304379402A1B2408110C2610251C2623261F0D2162001D0F1A62012408216200200B
1E62011B111E62012209256200002B2B2B012B2B2B2B2B2B2B818101715D005D17353636
123706232226353437363332171615140207062301363534262623220615141716333236
6C82E0D1299D7F8FCC667BC6A77792DEC6A1BE02331242794D598659415F2E7E1C250275
0124AF65DDB7B28BA98AABFBE2FE79816A02B9824E61E178A09ED377562C000300220000
04E6054C001E002B0038025940305A005A1E8900881E8933991A9D27AC1AAC27E91AEA27
E72F0C38007A2779339A329933A524AA33D81AD827D8280A043AB802E7B30F67363AB8FF
C0B31C22343AB8FFC040E31517343340212C343340191E34324023283432401B1D344424
4425891AD901D624DA33E52507042401250D32100315061B1A141E1624162815302E3245
244A34570158195A279602111000103A55015A24603A703A803AA03A081A301A32500003
10071A241E28192F040602031E171E4F3388249A24D93307203A403A503A630263036005
6006600760307606731A731B701E742473277A288406861B861E8F33803ACA2FDA2FEB24
FA241959080F1F1B092122101F1B16212333240003042C00352B1F24032229382C33032E
2228353509162928171716022E280808090890260126B8FFC0B23A3526B8FFC0B2423526
B8FF80B33F413426B8FFC0B343463426B8FFC040144235264C5F1C010A1E301C021C5504
2B1F382C31B8FFC040104535124004A004020004A004E0040304B8FFC0400A0D11340004
0120040104B801D1B62C22100F9E393ABC01D100210061011800182B2B4EF43C4DEDFD5D
712B5D714358B90031032DE91BB90031032DED592B103C3C3C10F45D72ED2B2B2B2B2B72
003F3C10ED3F3C10ED1112392FED12173911121739113901111217392B2B313043794041
2F342328181E01071A1B191B0206062624250225332628182633012F07313301231E2633
033401313303271B29330130052E3300251D2233001E32033533010100103C2B3C2B2B2B
012B2B2B2B2B2B2B2B2A81818181015D7101727272007271002B2B2B2B012B2B2B005D00
5D0116171615140606232135333237363511342726232335213217161615140625161633
3236363534262322071116333236353426262322060703B28D466180DFE5FD8033552517
1D274D33024AA463969E7CFD7B255F3992934EC2BA64507471B5BE56C28F3E581B02B41E
425C8565B95525362372036C7E212C251824B77766A10F07073F824D77A816FB6F1BA378
4F92540405000001004AFFE1050F056B002400FB4042091E2F012F022F032F1F960F991E
A30FA312B60FB7120B081E011617017D037B1578168D038A169D03961AAC03BB03090C03
0E04021D48035903052F081011241B00B80105B5021B0101BA00B8034BB6209A05281C03
01B802DFB5112BB0100110B80341B58F0D9F0D020DB8032F4029140902AC000101013210
ACAF11011F113F1102111A40260126093C2018010F181F18021849256463182B4E10F45D
724DED4E105DF672714DEDF471ED003FFD71F45DF4E63FEDECF4ED0110EDF4ED10C93130
43794020151B060C07251A260B261625061B092D000C15092D000819052D010A170D2D00
002B2B012B2B2B2B2B2B818101715D0072715D0113232626232206021514121633323637
170604232027263534122433321716333237363704D11F1F3EE6A187DA7D76ED9884CA79
1F66FEF0BBFEAFB98AB6013FBD938F2A121B141A0B056BFE33CFB689FED4DFB8FEF29071
A814B5A8FABAFCCB0154BB4816131B3000010048FFE105AA056B0034012540540A04462E
0219271A28021018101902203640366036780870187019782A90189019B018B0190B2D2F
760B870B0318362E1A503670368C04AD04E036040C03860BC0360348081E1F1B18F32212
1F1B172123341B00B80105B3021B0101BA01B30000034B402B319A2C1718182206282C03
0E282209012B1F1E22111150129012020F124F1202001210127F12FF12041212B802F840
100A3C5026010F261F2602264935648A182B4E10F45D724DFDF62F5D71723C10FD3CE400
3FED3FED12392F3C10ECF4ED0110EDF4ED2B2B313043794034202B07100825282729272A
2703060C2624250F21113B01201F1011072B0A2D000D230A2D0010200E3B000927062D01
0B250E2D002B2B2B012B2B103C103C2B2B2B2A2B818101715D2B005D015D017200727101
132326272623200706151412163332363711342626233521152322070615110606232027
2635343736373633321617163332363704E92323355479BEFEFD877196F3804B8C411F41
52020D194E1D1473E089FE77CC995666B295CB4A796F3813131B03056BFE54A05175CDAD
EFC2FEC09526250188663F21262634256DFE613E3AFCBDF7B3A4C3695718291523330001
00230000059D054C004500C740717047A047D047E0470413479E1C40365047E04702121F
1B0B2122221F1B1C2122341F1B2E2122451F1B3E2122021F1B0A2123131F1B1B2123251F
1B2D2123351F1B3D21230100232324241B3E3D3D0B0B0A022E2D2D1C1C1B081213220240
2201DF2201202230227022A022D022E0220622B80220401A10476047C047032047014745
2522355034603402349E4661DC182B4D10F4723C4DFD3C107172F45D71723CFD3C003F3C
103C103C3F3C103C103C12392F3CFD3C2B2B2B2B2B2B2B2B3130015D2B015D0121113427
262726232335211523220706061511141716171633331521353332373635112111141716
171633331521353332373635113427262726232335211523220706061501A502760D0A20
2B3030024430302B20170D0A1F2C3030FDBC30532619FD8A0D0A202B3031FDBB30542618
0D0A1F2C3030024531302B1F1802D701846821191218252517104164FC95672119121825
2531207A019DFE636721191218252531207A036B68211912182525171041640000010022
000005D8054C0043024040F17B0EBE0E026C00017F0075027B0E763079357A36BF0ABA0D
086D00011204452E1164363602550265028002894090029940B30DB40EBA33BA43D50DD7
320D120B01030E02060205328B0087329E00AC01A00ED132080B0001010002050C1F091E
0D2A0025012F093F094F098C00C634D900F2340F090B190B30013502334042405045A601
A302A540B602B60AB042CD00DC00D001D402D603EB00EB01F001F50AF00CF20D18163316
343432303454019900940D9632953409060D071B061E1F1B1821222F1F1B2821223C413D
1B3C0504041B050F1F1B1721231F1F1B2721233B343A1B3B010000220E0D140E0E0D4100
00B802C9405430341430303400010D3441054530004134043A0D01020C0B0A0907070E70
0EBF0E020E2607040707161619AC183C3B3B2828273D3A3A292926AC2718171706060527
0205083C93040E30302F0480050170050105B80238400C400D500D02800D01B00D010DB8
02F9B72F0F221F1E9E4445BC013C00210061011900182B2B4EF43C4DFD3CF65D7172FD5D
713C103C103C10E4003F3F103C103C103C10FD3C103C103C103C103C103C10FD3C103C10
3C1112395D2F12173912173901121739870E2E2B7D10C4870E2E182B7D10C4180010ED01
10C02B2B10ED0110C00010ED0110C02B2B10ED0110C0313001725D71007172435840092F
332D412F422D43045D595D2B435C58400A3618160D3F0A20143932B8FFE0B6103940100E
3901B8FFE8B20E3900B8FFE0B20E3901B8FFC0B2103900B8FFC0B11039002B2B2B2B2B2B
012B2B5901715D00715D0101161617152135323635342627011114171617163333152135
333237363511342726272623233521152322070606151136370037363534262323352115
0E02070607026401F47BAE57FD7B3A331335FE2C0D0A202B302EFDBE305426180D0A1F2C
303002422E2F2C1F18147501293E1B2A321F01F22C48684C16B502F0FE0F7B5906252527
1818263401CFFE4B6721191218252531207A036C6722181218252517104064FE61136C01
105B281E1723252501163F4614B9000200220000042B054C001F002C014EB9002EFFC040
933A352F2E7518741B741C7C28702E951C077918B724BA28DB1BDB1CDA24061927102E28
26392539273B28302E5A276927702E802E0BC600011A1C291C4B1CD719DB1B05A82801CA
24D917DA24D927D828EB24061C40231D28080E1F1B082122011F1B0721230F1F1B152123
001D202C2A1D283F234F2302232307152A281616150208070812001A101A301A401A701A
051A492EB8FFC0401A3F35002E01402EB02E029F2EC02ED02E032E2C01220F0E9E2D2EB8
0177B3216163182B2B4EF43C4DFD3C4D105D71722BF65D4D4358B90026032DE91BB90026
032DED59003F3C3F3C10ED1112392F5DED12393912392B2B2B31304379401C2429171C18
2528262917263301241C26330127192A3301251B2333002B2B012B2B2B2B818100495479
40101E22211F233B04221E203B0021201F0001103C103C2B002B8101715D007172017200
5D015D2B0111141716333315213533323736351134272623233521321616151406232226
2716163332363534262623220701A41C264D34FDBB335625141B274D3301F1B6D290DBC8
31724135521D68974884543350027BFE75801F2C2525381F74036C801F2C254BB27AA6D0
0E470A0AA18058974B1300010080FFE10405056B00380257401912952C010F010F020B03
000F04280029052B4F014F0209113AB8014640D03638361A031B045F185F195F1A5F1B06
050D050E002AC03A04740B740D740E760F701E701F702F70308908860B870D870E840F87
2BA804A83310120D330D340D351D331D341D352F012F022D04201E201F29292D333E013E
023F043019301E301F30213D343D35480D482A540B560D57105629562B1D1F011F021B33
1F351B3654185419541A59325C335C345C355C365A370E030B0B29130B1B29230B203A3B
29303A7212721389249807982F9930A82FC028C12AC62BC03AF03A144E08381B006F021B
AF01CF0102017F010101BA00B8034B400D359A311C1B1D6F1F1B1E1EBA1DB8034B403419
9A142A2B2B3C0C0E140C0C0E2B0C2A0E0426092B0C2A0E04062301FD0000062831032328
140902AC01401E223401011F260126B8012340102F11BF1102DF11012011AF11DF110311
B80287B71FAC1E2B10090109B80123401F9F2E01BF2EEF2EFF2E032E404735402ECF2EEF
2E03002E202E302EC02E042EBC014600390146011800182B4E10F45D5D2B71724DED72F4
EDFD5D7172FD72392F2BED003FED3FED3C10ED111217390111121739870E2E2B0E7D10C4
180010ECF4ED0110EDF4ED0010ECF4ED5D011071EDF4ED3130437940362430071328260B
2C093300290F263301241326330107300933000A2D0C33000B0C2C2B27102A33010F0E29
2A2512233300082F063301002B2B103C103C2B103C103C2B012B2B2B2B2B8181015D0072
5D43584009630B660D6710632B045D595D0171722B0071005D435C58400C0B180F390430
0F3933300F39002B2B2B590111232E0223220615141716171E0215140623222726262322
06072311331E023332363534262726242626353436333217163332363703AB25125DAC5C
68882B3EE9BE8B4BEFBC3B341FC31A191D0725251A58B56C7D91373A27FEA4934CE0AD6C
7938171A210A056BFE2B87A05E7F513E334B7D666D94519ADF09053F1E2F01D192916084
5A32662C1EC3748C5492D335191F2F000001003E000004B0054C001F00C9403A5A015A02
5A1D5A1E6B016B026B1D6B1E082F213F214F219805971BA805A61B0702011D1E161F1B10
2122091F1B0F2123071823001F02100F0821B802C0401309012B0040170E3F120F001F00
5000AF000400B80228B708092217161F2B1EB8FFC0400E170E3F12001E101E5F1EA01E04
1EBA0228001602C0B320645D182B10F6F45D4358B9001EFFC0B20B351EB8FFC0B20B0F34
2B2B592BE4103CFD3CF45D4358400900400B3500400B0F342B2B592BE410E6003F3C3F3C
FD3C2B2B0110C910C93130015D005D011323262726262323111417163333152135333237
363511232207060607231304A10F260B131F6754BF1B264F2FFDC130562416A35F28344A
072610054CFEC254243A37FBF47D1F2A2525342072040C0E136C5C013E000001000BFFE0
05B1054C002E0118B90030FFC0B32A2F3430B8FFC0B320243430B8FFC04054161C342806
3A064C06790F04252325273223322745234527A5270729273927025030740B7B0F9A27BF
27F826063C0E081F1B0221221F1F1B1921222A1F1B012123131F1B182123191818020201
0225280D092A29B802C94012090908400C3908403F352008300802081A30B8FFC040203F
351030017030A030B030E03004301F202212127F13016F130113192FF4A2182B4E10F45D
5D3C4D10FD3C4E105D712BF65D2B2B3C4D10FD3C003FED3F3C103C103C2B2B2B2B31304B
5179B137084379402621280A112221232102060F252725240E202D001011260C292D010B
0A2110252D00280B252D002B2B01103C2B103C2B2B2B2A8181015D71005D0071012B2B2B
013521152322070615111406062322262726351134262323352115232207061511141E02
3332363635113427262303D101E033502B1551EDCCDEE63020454D33024A345424191D4C
8F6885D24D1C274D05272525431F71FDDACCE1A19A8259F502127D4E2525352472FDB14F
CC724A74B5D802257F202C0000020049FFED038903AF0032003D0255406F0B1C8A330212
53360112201F39803FA809B60A03122B127D007D3386008B0B8B35061D12163A103F803F
04091C4C054C06452045224C3A403F891D080A0E0720002249014B0A490B49354B37433A
493D570B670B8509840A840B0F54168316021F3F5F3F0260083300343C2E292D34BB011B
000C000CFFC0B609390C280B390CB8FFC0401A3A35100C500C02400C500C600C03200C50
0C600C760C040C3C18B8FFD840290B394F185F186F18032F187F1802187E1F100110251E
07303C403C023C2C04702D802D022D35292C30B803464011040B2EC02D012D602500330D
0C0C343433BB016700240025FFC040140E3900251F2580259025044025F0250280250125
BB024300070015FFC0B21F3915B80167401E1B2F393107400E3920075007800703100701
F0070150070107433E436E182B4E10F45D7172722B4DEDF4ED2B10FD5D71722B3CFD3C10
3C103C103C10F65D3C003FF4FDE45D10ED713FED72FD5D712B11395D71722B2B2B2FED11
123911123939313043794047353B1C23051337383638020609080A080206212220220206
350B392000111D131C0012130F1F0D1C0122233B05391C00380834200135340B121C101C
010E22101C013A063C1C002B2B2B3C103C2B012B103C2B103C2B2B2A2A2A818181017201
710071017172005D4358B23F12015D59015D2B0072435C58B431400E392EB8FFE0B21039
2EB8FFE0B60E3937200E3920B8FFE8B20C3920B8FFE0400B0B3918200B3917200B391CB8
FFF0401A0B390A280B3937280B390A280A3937280A390A28093937280939002B2B2B2B2B
2B2B2B2B2B2B2B2B2B2B59005D2506070623222635343736363735342623220706151714
062322263534363332171617161511141616333237363715062322262711060706061514
16333202478D24363D5F7B1E29CBEC57533F2526022F26252FB09F7A4E3B1C120A170F10
0C153C7066313A01972C4F4456384C846D1119826A433144785624896622222C3A2E3234
2D5690291F422B85FEC9833B14070D3C38964493015D3C192C6039485F000002FFFBFFE4
03B9058E0016002400F04079102601A406B606B507EA1F040526430D5D36202675037604
86038704A603AB08074707013A0814270D41131E144418170C00041B1600102101215902
071B25090B1E311F0590050260058005AF050305EB0C16000018240C0C100D500D700D90
0D04800D900DB00D03000D100D200D300DB00DC00DD00D070DB8FFC0B73C350D6025C24B
182B4E10F42B5D71723C4D10FD3C103C10FD5D72ED003FED3FED723F111739F5EDFC01F5
3130437940281920030B072620031E20011A0A181C0019180B0C1C081E20011F04212001
190B1B1C001D061B2000002B2B2B012B103C103C2B2B2B81810071015D2B005D01720136
333216151407062322262711342626232207272533111116163332363534262322070601
3B859A8DD2A28BAB50A5560F20181C2A0E01132D336D395B9D9D6435352802F6B9F1D1F4
95803A3A03B59C481A102370FD28FDDC3233C8BFB0BD1B1400010046FFE4034A03AF0021
0182B40804011223B8FFC040732A2D340023430D5D36170D5705021C1354045305530654
07581B581C076705760580008021B41BC520D020E000E50509370147015618551C5F2360
18601C7618721C8A128E1390119018A601A402AF23B301C101C707C719E908E41CEA20F4
011806024A1257128B1F8B208023F02307112001BCFFE00020FFE0001FFFE0B2001D00B8
0346403010210160218021020021102120215021602170219021A021B021C021D021E021
F0210D21661DDF0F010FC7162509071DB8FFD8B214391DB8FFD8403812391D31030B21CC
1F0C014F0C8F0C020C2F100030004000600070009000B000C000E00009300040000200AA
731A831A02501A019F1A011AB8010C4012F00601000610062006300640060506432243B9
029100182B4E10F472714DED5D7271FD715DE47172ED003FED2B2B3FEDED7110F45D7172
E412393130383838013801715D005D017200722B2B435C58B4001018391BB8FFF0B61339
0510103901B8FFC0B2103920B8FFC0B11039002B2B2B2B012B5901710106062322023534
0033321615140623222726262726232207061514163332373637034A25D8839CE80101B4
87AE312C3B1E110B23233E643D51A189624E3734015CB5C30106DFD8010E8F4D262F2615
761F1E4A62A1A4FB432E790000020044FFE40405058E001F002D012EB9002FFFC0B34747
342FB8FFC040422B2E34602F7C047C058A04802FAF2FC02F07402F802F02002F162A152B
55055508542B960707480701202F370A470A560A9804A72AA02F07C02FF02B0220200020
21BAFFE0000BFFE040453C204F205E20660A6C207A209F009F20AA07B907C62A0B260813
270C41121E1344151D2716411C1E1D441F0020210B042C1500252509071F2C012C2C1F03
0B1F000B210C20B8016740121560168016AF16031F1690160216EB295006B8FFC0B3282E
3406B8FFC0B7473506432E437F182B4E10F42B2B4DEDFD725D3CFD3C3C3C3C3C003F3CED
723FED3F11173910F5EDFC01F50010F5EDFC01F531304379401A262B0408272526082920
002B0429200028072520012A052C2000002B2B012B2B2B8181005D3838383801715D0071
0172715D2B2B250606232226353412333217353426262322072725331114161633323717
052335112E0223220706151416333202C743804A96E0F8C3794F0F20181A2B0D01112D0F
21161B2D0BFEF02E063C632F58455BB06C5B67463DFBC5C501474DA99D481A102370FBDD
A1471C112371C901D84470394F68C8CAD7000002004CFFE4035303B00014001D02434019
125F185D1960006014D6030519201C3917201C3916401C391FB8FFC0400A434A34081F43
0D5D361FB8FFC0B32828341FB8FFC0405B2A2E341B06190958135E165F175A185B1A0701
0309060709080C0515490689028C06870C8A10851DA302AB18B513D402D90FDA10F402F3
031312000700081007100860076008700780078909C107C80FF0070C0401070D84020309
BAFFE00006FFE040433609460247094F1F5402540962027202891389199913A409A40AB8
08B509B00AC702E702E00CF40A1408D007010007D0070220079007A007B00704077D0414
00301615B8FFC040131239125F157F159F15DF150415151BD3040104B8FFD0B2143904B8
FFE8B2133904B8FFD84048123904310B0B5C1B011B25110707CC0816281B390F16016F16
7F168F160316F41414800801300890080230088F08DF08031008400860087008B008E008
0608AA0E15040089000200B8032C4012300E400E500E03000E100E200E03F00E010EB8FF
C04009434A340E431E434B182B4E10F42B7172724DFD713C10FD5D715D713C10ED5D712B
10ED003FED723FED2B2B2B7211392F5D4358B26F15015D592B3CFD3C10F45D7172393130
015D00383800715D014358B40600010202715971722B2B2B2B2B2B2B0072435C58B90007
FFC0400B23390C402D390D402D3908B8FFC0B2283907B8FFC0B2283906B8FFC0B21B3907
B8FFC0B21B3908B8FFC0B21B3907B8FFC0B20A3908B8FFC0B20A3907B8FFC0B2093908B8
FFC0400E093915101939192011390D201139002B2B012B002B2B2B2B2B2B2B2B2B2B2B2B
59130617163332363717060623220235341233321615252126272626232206DA01646487
5A852D1F15CA98A5EBF1B69AC6FD8701A805101963365383023BCC747463781489E10101
D9EB0107CBAA3A58243840810001004F0000037A058C002B011D403E8B20991599260344
03440C48198503850C059A04012F2D7F21900690079F089F099E109E11B02D0910061007
025F2A5F2B021C080EB41E08922201B41E07B80308401F231E9F1EBF1E021E1124251701
2B50100110302A2912110608070A101B011BB80152B38F2D012DB802F6B2012A2BB80110
400D282901120F1110920F0F01240EB8FFC0B36060340EB8FFC0B33A3A340EB8FFC0B33F
3F340EB8FFC0B32431340EB8FFC040161C2134900E01000E100E5F0E700EC00ED00E060E
192CBA0306030700182B4E10F45D722B2B2B2B2B4DED3C10F43C103C103C3CF43C10E65D
E472003F3C3F3C3C3CFD723C3FFD11395D2F2B2B31304379401225271416262515262516
281C002714241C012B012B2B2B81810172715D0072715D01111417163333152135333236
3635112335333534363633321716151406232226262726232206061515331501A61C253E
53FDDD29284219B2B258B57169583A341E17334A1F1F262E401CEC034CFDA680222C2424
284462025A483C89BE75442D381E35216D13133167D642480002003C00000207058E000B
002200DB4019902401602470249024A024F024052024502402402450240224B8FFC0B332
323424B8FFC0B3383A3424B8FFC0B32D303424B8FFC0B323253424B8FFC0402E191A3418
291E134A220D291E124A2321271941201E21440C190C1390060106390000220C0713120A
900901093903B8FFC0B2433503B8FFC0400F3F35036B0C0C0D190D2418402B3918B8FFC0
401A363A34901801501801601870189018A018F0180518B223B2A3182B10F65D71722B2B
ED3C103C10F42B2BED72003F3C3F3C3FED7211123910F5EDFC01F52B2B3130012B2B2B2B
2B015D715D01720132161514062322263534361311141616331521353236363511342726
26232207272501292A3B3B2A2A3C3B7E193141FE43432E1B09071E1A1C280E0114058E3B
2A2A3C3C2A2A3BFE21FD2056391C24241A3C550161952C20190F24700001003D0000020F
058E0015009BB79017C017F0170317B8FFC0B33F463417B8FFC0403A393B340117B20D64
3650170140175017601770179017A017F017070C291E074A2201291E06272314270D4113
1E144415000007060A0001240D0CB8FFC0B33F46340CB8FFC0401A353B34900C01500C01
600C700C900CA00CF00C050CB216B2A3182B10F65D71722B2B3CFD3C003F3C3F3CF5EDFC
01F52B2B3130015D0171012B012B2B017201111416163315213532363635113426262322
072725017B193447FE3F3F2E1A0E1F181A28110111058EFB4156381D24241A3C5503409B
471A10237000000100110000063003AF00570144401C3407D059EF16038059011159600D
5D362B0D01905901200820291E18B802FCB42237291E31B802FC400B224C291E474A2211
291E17B8030EB4232B291E30B8030DB42341291E46B8030D403E2355274D41541E554408
29374D4039292808000725475657073C2C05252C0C0A0B07060405074746313018170A59
17171A101124213020017020B0200220B80135400F37292E2B24383037017037B0370237
B8013540164D5741244C4C1F4D504D02804D904D02004D104D024DB8FFC0B61416344D60
5859B8025AB321A67F18B80164852B2B4EF42B5D71723C4D10FD3C10F471723CFDE410F4
71723CFD3C4E456544E6003F3C3C3C3C3C3F3C3C3F3C3C4DED10ED3F3C11121739011112
3900F5EDFC01F52B2B2B2B2B2B31304379401422240C0F0D252326240C211C010E0F220E
251C012B01103C2B2B2B81810172005D2B01715D01363736363332161736363332161716
151114171616331521353332373637363511342726232206070717111416163315213532
363736351134272623220706071114161633152135323636351134272626232207272533
015064122D6833567C15678E4B497121160D0A363DFE3C133B21170A041B2756356B4C02
02153A46FE314C390B05212C4F3635532D19314BFE3B3F321A09071E1A1C270F01142B02
EC640F262A645F784B4B553A7CFE765620161F24241710231150018A702E4035480B2BFE
4B5E2E1F242424241152018A7031401D2C37FE155A361B24241B3B55015E972C21190F24
70000001000C000003F703AF00330108403A35402A350835600D5D363035503560357035
9035052D040140356035703580359035B03506B035D03502B0350160358035C035031D08
16291E0FB8030F40112229291E244A2208291E0E4A231D291E23B8030E402E2331272A41
301E31441C00233233071A2C02072423230F0F0E0A17162407900801B008010F0870089F
08CF080408B802BD401B29331D242A1F295029602970290480299029B029030029102902
29B8FFC04009141634296034A67F182B10F62B5D71723CFD3C10FD5D71723CFD3C003F3C
103C103C3FED3F3C113939F5EDFC01F52B2B2B2B3130437940121819030604251903171C
01050618051A1C012B01103C2B2B8181015D71015D71005D01722B2B0136333216171615
111417161633152135333236373635113426232207111417161633152135333236351134
2626232207272533014BA1924B6C20160E0B3142FE3B1340330A04414D77760B0E314BFE
3B1446310F1F1A1C270F01142B02EDC24B563C7CFE79571F191C242427260F4F01777D71
82FE1D5D161D1B242447640154A5481A0F24700000020045FFE403B903AF000F001D0158
404512801501A716B616C501C909C41DD9090612E70A0148094516571585018C09890FD9
1B071F403235041F430D5D369F1F01C615C91A02401F014908102500071725080B1204B8
FFC0402B120B3F4F0401400401D0040140045004600470049004B0040604EC0C40120B3F
400C9F0C020C431E434B182B4E10F4722B4DED5D5D71722B4BB028534BB050515AB10C1E
49B11F0449525A58BD000CFFC00004FFC0001FFFC0383838594358BC001A033200040014
0332E910E91BBC001A0332000400140332ED10ED59003FED3FED313043794036011D1225
0E2602251C260A250626110F1420001D011A2001160914200018071A2001130D1020011B
03102001150B17200019051720002B2B2B2B012B2B2B2B2B2B2B2B2B2B8101720171722B
2B71015D0143584011750275067A0A7A0E7A1278167518751C085D595D005D435C584009
1C1011391B10113915B8FFF0B10B39002B2B2B5901321716151406062322272635343636
17220606151412333236353427260200D07E6B76CF7FCF7A677DCC53356B429F82617E69
4703AF9E87AF7BFC80A58BAD7EF977413F9E7CC8FEDEA0C3F48C60000002FFF9FE4A03BA
03AF002700390104407E0A3B430D5D36391049105B10891104862C013B2C3F3B4B2C5B2C
6A116A2C73087911792C8408A507E908F9090D303B583359346C3404403B012F08032829
121320291E19862213291E1827230027214F271E004402122B2803042E10360136590607
02072E250E0B19180E32311F0A900A02600A800AAF0A030AEB200213B80167401B202050
2170210280210100211021B021C021D0210521603AC24B182B10F65D71723C10FD3C10FD
5D72ED003F3C3FED3F3FED7211173910F5EDFC01F52B2B030E103C3C3C3C313043794020
2F35070D082534260C26302535073220012F0D3220013309362001310B2E2000002B2B01
2B2B2B2B2B2B81810171725D00715D2B0325331536363332171615140706232227262711
141616331521353316373636351134262623220705111417161633323736353427262322
070602011A26478F4F8A5C718870AA4A36283217394BFE20193727131510231E18250134
090E6D53643E515C4058302F24033972D679616C84D4ED9B7F150F2DFEE95E331E252501
160B316403625930180E7FFEAA6F233A584E66B9D2714E1812000001000D000002B703AF
002800A4406F2002200F3202320F4002400F820407402A015F2A011F291E18272211291E
17862327272041261E2744000A0B2A111420100104188009010939100C010C5903030007
18170A5F0601400601062E1F2A012A0011241F1F1F2001802090200200201020B020C020
D02005206029A66E182B4E10F45D71723C4D10FD3C1072E47172003F3C3F3C10ED72ED5D
11173901111239390010F5EDFC01F52B2B3130017271005D011536333216151406232226
2322070607111417161633152135323736373635113426262322072725014C7379374834
2423571512152D30130D423EFE2B4622190A050D231A1F270A011503AFCECE432C273645
14295EFE494C271B24242416102311500163A03D1C0F247000010064FFE402D503AF0031
03094029092C192C0212122E400B392C280B391814590C5A269B109424050A070A280A29
603370338033060F33B8012040870D5D36CB0DCB0EC424C425D723D624D92CE604E623E6
24E92C0B123F2C5D2C6E2D7D2C8F018F028F038015801A801B801C892D8F2E0D0F010F02
0A03090C061C0A2CC822C923081C031614121C161D19291B2C9909990A9B20932393240B
122B0D282C4A2C4F335F337829782C860EA823AF33E803E61C0C2908311E00BD021E011F
012F010201B8012BB200BD2EB8011A40112A181E19BD1B1E1A1F1A01101A201A021AB801
2BB219BD16B8011A4022121224230D0B040F2724230D0B04051E01C7002E2F2A31000005
252A071A7E191918B803464014162F1E25120B02CC12010112CF21DF21EF210321B80314
400B700F01600F700F900F030FB801364019271A192E1F0801082C9F2701602770278027
03202730270227BA012000320120B14B182B4E10F45D71724DED72F43C10FD5D71FD5D43
58B2FF21015D59392F435C58B2FF01015D59ED003FEDE4F43C10ED3FED3C103C10E410ED
1112173901111217394358400A242323240D0B140D0D0B870E2E2B0E7D10C459180010EC
F4ED5D720110EDF4ED0010ECF4ED5D0110EDF4ED31304379401C28291F20101106071F11
211C010629081C0020101E1C000728051C01002B2B012B2B81818181015D43584009FB07
F610F611FB28045D5901720071005D435840199F019F029F039F0B9A0D9015901A901B90
1C992297239F2D0C5D595D2B017100722B2B4358400B2F232F248B2C9B249B2C055D5943
5C584011282019390928193901400A3902400A391BB8FFC0B20A391AB8FFC040190A392E
400A392C400A392C4009390C101E123F0E201E123F0EB8FFF0B21B390EB8FFF0B2193924
B8FFE8B2133923B8FFE8B213390CB8FFE8B613392C1813391BB8FFC0B212391AB8FFC040
0F123901401239024012392C20123924B8FFF0400F0F392C180F3903100D392E400D3923
B8FFF040120D390C100D392C200D392C1811392C181139002B2B2B2B2B2B2B2B2B2B2B2B
2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B012B2B59005D01112326262322061514171617
171615140623222726232207231133161633323635342624272635343633321716333236
3702902126775C4656201F5F92CBBD75546C2115170D21211C9E62455761FEDE2D2D9B7B
364D331110120C03AFFEC8936A4A2D3828292E4763A27D991E0A1A01478C8E5139455E90
3A39577198170F0E180000010014FFF1023C04C1001B00D6B9000DFFE840410C395F015F
02023F1D99119919BF15BF16B819E819079F1D01891A014F0C4F0D5F0C5F0DF518050618
1518271803161518191A030118191A03141BA00103153004B8011B401C01033002020106
0C35082C0F0B16CF15DF15EF15031565141BCC000BB801EC402C200C010C2E2F1DB01D02
1D000101040405241450130180130100131013B013C013D013E0130613601CAB89182B10
F65D71723CFD3C103C103C105DF45DED10ED10F45D3C003FFDE43F3C10ED10EDFD3C10E4
01111739001117391239313000715D0171725D00722B0111331523111416333236373306
0623222626351123353636373637014AD6D63328213E11272380442E582A9137732D1729
04C1FED346FDAE593E29286263335F63026821166948266500010002FFE403FD03940025
00DC40350127600D5D36202760277027B02704340B371F3A20481F4820051A08134F1E18
2723214F1E25272308270141071E0844200B251D08B8014540130A002525191918061D2C
0E0E0A0B0A0B0B2120B80167400E00B001010F0170019F01CF010401B802BD402512191A
241212501390130280139013B01303001310132013B013C013D01306136026C27F182B4E
10F45D71723C4D10FD3C10FD5D713CFD3C3C103C003F3C10ED3F3C103C103C10ED111239
39F5EDFC01F52B2B3130437940101B1C0F111C0F1A1C0010111B101D1C002B01103C2B81
81005D015D2B011114161633323717052335060623222626351134262607352111141633
323637113426273503630F21161F270EFEEE2D767C454D712C1C37480141593F2B6D4B39
5A0394FDD59F471C112371C28042598C80019941321B0125FD9B8050364C02074E370225
0001001B000003E703940038036540FF12450A018F0D8F0E8F1187268734D60BD617DA27
DA33090F2E260A250B240C720A750BE632071C3A2E0F5A36042E3F053F103F1138263F28
3934303A490B4F104F11461E49264C284B34403A56195625503A75077F0B7F107F117F16
7F1795079F109F11A718BB261E0E050F100F110F2C1F101F111F2C290A29172F3A0A103A
55095A36503A0426181819171616273434350A0B0C0C33180A0907071926343534333525
128F162F110111350C0D0D160C1D7D19501E011E2F256F237F2302238F23012319252E2E
39332DF229292733500001007D35013505050725190707243525143535250C1627273033
0C1433330C35342618090C17332725190B382F402A26180A030C3407350F1C011C1F1F2C
2F2F2E131002381E00121111010100062E042D012D2D1E1E1D0A0CB80145B56F1601162E
25B8010EB320190119B8FFC0400C10354019B019E019F0190419B8FFC0B30F123419BB02
36003300070167B2352E27B80108B300330133BB02C10039003A024DB321CD89182B2BF6
5DEDF4ED10FD2B5D2B71EDF45DED003F3C103C105D3C3F3C103C103C10FD3C3C3C10FD3C
3C103C5D011112391117390011121739870E2E2B870E7DC4870E2E182B870E7DC4011812
397D2F18EC10E45D1112392FE41112392F1112395D2F5D10E45D10E41112392F107CEC5D
10E40708103C0E3C870E103C7DC4C4870E103CC408C4070E103C083C0E3C313001725D5D
2B005D01710071435C58B9000BFFF0B20A390BB8FFF8B7093917201E123F0BB8FFE8B31E
123F15B8FFE840091C113F0D401B103F18B8FFE8B31C113F18B8FFE84013170E3F054012
0B3F0718120B3F3640120B3F3AB8FFC0B7120B3F29280F390BB8FFF0B60F3925200F390A
B8FFD8B20F3907B8FFE0B20F3932B8FFE0B60D3925200D3907B8FFE0400F123926201239
26201139252011390BB8FFD8B20B390AB8FFE0B212390AB8FFE0B211390AB8FFE0401B0D
3910181239111812391740123910100F3911100F392C40153913B8FFF0B2153916B8FFF0
B2153912B8FFC0B215391AB8FFF0401315393608153928301439293014391108163909B8
FFE0401B163929401139294015393240153932201139172011390B20113912B8FFC0B111
39012B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B002B2B2B2B2B2B2B2B2B2B2B
2B2B2B2B012B2B2B2B2B2B2B2B002B2B2B2B591321152206151417161717373635342623
352115060706070713161617152135323736353427270706151416171521353637363737
272626231B01AF2921230B16414B4822260136312431557DE4544839FE502D1913408693
442D2DFED5241B265AC0AE4A513D0394251C17183210226868631A151D252503182272A7
FEB87931032424140E17175DC4C45B11182702242405141D77FFFC6C37000001000CFE46
03F403940032016C40AB0910120B3F0E2B95290213342E16643683058506020909051208
19081A092B141A26092412241A262B38013612351B47126809680A6919661A631B682C78
08790A7919771A741B782C890A891998009809971A962BBB00D034E506230909082B2B2C
2A2A0A0108021E011319141E13002C321E00120A111E12260820191A1A302A0A142A2A0A
2C2B2B24090814092B2A09082B2A1A09040A082C2B2A1A190A0908082313121201010006
27B8FFC0400E120B3F272F23391D0F3417171A19B80108401B8F0A01DF0AF00A02600A70
0AEF0A030A7D3F094F095F0903097D08B8010E401D2BD60F20010F209F2002208F5F2C01
2F2C3F2C022C193334A921A67F182B2B4EF45D724DE45D71E4FDF45DF45D5D71FD4E4565
44E6003F4DFDE42B3F3C103C103C121739011112173987082E2B0E7D10C4870E2E182B7D
10C401111239180010ED0110C00010ED0110C00010ED0110C00010ED0110C00710083C08
3C3130015D01712B005D012B132115232206151417131336353427262623352115060607
0607010606232226353436333217163332363737012627262726270C01AB152D2D21DFCD
110708222B012A2528180919FE8B36AF513B4C37302139280A1E472441FEB70F21191017
33039425271D2745FE3201FA292812090B0D25250418210E3FFC6E8588442C2A33160F3E
599F02B31F2E230C100C000100290000036C03940015015F403812080418049F049F0D9F
0EA904B804079F17010D17750D31365000580F5015031B04170E130F5E04520FDF04D00F
0700F6101041151E000BB8012440320505B40A1E0B040E0F0F24030414030304030B0204
0F010C0E170D030F020E0450050105300D0C06105F0F010F3001020A0FBB023E000E0004
023E402C03012E9F0001002E500D01300D400D700D030D1A2F173F174F1703170C2E0B35
00020102191617A121CD89182B2B4EF4714DF4E44E105DF65D724DF45DE410E410E4003F
3CFD723C3F3CFD723C391112390111123911123939111239872E2B877DC4180110EDEC00
10F50110EDEC0010F53130017200722B5D015D435C58B9000EFFD040091E123F03301E12
3F04B8FFC0400D1E123F0F401E123F042416390FB8FFDCB61639042814390FB8FFD8B614
39047012390FB8FF90B61239041815390FB8FFE8B6153904180F390FB8FFE8B10F39012B
2B2B2B2B2B2B2B2B2B2B2B002B2B59010321350121220607060723372115012132363736
37035C0BFCD80260FED4613C131B0428060300FD9A014E694B17100B0119FEE724032A19
23324AFE25FCD4232C206700000100250280045B02D20003002BB9000203274016013000
40000200001000200003001A05015C04585E182B10E64E10E65D5D002F4DED3130012135
21045BFBCA043602805200010074FE4604490394002900B640138108011C001404020C0F
1E1D1D151514062524B8FFC040120B0D3424242222270B1A2C020B001C252F24B8FFC040
0D1F21347F24A024B024C0240424B8FFC04037090D3424241F0405161309090F0F131E1F
1D1C24201F010F1F701F9F1FCF1F041F1F1516141624501301A013C01302001310139013
03132F5D5D71ED3C103C332F5D71FD3C103C11332F332F1112393911332F2B5D2BED103C
003FED3F332F332F2B3C3F3C103C103C3F11391139393130015D25062322271514171615
140623222635343736351133111416163332371133111416333237330623222602D8B586
564A1C203A2B2C341A1EA51F4F32598EA6242B44142413AD4B63A6C242355C5D67212E3C
4631245E6862038BFDAF5A54357A02BAFD49584385E36500000100000002D1EC4FD4E7B1
5F0F3CF50819080000000000A2E31DC200000000B53DB300FB74FD8C103A080E00000009
0001000000000000000100000721FE4500571000FB74FE52103A00010000000000000000
00000000000000310639011C00000000020000000200000002AA005402AA002E02AA0053
02000091023900030400004A040000F00400002C04000053040000200400006204000058
0400004C0400007C04000051055600220556004A05C7004805C7002305C7002204730022
0473008004E3003E05C7000B038D00490400FFFB038D004604000044038D004C02AA004F
0239003C0239003D063900110400000C040000450400FFF902AA000D031D006402390014
040000020400001B0400000C038D002904830025044A0074000000000000000000000042
008300B000DC011501AE022002F703E6046F052905DF0650075A081D099D0A560B370BFC
0D7F0E670FE4107B114A12CD137E1474154F16A2176E1812188519A31A721B4E1C251CB4
1E811F181FC021C722CB23A423C7245E00010000003100F2003C006F000500020010002F
0041000005CD0F1800030002B9FFC003E1B345453240B803E1B32B2E3240B803E1B22829
32B9FFC003E1B21A1C32BD03E102AC0027001FFFC003DFB2161B32B9FFC003DEB2424232
B9FFC003DEB2363832B9FFC003DEB32A2D32DF410A03DE00EF03DE000203DE03DF002800
1FFFC003DFB3282E32F0410D03DF0001037E000F0101001F00A003DD00B003DD00020040
03DAB32426329FBF03CC000103CA03C90064001FFFC003C9B20D1132410A03C703B70012
001F03B603B50064001FFFC003B5B30E1132004173038D000100C0038D00D0038D00E003
8D00F0038D0004006F03A7007F03A7008F03A700AF03A70004000F03A7001F03A7002F03
A7004F03A7000403AB03AB00EF03A50001000F03A5002F03A5006F03A5008F03A5000400
5403AA0001006B03AA000103A8036A0022001F038C03940015001F038B03930015001F03
A40393001A001F03A20394001E001F03A10393001E001F039F0394001E001F039B039400
1A001F039A0393001E001F039903940016001F039803940016001F03970393001B001F03
960394001B001F03950393001B001F03760375001A001F03740375001A001F03A00373B2
1E1F10411E03930020039300300393000300200394003003940040039400030000039400
010383036C001E001F03B1036C0032001F036D036C0032001FFFC0037DB2212332B9FFC0
037DB3171932A0410A037D00B0037D00C0037D00D0037D0004FFC0037CB2212332B9FFC0
037CB3171932A0412D037C00B0037C00C0037C00D0037C00040030037300400373000200
0003730010037300200373000300E0037300F00373000200B0037300C0037300D0037300
03008403730090037300A0037300030377036A0029001F0389036AB2281F40B80367B339
40323FBB0366000100400366B3191D328FBB0366000100400366B3090A3240B80366B309
0E3240B80366B3090F323FBB0365000100400365B3090C3240B80365B31A1D3240B80365
B3090E326B410E0363007B036300020014036300240363003403630044036300040363B2
242F1FBA034E006D0800400E1F7F027F037F047F050430440112BF033200500800001F00
12032D003C080040291F5F3C01376009700980090310092009300940095009056F037F03
8F03031F032F033F034F035F0305B8FFC0B2073A33B8FFC04047063A33900BA00BB00BC0
0BD00B05B006C006D006E006F0060520063006400650066006700680069006A006099006
900702600B700B800B03100B200B300B400B500B051F0701A04185036200010000036200
1003620070036200900362000400F0035F00010020035E0020035F0030035F0040035E00
040000035E0000035F0010035F00D0035E00E0035F00050010030F0020030F0030030F00
D0030F00E0030F00050000030F0010030F0050030F0060030F0070030F00D0030F000600
00030F0010030F0020030F0030030F00E0030F00F0030F0006030F00270000030E003003
0E000200E0030E00F0030E0002030E004A00E0030D00F0030D0002030D002700D002FC00
01001002FC002002FC005002FC000300D002FC00E002FC0002000002FC001002FC002002
FC003002FC005002FC006002FC000600E002FC00F002FC0002002002FC003002FC004002
FC000302FC406127C02901B02901A02901902901403C3F413240223F41321212125F235F
255F285FA5046F236F256F286FA5044F234F254F284FA5043F233F253F283FA5042F232F
252F282FA5041F231F251F281FA5048F4CAF4CBF4CCF4C045F4C6F4C7F4C0337B8FFC0B3
B22B3032B8FFC0B3B2222532B8FFC0B5B2191A32370F413D02AF0001005F02AF009F02AF
00DF02AF0003000F02AF001F02AF002F02AF003F02AF006F02AF000502AF02AF001F02AD
002F02AD003F02AD004F02AD005F02AD000500DF02AD0001000F02AD001F02AD003F02AD
005F02AD009F02AD0005005F02AD00DF02AD0002000F02AD001F02AD003F02AD00030040
02ACB23A334F414C02AC005F02AC009F02AC0003002F02AC003F02AC0002000F02AC003F
02AC00AF02AC000300B002AC00E002AC0002004F02AC005F02AC00A002AC0003000F02AC
001F02AC002F02AC003F02AC0004000F035A0001000F035A001F035A003F035A005F035A
0070035A000500CF035700DF03570002000F0357001F03570070035700AF03570004035A
035A0357035702AD02AD02AC02AC032C400D31151F001616000000121108104110020C00
4A000D01A8004A000D0198004A000D0189004A000D013F004A000D0124400E4A0DF64A0D
BE4A0D864A0D274A0DBE02280041000D01940041000D0121400B410DB4410D4F410D2941
0D411002170021000D02150021000D02060021000D01EB0021000D014E0021000D012C40
14210DF9210DF3210DF1210D9D210D71210D3D210D4110021C001F000D0214001F000D02
0B001F000D0196001F000D014A001F000D0126400B1F0DC61F0D571F0D371F0D410D019E
0141000D00420141000D001E0141000D001B0141000D01F2B40F440F0009BB01F2004400
0D0201B23C291FB80200B23C291FB801FFB23C411FB801FEB23C471FB801FDB23C9E1FB8
01FAB23C931FBC01F9010F0101001F01F6B224E41F411501F401490401001F01F3014904
01001F01F1014900AB001F01F001490067001F01A6003C0125001F01A4B23C811F411501
A3010F019A001F01A200220801001F01A100500401001F019F0149019A001F019D014900
67001F019CB22C621FB8019BB22C791FBC019A002C0101001F0197B22CE41FB80193B22C
891FB80192B22C6C1FB8018FB2259E1FB8016AB23C2A1F4111016700240201001F016300
2502AB001F014C010F019A001F01480149006C001F0147B22C891FB80145B22C9E1FB801
44B22C791FB80143B223311FB80127B23C811FBC012300500101001F011FB223E41F4115
011D0023019A001F011C00230801001F011B00250801001F010E010F0401001F010D0022
0401001F0108B223811FB80106B425E41FF73CBB0125001F00F5010FB29E1FE3BC014901
56001F00E20149B2AB1FD1B901490401B21FCF2CB80125B61FCE23BB1FC524B80156B21F
C02CB80801B21FBF2CB80201B51FB124E41FB0B901490201B61FAF2C671FAD23B80801B2
1FA523B80201400B1F9F3C2D1F9B235A1F9925B80201B21F812CBC0401001F006D010F01
56400B1F592C3E1F4C3CAB1F4625B80101B21F403CB80125400A1F3A23721F393CAB1F38
B80149B3AB1F3124B80401B21F3025B802ABB61F2A24E41F2623B80156B21F5537BA0235
00070175402C0774076207560751073B0733072D0720071D071C071408120810080E080C
080A080808060804080208000814B8FFE0402B0000010014061000000100060400000100
0410000001001002000001000200000001000002010802004A00B013034B024B534201B0
124B004B5442B0372B4BB807FF52B0382B4BB008505B58B101018E59B0382BB00288B801
005458B801FFB101018E851BB0124358B90001012F858D1BB90001017C858D5959014BB0
C063004B6220B0F65323B8010A515AB0052342180016763F183F123E113946443E113946
443E113946443E113946443E11394660443E11394660442B2B2B2B2B2B2B2B2B2B2B182B
2B2B2B2B2B2B2B2B2B2B2B2B181DB0964B5358B0AA1D59B0324B5358B0FF1D594BB04753
205C58B90271026F4544B90270026F45445958B9017A0271455258B90271017A4459594B
B04753205C58B9002202704544B9003C027045445958B901B30022455258B9002201B344
59594BB04C53205C58B9014900224544B1222245445958B901C20149455258B9014901C2
4459594BB06753205C58B9002402714544B90050027145445958B9021E0024455258B900
24021E4459594BB8020153205C58B9010F00224544B1222245445958B90C00010F455258
B9010F0C004459594BB01C53205C58B125254544B12C2545445958B13725455258B12537
4459594BB0AB53205C58B125254544B1232545445958B901590025455258B90025015944
59594BB8010153205C58B125254544B1282545445958B902080025455258B90025020844
59592B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B
2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B
2B2B2B2B2B2B2B65422B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B
2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B2B01B361DC6463456523456023456560234560B08B
766818B080622020B164DC4565234520B003266062636820B003266165B0DC236544B064
234420B161634565234520B003266062636820B003266165B063236544B0612344B10063
455458B163406544B26140614523614459B3A67F434B456523456023456560234560B089
766818B080622020B1437F4565234520B003266062636820B003266165B07F236544B043
234420B1A64B4565234520B003266062636820B003266165B04B236544B0A62344B1004B
455458B14B406544B2A640A645236144594B5242014B5058B108004259435C58B1080042
59B3020B0A124358601B2159421610703EB0124358B93B21187E1BBA040001A8000B2B59
B00C2342B00D2342B0124358B92D412D411BBA04000400000B2B59B00E2342B00F2342B0
124358B9187E3B211BBA01A80400000B2B59B0102342B0112342002B0018456944456944
456944456944737373747373737475752B7373747475184569447373742B4BB021534BB0
46515A58B03CB03C45B040604459012B2B2B2B757575757575757543584010BF3CCF3C02
6F3C7F3C8F3C9F3CAF3C0575755943584012BF22CF22025F226F227F228F229F22AF2206
757559435C58B6403C9F22EF220375592B2B017474747445447373747475754544734544
73744544737475737373737300757575737575752B2B757575752B752B43584122006303
2D00010003032D0013032D0023032D0033032D0053032D000500C3032D00D3032D00E303
2D00F3032D00040083032D0093032D00A3032D00B3032D0004032D032D45186944747475
75592B4358B900180332B330353238B80332B361663238B80332B3535A3238B80332B345
4E3238B80332B33C413218B80332B23F330A410F0332000100BA033200CA033200DA0332
00EA033200FA03320005033203324518694474752B2B2B2B2B2B597300732B012B757500
2B2B2B74002B2B2B732B74012B002B2B017373737474732B2B00732B2B002B2B2B017374
732B012B2B012B2B2B2B2B2B2B2B2B2B2B2B2B2B00017375007373004569440073730173
742B2B2B2B2B732B00732B752B2B732B2B2B2B2B2B2B2B2B00>]def
/CharStrings 47 dict dup begin
/.notdef 0 def
/minus 47 def
/space 3 def
/parenleft 4 def
/parenright 5 def
/hyphen 6 def
/period 7 def
/slash 8 def
/zero 9 def
/one 10 def
/two 11 def
/three 12 def
/four 13 def
/five 14 def
/six 15 def
/seven 16 def
/eight 17 def
/nine 18 def
/mu 48 def
/B 19 def
/C 20 def
/G 21 def
/H 22 def
/K 23 def
/P 24 def
/S 25 def
/T 26 def
/U 27 def
/a 28 def
/b 29 def
/c 30 def
/d 31 def
/e 32 def
/f 33 def
/i 34 def
/l 35 def
/m 36 def
/n 37 def
/o 38 def
/p 39 def
/r 40 def
/s 41 def
/t 42 def
/u 43 def
/x 44 def
/y 45 def
/z 46 def
end readonly def

systemdict/resourcestatus known
 {42 /FontType resourcestatus
   {pop pop false}{true}ifelse}
 {true}ifelse
{/TrueDict where{pop}{(%%[ Error: no TrueType rasterizer ]%%)= flush}ifelse
/FontType 3 def
 /TrueState 271 string def
 TrueDict begin sfnts save
 72 0 matrix defaultmatrix dtransform dup
 mul exch dup mul add sqrt cvi 0 72 matrix
 defaultmatrix dtransform dup mul exch dup
 mul add sqrt cvi 3 -1 roll restore
 TrueState initer end
 /BuildGlyph{exch begin
  CharStrings dup 2 index known
    {exch}{exch pop /.notdef}ifelse
  get dup xcheck
    {currentdict systemdict begin begin exec end end}
    {TrueDict begin /bander load cvlit exch TrueState render end}
    ifelse
 end}bind def
 /BuildChar{
  1 index /Encoding get exch get
  1 index /BuildGlyph get exec
 }bind def
}if

FontName currentdict end definefont pop
end
%%EndProlog
mpldict begin
-363.5 7.977 translate
1339 776.046 0 0 clipbox
gsave
0 -0 m
1339 -0 l
1339 776.04625 l
0 776.04625 l
cl
1.000 setgray
fill
grestore
gsave
79.2 454.68625 m
393.969231 454.68625 l
393.969231 731.88625 l
79.2 731.88625 l
cl
1.000 setgray
fill
grestore
2.500 setlinewidth
1 setlinejoin
0 setlinecap
[] 0 setdash
0.000 setgray
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
92.1771 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
92.1771 731.886 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

85.9271 427.327 translate
0 rotate
0 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
152.535 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
152.535 731.886 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

133.785 427.327 translate
0 rotate
0 0 m /three glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
212.894 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
212.894 731.886 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

194.144 427.327 translate
0 rotate
0 0 m /six glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
273.252 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
273.252 731.886 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

254.502 427.327 translate
0 rotate
0 0 m /nine glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
333.611 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
333.611 731.886 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

308.611 427.327 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /two glyphshow
25 0 m /zero glyphshow
37.5 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 731.886 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

368.969 427.327 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /five glyphshow
25 0 m /zero glyphshow
37.5 0 m /zero glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
107.267 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
107.267 731.886 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
122.356 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
122.356 731.886 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
137.446 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
137.446 731.886 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
167.625 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
167.625 731.886 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
182.715 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
182.715 731.886 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
197.804 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
197.804 731.886 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
227.984 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
227.984 731.886 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
243.073 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
243.073 731.886 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
258.163 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
258.163 731.886 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
288.342 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
288.342 731.886 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
303.432 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
303.432 731.886 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
318.521 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
318.521 731.886 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
348.7 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
348.7 731.886 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
363.79 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
363.79 731.886 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
378.88 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
378.88 731.886 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

153.124 400.624 translate
0 rotate
0 0 m /T glyphshow
13.521 0 m /e glyphshow
24.6172 0 m /m glyphshow
44.063 0 m /p glyphshow
56.563 0 m /e glyphshow
67.6592 0 m /r glyphshow
75.9844 0 m /a glyphshow
87.0806 0 m /t glyphshow
94.0264 0 m /u glyphshow
106.526 0 m /r glyphshow
114.852 0 m /e glyphshow
125.948 0 m /space glyphshow
132.198 0 m /parenleft glyphshow
140.523 0 m /K glyphshow
158.577 0 m /parenright glyphshow
grestore
2.500 setlinewidth
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 454.686 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

56.7 446.007 translate
0 rotate
0 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 500.886 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 500.886 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

44.2 492.207 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /five glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 547.086 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 547.086 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

44.2 538.407 translate
0 rotate
0 0 m /three glyphshow
12.5 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 593.286 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 593.286 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

44.2 584.607 translate
0 rotate
0 0 m /four glyphshow
12.5 0 m /five glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 639.486 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 639.486 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

44.2 630.807 translate
0 rotate
0 0 m /six glyphshow
12.5 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 685.686 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 685.686 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

44.2 677.007 translate
0 rotate
0 0 m /seven glyphshow
12.5 0 m /five glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 731.886 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 731.886 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

44.2 723.207 translate
0 rotate
0 0 m /nine glyphshow
12.5 0 m /zero glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 466.236 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 466.236 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 477.786 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 477.786 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 489.336 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 489.336 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 512.436 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 512.436 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 523.986 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 523.986 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 535.536 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 535.536 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 558.636 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 558.636 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 570.186 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 570.186 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 581.736 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 581.736 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 604.836 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 604.836 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 616.386 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 616.386 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 627.936 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 627.936 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 651.036 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 651.036 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 662.586 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 662.586 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 674.136 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 674.136 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 697.236 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 697.236 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 708.786 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 708.786 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 720.336 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 720.336 o
grestore
gsave
34.2 482.286 translate
90 rotate
/TimesNewRomanPSMT 25.0 selectfont
0 0.148438 moveto
/P glyphshow
13.9038 0.148438 moveto
/o glyphshow
26.4038 0.148438 moveto
/l glyphshow
33.3496 0.148438 moveto
/a glyphshow
44.4458 0.148438 moveto
/r glyphshow
52.771 0.148438 moveto
/i glyphshow
59.7168 0.148438 moveto
/z glyphshow
70.813 0.148438 moveto
/a glyphshow
81.9092 0.148438 moveto
/t glyphshow
88.855 0.148438 moveto
/i glyphshow
95.8008 0.148438 moveto
/o glyphshow
108.301 0.148438 moveto
/n glyphshow
120.801 0.148438 moveto
/space glyphshow
127.051 0.148438 moveto
/parenleft glyphshow
135.376 0.148438 moveto
/mu glyphshow
148.779 0.148438 moveto
/C glyphshow
165.454 0.148438 moveto
/slash glyphshow
172.4 0.148438 moveto
/c glyphshow
183.496 0.148438 moveto
/m glyphshow
/TimesNewRomanPSMT 17.5 selectfont
203.318 15.1766 moveto
/two glyphshow
/TimesNewRomanPSMT 25.0 selectfont
213.141 0.148438 moveto
/parenright glyphshow
grestore
2 setlinecap
0.749 0.212 0.047 setrgbcolor
gsave
314.769 277.2 79.2 454.686 clipbox
372.843779 495.061387 m
370.831831 528.025685 l
368.819883 633.661191 l
366.807936 637.320363 l
362.78404 640.739506 l
360.772092 643.624301 l
358.760145 645.325668 l
356.748197 646.798942 l
354.736249 648.802312 l
352.724301 649.521063 l
350.712353 651.524994 l
348.700406 652.542248 l
346.688458 653.708944 l
344.67651 655.34112 l
340.652614 657.325147 l
337.634693 659.272445 l
331.598849 661.865016 l
329.586902 663.070287 l
327.574954 663.718192 l
325.563006 664.483028 l
323.551058 665.663643 l
321.539111 666.368545 l
319.527163 667.393003 l
317.515215 668.099883 l
315.503267 668.510415 l
313.491319 669.4614 l
309.467424 670.661696 l
305.443528 672.382881 l
303.431581 672.896161 l
301.419633 673.762198 l
299.407685 674.285593 l
297.395737 675.122451 l
295.383789 675.603958 l
293.371842 676.333408 l
289.347946 677.419916 l
287.335998 677.837197 l
285.32405 678.676753 l
283.312103 679.073077 l
281.300155 679.680484 l
279.288207 680.092342 l
277.276259 680.964299 l
273.252364 681.763158 l
269.228468 682.904339 l
267.21652 683.336471 l
263.192625 684.505388 l
261.180677 684.867143 l
257.156781 685.889495 l
255.144834 686.235995 l
253.132886 686.824736 l
249.10899 687.655194 l
247.097043 688.168158 l
243.073147 688.993899 l
241.061199 689.493634 l
237.037304 690.268597 l
231.00146 691.460977 l
208.870035 695.858088 l
200.822244 697.219331 l
198.810296 697.704173 l
194.7864 698.358317 l
190.762505 698.975259 l
186.738609 699.755462 l
178.690818 701.031856 l
168.631079 702.671388 l
166.619131 702.908794 l
164.607183 703.328693 l
158.57134 704.217819 l
154.547445 704.775848 l
152.535497 704.963526 l
148.511601 705.711226 l
140.46381 706.752458 l
134.427967 707.609001 l
124.368228 709.014206 l
102.236802 711.924877 l
94.189011 712.920386 l
94.189011 712.920386 l
stroke
grestore
0.937 0.424 0.000 setrgbcolor
gsave
314.769 277.2 79.2 454.686 clipbox
372.843779 463.983016 m
370.831831 474.082007 l
368.819883 475.928746 l
366.807936 471.038291 l
364.795988 478.145371 l
362.78404 499.979152 l
360.772092 488.3136 l
358.760145 494.883499 l
356.748197 504.805959 l
354.736249 519.283865 l
352.724301 628.479636 l
350.712353 631.050472 l
348.700406 634.721404 l
346.688458 636.828578 l
344.67651 639.570025 l
342.664562 641.533441 l
340.652614 643.067422 l
338.640667 645.270475 l
334.616771 648.430465 l
330.592876 651.335697 l
328.580928 652.335789 l
326.56898 653.872684 l
324.557032 655.027671 l
322.545084 655.984019 l
320.533137 657.124928 l
318.521189 657.970799 l
316.509241 659.21007 l
314.497293 660.138152 l
312.485346 661.387158 l
310.473398 662.146391 l
308.46145 663.094791 l
304.437554 664.748942 l
302.425607 665.619347 l
300.413659 666.307995 l
298.401711 667.451053 l
296.389763 668.085755 l
294.377815 668.451074 l
292.365868 669.410469 l
290.35392 670.063049 l
288.341972 670.923253 l
282.306129 672.896149 l
280.294181 673.653172 l
276.270285 674.935387 l
274.258338 675.689469 l
272.24639 676.071856 l
270.234442 676.743293 l
268.222494 677.231075 l
266.210547 678.02274 l
262.186651 679.157294 l
258.162755 680.169606 l
256.150808 680.893436 l
254.13886 681.129942 l
252.126912 681.844943 l
250.114964 682.440781 l
246.091069 683.258135 l
244.079121 683.738547 l
242.067173 684.459979 l
236.03133 685.696126 l
234.019382 686.147935 l
229.995486 687.348157 l
227.983539 687.586887 l
225.971591 688.129895 l
223.959643 688.479829 l
221.947695 689.109436 l
217.9238 689.791134 l
213.899904 690.776857 l
211.887956 691.050365 l
209.876009 691.447998 l
207.864061 692.019921 l
205.852113 692.291867 l
203.840165 692.731351 l
201.828217 693.304479 l
199.81627 693.463375 l
197.804322 693.963217 l
185.732635 696.112392 l
183.720687 696.599123 l
173.660948 698.356983 l
171.649001 698.595751 l
169.637053 699.025193 l
163.60121 700.041678 l
151.529523 702.000711 l
121.350306 706.618165 l
119.338358 706.820652 l
115.314463 707.418225 l
107.266672 708.555564 l
93.183037 710.445738 l
93.183037 710.445738 l
stroke
grestore
1.000 0.702 0.000 setrgbcolor
gsave
314.769 277.2 79.2 454.686 clipbox
372.843779 461.64888 m
370.831831 459.42444 l
368.819883 460.277933 l
366.807936 467.687591 l
364.795988 462.537161 l
362.78404 465.344249 l
360.772092 460.697313 l
358.760145 461.525624 l
356.748197 472.807424 l
354.736249 469.375863 l
352.724301 482.004599 l
350.712353 464.05725 l
348.700406 488.223729 l
346.688458 477.380621 l
344.67651 485.96509 l
342.664562 524.4908 l
340.652614 502.748469 l
338.640667 538.947286 l
336.628719 528.823644 l
334.616771 626.833824 l
332.604823 629.703283 l
330.592876 633.468161 l
328.580928 636.294052 l
324.557032 640.860214 l
320.533137 644.353554 l
316.509241 647.270923 l
314.497293 648.926815 l
310.473398 651.527235 l
308.46145 652.959581 l
306.449502 653.998114 l
304.437554 655.366211 l
300.413659 657.029808 l
296.389763 659.726486 l
294.377815 660.438742 l
290.35392 662.110804 l
288.341972 663.318496 l
286.330024 663.878408 l
284.318077 664.662282 l
282.306129 665.70941 l
280.294181 666.603257 l
278.282233 667.083856 l
276.270285 667.91911 l
270.234442 670.019151 l
268.222494 670.982398 l
266.210547 671.416992 l
264.198599 672.192287 l
262.186651 672.794708 l
260.174703 673.551204 l
256.150808 674.718312 l
254.13886 675.464453 l
250.114964 676.67318 l
246.091069 677.94174 l
240.055225 679.585764 l
238.043278 680.295622 l
234.019382 680.991696 l
221.947695 684.289882 l
219.935747 684.629062 l
217.9238 685.160633 l
215.911852 685.529575 l
211.887956 686.566037 l
207.864061 687.56645 l
205.852113 687.921382 l
201.828217 688.865672 l
193.780426 690.437979 l
191.768479 690.967548 l
175.672896 694.205559 l
171.649001 694.844089 l
169.637053 695.392584 l
167.625105 695.660107 l
165.613157 696.145484 l
163.60121 696.305484 l
161.589262 696.815539 l
141.469784 700.250685 l
139.457836 700.533332 l
137.445888 700.968952 l
131.410045 701.910354 l
129.398097 702.28234 l
127.386149 702.526222 l
123.362254 703.151446 l
93.183037 707.678061 l
93.183037 707.678061 l
stroke
grestore
0.031 0.659 0.255 setrgbcolor
gsave
314.769 277.2 79.2 454.686 clipbox
372.843779 459.244232 m
370.831831 457.385892 l
368.819883 457.519192 l
366.807936 459.421224 l
364.795988 461.881494 l
362.78404 457.65856 l
360.772092 463.514694 l
358.760145 460.823947 l
356.748197 461.228276 l
354.736249 462.59059 l
352.724301 465.098196 l
350.712353 463.793145 l
348.700406 470.005678 l
346.688458 461.221808 l
344.67651 463.313906 l
342.664562 466.861918 l
340.652614 479.869951 l
338.640667 480.571952 l
336.628719 488.57242 l
334.616771 490.503615 l
332.604823 475.629462 l
330.592876 494.97221 l
328.580928 527.509313 l
326.56898 483.971872 l
324.557032 499.676354 l
322.545084 549.295923 l
320.533137 541.267228 l
318.521189 621.032559 l
316.509241 626.021717 l
314.497293 629.679044 l
312.485346 632.938694 l
310.473398 635.910178 l
308.46145 637.692392 l
306.449502 640.551819 l
304.437554 641.915722 l
302.425607 643.787301 l
300.413659 645.164815 l
298.401711 646.999793 l
296.389763 648.018015 l
294.377815 649.778309 l
292.365868 650.88201 l
290.35392 652.227144 l
288.341972 653.207467 l
284.318077 655.798671 l
282.306129 656.713329 l
280.294181 657.9856 l
278.282233 658.618911 l
276.270285 659.731167 l
274.258338 660.639676 l
272.24639 661.393671 l
270.234442 662.436035 l
268.222494 662.951236 l
266.210547 664.108379 l
248.103016 670.891995 l
246.091069 671.329027 l
244.079121 672.440695 l
240.055225 673.405151 l
238.043278 674.289046 l
221.947695 678.938078 l
217.9238 679.876552 l
213.899904 681.116077 l
203.840165 683.641222 l
201.828217 684.196592 l
195.792374 685.560957 l
193.780426 686.09245 l
191.768479 686.422168 l
189.756531 687.005552 l
185.732635 687.769949 l
183.720687 688.294798 l
181.70874 688.64431 l
179.696792 689.217335 l
147.505627 695.645894 l
143.481732 696.317956 l
141.469784 696.760075 l
135.433941 697.756997 l
131.410045 698.540038 l
127.386149 699.137248 l
125.374202 699.572684 l
121.350306 700.227818 l
99.21888 703.791214 l
93.183037 704.707579 l
93.183037 704.707579 l
stroke
grestore
0.031 0.416 0.416 setrgbcolor
gsave
314.769 277.2 79.2 454.686 clipbox
372.843779 459.083988 m
370.831831 457.138266 l
368.819883 457.466949 l
366.807936 457.368603 l
364.795988 461.37643 l
362.78404 456.16997 l
360.772092 458.338205 l
358.760145 458.96568 l
354.736249 456.158876 l
352.724301 458.395656 l
350.712353 458.964997 l
348.700406 461.507562 l
346.688458 458.211466 l
344.67651 461.893964 l
342.664562 473.657867 l
340.652614 462.156483 l
338.640667 456.596861 l
336.628719 460.294338 l
334.616771 463.585365 l
332.604823 470.364364 l
330.592876 468.574035 l
328.580928 468.017147 l
326.56898 476.94648 l
324.557032 478.864735 l
322.545084 482.612305 l
320.533137 502.88769 l
318.521189 500.635246 l
316.509241 484.533879 l
314.497293 495.109538 l
312.485346 520.09736 l
310.473398 529.42065 l
308.46145 551.362465 l
306.449502 535.456193 l
304.437554 560.39263 l
302.425607 618.519465 l
300.413659 622.951821 l
298.401711 628.107558 l
296.389763 630.548156 l
294.377815 633.585775 l
290.35392 638.329976 l
288.341972 640.104585 l
286.330024 642.143944 l
284.318077 644.001661 l
282.306129 645.272494 l
280.294181 646.72682 l
278.282233 648.352669 l
276.270285 649.292043 l
274.258338 650.761663 l
270.234442 653.050966 l
268.222494 654.260135 l
266.210547 654.840242 l
264.198599 656.373654 l
262.186651 657.390977 l
260.174703 658.072941 l
258.162755 659.058882 l
256.150808 660.367768 l
252.126912 661.986752 l
242.067173 665.859072 l
240.055225 666.862002 l
238.043278 667.474973 l
234.019382 669.074064 l
227.983539 671.093724 l
225.971591 671.898894 l
223.959643 672.32727 l
219.935747 673.699093 l
217.9238 674.424551 l
215.911852 675.025436 l
211.887956 676.019217 l
201.828217 678.968265 l
195.792374 680.564702 l
193.780426 681.154305 l
191.768479 681.592234 l
189.756531 682.152777 l
187.744583 682.592039 l
185.732635 683.198524 l
179.696792 684.48513 l
175.672896 685.518272 l
167.625105 687.500216 l
163.60121 688.22116 l
159.577314 689.13508 l
157.565366 689.489319 l
153.541471 690.494658 l
147.505627 691.586902 l
141.469784 692.935142 l
139.457836 693.277206 l
137.445888 693.736607 l
133.421993 694.392238 l
129.398097 695.199838 l
123.362254 696.341848 l
119.338358 697.073483 l
113.302515 698.142321 l
93.183037 701.536729 l
93.183037 701.536729 l
stroke
grestore
0.031 0.659 0.659 setrgbcolor
gsave
314.769 277.2 79.2 454.686 clipbox
372.843779 456.165434 m
370.831831 456.77711 l
368.819883 459.826255 l
366.807936 457.096839 l
362.78404 455.865717 l
360.772092 456.484497 l
358.760145 455.618924 l
356.748197 458.284703 l
354.736249 457.171391 l
352.724301 456.764338 l
350.712353 460.414451 l
348.700406 457.611525 l
346.688458 457.029605 l
344.67651 459.313814 l
342.664562 457.422326 l
340.652614 457.497422 l
338.640667 458.879697 l
336.628719 458.478273 l
334.616771 461.270977 l
332.604823 460.801293 l
330.592876 464.351852 l
328.580928 457.433322 l
326.56898 462.315024 l
324.557032 462.958217 l
322.545084 461.957533 l
320.533137 457.365481 l
318.521189 460.656536 l
316.509241 459.01832 l
314.497293 465.180274 l
312.485346 470.930043 l
310.473398 465.290954 l
308.46145 491.846542 l
306.449502 481.721582 l
304.437554 508.832793 l
302.425607 511.750521 l
300.413659 516.247569 l
298.401711 522.466085 l
296.389763 513.18678 l
294.377815 550.486236 l
292.365868 551.088428 l
290.35392 558.253753 l
288.341972 582.392238 l
286.330024 617.012466 l
284.318077 623.061559 l
280.294181 630.03909 l
278.282233 632.331788 l
276.270285 635.146956 l
272.24639 638.971132 l
270.234442 640.958259 l
264.198599 645.596568 l
262.186651 646.643528 l
260.174703 648.139823 l
258.162755 649.4421 l
256.150808 650.528614 l
254.13886 651.78696 l
246.091069 655.997669 l
244.079121 656.91638 l
240.055225 659.0245 l
238.043278 659.794164 l
234.019382 661.668283 l
225.971591 665.125814 l
223.959643 665.536099 l
221.947695 666.490594 l
217.9238 667.817646 l
213.899904 669.351695 l
211.887956 669.947078 l
209.876009 670.699781 l
205.852113 671.994706 l
201.828217 673.224581 l
197.804322 674.450402 l
193.780426 675.694364 l
187.744583 677.538159 l
183.720687 678.519492 l
181.70874 679.172122 l
161.589262 684.248974 l
159.577314 684.64012 l
153.541471 686.125943 l
151.529523 686.466414 l
149.517575 687.054314 l
113.302515 694.501143 l
105.254724 695.944099 l
93.183037 698.148362 l
93.183037 698.148362 l
stroke
grestore
2.500 setlinewidth
0 setlinejoin
0.000 setgray
gsave
79.2 454.68625 m
79.2 731.88625 l
stroke
grestore
gsave
393.969231 454.68625 m
393.969231 731.88625 l
stroke
grestore
gsave
79.2 454.68625 m
393.969231 454.68625 l
stroke
grestore
gsave
79.2 731.88625 m
393.969231 731.88625 l
stroke
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

132.416 751.487 translate
0 rotate
0 0 m /parenleft glyphshow
8.3252 0 m /a glyphshow
19.4214 0 m /parenright glyphshow
27.7466 0 m /space glyphshow
33.9966 0 m /H glyphshow
52.0508 0 m /y glyphshow
64.5508 0 m /d glyphshow
77.0508 0 m /r glyphshow
85.376 0 m /o glyphshow
97.876 0 m /s glyphshow
107.605 0 m /t glyphshow
114.551 0 m /a glyphshow
125.647 0 m /t glyphshow
132.593 0 m /i glyphshow
139.539 0 m /c glyphshow
150.635 0 m /space glyphshow
156.885 0 m /P glyphshow
170.789 0 m /r glyphshow
179.114 0 m /e glyphshow
190.21 0 m /s glyphshow
199.939 0 m /s glyphshow
209.668 0 m /u glyphshow
222.168 0 m /r glyphshow
230.493 0 m /e glyphshow
grestore
1.500 setlinewidth
1 setlinejoin
0.749 0.212 0.047 setrgbcolor
gsave
97.2 625.37375 m
117.2 625.37375 l
137.2 625.37375 l
stroke
grestore
0.000 setgray
/TimesNewRomanPSMT 20.000 selectfont
gsave

153.2 618.374 translate
0 rotate
0 0 m /space glyphshow
5 0 m /hyphen glyphshow
11.6602 0 m /five glyphshow
21.6602 0 m /G glyphshow
36.1035 0 m /P glyphshow
47.2266 0 m /a glyphshow
grestore
0.937 0.424 0.000 setrgbcolor
gsave
97.2 597.0925 m
117.2 597.0925 l
137.2 597.0925 l
stroke
grestore
0.000 setgray
/TimesNewRomanPSMT 20.000 selectfont
gsave

153.2 590.092 translate
0 rotate
0 0 m /space glyphshow
5 0 m /hyphen glyphshow
11.6602 0 m /three glyphshow
21.6602 0 m /G glyphshow
36.1035 0 m /P glyphshow
47.2266 0 m /a glyphshow
grestore
1.000 0.702 0.000 setrgbcolor
gsave
97.2 568.81125 m
117.2 568.81125 l
137.2 568.81125 l
stroke
grestore
0.000 setgray
/TimesNewRomanPSMT 20.000 selectfont
gsave

153.2 561.811 translate
0 rotate
0 0 m /space glyphshow
5 0 m /hyphen glyphshow
11.6602 0 m /one glyphshow
21.6602 0 m /G glyphshow
36.1035 0 m /P glyphshow
47.2266 0 m /a glyphshow
grestore
0.031 0.659 0.255 setrgbcolor
gsave
97.2 540.53 m
117.2 540.53 l
137.2 540.53 l
stroke
grestore
0.000 setgray
/TimesNewRomanPSMT 20.000 selectfont
gsave

153.2 533.53 translate
0 rotate
0 0 m /space glyphshow
5 0 m /one glyphshow
15 0 m /G glyphshow
29.4434 0 m /P glyphshow
40.5664 0 m /a glyphshow
grestore
0.031 0.416 0.416 setrgbcolor
gsave
97.2 512.24875 m
117.2 512.24875 l
137.2 512.24875 l
stroke
grestore
0.000 setgray
/TimesNewRomanPSMT 20.000 selectfont
gsave

153.2 505.249 translate
0 rotate
0 0 m /space glyphshow
5 0 m /three glyphshow
15 0 m /G glyphshow
29.4434 0 m /P glyphshow
40.5664 0 m /a glyphshow
grestore
0.031 0.659 0.659 setrgbcolor
gsave
97.2 483.9675 m
117.2 483.9675 l
137.2 483.9675 l
stroke
grestore
0.000 setgray
/TimesNewRomanPSMT 20.000 selectfont
gsave

153.2 476.967 translate
0 rotate
0 0 m /space glyphshow
5 0 m /five glyphshow
15 0 m /G glyphshow
29.4434 0 m /P glyphshow
40.5664 0 m /a glyphshow
grestore
gsave
535.615385 454.68625 m
850.384615 454.68625 l
850.384615 731.88625 l
535.615385 731.88625 l
cl
1.000 setgray
fill
grestore
2.500 setlinewidth
0 setlinecap
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
553.103 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
553.103 731.886 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

546.853 427.327 translate
0 rotate
0 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
623.051 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
623.051 731.886 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

604.301 427.327 translate
0 rotate
0 0 m /four glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
693 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
693 731.886 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

674.25 427.327 translate
0 rotate
0 0 m /eight glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
762.949 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
762.949 731.886 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

737.949 427.327 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /two glyphshow
25 0 m /zero glyphshow
37.5 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
832.897 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
832.897 731.886 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

807.897 427.327 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /six glyphshow
25 0 m /zero glyphshow
37.5 0 m /zero glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
570.59 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
570.59 731.886 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
588.077 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
588.077 731.886 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
605.564 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
605.564 731.886 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
640.538 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
640.538 731.886 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
658.026 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
658.026 731.886 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
675.513 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
675.513 731.886 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
710.487 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
710.487 731.886 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
727.974 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
727.974 731.886 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
745.462 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
745.462 731.886 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
780.436 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
780.436 731.886 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
797.923 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
797.923 731.886 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
815.41 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
815.41 731.886 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.385 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.385 731.886 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

609.539 400.624 translate
0 rotate
0 0 m /T glyphshow
13.521 0 m /e glyphshow
24.6172 0 m /m glyphshow
44.063 0 m /p glyphshow
56.563 0 m /e glyphshow
67.6592 0 m /r glyphshow
75.9844 0 m /a glyphshow
87.0806 0 m /t glyphshow
94.0264 0 m /u glyphshow
106.526 0 m /r glyphshow
114.852 0 m /e glyphshow
125.948 0 m /space glyphshow
132.198 0 m /parenleft glyphshow
140.523 0 m /K glyphshow
158.577 0 m /parenright glyphshow
grestore
2.500 setlinewidth
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
535.615 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.385 454.686 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

513.115 446.007 translate
0 rotate
0 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
535.615 500.886 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.385 500.886 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

500.615 492.207 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /five glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
535.615 547.086 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.385 547.086 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

500.615 538.407 translate
0 rotate
0 0 m /three glyphshow
12.5 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
535.615 593.286 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.385 593.286 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

500.615 584.607 translate
0 rotate
0 0 m /four glyphshow
12.5 0 m /five glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
535.615 639.486 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.385 639.486 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

500.615 630.807 translate
0 rotate
0 0 m /six glyphshow
12.5 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
535.615 685.686 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.385 685.686 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

500.615 677.007 translate
0 rotate
0 0 m /seven glyphshow
12.5 0 m /five glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
535.615 731.886 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.385 731.886 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

500.615 723.207 translate
0 rotate
0 0 m /nine glyphshow
12.5 0 m /zero glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
535.615 466.236 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.385 466.236 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
535.615 477.786 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.385 477.786 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
535.615 489.336 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.385 489.336 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
535.615 512.436 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.385 512.436 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
535.615 523.986 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.385 523.986 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
535.615 535.536 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.385 535.536 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
535.615 558.636 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.385 558.636 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
535.615 570.186 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.385 570.186 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
535.615 581.736 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.385 581.736 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
535.615 604.836 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.385 604.836 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
535.615 616.386 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.385 616.386 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
535.615 627.936 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.385 627.936 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
535.615 651.036 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.385 651.036 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
535.615 662.586 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.385 662.586 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
535.615 674.136 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.385 674.136 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
535.615 697.236 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.385 697.236 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
535.615 708.786 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.385 708.786 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
535.615 720.336 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.385 720.336 o
grestore
gsave
490.615 482.286 translate
90 rotate
/TimesNewRomanPSMT 25.0 selectfont
0 0.148438 moveto
/P glyphshow
13.9038 0.148438 moveto
/o glyphshow
26.4038 0.148438 moveto
/l glyphshow
33.3496 0.148438 moveto
/a glyphshow
44.4458 0.148438 moveto
/r glyphshow
52.771 0.148438 moveto
/i glyphshow
59.7168 0.148438 moveto
/z glyphshow
70.813 0.148438 moveto
/a glyphshow
81.9092 0.148438 moveto
/t glyphshow
88.855 0.148438 moveto
/i glyphshow
95.8008 0.148438 moveto
/o glyphshow
108.301 0.148438 moveto
/n glyphshow
120.801 0.148438 moveto
/space glyphshow
127.051 0.148438 moveto
/parenleft glyphshow
135.376 0.148438 moveto
/mu glyphshow
148.779 0.148438 moveto
/C glyphshow
165.454 0.148438 moveto
/slash glyphshow
172.4 0.148438 moveto
/c glyphshow
183.496 0.148438 moveto
/m glyphshow
/TimesNewRomanPSMT 17.5 selectfont
203.318 15.1766 moveto
/two glyphshow
/TimesNewRomanPSMT 25.0 selectfont
213.141 0.148438 moveto
/parenright glyphshow
grestore
2 setlinecap
0.749 0.212 0.047 setrgbcolor
gsave
314.769 277.2 535.615 454.686 clipbox
901.971795 455.457994 m
900.223077 454.895897 l
898.474359 455.755398 l
896.725641 455.95826 l
894.976923 456.586865 l
893.228205 455.347448 l
891.479487 456.215343 l
889.730769 456.73391 l
887.982051 455.855291 l
886.233333 456.053208 l
884.484615 455.516591 l
880.987179 456.12154 l
879.238462 455.859796 l
877.489744 455.950019 l
875.741026 456.62816 l
873.992308 456.57624 l
872.24359 455.726341 l
870.494872 456.996714 l
868.746154 456.423192 l
866.997436 456.067739 l
865.248718 457.396067 l
863.5 455.966038 l
860.002564 455.222101 l
858.253846 455.217095 l
856.505128 455.93287 l
853.007692 455.796961 l
851.258974 456.81849 l
849.510256 456.401212 l
847.761538 455.163837 l
846.012821 454.875126 l
844.264103 457.539405 l
842.515385 456.373686 l
840.766667 458.127402 l
839.017949 457.941924 l
837.269231 455.041762 l
835.520513 457.994632 l
833.771795 456.07482 l
832.023077 456.990197 l
830.274359 457.03739 l
828.525641 457.822598 l
826.776923 457.175731 l
823.279487 457.167791 l
821.530769 457.34337 l
819.782051 456.612437 l
818.033333 455.718255 l
816.284615 457.392135 l
814.535897 462.926881 l
812.787179 458.297911 l
811.038462 460.535864 l
809.289744 461.323846 l
807.541026 458.391771 l
805.792308 456.682072 l
804.04359 461.259186 l
802.294872 457.840498 l
800.546154 457.595658 l
798.797436 463.610752 l
797.048718 461.152712 l
795.3 471.36103 l
793.551282 462.675489 l
791.802564 475.208611 l
790.053846 463.731714 l
788.305128 469.510058 l
786.55641 480.096163 l
784.807692 468.837972 l
783.058974 514.376539 l
781.310256 527.782864 l
779.561538 470.829035 l
777.812821 520.072197 l
776.064103 530.461644 l
774.315385 532.110768 l
772.566667 506.829411 l
770.817949 556.306469 l
769.069231 544.457886 l
767.320513 576.54465 l
765.571795 567.446424 l
763.823077 561.926328 l
762.074359 573.744926 l
760.325641 589.981559 l
758.576923 568.914935 l
756.828205 599.901609 l
755.079487 603.943034 l
753.330769 605.389495 l
751.582051 610.468845 l
749.833333 611.650218 l
748.084615 614.614255 l
746.335897 617.952785 l
744.587179 619.650685 l
742.838462 623.19191 l
739.341026 627.130681 l
737.592308 630.012338 l
735.84359 630.583872 l
734.094872 627.874589 l
732.346154 626.961151 l
730.597436 630.246944 l
728.848718 633.061177 l
727.1 633.763194 l
725.351282 636.609001 l
723.602564 638.725677 l
721.853846 639.985924 l
720.105128 642.028699 l
718.35641 643.514188 l
716.607692 644.660593 l
714.858974 646.486521 l
713.110256 647.703184 l
711.361538 649.16408 l
709.612821 650.313184 l
707.864103 650.984851 l
706.115385 652.825949 l
704.366667 653.708071 l
702.617949 654.716181 l
700.869231 655.490499 l
699.120513 656.624353 l
697.371795 658.02307 l
695.623077 658.68262 l
692.125641 660.74354 l
681.633333 665.757683 l
679.884615 666.372364 l
678.135897 667.106061 l
676.387179 667.967316 l
672.889744 669.334465 l
671.141026 670.045179 l
669.392308 670.618145 l
667.64359 671.34053 l
665.894872 671.852632 l
662.397436 673.481248 l
660.648718 673.89805 l
658.9 674.656063 l
651.905128 676.93378 l
648.407692 678.132319 l
644.910256 679.246569 l
637.915385 681.443802 l
636.166667 681.842745 l
634.417949 682.37164 l
632.669231 683.056212 l
630.920513 683.295767 l
629.171795 683.945058 l
625.674359 684.90864 l
622.176923 685.98002 l
620.428205 686.325773 l
618.679487 686.840565 l
613.433333 688.130702 l
611.684615 688.560593 l
609.935897 689.134267 l
606.438462 689.84842 l
604.689744 690.412915 l
597.694872 691.959767 l
595.946154 692.420441 l
594.197436 692.680144 l
590.7 693.604912 l
585.453846 694.790195 l
576.710256 696.702998 l
574.961538 696.968295 l
571.464103 697.750501 l
553.976923 701.200827 l
553.976923 701.200827 l
stroke
grestore
0.937 0.424 0.000 setrgbcolor
gsave
314.769 277.2 535.615 454.686 clipbox
901.971795 454.8896 m
900.223077 456.019864 l
898.474359 455.451934 l
894.976923 455.124299 l
893.228205 455.533157 l
891.479487 455.443459 l
889.730769 456.465927 l
887.982051 455.488056 l
886.233333 455.324469 l
884.484615 455.868965 l
882.735897 455.417935 l
880.987179 456.132519 l
879.238462 455.361731 l
875.741026 455.862648 l
873.992308 455.230038 l
872.24359 455.507354 l
868.746154 455.389657 l
866.997436 456.777737 l
865.248718 455.859304 l
863.5 455.572539 l
861.751282 456.022631 l
860.002564 455.070971 l
858.253846 456.298713 l
856.505128 455.479722 l
854.75641 456.929437 l
853.007692 456.07797 l
851.258974 456.030961 l
849.510256 456.293218 l
847.761538 455.858028 l
846.012821 456.673099 l
844.264103 455.831973 l
842.515385 456.638919 l
840.766667 455.474898 l
839.017949 456.414125 l
837.269231 456.91863 l
835.520513 456.459162 l
833.771795 455.859826 l
832.023077 458.506555 l
830.274359 458.813217 l
828.525641 456.854076 l
826.776923 458.568949 l
825.028205 459.008481 l
823.279487 455.494935 l
821.530769 458.035636 l
819.782051 456.919696 l
818.033333 459.073915 l
816.284615 458.592503 l
814.535897 458.347581 l
812.787179 457.163353 l
811.038462 461.030366 l
809.289744 458.611003 l
807.541026 459.111797 l
805.792308 460.498511 l
804.04359 462.444039 l
802.294872 461.513877 l
800.546154 456.926557 l
798.797436 455.983194 l
797.048718 463.684159 l
795.3 456.12673 l
793.551282 463.284527 l
791.802564 461.629436 l
790.053846 466.092522 l
788.305128 461.737767 l
786.55641 474.335478 l
784.807692 477.194187 l
783.058974 459.763264 l
781.310256 469.121687 l
779.561538 499.426634 l
777.812821 492.50829 l
776.064103 509.762473 l
774.315385 522.145428 l
772.566667 494.264627 l
770.817949 534.166443 l
767.320513 550.177431 l
765.571795 548.825874 l
763.823077 566.580843 l
762.074359 534.687621 l
760.325641 583.644127 l
758.576923 577.381712 l
756.828205 585.999035 l
751.582051 600.329713 l
749.833333 604.634015 l
748.084615 608.065652 l
746.335897 610.234557 l
744.587179 613.002726 l
742.838462 622.696436 l
741.089744 635.032627 l
739.341026 636.930817 l
737.592308 639.349974 l
735.84359 641.325032 l
734.094872 642.430244 l
732.346154 644.710625 l
730.597436 645.253866 l
728.848718 647.645509 l
725.351282 649.440715 l
723.602564 650.993719 l
721.853846 651.979887 l
720.105128 653.480713 l
718.35641 654.458811 l
716.607692 655.57383 l
714.858974 656.424478 l
713.110256 657.66313 l
711.361538 658.300473 l
704.366667 662.20825 l
702.617949 662.756207 l
700.869231 663.844318 l
699.120513 664.462736 l
695.623077 666.132609 l
692.125641 667.803181 l
690.376923 668.297497 l
686.879487 670.084454 l
685.130769 670.567021 l
681.633333 671.839219 l
679.884615 672.754972 l
674.638462 674.478566 l
671.141026 675.730873 l
669.392308 676.374236 l
665.894872 677.465711 l
660.648718 679.142317 l
650.15641 682.431343 l
648.407692 682.847155 l
646.658974 683.146726 l
644.910256 683.895066 l
641.412821 684.849127 l
639.664103 685.202312 l
636.166667 686.265581 l
634.417949 686.783118 l
632.669231 687.149112 l
627.423077 688.562011 l
623.925641 689.46077 l
622.176923 689.724603 l
620.428205 690.252299 l
618.679487 690.552981 l
615.182051 691.473007 l
606.438462 693.469723 l
604.689744 693.817573 l
602.941026 694.304562 l
597.694872 695.411553 l
595.946154 695.860151 l
592.448718 696.499772 l
566.217949 701.748839 l
562.720513 702.331448 l
559.223077 702.987259 l
553.976923 703.942214 l
553.976923 703.942214 l
stroke
grestore
1.000 0.702 0.000 setrgbcolor
gsave
314.769 277.2 535.615 454.686 clipbox
901.971795 455.821894 m
900.223077 456.248361 l
898.474359 456.017332 l
896.725641 455.615229 l
894.976923 455.352903 l
893.228205 455.833645 l
891.479487 455.91965 l
889.730769 455.257497 l
887.982051 455.389567 l
886.233333 455.16098 l
884.484615 456.003628 l
882.735897 455.771345 l
879.238462 455.681671 l
877.489744 456.237597 l
875.741026 455.71125 l
873.992308 456.590102 l
872.24359 455.602212 l
870.494872 456.647878 l
868.746154 455.905471 l
866.997436 455.31544 l
865.248718 455.012154 l
863.5 455.25349 l
861.751282 456.490994 l
860.002564 455.651519 l
858.253846 456.679481 l
856.505128 455.82243 l
854.75641 455.777587 l
853.007692 456.858259 l
851.258974 456.964017 l
849.510256 456.292447 l
847.761538 455.312278 l
846.012821 456.260467 l
844.264103 455.55487 l
842.515385 456.756088 l
840.766667 456.294724 l
839.017949 455.995838 l
837.269231 458.470965 l
835.520513 456.983143 l
833.771795 456.630392 l
832.023077 457.339208 l
830.274359 458.242294 l
828.525641 455.831203 l
826.776923 456.87337 l
825.028205 456.866873 l
823.279487 457.184703 l
821.530769 455.763195 l
819.782051 456.616791 l
818.033333 456.361866 l
816.284615 456.821434 l
814.535897 458.727653 l
812.787179 459.004107 l
811.038462 458.498873 l
809.289744 458.978151 l
807.541026 457.102992 l
805.792308 456.846559 l
804.04359 457.520177 l
802.294872 456.160563 l
800.546154 459.301692 l
798.797436 458.341248 l
797.048718 460.252148 l
795.3 462.589624 l
793.551282 459.163728 l
791.802564 458.10322 l
790.053846 460.889046 l
788.305128 460.282481 l
786.55641 461.740995 l
784.807692 464.871102 l
783.058974 465.021985 l
781.310256 473.617773 l
779.561538 470.224867 l
777.812821 464.490109 l
776.064103 482.350919 l
774.315385 466.578223 l
772.566667 495.381428 l
770.817949 520.000435 l
769.069231 475.072358 l
767.320513 512.479957 l
765.571795 524.400155 l
763.823077 535.304123 l
762.074359 544.854794 l
760.325641 482.446488 l
758.576923 568.153613 l
756.828205 574.631725 l
755.079487 591.502303 l
753.330769 625.581303 l
751.582051 630.349184 l
746.335897 637.875305 l
744.587179 640.04092 l
742.838462 641.659556 l
741.089744 643.711534 l
739.341026 645.307649 l
737.592308 646.634825 l
735.84359 648.326832 l
732.346154 650.744404 l
728.848718 653.368218 l
727.1 654.388926 l
725.351282 655.773054 l
723.602564 656.766195 l
721.853846 657.601332 l
720.105128 658.889508 l
718.35641 659.837427 l
716.607692 660.465776 l
714.858974 661.740371 l
711.361538 663.348355 l
709.612821 663.95523 l
706.115385 665.753073 l
704.366667 666.364995 l
700.869231 668.13636 l
699.120513 668.687031 l
697.371795 669.582565 l
690.376923 672.054434 l
688.628205 672.940376 l
685.130769 674.039559 l
683.382051 674.852614 l
681.633333 675.277334 l
679.884615 676.110303 l
669.392308 679.549045 l
667.64359 679.883141 l
665.894872 680.630976 l
664.146154 681.206362 l
662.397436 681.649224 l
660.648718 682.209997 l
658.9 682.635067 l
657.151282 683.239379 l
655.402564 683.646927 l
650.15641 685.214969 l
644.910256 686.519661 l
636.166667 688.862985 l
634.417949 689.185398 l
627.423077 690.965865 l
620.428205 692.487737 l
618.679487 693.000987 l
595.946154 697.824916 l
585.453846 699.907068 l
581.95641 700.623989 l
574.961538 701.901002 l
573.212821 702.297959 l
567.966667 703.152968 l
564.469231 703.828105 l
553.976923 705.650822 l
553.976923 705.650822 l
stroke
grestore
0.031 0.659 0.255 setrgbcolor
gsave
314.769 277.2 535.615 454.686 clipbox
901.971795 456.35221 m
900.223077 455.102799 l
898.474359 455.685626 l
896.725641 455.832673 l
893.228205 455.236127 l
889.730769 455.52406 l
887.982051 456.19695 l
886.233333 456.416035 l
884.484615 455.423006 l
882.735897 456.974045 l
880.987179 455.520643 l
879.238462 455.404126 l
877.489744 456.2221 l
875.741026 455.464793 l
873.992308 456.89193 l
872.24359 455.73887 l
870.494872 455.569669 l
868.746154 456.009313 l
866.997436 455.609935 l
865.248718 456.330579 l
861.751282 455.390104 l
860.002564 457.682522 l
858.253846 455.758821 l
856.505128 455.206021 l
854.75641 457.805009 l
853.007692 457.121523 l
851.258974 456.083659 l
849.510256 458.156332 l
847.761538 455.987041 l
846.012821 456.132819 l
844.264103 456.617093 l
840.766667 456.713778 l
839.017949 457.852836 l
837.269231 455.549948 l
835.520513 457.2639 l
833.771795 457.195843 l
832.023077 456.540991 l
830.274359 454.918031 l
828.525641 456.858448 l
826.776923 458.611482 l
825.028205 455.934968 l
823.279487 456.935011 l
821.530769 457.040551 l
819.782051 458.10294 l
818.033333 457.45201 l
816.284615 457.612125 l
814.535897 458.391992 l
812.787179 456.934914 l
811.038462 457.407459 l
809.289744 459.048732 l
807.541026 458.872303 l
805.792308 455.738919 l
804.04359 456.620337 l
802.294872 459.776729 l
800.546154 462.44037 l
798.797436 460.914861 l
797.048718 460.472019 l
795.3 457.874213 l
793.551282 463.482062 l
791.802564 460.411594 l
790.053846 461.115138 l
788.305128 458.240877 l
786.55641 471.796329 l
784.807692 480.292815 l
783.058974 479.038401 l
781.310256 501.732404 l
779.561538 522.76565 l
777.812821 533.824671 l
776.064103 536.604855 l
772.566667 549.410554 l
770.817949 557.808244 l
769.069231 560.317043 l
767.320513 564.19414 l
765.571795 570.046826 l
763.823077 572.753025 l
762.074359 577.80845 l
760.325641 580.103217 l
758.576923 584.677762 l
756.828205 586.563111 l
755.079487 589.383858 l
753.330769 635.674979 l
749.833333 640.332458 l
748.084615 641.506929 l
744.587179 645.148006 l
742.838462 646.342024 l
741.089744 647.920792 l
739.341026 649.349723 l
737.592308 651.284214 l
732.346154 654.796738 l
730.597436 655.594848 l
728.848718 656.959774 l
727.1 657.7745 l
725.351282 658.837416 l
720.105128 661.572532 l
718.35641 662.387046 l
716.607692 663.484998 l
714.858974 664.358075 l
713.110256 665.040865 l
711.361538 666.204859 l
707.864103 667.607726 l
706.115385 668.288343 l
704.366667 668.832745 l
695.623077 672.607134 l
692.125641 673.662743 l
690.376923 674.391183 l
688.628205 674.872175 l
686.879487 675.670414 l
685.130769 676.173946 l
683.382051 676.905784 l
679.884615 677.847722 l
678.135897 678.573935 l
672.889744 680.185531 l
658.9 684.305784 l
657.151282 684.721797 l
655.402564 685.308154 l
646.658974 687.65912 l
644.910256 687.933781 l
643.161538 688.593987 l
634.417949 690.612593 l
625.674359 692.73431 l
623.925641 693.197982 l
620.428205 693.759403 l
618.679487 694.299096 l
613.433333 695.316709 l
611.684615 695.851371 l
609.935897 696.113514 l
608.187179 696.586248 l
594.197436 699.29788 l
592.448718 699.719345 l
588.951282 700.281307 l
585.453846 701.013485 l
564.469231 704.836098 l
557.474359 706.02472 l
553.976923 706.611343 l
553.976923 706.611343 l
stroke
grestore
0.031 0.416 0.416 setrgbcolor
gsave
314.769 277.2 535.615 454.686 clipbox
901.971795 455.35138 m
900.223077 455.664392 l
898.474359 456.134124 l
896.725641 456.14877 l
894.976923 456.484537 l
893.228205 456.269259 l
891.479487 455.723702 l
889.730769 456.770308 l
887.982051 455.889277 l
886.233333 456.492858 l
882.735897 456.289824 l
880.987179 457.527035 l
879.238462 456.165307 l
877.489744 457.272426 l
875.741026 455.841617 l
873.992308 457.582819 l
870.494872 456.823581 l
868.746154 456.345068 l
866.997436 458.108997 l
865.248718 455.46594 l
863.5 457.368208 l
861.751282 456.439509 l
860.002564 455.080002 l
858.253846 456.371705 l
856.505128 457.802858 l
854.75641 455.97915 l
853.007692 455.43941 l
851.258974 458.401872 l
849.510256 456.910681 l
847.761538 458.622169 l
846.012821 455.630421 l
844.264103 458.489657 l
840.766667 456.299816 l
839.017949 455.840468 l
837.269231 458.022431 l
835.520513 460.01431 l
833.771795 455.575761 l
832.023077 459.453592 l
830.274359 461.010536 l
828.525641 460.803231 l
826.776923 465.509647 l
825.028205 459.968348 l
823.279487 456.309899 l
821.530769 463.247157 l
819.782051 456.750832 l
818.033333 463.23637 l
816.284615 457.585832 l
814.535897 456.768679 l
812.787179 459.872685 l
811.038462 486.937208 l
809.289744 460.211769 l
807.541026 522.61603 l
805.792308 540.251911 l
804.04359 537.64771 l
802.294872 551.333845 l
800.546154 545.972331 l
798.797436 558.053998 l
797.048718 562.568344 l
795.3 567.995038 l
793.551282 570.549523 l
791.802564 573.42243 l
790.053846 578.444262 l
788.305128 582.571875 l
786.55641 585.827335 l
783.058974 593.795983 l
781.310256 595.659567 l
779.561538 596.460397 l
777.812821 600.05297 l
776.064103 602.941473 l
774.315385 604.914636 l
772.566667 607.735002 l
770.817949 609.855382 l
769.069231 612.917449 l
767.320513 614.015554 l
765.571795 617.328892 l
763.823077 618.668056 l
762.074359 620.975456 l
760.325641 622.731405 l
758.576923 624.753619 l
756.828205 626.342931 l
755.079487 629.007606 l
753.330769 629.768204 l
751.582051 631.613623 l
749.833333 633.761922 l
748.084615 634.895116 l
746.335897 637.330618 l
744.587179 638.207127 l
741.089744 641.708316 l
739.341026 642.752906 l
737.592308 644.341373 l
732.346154 648.604435 l
730.597436 649.638755 l
727.1 652.637303 l
725.351282 653.724417 l
723.602564 661.1701 l
721.853846 662.313024 l
718.35641 663.957363 l
713.110256 666.348871 l
711.361538 666.782433 l
709.612821 667.846126 l
706.115385 669.316954 l
702.617949 670.606334 l
699.120513 672.192475 l
697.371795 672.675383 l
695.623077 673.408689 l
693.874359 673.92083 l
692.125641 674.768828 l
690.376923 675.310633 l
688.628205 675.99168 l
676.387179 679.799083 l
674.638462 680.260407 l
669.392308 682.023717 l
655.402564 686.024962 l
653.653846 686.280569 l
646.658974 688.184834 l
644.910256 688.56536 l
641.412821 689.572216 l
639.664103 689.847918 l
634.417949 691.198566 l
630.920513 691.980888 l
623.925641 693.637902 l
620.428205 694.298668 l
618.679487 694.795404 l
616.930769 695.094768 l
615.182051 695.555146 l
613.433333 695.854691 l
609.935897 696.70797 l
606.438462 697.288791 l
599.44359 698.765077 l
594.197436 699.77969 l
588.951282 700.840122 l
583.705128 701.713794 l
580.207692 702.379039 l
562.720513 705.497602 l
553.976923 706.962689 l
553.976923 706.962689 l
stroke
grestore
0.031 0.659 0.659 setrgbcolor
gsave
314.769 277.2 535.615 454.686 clipbox
901.971795 458.187794 m
900.223077 455.707702 l
898.474359 455.280223 l
896.725641 456.899553 l
894.976923 456.368561 l
893.228205 455.432331 l
891.479487 455.454908 l
889.730769 455.846751 l
887.982051 455.267147 l
886.233333 458.78009 l
884.484615 457.924226 l
880.987179 456.440975 l
879.238462 455.500238 l
877.489744 458.799078 l
875.741026 456.067848 l
873.992308 458.099319 l
872.24359 457.716061 l
870.494872 456.642895 l
868.746154 457.125965 l
865.248718 456.804975 l
863.5 457.339451 l
861.751282 456.55431 l
860.002564 461.277696 l
858.253846 461.474457 l
856.505128 458.095547 l
854.75641 460.283385 l
853.007692 464.725103 l
851.258974 456.50235 l
849.510256 465.086009 l
847.761538 470.816258 l
846.012821 460.751638 l
844.264103 464.248567 l
842.515385 456.853481 l
840.766667 464.904718 l
839.017949 467.362223 l
837.269231 502.614678 l
835.520513 500.214436 l
833.771795 460.210365 l
832.023077 538.538006 l
830.274359 534.100637 l
828.525641 548.682522 l
826.776923 551.332911 l
825.028205 558.228081 l
823.279487 562.453269 l
821.530769 567.231375 l
819.782051 568.239282 l
818.033333 572.918767 l
816.284615 579.321703 l
814.535897 584.266937 l
812.787179 589.88856 l
811.038462 589.808689 l
809.289744 595.1982 l
807.541026 596.213029 l
805.792308 599.346817 l
804.04359 602.931475 l
802.294872 603.766851 l
800.546154 606.917205 l
798.797436 609.146045 l
797.048718 611.626008 l
795.3 614.50447 l
791.802564 618.322303 l
790.053846 620.093377 l
788.305128 622.92232 l
786.55641 624.338699 l
784.807692 626.436387 l
783.058974 628.244934 l
781.310256 629.002845 l
779.561538 631.738269 l
777.812821 633.706638 l
776.064103 634.676452 l
774.315385 636.801313 l
772.566667 638.553984 l
770.817949 639.371096 l
769.069231 641.046368 l
767.320513 643.109844 l
765.571795 644.264772 l
762.074359 647.587875 l
760.325641 648.741339 l
756.828205 651.90086 l
755.079487 652.839027 l
753.330769 654.523459 l
751.582051 655.467027 l
748.084615 658.347429 l
742.838462 661.610151 l
741.089744 663.200256 l
739.341026 664.274545 l
737.592308 665.555907 l
735.84359 666.677465 l
734.094872 667.317372 l
730.597436 669.570679 l
728.848718 671.20066 l
727.1 671.820916 l
721.853846 674.852652 l
720.105128 676.062156 l
716.607692 677.786179 l
714.858974 679.23322 l
709.612821 682.193132 l
707.864103 682.675486 l
706.115385 683.920955 l
704.366667 684.754508 l
702.617949 685.804653 l
700.869231 686.53371 l
699.120513 687.540083 l
693.874359 690.054135 l
692.125641 691.153255 l
688.628205 693.037289 l
686.879487 693.459208 l
685.130769 694.518082 l
683.382051 695.012625 l
681.633333 695.869498 l
679.884615 678.799301 l
678.135897 679.284361 l
676.387179 680.017356 l
674.638462 680.310728 l
672.889744 680.821253 l
671.141026 681.486233 l
667.64359 682.345093 l
664.146154 683.616503 l
662.397436 683.890215 l
657.151282 685.494708 l
655.402564 685.978296 l
653.653846 686.283083 l
651.905128 686.877601 l
648.407692 687.653208 l
644.910256 688.545876 l
643.161538 688.988224 l
639.664103 689.662457 l
637.915385 690.222261 l
634.417949 690.982252 l
630.920513 691.879838 l
629.171795 692.352833 l
623.925641 693.436108 l
622.176923 693.962704 l
618.679487 694.647154 l
616.930769 695.060124 l
615.182051 695.329429 l
606.438462 697.272829 l
604.689744 697.479368 l
594.197436 699.654026 l
592.448718 699.872177 l
587.202564 700.964207 l
583.705128 701.615966 l
559.223077 705.959043 l
553.976923 706.810608 l
553.976923 706.810608 l
stroke
grestore
2.500 setlinewidth
0 setlinejoin
0.000 setgray
gsave
535.615385 454.68625 m
535.615385 731.88625 l
stroke
grestore
gsave
850.384615 454.68625 m
850.384615 731.88625 l
stroke
grestore
gsave
535.615385 454.68625 m
850.384615 454.68625 l
stroke
grestore
gsave
535.615385 731.88625 m
850.384615 731.88625 l
stroke
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

605.564 751.487 translate
0 rotate
0 0 m /parenleft glyphshow
8.3252 0 m /b glyphshow
20.8252 0 m /parenright glyphshow
29.1504 0 m /space glyphshow
35.4004 0 m /U glyphshow
53.4546 0 m /n glyphshow
65.9546 0 m /i glyphshow
72.9004 0 m /a glyphshow
83.9966 0 m /x glyphshow
96.4966 0 m /i glyphshow
103.442 0 m /a glyphshow
114.539 0 m /l glyphshow
121.484 0 m /space glyphshow
127.734 0 m /S glyphshow
141.638 0 m /t glyphshow
148.584 0 m /r glyphshow
156.909 0 m /e glyphshow
168.005 0 m /s glyphshow
177.734 0 m /s glyphshow
grestore
1.500 setlinewidth
1 setlinejoin
0.749 0.212 0.047 setrgbcolor
gsave
553.615385 625.37375 m
573.615385 625.37375 l
593.615385 625.37375 l
stroke
grestore
0.000 setgray
/TimesNewRomanPSMT 20.000 selectfont
gsave

609.615 618.374 translate
0 rotate
0 0 m /space glyphshow
5 0 m /hyphen glyphshow
11.6602 0 m /five glyphshow
21.6602 0 m /G glyphshow
36.1035 0 m /P glyphshow
47.2266 0 m /a glyphshow
grestore
0.937 0.424 0.000 setrgbcolor
gsave
553.615385 597.0925 m
573.615385 597.0925 l
593.615385 597.0925 l
stroke
grestore
0.000 setgray
/TimesNewRomanPSMT 20.000 selectfont
gsave

609.615 590.092 translate
0 rotate
0 0 m /space glyphshow
5 0 m /hyphen glyphshow
11.6602 0 m /three glyphshow
21.6602 0 m /G glyphshow
36.1035 0 m /P glyphshow
47.2266 0 m /a glyphshow
grestore
1.000 0.702 0.000 setrgbcolor
gsave
553.615385 568.81125 m
573.615385 568.81125 l
593.615385 568.81125 l
stroke
grestore
0.000 setgray
/TimesNewRomanPSMT 20.000 selectfont
gsave

609.615 561.811 translate
0 rotate
0 0 m /space glyphshow
5 0 m /hyphen glyphshow
11.6602 0 m /one glyphshow
21.6602 0 m /G glyphshow
36.1035 0 m /P glyphshow
47.2266 0 m /a glyphshow
grestore
0.031 0.659 0.255 setrgbcolor
gsave
553.615385 540.53 m
573.615385 540.53 l
593.615385 540.53 l
stroke
grestore
0.000 setgray
/TimesNewRomanPSMT 20.000 selectfont
gsave

609.615 533.53 translate
0 rotate
0 0 m /space glyphshow
5 0 m /one glyphshow
15 0 m /G glyphshow
29.4434 0 m /P glyphshow
40.5664 0 m /a glyphshow
grestore
0.031 0.416 0.416 setrgbcolor
gsave
553.615385 512.24875 m
573.615385 512.24875 l
593.615385 512.24875 l
stroke
grestore
0.000 setgray
/TimesNewRomanPSMT 20.000 selectfont
gsave

609.615 505.249 translate
0 rotate
0 0 m /space glyphshow
5 0 m /three glyphshow
15 0 m /G glyphshow
29.4434 0 m /P glyphshow
40.5664 0 m /a glyphshow
grestore
0.031 0.659 0.659 setrgbcolor
gsave
553.615385 483.9675 m
573.615385 483.9675 l
593.615385 483.9675 l
stroke
grestore
0.000 setgray
/TimesNewRomanPSMT 20.000 selectfont
gsave

609.615 476.967 translate
0 rotate
0 0 m /space glyphshow
5 0 m /five glyphshow
15 0 m /G glyphshow
29.4434 0 m /P glyphshow
40.5664 0 m /a glyphshow
grestore
gsave
992.030769 454.68625 m
1306.8 454.68625 l
1306.8 731.88625 l
992.030769 731.88625 l
cl
1.000 setgray
fill
grestore
2.500 setlinewidth
0 setlinecap
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1010.55 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1010.55 731.886 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

1004.3 427.327 translate
0 rotate
0 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1084.61 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1084.61 731.886 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

1065.86 427.327 translate
0 rotate
0 0 m /four glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1158.67 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1158.67 731.886 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

1139.92 427.327 translate
0 rotate
0 0 m /eight glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1232.74 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1232.74 731.886 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

1207.74 427.327 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /two glyphshow
25 0 m /zero glyphshow
37.5 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1306.8 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1306.8 731.886 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

1281.8 427.327 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /six glyphshow
25 0 m /zero glyphshow
37.5 0 m /zero glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1029.06 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1029.06 731.886 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1047.58 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1047.58 731.886 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1066.09 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1066.09 731.886 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1103.13 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1103.13 731.886 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1121.64 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1121.64 731.886 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1140.16 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1140.16 731.886 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1177.19 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1177.19 731.886 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1195.7 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1195.7 731.886 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1214.22 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1214.22 731.886 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1251.25 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1251.25 731.886 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1269.77 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1269.77 731.886 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1288.28 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1288.28 731.886 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

1065.95 400.624 translate
0 rotate
0 0 m /T glyphshow
13.521 0 m /e glyphshow
24.6172 0 m /m glyphshow
44.063 0 m /p glyphshow
56.563 0 m /e glyphshow
67.6592 0 m /r glyphshow
75.9844 0 m /a glyphshow
87.0806 0 m /t glyphshow
94.0264 0 m /u glyphshow
106.526 0 m /r glyphshow
114.852 0 m /e glyphshow
125.948 0 m /space glyphshow
132.198 0 m /parenleft glyphshow
140.523 0 m /K glyphshow
158.577 0 m /parenright glyphshow
grestore
2.500 setlinewidth
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
992.031 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1306.8 454.686 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

969.531 446.007 translate
0 rotate
0 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
992.031 500.886 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1306.8 500.886 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

957.031 492.207 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /five glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
992.031 547.086 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1306.8 547.086 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

957.031 538.407 translate
0 rotate
0 0 m /three glyphshow
12.5 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
992.031 593.286 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1306.8 593.286 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

957.031 584.607 translate
0 rotate
0 0 m /four glyphshow
12.5 0 m /five glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
992.031 639.486 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1306.8 639.486 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

957.031 630.807 translate
0 rotate
0 0 m /six glyphshow
12.5 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
992.031 685.686 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1306.8 685.686 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

957.031 677.007 translate
0 rotate
0 0 m /seven glyphshow
12.5 0 m /five glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
992.031 731.886 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1306.8 731.886 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

957.031 723.207 translate
0 rotate
0 0 m /nine glyphshow
12.5 0 m /zero glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
992.031 466.236 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1306.8 466.236 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
992.031 477.786 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1306.8 477.786 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
992.031 489.336 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1306.8 489.336 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
992.031 512.436 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1306.8 512.436 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
992.031 523.986 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1306.8 523.986 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
992.031 535.536 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1306.8 535.536 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
992.031 558.636 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1306.8 558.636 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
992.031 570.186 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1306.8 570.186 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
992.031 581.736 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1306.8 581.736 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
992.031 604.836 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1306.8 604.836 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
992.031 616.386 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1306.8 616.386 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
992.031 627.936 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1306.8 627.936 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
992.031 651.036 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1306.8 651.036 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
992.031 662.586 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1306.8 662.586 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
992.031 674.136 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1306.8 674.136 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
992.031 697.236 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1306.8 697.236 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
992.031 708.786 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1306.8 708.786 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
992.031 720.336 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1306.8 720.336 o
grestore
gsave
947.031 482.286 translate
90 rotate
/TimesNewRomanPSMT 25.0 selectfont
0 0.148438 moveto
/P glyphshow
13.9038 0.148438 moveto
/o glyphshow
26.4038 0.148438 moveto
/l glyphshow
33.3496 0.148438 moveto
/a glyphshow
44.4458 0.148438 moveto
/r glyphshow
52.771 0.148438 moveto
/i glyphshow
59.7168 0.148438 moveto
/z glyphshow
70.813 0.148438 moveto
/a glyphshow
81.9092 0.148438 moveto
/t glyphshow
88.855 0.148438 moveto
/i glyphshow
95.8008 0.148438 moveto
/o glyphshow
108.301 0.148438 moveto
/n glyphshow
120.801 0.148438 moveto
/space glyphshow
127.051 0.148438 moveto
/parenleft glyphshow
135.376 0.148438 moveto
/mu glyphshow
148.779 0.148438 moveto
/C glyphshow
165.454 0.148438 moveto
/slash glyphshow
172.4 0.148438 moveto
/c glyphshow
183.496 0.148438 moveto
/m glyphshow
/TimesNewRomanPSMT 17.5 selectfont
203.318 15.1766 moveto
/two glyphshow
/TimesNewRomanPSMT 25.0 selectfont
213.141 0.148438 moveto
/parenright glyphshow
grestore
2 setlinecap
0.749 0.212 0.047 setrgbcolor
gsave
314.769 277.2 992.031 454.686 clipbox
1340 455.482302 m
1339.202715 455.615046 l
1337.351131 455.004897 l
1335.499548 455.762591 l
1333.647964 455.155748 l
1331.79638 455.793473 l
1329.944796 457.575378 l
1328.093213 456.222108 l
1324.390045 456.065152 l
1322.538462 457.827111 l
1318.835294 455.989643 l
1316.98371 455.993528 l
1315.132127 456.45994 l
1313.280543 455.46107 l
1311.428959 455.639975 l
1309.577376 456.838709 l
1307.725792 455.21306 l
1305.874208 458.809335 l
1304.022624 457.832391 l
1302.171041 456.64717 l
1300.319457 460.529309 l
1298.467873 456.275122 l
1296.61629 459.814829 l
1294.764706 457.3549 l
1292.913122 460.002092 l
1291.061538 464.909878 l
1289.209955 458.641216 l
1287.358371 457.710822 l
1285.506787 457.548369 l
1283.655204 465.43895 l
1281.80362 472.154675 l
1279.952036 463.929802 l
1278.100452 487.415317 l
1276.248869 485.883021 l
1274.397285 470.618253 l
1272.545701 529.012632 l
1270.694118 531.76879 l
1268.842534 534.273645 l
1266.99095 548.893111 l
1265.139367 548.775383 l
1263.287783 562.218728 l
1261.436199 563.384727 l
1259.584615 569.163439 l
1257.733032 572.868545 l
1255.881448 579.210594 l
1254.029864 580.838477 l
1252.178281 585.549586 l
1250.326697 587.850107 l
1248.475113 593.750535 l
1246.623529 594.972285 l
1244.771946 597.404305 l
1242.920362 600.985753 l
1241.068778 603.5674 l
1239.217195 604.766279 l
1237.365611 608.269221 l
1235.514027 610.02509 l
1233.662443 613.104402 l
1231.81086 615.200769 l
1229.959276 616.30675 l
1228.107692 619.45525 l
1226.256109 621.612882 l
1224.404525 623.416126 l
1222.552941 624.819887 l
1220.701357 627.499392 l
1216.99819 630.834598 l
1215.146606 632.918765 l
1213.295023 634.403322 l
1209.591855 637.687558 l
1207.740271 639.65986 l
1205.888688 641.181716 l
1204.037104 643.036845 l
1202.18552 644.085067 l
1200.333937 645.778315 l
1198.482353 647.961712 l
1194.779186 650.173131 l
1192.927602 651.81726 l
1191.076018 652.917201 l
1189.224434 654.614169 l
1185.521267 656.942218 l
1183.669683 658.574379 l
1178.114932 662.187283 l
1176.263348 663.758888 l
1174.411765 664.584364 l
1172.560181 665.975982 l
1168.857014 668.195576 l
1167.00543 669.605986 l
1165.153846 670.515307 l
1163.302262 671.818589 l
1161.450679 672.78891 l
1159.599095 674.223203 l
1157.747511 674.960161 l
1155.895928 676.166181 l
1150.341176 679.274382 l
1148.489593 680.058828 l
1146.638009 681.310315 l
1144.786425 682.243976 l
1142.934842 683.038203 l
1141.083258 684.228612 l
1135.528507 687.332928 l
1131.825339 689.134405 l
1129.973756 690.12551 l
1128.122172 690.928026 l
1126.270588 692.034257 l
1124.419005 692.678046 l
1122.567421 693.95619 l
1118.864253 695.36604 l
1115.161086 697.442676 l
1113.309502 698.173459 l
1111.457919 699.268134 l
1109.606335 699.975504 l
1105.903167 701.806992 l
1104.051584 702.448877 l
1102.2 678.01659 l
1092.942081 680.648437 l
1091.090498 681.25668 l
1061.465158 688.744828 l
1055.910407 689.931939 l
1054.058824 690.323457 l
1052.20724 690.832288 l
1050.355656 691.167641 l
1044.800905 692.472296 l
1026.285068 696.254049 l
1018.878733 697.72629 l
1011.472398 699.089744 l
1011.472398 699.089744 l
stroke
grestore
0.937 0.424 0.000 setrgbcolor
gsave
314.769 277.2 992.031 454.686 clipbox
1340 456.463323 m
1339.202715 456.704966 l
1337.351131 457.401955 l
1335.499548 456.441606 l
1333.647964 456.611416 l
1331.79638 456.629766 l
1329.944796 455.795046 l
1328.093213 456.577717 l
1326.241629 455.260748 l
1324.390045 456.340092 l
1322.538462 456.303561 l
1320.686878 455.543 l
1318.835294 456.091378 l
1316.98371 455.579467 l
1315.132127 455.930727 l
1311.428959 456.167922 l
1309.577376 458.289759 l
1307.725792 456.342043 l
1305.874208 456.044323 l
1304.022624 457.148704 l
1302.171041 455.504534 l
1300.319457 456.385058 l
1298.467873 455.107468 l
1296.61629 456.295614 l
1294.764706 456.279725 l
1292.913122 457.051564 l
1291.061538 456.327723 l
1289.209955 456.744297 l
1287.358371 463.742249 l
1285.506787 456.561293 l
1283.655204 457.143497 l
1281.80362 457.098708 l
1279.952036 461.137797 l
1278.100452 459.188433 l
1276.248869 462.284347 l
1274.397285 459.687397 l
1272.545701 456.678627 l
1270.694118 464.696754 l
1268.842534 457.571406 l
1266.99095 492.52502 l
1265.139367 495.190825 l
1263.287783 464.605979 l
1261.436199 478.795415 l
1259.584615 459.32129 l
1257.733032 515.775213 l
1255.881448 456.399006 l
1254.029864 548.910421 l
1252.178281 548.490598 l
1250.326697 559.87588 l
1248.475113 563.792884 l
1246.623529 565.049211 l
1244.771946 573.136223 l
1241.068778 580.127438 l
1239.217195 582.607376 l
1237.365611 587.523579 l
1235.514027 590.965172 l
1233.662443 593.907467 l
1231.81086 595.141593 l
1229.959276 598.745468 l
1228.107692 600.844236 l
1224.404525 606.073467 l
1222.552941 610.038612 l
1220.701357 611.211235 l
1218.849774 614.152251 l
1216.99819 615.673524 l
1215.146606 618.234087 l
1213.295023 620.266955 l
1211.443439 621.83745 l
1207.740271 625.9592 l
1205.888688 627.244686 l
1202.18552 631.18176 l
1200.333937 633.018635 l
1198.482353 634.206835 l
1196.630769 636.284826 l
1192.927602 639.827221 l
1191.076018 641.372286 l
1189.224434 642.352972 l
1187.372851 644.215234 l
1185.521267 645.730089 l
1183.669683 647.472188 l
1179.966516 649.467343 l
1176.263348 652.539397 l
1174.411765 653.783408 l
1172.560181 655.684928 l
1168.857014 657.571193 l
1165.153846 659.940386 l
1163.302262 661.585307 l
1161.450679 662.447839 l
1159.599095 663.753557 l
1152.19276 667.152194 l
1146.638009 668.937534 l
1144.786425 669.875448 l
1142.934842 670.236112 l
1141.083258 671.222745 l
1135.528507 673.018379 l
1133.676923 673.794489 l
1131.825339 674.263335 l
1129.973756 675.026498 l
1126.270588 676.033557 l
1124.419005 676.748538 l
1118.864253 678.411523 l
1117.01267 679.021641 l
1115.161086 679.481518 l
1111.457919 680.704522 l
1109.606335 681.232725 l
1107.754751 681.590177 l
1105.903167 682.15264 l
1104.051584 682.598259 l
1100.348416 683.733328 l
1098.496833 684.239259 l
1096.645249 684.514227 l
1094.793665 685.135824 l
1092.942081 685.516779 l
1087.38733 686.990743 l
1083.684163 687.857637 l
1081.832579 688.389744 l
1078.129412 689.192411 l
1074.426244 690.040613 l
1068.871493 691.266709 l
1059.613575 693.404403 l
1057.761991 693.650398 l
1044.800905 696.33032 l
1042.949321 696.636414 l
1041.097738 697.086366 l
1035.542986 698.106657 l
1033.691403 698.540423 l
1022.5819 700.556857 l
1018.878733 701.206528 l
1011.472398 702.519624 l
1011.472398 702.519624 l
stroke
grestore
1.000 0.702 0.000 setrgbcolor
gsave
314.769 277.2 992.031 454.686 clipbox
1340 456.484198 m
1339.202715 455.896952 l
1337.351131 455.915861 l
1335.499548 455.384992 l
1333.647964 455.588255 l
1331.79638 455.63283 l
1329.944796 455.960529 l
1328.093213 458.222806 l
1326.241629 456.219837 l
1324.390045 456.319781 l
1322.538462 455.92271 l
1320.686878 455.958263 l
1318.835294 456.254401 l
1316.98371 457.284385 l
1315.132127 457.53138 l
1313.280543 455.160103 l
1311.428959 456.605961 l
1309.577376 455.308358 l
1307.725792 456.871815 l
1305.874208 456.250999 l
1304.022624 455.848706 l
1302.171041 456.616004 l
1300.319457 456.652577 l
1298.467873 456.900425 l
1294.764706 455.948052 l
1292.913122 456.903362 l
1287.358371 457.75496 l
1285.506787 458.387802 l
1283.655204 455.98261 l
1281.80362 456.897711 l
1279.952036 457.461118 l
1278.100452 456.635868 l
1276.248869 456.103702 l
1274.397285 456.453695 l
1270.694118 459.216064 l
1268.842534 457.314813 l
1266.99095 459.062002 l
1265.139367 459.298187 l
1263.287783 459.196457 l
1261.436199 468.80647 l
1259.584615 457.105174 l
1257.733032 465.830581 l
1255.881448 462.814281 l
1254.029864 467.138125 l
1252.178281 460.011072 l
1250.326697 478.284675 l
1248.475113 460.54412 l
1246.623529 494.980193 l
1244.771946 509.475984 l
1242.920362 519.036538 l
1241.068778 534.915691 l
1237.365611 551.713853 l
1235.514027 553.787168 l
1233.662443 559.262621 l
1231.81086 566.823225 l
1229.959276 568.934558 l
1228.107692 573.161456 l
1226.256109 577.024907 l
1224.404525 577.914625 l
1222.552941 582.553189 l
1220.701357 584.905816 l
1218.849774 588.365522 l
1216.99819 593.08418 l
1215.146606 598.738951 l
1213.295023 636.743751 l
1211.443439 638.978818 l
1207.740271 642.73525 l
1205.888688 644.176444 l
1202.18552 647.749533 l
1200.333937 648.51094 l
1198.482353 650.248042 l
1196.630769 651.643214 l
1192.927602 653.816974 l
1191.076018 655.21188 l
1189.224434 656.001431 l
1187.372851 657.084861 l
1185.521267 658.434839 l
1183.669683 659.039782 l
1181.8181 660.374913 l
1179.966516 660.952592 l
1178.114932 662.250575 l
1176.263348 662.922287 l
1174.411765 663.467119 l
1172.560181 664.511341 l
1168.857014 665.907496 l
1167.00543 666.848179 l
1165.153846 667.508327 l
1163.302262 668.452523 l
1161.450679 669.20264 l
1159.599095 669.7625 l
1157.747511 670.639211 l
1150.341176 673.268125 l
1146.638009 674.408518 l
1144.786425 675.267156 l
1142.934842 675.731109 l
1141.083258 676.366451 l
1139.231674 676.813095 l
1133.676923 678.685048 l
1131.825339 679.112372 l
1128.122172 680.324853 l
1126.270588 680.716145 l
1117.01267 683.471331 l
1111.457919 684.755964 l
1109.606335 685.381974 l
1107.754751 685.80366 l
1105.903167 686.110845 l
1104.051584 686.773835 l
1102.2 687.270268 l
1100.348416 687.586932 l
1098.496833 688.105333 l
1096.645249 688.404664 l
1094.793665 688.965 l
1091.090498 689.797528 l
1085.535747 691.098757 l
1076.277828 693.119092 l
1074.426244 693.413019 l
1070.723077 694.277245 l
1057.761991 696.955802 l
1054.058824 697.642884 l
1046.652489 699.037825 l
1026.285068 702.694495 l
1011.472398 705.171732 l
1011.472398 705.171732 l
stroke
grestore
0.031 0.659 0.255 setrgbcolor
gsave
314.769 277.2 992.031 454.686 clipbox
1340 455.754641 m
1339.202715 455.786896 l
1335.499548 456.697079 l
1333.647964 455.654138 l
1331.79638 455.962685 l
1329.944796 455.997967 l
1328.093213 456.182409 l
1326.241629 455.775488 l
1324.390045 455.922519 l
1322.538462 456.947559 l
1320.686878 457.301716 l
1318.835294 456.025708 l
1316.98371 456.47991 l
1315.132127 456.435962 l
1313.280543 457.455223 l
1311.428959 457.787687 l
1307.725792 457.252943 l
1305.874208 458.137046 l
1302.171041 456.91122 l
1300.319457 456.840944 l
1298.467873 457.929195 l
1296.61629 455.977997 l
1294.764706 457.981336 l
1292.913122 457.864873 l
1291.061538 461.337184 l
1289.209955 457.730158 l
1287.358371 457.986186 l
1285.506787 456.842175 l
1281.80362 457.577668 l
1279.952036 459.395609 l
1278.100452 457.940463 l
1276.248869 457.204071 l
1274.397285 462.706672 l
1272.545701 457.317611 l
1270.694118 466.086722 l
1268.842534 457.928055 l
1266.99095 458.513672 l
1265.139367 468.047075 l
1263.287783 472.462005 l
1261.436199 459.08894 l
1259.584615 457.790268 l
1257.733032 468.846189 l
1255.881448 475.307756 l
1254.029864 475.101092 l
1252.178281 469.119287 l
1250.326697 495.22933 l
1248.475113 472.626518 l
1246.623529 529.086065 l
1244.771946 520.711449 l
1242.920362 509.08908 l
1241.068778 496.487533 l
1239.217195 545.588614 l
1237.365611 562.996266 l
1235.514027 546.384586 l
1233.662443 566.693592 l
1231.81086 575.356836 l
1229.959276 580.805417 l
1228.107692 631.013993 l
1226.256109 633.994841 l
1222.552941 639.154901 l
1218.849774 642.595928 l
1216.99819 644.856208 l
1215.146606 645.668512 l
1213.295023 647.579616 l
1207.740271 651.695236 l
1205.888688 652.741287 l
1204.037104 654.240081 l
1202.18552 655.012503 l
1198.482353 657.370806 l
1196.630769 658.537527 l
1192.927602 660.262053 l
1191.076018 661.259341 l
1187.372851 662.960038 l
1183.669683 664.924547 l
1181.8181 665.531184 l
1178.114932 667.136551 l
1176.263348 667.964643 l
1172.560181 669.156682 l
1170.708597 670.143753 l
1168.857014 670.936074 l
1167.00543 671.378753 l
1157.747511 674.744911 l
1150.341176 677.281766 l
1146.638009 678.238707 l
1144.786425 678.91624 l
1141.083258 680.014952 l
1135.528507 681.575187 l
1133.676923 681.905386 l
1131.825339 682.684892 l
1129.973756 683.093249 l
1126.270588 684.08851 l
1118.864253 686.04608 l
1117.01267 686.619834 l
1115.161086 686.985616 l
1113.309502 687.226105 l
1111.457919 688.000762 l
1109.606335 688.245795 l
1105.903167 689.295478 l
1104.051584 689.518069 l
1102.2 690.118733 l
1096.645249 691.264145 l
1092.942081 692.143594 l
1091.090498 692.440191 l
1081.832579 694.566541 l
1079.980995 694.82516 l
1078.129412 695.274885 l
1076.277828 695.591443 l
1072.574661 696.47305 l
1068.871493 697.053846 l
1067.01991 697.386488 l
1065.168326 697.866245 l
1061.465158 698.549291 l
1054.058824 699.911499 l
1048.504072 700.835184 l
1037.39457 702.852453 l
1031.839819 703.773634 l
1028.136652 704.356543 l
1022.5819 705.337515 l
1013.323982 706.775554 l
1011.472398 707.07488 l
1011.472398 707.07488 l
stroke
grestore
0.031 0.416 0.416 setrgbcolor
gsave
314.769 277.2 992.031 454.686 clipbox
1340 456.052823 m
1339.202715 456.561362 l
1337.351131 456.066432 l
1335.499548 456.835885 l
1333.647964 456.358241 l
1331.79638 455.256426 l
1329.944796 458.757847 l
1328.093213 457.403081 l
1326.241629 458.584206 l
1324.390045 456.022408 l
1322.538462 457.205601 l
1320.686878 455.681417 l
1318.835294 457.115078 l
1316.98371 456.350367 l
1315.132127 455.074115 l
1313.280543 459.237711 l
1311.428959 459.336425 l
1309.577376 457.583627 l
1307.725792 460.241408 l
1305.874208 457.079733 l
1304.022624 458.861597 l
1302.171041 458.444751 l
1300.319457 457.364548 l
1298.467873 458.700452 l
1296.61629 458.951727 l
1294.764706 456.775348 l
1292.913122 458.412472 l
1291.061538 458.8582 l
1289.209955 460.005491 l
1287.358371 466.845219 l
1285.506787 460.952316 l
1283.655204 459.518579 l
1281.80362 467.381075 l
1279.952036 459.411708 l
1278.100452 465.949332 l
1276.248869 473.093131 l
1274.397285 460.643929 l
1272.545701 475.880355 l
1270.694118 486.034017 l
1268.842534 483.910155 l
1266.99095 458.256835 l
1265.139367 482.881499 l
1263.287783 514.255461 l
1261.436199 519.680122 l
1259.584615 533.381472 l
1257.733032 459.279536 l
1255.881448 545.709202 l
1254.029864 501.749317 l
1252.178281 554.725533 l
1250.326697 574.675509 l
1248.475113 584.168986 l
1246.623529 576.216521 l
1244.771946 586.404529 l
1242.920362 578.104158 l
1241.068778 598.907778 l
1239.217195 601.141794 l
1237.365611 604.479584 l
1235.514027 608.706284 l
1233.662443 636.110952 l
1231.81086 638.279551 l
1226.256109 644.03355 l
1224.404525 645.030699 l
1222.552941 646.732179 l
1218.849774 649.775736 l
1216.99819 650.933553 l
1215.146606 652.274294 l
1211.443439 654.191189 l
1209.591855 655.346643 l
1207.740271 656.771736 l
1202.18552 659.693051 l
1196.630769 662.640314 l
1194.779186 663.030024 l
1192.927602 664.252054 l
1189.224434 665.680634 l
1187.372851 666.290083 l
1185.521267 667.335012 l
1174.411765 671.635761 l
1172.560181 672.491527 l
1167.00543 674.344259 l
1165.153846 675.144993 l
1161.450679 676.107335 l
1159.599095 676.799271 l
1157.747511 677.36898 l
1155.895928 677.810688 l
1154.044344 678.530846 l
1150.341176 679.677878 l
1148.489593 680.062723 l
1146.638009 680.683161 l
1144.786425 681.147015 l
1142.934842 681.783129 l
1137.38009 683.235942 l
1135.528507 683.822921 l
1133.676923 684.131863 l
1131.825339 684.714175 l
1126.270588 686.135638 l
1122.567421 687.062225 l
1117.01267 688.398334 l
1115.161086 688.941303 l
1111.457919 689.69878 l
1105.903167 691.022048 l
1104.051584 691.429387 l
1102.2 691.71768 l
1098.496833 692.633438 l
1096.645249 692.941664 l
1091.090498 694.198794 l
1087.38733 694.946776 l
1083.684163 695.71985 l
1074.426244 697.427391 l
1070.723077 698.278633 l
1065.168326 699.176928 l
1059.613575 700.278464 l
1055.910407 701.019639 l
1054.058824 701.224233 l
1050.355656 701.931361 l
1044.800905 702.874606 l
1042.949321 703.155428 l
1041.097738 703.555409 l
1039.246154 703.789648 l
1037.39457 704.156065 l
1035.542986 704.374736 l
1028.136652 705.630594 l
1013.323982 707.938302 l
1011.472398 708.227055 l
1011.472398 708.227055 l
stroke
grestore
0.031 0.659 0.659 setrgbcolor
gsave
314.769 277.2 992.031 454.686 clipbox
1340 458.504359 m
1339.202715 458.82287 l
1337.351131 459.784785 l
1335.499548 456.230697 l
1333.647964 461.317318 l
1331.79638 456.57896 l
1329.944796 457.494581 l
1328.093213 456.156047 l
1326.241629 456.999877 l
1324.390045 457.439623 l
1322.538462 461.593142 l
1320.686878 458.917801 l
1318.835294 461.887936 l
1316.98371 461.306922 l
1315.132127 455.954916 l
1313.280543 456.582521 l
1311.428959 460.773759 l
1309.577376 456.129564 l
1307.725792 467.844898 l
1305.874208 459.312297 l
1304.022624 464.082627 l
1302.171041 458.350675 l
1300.319457 463.533176 l
1298.467873 464.759475 l
1296.61629 458.979484 l
1294.764706 477.272673 l
1292.913122 489.004341 l
1291.061538 463.374622 l
1289.209955 503.178654 l
1287.358371 477.873064 l
1285.506787 509.957026 l
1283.655204 515.179953 l
1281.80362 523.463955 l
1279.952036 507.367132 l
1278.100452 542.794381 l
1276.248869 503.734041 l
1274.397285 550.219163 l
1272.545701 532.503153 l
1270.694118 553.418558 l
1268.842534 456.536141 l
1266.99095 456.256623 l
1265.139367 455.499591 l
1261.436199 455.126786 l
1259.584615 456.028168 l
1257.733032 455.192245 l
1255.881448 454.90932 l
1252.178281 455.629838 l
1250.326697 455.131158 l
1246.623529 455.365433 l
1244.771946 455.07662 l
1242.920362 455.041362 l
1241.068778 455.133825 l
1239.217195 455.367021 l
1237.365611 455.391326 l
1235.514027 455.073445 l
1233.662443 455.319462 l
1231.81086 455.069983 l
1229.959276 455.279302 l
1228.107692 454.808263 l
1226.256109 455.132925 l
1224.404525 454.889072 l
1220.701357 455.023282 l
1218.849774 455.271517 l
1216.99819 455.122932 l
1215.146606 455.091639 l
1213.295023 455.253667 l
1209.591855 455.362305 l
1207.740271 455.097792 l
1204.037104 455.086393 l
1202.18552 455.450557 l
1198.482353 454.818764 l
1194.779186 455.127221 l
1192.927602 455.111088 l
1191.076018 454.980903 l
1185.521267 454.922803 l
1183.669683 455.252991 l
1181.8181 455.02975 l
1178.114932 454.943208 l
1176.263348 454.987105 l
1174.411765 454.840276 l
1172.560181 455.148855 l
1168.857014 454.87027 l
1163.302262 454.8496 l
1161.450679 455.052149 l
1159.599095 455.053444 l
1157.747511 454.780423 l
1144.786425 455.095976 l
1142.934842 454.78801 l
1141.083258 454.836443 l
1139.231674 455.011488 l
1137.38009 454.874873 l
1135.528507 454.948268 l
1133.676923 454.733383 l
1129.973756 454.956219 l
1126.270588 454.824107 l
1124.419005 454.864507 l
1122.567421 455.01751 l
1120.715837 454.830387 l
1118.864253 454.949044 l
1117.01267 454.814826 l
1113.309502 454.872308 l
1111.457919 455.072447 l
1109.606335 454.855109 l
1107.754751 454.768107 l
1105.903167 454.927886 l
1104.051584 454.828756 l
1102.2 454.843285 l
1100.348416 454.729543 l
1098.496833 454.847319 l
1092.942081 454.793405 l
1091.090498 454.873838 l
1089.238914 454.808604 l
1087.38733 454.941553 l
1085.535747 454.806341 l
1081.832579 454.909368 l
1079.980995 454.783255 l
1076.277828 454.988423 l
1074.426244 454.877662 l
1072.574661 454.92827 l
1070.723077 454.736752 l
1068.871493 454.876384 l
1059.613575 454.800159 l
1011.472398 454.695583 l
1011.472398 454.695583 l
stroke
grestore
2.500 setlinewidth
0 setlinejoin
0.000 setgray
gsave
992.030769 454.68625 m
992.030769 731.88625 l
stroke
grestore
gsave
1306.8 454.68625 m
1306.8 731.88625 l
stroke
grestore
gsave
992.030769 454.68625 m
1306.8 454.68625 l
stroke
grestore
gsave
992.030769 731.88625 m
1306.8 731.88625 l
stroke
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

1075.35 751.487 translate
0 rotate
0 0 m /parenleft glyphshow
8.3252 0 m /c glyphshow
19.4214 0 m /parenright glyphshow
27.7466 0 m /space glyphshow
33.9966 0 m /B glyphshow
50.6714 0 m /i glyphshow
57.6172 0 m /a glyphshow
68.7134 0 m /x glyphshow
81.2134 0 m /i glyphshow
88.1592 0 m /a glyphshow
99.2554 0 m /l glyphshow
106.201 0 m /space glyphshow
112.451 0 m /S glyphshow
126.355 0 m /t glyphshow
133.301 0 m /r glyphshow
141.626 0 m /e glyphshow
152.722 0 m /s glyphshow
162.451 0 m /s glyphshow
grestore
1.500 setlinewidth
1 setlinejoin
0.749 0.212 0.047 setrgbcolor
gsave
1010.030769 625.37375 m
1030.030769 625.37375 l
1050.030769 625.37375 l
stroke
grestore
0.000 setgray
/TimesNewRomanPSMT 20.000 selectfont
gsave

1066.03 618.374 translate
0 rotate
0 0 m /space glyphshow
5 0 m /hyphen glyphshow
11.6602 0 m /five glyphshow
21.6602 0 m /G glyphshow
36.1035 0 m /P glyphshow
47.2266 0 m /a glyphshow
grestore
0.937 0.424 0.000 setrgbcolor
gsave
1010.030769 597.0925 m
1030.030769 597.0925 l
1050.030769 597.0925 l
stroke
grestore
0.000 setgray
/TimesNewRomanPSMT 20.000 selectfont
gsave

1066.03 590.092 translate
0 rotate
0 0 m /space glyphshow
5 0 m /hyphen glyphshow
11.6602 0 m /three glyphshow
21.6602 0 m /G glyphshow
36.1035 0 m /P glyphshow
47.2266 0 m /a glyphshow
grestore
1.000 0.702 0.000 setrgbcolor
gsave
1010.030769 568.81125 m
1030.030769 568.81125 l
1050.030769 568.81125 l
stroke
grestore
0.000 setgray
/TimesNewRomanPSMT 20.000 selectfont
gsave

1066.03 561.811 translate
0 rotate
0 0 m /space glyphshow
5 0 m /hyphen glyphshow
11.6602 0 m /one glyphshow
21.6602 0 m /G glyphshow
36.1035 0 m /P glyphshow
47.2266 0 m /a glyphshow
grestore
0.031 0.659 0.255 setrgbcolor
gsave
1010.030769 540.53 m
1030.030769 540.53 l
1050.030769 540.53 l
stroke
grestore
0.000 setgray
/TimesNewRomanPSMT 20.000 selectfont
gsave

1066.03 533.53 translate
0 rotate
0 0 m /space glyphshow
5 0 m /one glyphshow
15 0 m /G glyphshow
29.4434 0 m /P glyphshow
40.5664 0 m /a glyphshow
grestore
0.031 0.416 0.416 setrgbcolor
gsave
1010.030769 512.24875 m
1030.030769 512.24875 l
1050.030769 512.24875 l
stroke
grestore
0.000 setgray
/TimesNewRomanPSMT 20.000 selectfont
gsave

1066.03 505.249 translate
0 rotate
0 0 m /space glyphshow
5 0 m /three glyphshow
15 0 m /G glyphshow
29.4434 0 m /P glyphshow
40.5664 0 m /a glyphshow
grestore
0.031 0.659 0.659 setrgbcolor
gsave
1010.030769 483.9675 m
1030.030769 483.9675 l
1050.030769 483.9675 l
stroke
grestore
0.000 setgray
/TimesNewRomanPSMT 20.000 selectfont
gsave

1066.03 476.967 translate
0 rotate
0 0 m /space glyphshow
5 0 m /five glyphshow
15 0 m /G glyphshow
29.4434 0 m /P glyphshow
40.5664 0 m /a glyphshow
grestore
gsave
79.2 66.60625 m
393.969231 66.60625 l
393.969231 343.80625 l
79.2 343.80625 l
cl
1.000 setgray
fill
grestore
2.500 setlinewidth
0 setlinecap
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 343.806 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

65.9031 39.2469 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /six glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
131.662 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
131.662 343.806 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

118.365 39.2469 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /four glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
184.123 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
184.123 343.806 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

170.826 39.2469 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /two glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
236.585 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
236.585 343.806 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

230.335 39.2469 translate
0 rotate
0 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
289.046 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
289.046 343.806 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

282.796 39.2469 translate
0 rotate
0 0 m /two glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
341.508 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
341.508 343.806 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

335.258 39.2469 translate
0 rotate
0 0 m /four glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 343.806 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

387.719 39.2469 translate
0 rotate
0 0 m /six glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
92.3154 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
92.3154 343.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
105.431 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
105.431 343.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
118.546 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
118.546 343.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
144.777 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
144.777 343.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
157.892 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
157.892 343.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
171.008 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
171.008 343.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
197.238 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
197.238 343.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
210.354 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
210.354 343.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
223.469 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
223.469 343.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
249.7 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
249.7 343.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
262.815 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
262.815 343.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
275.931 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
275.931 343.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
302.162 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
302.162 343.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
315.277 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
315.277 343.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
328.392 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
328.392 343.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
354.623 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
354.623 343.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
367.738 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
367.738 343.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
380.854 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
380.854 343.806 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

161.241 12.5438 translate
0 rotate
0 0 m /P glyphshow
13.9038 0 m /r glyphshow
22.229 0 m /e glyphshow
33.3252 0 m /s glyphshow
43.0542 0 m /s glyphshow
52.7832 0 m /u glyphshow
65.2832 0 m /r glyphshow
73.6084 0 m /e glyphshow
84.7046 0 m /space glyphshow
90.9546 0 m /parenleft glyphshow
99.2798 0 m /G glyphshow
117.334 0 m /P glyphshow
131.238 0 m /a glyphshow
142.334 0 m /parenright glyphshow
grestore
2.500 setlinewidth
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 66.6062 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

37.95 57.9266 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /eight glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 135.906 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 135.906 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

37.95 127.227 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /nine glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 205.206 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 205.206 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

37.95 196.527 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 274.506 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 274.506 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

37.95 265.827 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /period glyphshow
18.75 0 m /one glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 343.806 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 343.806 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

37.95 335.127 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /period glyphshow
18.75 0 m /two glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 80.4663 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 80.4663 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 94.3263 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 94.3263 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 108.186 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 108.186 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 122.046 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 122.046 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 149.766 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 149.766 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 163.626 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 163.626 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 177.486 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 177.486 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 191.346 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 191.346 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 219.066 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 219.066 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 232.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 232.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 246.786 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 246.786 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 260.646 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 260.646 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 288.366 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 288.366 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 302.226 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 302.226 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 316.086 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 316.086 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 329.946 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 329.946 o
grestore
gsave
26.95 158.706 translate
90 rotate
/TimesNewRomanPSMT 25.0 selectfont
0 0.640625 moveto
/T glyphshow
/TimesNewRomanPSMT 17.5 selectfont
15.6467 -5.8 moveto
/C glyphshow
/TimesNewRomanPSMT 25.0 selectfont
28.3925 0.640625 moveto
/slash glyphshow
35.3383 0.640625 moveto
/T glyphshow
/TimesNewRomanPSMT 17.5 selectfont
50.985 -5.8 moveto
/C glyphshow
/TimesNewRomanPSMT 25.0 selectfont
63.7308 0.640625 moveto
/parenleft glyphshow
72.056 0.640625 moveto
/zero glyphshow
84.556 0.640625 moveto
/parenright glyphshow
grestore
[9.6 2.4 1.5 2.4] 0 setdash
0.749 0.212 0.047 setrgbcolor
gsave
314.769 277.2 79.2 66.606 clipbox
367.738462 83.783173 m
341.507692 107.475481 l
315.276923 131.167788 l
289.046154 157.821635 l
262.815385 178.552404 l
236.584615 205.20625 l
210.353846 225.937019 l
184.123077 252.590865 l
157.892308 279.244712 l
131.661538 299.975481 l
105.430769 326.629327 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
314.769 277.2 79.2 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
367.738 83.7832 o
341.508 107.475 o
315.277 131.168 o
289.046 157.822 o
262.815 178.552 o
236.585 205.206 o
210.354 225.937 o
184.123 252.591 o
157.892 279.245 o
131.662 299.975 o
105.431 326.629 o
grestore
2.500 setlinewidth
0 setlinejoin
2 setlinecap
0.000 setgray
gsave
79.2 66.60625 m
79.2 343.80625 l
stroke
grestore
gsave
393.969231 66.60625 m
393.969231 343.80625 l
stroke
grestore
gsave
79.2 66.60625 m
393.969231 66.60625 l
stroke
grestore
gsave
79.2 343.80625 m
393.969231 343.80625 l
stroke
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

223.469 361.097 translate
0 rotate
0 0 m /parenleft glyphshow
8.3252 0 m /d glyphshow
20.8252 0 m /parenright glyphshow
grestore
gsave
535.615385 66.60625 m
850.384615 66.60625 l
850.384615 343.80625 l
535.615385 343.80625 l
cl
1.000 setgray
fill
grestore
1 setlinejoin
0 setlinecap
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
535.615 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
535.615 343.806 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

522.319 39.2469 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /six glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
588.077 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
588.077 343.806 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

574.78 39.2469 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /four glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
640.538 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
640.538 343.806 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

627.242 39.2469 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /two glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
693 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
693 343.806 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

686.75 39.2469 translate
0 rotate
0 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
745.462 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
745.462 343.806 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

739.212 39.2469 translate
0 rotate
0 0 m /two glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
797.923 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
797.923 343.806 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

791.673 39.2469 translate
0 rotate
0 0 m /four glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.385 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.385 343.806 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

844.135 39.2469 translate
0 rotate
0 0 m /six glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
548.731 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
548.731 343.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
561.846 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
561.846 343.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
574.962 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
574.962 343.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
601.192 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
601.192 343.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.308 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.308 343.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
627.423 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
627.423 343.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
653.654 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
653.654 343.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
666.769 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
666.769 343.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
679.885 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
679.885 343.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
706.115 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
706.115 343.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
719.231 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
719.231 343.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
732.346 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
732.346 343.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
758.577 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
758.577 343.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
771.692 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
771.692 343.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
784.808 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
784.808 343.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
811.038 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
811.038 343.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
824.154 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
824.154 343.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
837.269 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
837.269 343.806 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

583.961 12.5438 translate
0 rotate
0 0 m /U glyphshow
18.0542 0 m /n glyphshow
30.5542 0 m /i glyphshow
37.5 0 m /a glyphshow
48.5962 0 m /x glyphshow
61.0962 0 m /i glyphshow
68.042 0 m /a glyphshow
79.1382 0 m /l glyphshow
86.084 0 m /space glyphshow
92.334 0 m /S glyphshow
106.238 0 m /t glyphshow
113.184 0 m /r glyphshow
121.509 0 m /e glyphshow
132.605 0 m /s glyphshow
142.334 0 m /s glyphshow
152.063 0 m /space glyphshow
158.313 0 m /parenleft glyphshow
166.638 0 m /G glyphshow
184.692 0 m /P glyphshow
198.596 0 m /a glyphshow
209.692 0 m /parenright glyphshow
grestore
2.500 setlinewidth
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
535.615 82.5373 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.385 82.5373 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

494.365 73.8576 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /six glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
535.615 146.261 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.385 146.261 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

494.365 137.582 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /eight glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
535.615 209.986 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.385 209.986 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

494.365 201.306 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
535.615 273.71 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.385 273.71 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

494.365 265.03 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /period glyphshow
18.75 0 m /two glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
535.615 337.434 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.385 337.434 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

494.365 328.754 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /period glyphshow
18.75 0 m /four glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
535.615 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.385 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
535.615 98.4683 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.385 98.4683 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
535.615 114.399 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.385 114.399 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
535.615 130.33 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.385 130.33 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
535.615 162.192 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.385 162.192 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
535.615 178.123 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.385 178.123 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
535.615 194.055 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.385 194.055 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
535.615 225.917 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.385 225.917 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
535.615 241.848 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.385 241.848 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
535.615 257.779 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.385 257.779 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
535.615 289.641 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.385 289.641 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
535.615 305.572 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.385 305.572 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
535.615 321.503 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.385 321.503 o
grestore
gsave
483.365 158.706 translate
90 rotate
/TimesNewRomanPSMT 25.0 selectfont
0 0.640625 moveto
/T glyphshow
/TimesNewRomanPSMT 17.5 selectfont
15.6467 -5.8 moveto
/C glyphshow
/TimesNewRomanPSMT 25.0 selectfont
28.3925 0.640625 moveto
/slash glyphshow
35.3383 0.640625 moveto
/T glyphshow
/TimesNewRomanPSMT 17.5 selectfont
50.985 -5.8 moveto
/C glyphshow
/TimesNewRomanPSMT 25.0 selectfont
63.7308 0.640625 moveto
/parenleft glyphshow
72.056 0.640625 moveto
/zero glyphshow
84.556 0.640625 moveto
/parenright glyphshow
grestore
[9.6 2.4 1.5 2.4] 0 setdash
0.749 0.212 0.047 setrgbcolor
gsave
314.769 277.2 535.615 66.606 clipbox
824.153846 87.960615 m
797.923077 124.568099 l
771.692308 157.108084 l
745.461538 176.089742 l
719.230769 201.850564 l
693 209.98556 l
666.769231 204.562229 l
640.538462 197.783066 l
614.307692 185.580571 l
588.076923 174.73391 l
561.846154 170.666411 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
314.769 277.2 535.615 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
824.154 87.9606 o
797.923 124.568 o
771.692 157.108 o
745.462 176.09 o
719.231 201.851 o
693 209.986 o
666.769 204.562 o
640.538 197.783 o
614.308 185.581 o
588.077 174.734 o
561.846 170.666 o
grestore
1.500 setlinewidth
[5.55 2.4] 0 setdash
0.937 0.424 0.000 setrgbcolor
gsave
314.769 277.2 535.615 66.606 clipbox
824.153846 322.519676 m
797.923077 299.47052 l
771.692308 280.488862 l
745.461538 260.151371 l
719.230769 238.458048 l
693 209.98556 l
666.769231 222.188055 l
640.538462 227.611386 l
614.307692 237.102215 l
588.076923 233.034717 l
561.846154 247.948877 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
314.769 277.2 535.615 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-8 -8 m
8 -8 l
8 8 l
-8 8 l
cl

gsave
grestore
stroke
grestore
} bind def
824.154 322.52 o
797.923 299.471 o
771.692 280.489 o
745.462 260.151 o
719.231 238.458 o
693 209.986 o
666.769 222.188 o
640.538 227.611 o
614.308 237.102 o
588.077 233.035 o
561.846 247.949 o
grestore
2.500 setlinewidth
2 setlinecap
0.000 setgray
gsave
535.615385 66.60625 m
535.615385 343.80625 l
stroke
grestore
gsave
850.384615 66.60625 m
850.384615 343.80625 l
stroke
grestore
gsave
535.615385 66.60625 m
850.384615 66.60625 l
stroke
grestore
gsave
535.615385 343.80625 m
850.384615 343.80625 l
stroke
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

679.885 361.495 translate
0 rotate
0 0 m /parenleft glyphshow
8.3252 0 m /e glyphshow
19.4214 0 m /parenright glyphshow
grestore
1.500 setlinewidth
1 setlinejoin
0 setlinecap
[9.6 2.4 1.5 2.4] 0 setdash
0.749 0.212 0.047 setrgbcolor
gsave
553.615385 318.80625 m
573.615385 318.80625 l
593.615385 318.80625 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
573.615 318.806 o
grestore
0.000 setgray
gsave
609.615 311.806 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.75 moveto
/T glyphshow
/TimesNewRomanPSMT 14.0 selectfont
12.5174 -4.4025 moveto
/C glyphshow
/TimesNewRomanPSMT 9.799999999999999 selectfont
22.0656 -8.00925 moveto
/two glyphshow
grestore
1.500 setlinewidth
[5.55 2.4] 0 setdash
0.937 0.424 0.000 setrgbcolor
gsave
553.615385 285.80625 m
573.615385 285.80625 l
593.615385 285.80625 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-8 -8 m
8 -8 l
8 8 l
-8 8 l
cl

gsave
grestore
stroke
grestore
} bind def
573.615 285.806 o
grestore
0.000 setgray
gsave
609.615 278.806 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.75 moveto
/T glyphshow
/TimesNewRomanPSMT 14.0 selectfont
12.5174 -4.4025 moveto
/C glyphshow
/TimesNewRomanPSMT 9.799999999999999 selectfont
22.0656 -8.00925 moveto
/one glyphshow
grestore
gsave
992.030769 66.60625 m
1306.8 66.60625 l
1306.8 343.80625 l
992.030769 343.80625 l
cl
1.000 setgray
fill
grestore
2.500 setlinewidth
1 setlinejoin
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1005.59 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1005.59 343.806 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

992.289 39.2469 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /five glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1065.83 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1065.83 343.806 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

1052.53 39.2469 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /three glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1126.07 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1126.07 343.806 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

1112.77 39.2469 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /one glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1186.31 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1186.31 343.806 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

1180.06 39.2469 translate
0 rotate
0 0 m /one glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1246.56 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1246.56 343.806 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

1240.31 39.2469 translate
0 rotate
0 0 m /three glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1306.8 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1306.8 343.806 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

1300.55 39.2469 translate
0 rotate
0 0 m /five glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1020.65 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1020.65 343.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1035.71 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1035.71 343.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1050.77 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1050.77 343.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1080.89 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1080.89 343.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1095.95 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1095.95 343.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1111.01 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1111.01 343.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1141.13 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1141.13 343.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1156.19 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1156.19 343.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1171.25 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1171.25 343.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1201.37 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1201.37 343.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1216.44 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1216.44 343.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1231.5 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1231.5 343.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1261.62 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1261.62 343.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1276.68 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1276.68 343.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1291.74 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1291.74 343.806 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

1047.32 12.5438 translate
0 rotate
0 0 m /B glyphshow
16.6748 0 m /i glyphshow
23.6206 0 m /a glyphshow
34.7168 0 m /x glyphshow
47.2168 0 m /i glyphshow
54.1626 0 m /a glyphshow
65.2588 0 m /l glyphshow
72.2046 0 m /space glyphshow
78.4546 0 m /S glyphshow
92.3584 0 m /t glyphshow
99.3042 0 m /r glyphshow
107.629 0 m /e glyphshow
118.726 0 m /s glyphshow
128.455 0 m /s glyphshow
138.184 0 m /space glyphshow
144.434 0 m /parenleft glyphshow
152.759 0 m /G glyphshow
170.813 0 m /P glyphshow
184.717 0 m /a glyphshow
195.813 0 m /parenright glyphshow
grestore
2.500 setlinewidth
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
992.031 70.0188 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1306.8 70.0188 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

950.781 61.3391 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /four glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
992.031 131.706 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1306.8 131.706 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

950.781 123.027 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /six glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
992.031 193.394 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1306.8 193.394 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

950.781 184.714 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /eight glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
992.031 255.081 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1306.8 255.081 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

950.781 246.402 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
992.031 316.769 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1306.8 316.769 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

950.781 308.089 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /period glyphshow
18.75 0 m /two glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
992.031 85.4406 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1306.8 85.4406 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
992.031 100.863 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1306.8 100.863 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
992.031 116.284 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1306.8 116.284 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
992.031 147.128 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1306.8 147.128 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
992.031 162.55 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1306.8 162.55 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
992.031 177.972 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1306.8 177.972 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
992.031 208.816 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1306.8 208.816 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
992.031 224.237 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1306.8 224.237 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
992.031 239.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1306.8 239.659 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
992.031 270.503 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1306.8 270.503 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
992.031 285.925 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1306.8 285.925 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
992.031 301.347 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1306.8 301.347 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
992.031 332.191 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1306.8 332.191 o
grestore
gsave
939.781 158.706 translate
90 rotate
/TimesNewRomanPSMT 25.0 selectfont
0 0.640625 moveto
/T glyphshow
/TimesNewRomanPSMT 17.5 selectfont
15.6467 -5.8 moveto
/C glyphshow
/TimesNewRomanPSMT 25.0 selectfont
28.3925 0.640625 moveto
/slash glyphshow
35.3383 0.640625 moveto
/T glyphshow
/TimesNewRomanPSMT 17.5 selectfont
50.985 -5.8 moveto
/C glyphshow
/TimesNewRomanPSMT 25.0 selectfont
63.7308 0.640625 moveto
/parenleft glyphshow
72.056 0.640625 moveto
/zero glyphshow
84.556 0.640625 moveto
/parenright glyphshow
grestore
[9.6 2.4 1.5 2.4] 0 setdash
0.749 0.212 0.047 setrgbcolor
gsave
314.769 277.2 992.031 66.606 clipbox
1276.678543 274.76875 m
1246.557085 264.26875 l
1216.435628 261.64375 l
1186.31417 256.39375 l
1156.192713 255.08125 l
1126.071255 238.01875 l
1095.949798 201.26875 l
1065.82834 157.95625 l
1035.706883 122.51875 l
1005.585425 79.20625 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
314.769 277.2 992.031 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
1276.68 274.769 o
1246.56 264.269 o
1216.44 261.644 o
1186.31 256.394 o
1156.19 255.081 o
1126.07 238.019 o
1095.95 201.269 o
1065.83 157.956 o
1035.71 122.519 o
1005.59 79.2063 o
grestore
1.500 setlinewidth
[5.55 2.4] 0 setdash
0.937 0.424 0.000 setrgbcolor
gsave
314.769 277.2 992.031 66.606 clipbox
1276.678543 320.70625 m
1246.557085 303.64375 l
1216.435628 289.20625 l
1186.31417 278.70625 l
1156.192713 255.08125 l
1126.071255 287.89375 l
1095.949798 302.33125 l
1065.82834 303.64375 l
1035.706883 320.70625 l
1005.585425 331.20625 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
314.769 277.2 992.031 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-8 -8 m
8 -8 l
8 8 l
-8 8 l
cl

gsave
grestore
stroke
grestore
} bind def
1276.68 320.706 o
1246.56 303.644 o
1216.44 289.206 o
1186.31 278.706 o
1156.19 255.081 o
1126.07 287.894 o
1095.95 302.331 o
1065.83 303.644 o
1035.71 320.706 o
1005.59 331.206 o
grestore
2.500 setlinewidth
2 setlinecap
0.000 setgray
gsave
992.030769 66.60625 m
992.030769 343.80625 l
stroke
grestore
gsave
1306.8 66.60625 m
1306.8 343.80625 l
stroke
grestore
gsave
992.030769 66.60625 m
1306.8 66.60625 l
stroke
grestore
gsave
992.030769 343.80625 m
1306.8 343.80625 l
stroke
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

1141.13 361.097 translate
0 rotate
0 0 m /parenleft glyphshow
8.3252 0 m /f glyphshow
16.6504 0 m /parenright glyphshow
grestore
1.500 setlinewidth
1 setlinejoin
0 setlinecap
[9.6 2.4 1.5 2.4] 0 setdash
0.749 0.212 0.047 setrgbcolor
gsave
1203.8 133.60625 m
1223.8 133.60625 l
1243.8 133.60625 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
1223.8 133.606 o
grestore
0.000 setgray
gsave
1259.8 126.606 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.75 moveto
/T glyphshow
/TimesNewRomanPSMT 14.0 selectfont
12.5174 -4.4025 moveto
/C glyphshow
/TimesNewRomanPSMT 9.799999999999999 selectfont
22.0656 -8.00925 moveto
/two glyphshow
grestore
1.500 setlinewidth
[5.55 2.4] 0 setdash
0.937 0.424 0.000 setrgbcolor
gsave
1203.8 100.60625 m
1223.8 100.60625 l
1243.8 100.60625 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-8 -8 m
8 -8 l
8 8 l
-8 8 l
cl

gsave
grestore
stroke
grestore
} bind def
1223.8 100.606 o
grestore
0.000 setgray
gsave
1259.8 93.6063 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.75 moveto
/T glyphshow
/TimesNewRomanPSMT 14.0 selectfont
12.5174 -4.4025 moveto
/C glyphshow
/TimesNewRomanPSMT 9.799999999999999 selectfont
22.0656 -8.00925 moveto
/one glyphshow
grestore

end
showpage
