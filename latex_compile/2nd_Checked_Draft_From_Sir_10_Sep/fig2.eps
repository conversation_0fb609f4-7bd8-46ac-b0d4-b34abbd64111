%!PS-Adobe-3.0 EPSF-3.0
%%Title: Fig2_with_exp.eps
%%Creator: Mat<PERSON>lotlib v3.7.1, https://matplotlib.org/
%%CreationDate: Thu Oct 17 12:54:29 2024
%%Orientation: portrait
%%BoundingBox: -203 -17 816 810
%%HiResBoundingBox: -203.475000 -17.442812 815.475000 809.442812
%%EndComments
%%BeginProlog
/mpldict 11 dict def
mpldict begin
/_d { bind def } bind def
/m { moveto } _d
/l { lineto } _d
/r { rlineto } _d
/c { curveto } _d
/cl { closepath } _d
/ce { closepath eofill } _d
/box {
      m
      1 index 0 r
      0 exch r
      neg 0 r
      cl
    } _d
/clipbox {
      box
      clip
      newpath
    } _d
/sc { setcachedevice } _d
%!PS-TrueTypeFont-1.0-2.53740
%%Title: unknown
%%Creator: Converted from TrueType to type 42 by PPR
15 dict begin
/FontName /TimesNewRomanPSMT def
/PaintType 0 def
/FontMatrix[1 0 0 1 0 0]def
/FontBBox[-568 -307 2028 1007]def
/FontType 42 def
/Encoding StandardEncoding def
/FontInfo 10 dict dup begin
/FamilyName (unknown) def
/FullName (unknown) def
/Weight (unknown) def
/Version (unknown) def
/ItalicAngle 0.0 def
/isFixedPitch false def
/UnderlinePosition -223 def
/UnderlineThickness 100 def
end readonly def
/sfnts[<00010000000900800003001063767420F34DDA810000009C000007C46670676D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>]def
/CharStrings 49 dict dup begin
/.notdef 0 def
/Gamma 48 def
/space 3 def
/parenleft 4 def
/parenright 5 def
/comma 6 def
/period 7 def
/slash 8 def
/zero 9 def
/one 10 def
/two 11 def
/three 12 def
/four 13 def
/five 14 def
/six 15 def
/seven 16 def
/eight 17 def
/nine 18 def
/less 19 def
/mu 49 def
/greater 20 def
/C 21 def
/omega 50 def
/K 22 def
/L 23 def
/P 24 def
/R 25 def
/T 26 def
/bracketleft 27 def
/bracketright 28 def
/a 29 def
/b 30 def
/c 31 def
/d 32 def
/e 33 def
/f 34 def
/i 35 def
/l 36 def
/m 37 def
/n 38 def
/o 39 def
/p 40 def
/r 41 def
/s 42 def
/t 43 def
/u 44 def
/x 45 def
/y 46 def
/z 47 def
end readonly def

systemdict/resourcestatus known
 {42 /FontType resourcestatus
   {pop pop false}{true}ifelse}
 {true}ifelse
{/TrueDict where{pop}{(%%[ Error: no TrueType rasterizer ]%%)= flush}ifelse
/FontType 3 def
 /TrueState 271 string def
 TrueDict begin sfnts save
 72 0 matrix defaultmatrix dtransform dup
 mul exch dup mul add sqrt cvi 0 72 matrix
 defaultmatrix dtransform dup mul exch dup
 mul add sqrt cvi 3 -1 roll restore
 TrueState initer end
 /BuildGlyph{exch begin
  CharStrings dup 2 index known
    {exch}{exch pop /.notdef}ifelse
  get dup xcheck
    {currentdict systemdict begin begin exec end end}
    {TrueDict begin /bander load cvlit exch TrueState render end}
    ifelse
 end}bind def
 /BuildChar{
  1 index /Encoding get exch get
  1 index /BuildGlyph get exec
 }bind def
}if

FontName currentdict end definefont pop
end
%%EndProlog
mpldict begin
-203.475 -17.443 translate
1018.95 826.886 0 0 clipbox
gsave
0 0 m
1018.95 0 l
1018.95 826.885625 l
0 826.885625 l
cl
1.000 setgray
fill
grestore
gsave
93.95 493.154821 m
465.95 493.154821 l
465.95 787.32625 l
93.95 787.32625 l
cl
1.000 setgray
fill
grestore
2.500 setlinewidth
1 setlinejoin
0 setlinecap
[] 0 setdash
0.000 setgray
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
105.95 493.155 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
105.95 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

99.7 465.795 translate
0 rotate
0 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
177.95 493.155 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
177.95 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

159.2 465.795 translate
0 rotate
0 0 m /three glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
249.95 493.155 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
249.95 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

231.2 465.795 translate
0 rotate
0 0 m /six glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
321.95 493.155 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
321.95 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

303.2 465.795 translate
0 rotate
0 0 m /nine glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.95 493.155 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.95 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

368.95 465.795 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /two glyphshow
25 0 m /zero glyphshow
37.5 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.95 493.155 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.95 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

440.95 465.795 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /five glyphshow
25 0 m /zero glyphshow
37.5 0 m /zero glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
123.95 493.155 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
123.95 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
141.95 493.155 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
141.95 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
159.95 493.155 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
159.95 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
195.95 493.155 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
195.95 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
213.95 493.155 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
213.95 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
231.95 493.155 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
231.95 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
267.95 493.155 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
267.95 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
285.95 493.155 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
285.95 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
303.95 493.155 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
303.95 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
339.95 493.155 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
339.95 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
357.95 493.155 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
357.95 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
375.95 493.155 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
375.95 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
411.95 493.155 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
411.95 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
429.95 493.155 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
429.95 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
447.95 493.155 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
447.95 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

196.489 439.092 translate
0 rotate
0 0 m /T glyphshow
13.521 0 m /e glyphshow
24.6172 0 m /m glyphshow
44.063 0 m /p glyphshow
56.563 0 m /e glyphshow
67.6592 0 m /r glyphshow
75.9844 0 m /a glyphshow
87.0806 0 m /t glyphshow
94.0264 0 m /u glyphshow
106.526 0 m /r glyphshow
114.852 0 m /e glyphshow
125.948 0 m /space glyphshow
132.198 0 m /parenleft glyphshow
140.523 0 m /K glyphshow
158.577 0 m /parenright glyphshow
grestore
2.500 setlinewidth
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 506.345 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.95 506.345 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

40.2 497.666 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 614.119 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.95 614.119 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

40.2 605.439 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /two glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 721.892 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.95 721.892 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

40.2 713.212 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /four glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 533.289 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.95 533.289 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 560.232 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.95 560.232 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 587.175 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.95 587.175 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 641.062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.95 641.062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 668.005 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.95 668.005 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 694.949 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.95 694.949 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 748.835 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.95 748.835 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 775.779 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.95 775.779 o
grestore
gsave
25.2 583.741 translate
90 rotate
/TimesNewRomanPSMT 25.0 selectfont
0 0.640625 moveto
/less glyphshow
14.0991 0.640625 moveto
/u glyphshow
/TimesNewRomanPSMT 17.5 selectfont
26.9748 -5.8 moveto
/x glyphshow
35.7248 -5.8 moveto
/comma glyphshow
42.7458 -5.8 moveto
/y glyphshow
51.4958 -5.8 moveto
/comma glyphshow
58.5168 -5.8 moveto
/z glyphshow
/TimesNewRomanPSMT 25.0 selectfont
67.3576 0.640625 moveto
/parenleft glyphshow
75.6828 0.640625 moveto
/Gamma glyphshow
90.1359 0.640625 moveto
/parenright glyphshow
98.4611 0.640625 moveto
/greater glyphshow
grestore
0.050 setlinewidth
[0.185 0.08] 0 setdash
0.722 0.224 0.055 setrgbcolor
gsave
372 294.171 93.95 493.155 clipbox
461.15 509.466794 m
456.35 515.090195 l
451.55 506.692814 l
446.75 507.75546 l
441.95 516.601394 l
437.15 519.270726 l
432.35 514.079927 l
427.55 526.531313 l
422.75 511.534049 l
417.95 520.386234 l
413.15 518.960661 l
408.35 512.157303 l
403.55 548.149205 l
398.75 549.443833 l
393.95 508.982244 l
389.15 517.519677 l
384.35 683.066215 l
379.55 691.028999 l
374.75 698.133207 l
369.95 702.090647 l
365.15 705.592259 l
360.35 707.392937 l
355.55 711.294874 l
350.75 714.62782 l
345.95 716.195008 l
341.15 718.69077 l
336.35 722.06688 l
331.55 723.481837 l
326.75 725.173665 l
321.95 727.611553 l
317.15 729.324773 l
312.35 730.86728 l
307.55 732.636327 l
302.75 733.967706 l
297.95 735.821085 l
293.15 736.473438 l
288.35 738.354515 l
283.55 739.690851 l
278.75 740.831956 l
273.95 742.44457 l
269.15 743.607229 l
264.35 744.418278 l
259.55 745.86352 l
254.75 747.360816 l
249.95 748.547778 l
245.15 749.692871 l
240.35 750.779765 l
235.55 751.835137 l
230.75 752.88291 l
225.95 753.871138 l
221.15 754.648023 l
216.35 755.898841 l
211.55 756.714686 l
206.75 757.63173 l
201.95 758.526734 l
197.15 759.590943 l
192.35 760.38243 l
187.55 761.259167 l
182.75 762.271052 l
177.95 763.114055 l
173.15 763.86723 l
168.35 764.726346 l
163.55 765.569295 l
158.75 766.456594 l
153.95 767.355478 l
149.15 767.966931 l
144.35 768.681684 l
139.55 769.626479 l
134.75 770.38612 l
129.95 771.022361 l
125.15 771.735174 l
120.35 772.523429 l
115.55 773.209838 l
110.75 773.918232 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
372 294.171 93.95 493.155 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

0 2 m
-2 -2 l
2 -2 l
cl

gsave
0.722 0.224 0.055 setrgbcolor
fill
grestore
stroke
grestore
} bind def
461.15 509.467 o
456.35 515.09 o
451.55 506.693 o
446.75 507.755 o
441.95 516.601 o
437.15 519.271 o
432.35 514.08 o
427.55 526.531 o
422.75 511.534 o
417.95 520.386 o
413.15 518.961 o
408.35 512.157 o
403.55 548.149 o
398.75 549.444 o
393.95 508.982 o
389.15 517.52 o
384.35 683.066 o
379.55 691.029 o
374.75 698.133 o
369.95 702.091 o
365.15 705.592 o
360.35 707.393 o
355.55 711.295 o
350.75 714.628 o
345.95 716.195 o
341.15 718.691 o
336.35 722.067 o
331.55 723.482 o
326.75 725.174 o
321.95 727.612 o
317.15 729.325 o
312.35 730.867 o
307.55 732.636 o
302.75 733.968 o
297.95 735.821 o
293.15 736.473 o
288.35 738.355 o
283.55 739.691 o
278.75 740.832 o
273.95 742.445 o
269.15 743.607 o
264.35 744.418 o
259.55 745.864 o
254.75 747.361 o
249.95 748.548 o
245.15 749.693 o
240.35 750.78 o
235.55 751.835 o
230.75 752.883 o
225.95 753.871 o
221.15 754.648 o
216.35 755.899 o
211.55 756.715 o
206.75 757.632 o
201.95 758.527 o
197.15 759.591 o
192.35 760.382 o
187.55 761.259 o
182.75 762.271 o
177.95 763.114 o
173.15 763.867 o
168.35 764.726 o
163.55 765.569 o
158.75 766.457 o
153.95 767.355 o
149.15 767.967 o
144.35 768.682 o
139.55 769.626 o
134.75 770.386 o
129.95 771.022 o
125.15 771.735 o
120.35 772.523 o
115.55 773.21 o
110.75 773.918 o
grestore
0.050 setlinewidth
1 setlinejoin
[0.185 0.08] 0 setdash
1.000 0.271 0.000 setrgbcolor
gsave
372 294.171 93.95 493.155 clipbox
461.15 507.919167 m
456.35 508.533314 l
451.55 509.433923 l
446.75 516.474383 l
441.95 507.168849 l
437.15 508.0853 l
432.35 507.763704 l
427.55 508.050436 l
422.75 517.438739 l
417.95 507.267623 l
413.15 512.693637 l
408.35 532.466233 l
403.55 522.815663 l
398.75 527.365102 l
393.95 530.125017 l
389.15 552.580686 l
384.35 684.240729 l
379.55 691.949277 l
374.75 697.803259 l
369.95 701.725295 l
365.15 705.66894 l
360.35 708.656149 l
355.55 711.398713 l
350.75 713.626444 l
345.95 716.946565 l
341.15 719.235565 l
336.35 721.278895 l
331.55 723.799554 l
326.75 725.480118 l
321.95 727.042402 l
317.15 729.319384 l
312.35 731.13472 l
307.55 732.018623 l
302.75 733.511662 l
297.95 735.567548 l
293.15 736.958202 l
288.35 738.264793 l
283.55 740.034594 l
278.75 741.05952 l
273.95 742.371984 l
269.15 743.734186 l
264.35 744.894852 l
259.55 746.389077 l
254.75 747.287045 l
249.95 748.356319 l
245.15 749.609454 l
240.35 750.895245 l
235.55 751.587473 l
230.75 753.006741 l
225.95 753.743103 l
221.15 754.972151 l
216.35 755.823238 l
211.55 756.678528 l
206.75 757.777439 l
201.95 758.743466 l
197.15 759.575639 l
192.35 760.440413 l
187.55 761.402721 l
182.75 762.214686 l
177.95 763.115295 l
173.15 763.940624 l
168.35 764.709317 l
163.55 765.640641 l
158.75 766.421352 l
153.95 767.324817 l
149.15 768.049377 l
144.35 768.881442 l
139.55 769.502055 l
134.75 770.249033 l
129.95 770.993585 l
125.15 771.785989 l
120.35 772.524938 l
115.55 773.225357 l
110.75 773.954821 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
372 294.171 93.95 493.155 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-4 -4 m
4 -4 l
4 4 l
-4 4 l
cl

gsave
grestore
stroke
grestore
} bind def
461.15 507.919 o
456.35 508.533 o
451.55 509.434 o
446.75 516.474 o
441.95 507.169 o
437.15 508.085 o
432.35 507.764 o
427.55 508.05 o
422.75 517.439 o
417.95 507.268 o
413.15 512.694 o
408.35 532.466 o
403.55 522.816 o
398.75 527.365 o
393.95 530.125 o
389.15 552.581 o
384.35 684.241 o
379.55 691.949 o
374.75 697.803 o
369.95 701.725 o
365.15 705.669 o
360.35 708.656 o
355.55 711.399 o
350.75 713.626 o
345.95 716.947 o
341.15 719.236 o
336.35 721.279 o
331.55 723.8 o
326.75 725.48 o
321.95 727.042 o
317.15 729.319 o
312.35 731.135 o
307.55 732.019 o
302.75 733.512 o
297.95 735.568 o
293.15 736.958 o
288.35 738.265 o
283.55 740.035 o
278.75 741.06 o
273.95 742.372 o
269.15 743.734 o
264.35 744.895 o
259.55 746.389 o
254.75 747.287 o
249.95 748.356 o
245.15 749.609 o
240.35 750.895 o
235.55 751.587 o
230.75 753.007 o
225.95 753.743 o
221.15 754.972 o
216.35 755.823 o
211.55 756.679 o
206.75 757.777 o
201.95 758.743 o
197.15 759.576 o
192.35 760.44 o
187.55 761.403 o
182.75 762.215 o
177.95 763.115 o
173.15 763.941 o
168.35 764.709 o
163.55 765.641 o
158.75 766.421 o
153.95 767.325 o
149.15 768.049 o
144.35 768.881 o
139.55 769.502 o
134.75 770.249 o
129.95 770.994 o
125.15 771.786 o
120.35 772.525 o
115.55 773.225 o
110.75 773.955 o
grestore
0.050 setlinewidth
1 setlinejoin
[0.185 0.08] 0 setdash
1.000 0.514 0.000 setrgbcolor
gsave
372 294.171 93.95 493.155 clipbox
461.15 506.52625 m
456.35 512.06839 l
451.55 509.641764 l
446.75 515.187568 l
441.95 508.277352 l
437.15 506.627018 l
432.35 511.35951 l
427.55 518.651945 l
422.75 507.898259 l
417.95 519.170119 l
413.15 508.668732 l
408.35 535.641022 l
403.55 508.123452 l
398.75 589.347801 l
393.95 600.779974 l
389.15 590.921078 l
384.35 682.797051 l
379.55 691.802812 l
374.75 696.821335 l
369.95 701.732785 l
365.15 705.413247 l
360.35 708.83667 l
355.55 711.904763 l
350.75 714.435768 l
345.95 717.059404 l
341.15 718.654127 l
336.35 721.208573 l
331.55 723.525162 l
326.75 725.400581 l
321.95 727.184501 l
317.15 728.955757 l
312.35 730.816788 l
307.55 732.362906 l
302.75 733.706625 l
297.95 735.552029 l
293.15 737.179299 l
288.35 738.167636 l
283.55 739.58755 l
278.75 741.016033 l
273.95 742.475501 l
269.15 743.507323 l
264.35 744.722037 l
259.55 746.258725 l
254.75 747.250671 l
249.95 748.373131 l
245.15 749.499687 l
240.35 750.740698 l
235.55 751.587635 l
230.75 752.682613 l
225.95 753.834549 l
221.15 754.772663 l
216.35 755.693048 l
211.55 756.800096 l
206.75 757.622946 l
201.95 758.636501 l
197.15 759.590727 l
192.35 760.560634 l
187.55 761.327334 l
182.75 762.280105 l
177.95 763.270596 l
173.15 763.982062 l
168.35 764.89625 l
163.55 765.612243 l
158.75 766.446733 l
153.95 767.268828 l
149.15 768.030194 l
144.35 768.815862 l
139.55 769.639305 l
134.75 770.364566 l
129.95 771.042245 l
125.15 771.823279 l
120.35 772.566538 l
115.55 773.221154 l
110.75 773.950995 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
372 294.171 93.95 493.155 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
461.15 506.526 o
456.35 512.068 o
451.55 509.642 o
446.75 515.188 o
441.95 508.277 o
437.15 506.627 o
432.35 511.36 o
427.55 518.652 o
422.75 507.898 o
417.95 519.17 o
413.15 508.669 o
408.35 535.641 o
403.55 508.123 o
398.75 589.348 o
393.95 600.78 o
389.15 590.921 o
384.35 682.797 o
379.55 691.803 o
374.75 696.821 o
369.95 701.733 o
365.15 705.413 o
360.35 708.837 o
355.55 711.905 o
350.75 714.436 o
345.95 717.059 o
341.15 718.654 o
336.35 721.209 o
331.55 723.525 o
326.75 725.401 o
321.95 727.185 o
317.15 728.956 o
312.35 730.817 o
307.55 732.363 o
302.75 733.707 o
297.95 735.552 o
293.15 737.179 o
288.35 738.168 o
283.55 739.588 o
278.75 741.016 o
273.95 742.476 o
269.15 743.507 o
264.35 744.722 o
259.55 746.259 o
254.75 747.251 o
249.95 748.373 o
245.15 749.5 o
240.35 750.741 o
235.55 751.588 o
230.75 752.683 o
225.95 753.835 o
221.15 754.773 o
216.35 755.693 o
211.55 756.8 o
206.75 757.623 o
201.95 758.637 o
197.15 759.591 o
192.35 760.561 o
187.55 761.327 o
182.75 762.28 o
177.95 763.271 o
173.15 763.982 o
168.35 764.896 o
163.55 765.612 o
158.75 766.447 o
153.95 767.269 o
149.15 768.03 o
144.35 768.816 o
139.55 769.639 o
134.75 770.365 o
129.95 771.042 o
125.15 771.823 o
120.35 772.567 o
115.55 773.221 o
110.75 773.951 o
grestore
2.500 setlinewidth
0 setlinejoin
2 setlinecap
0.000 setgray
gsave
93.95 493.154821 m
93.95 787.32625 l
stroke
grestore
gsave
465.95 493.154821 m
465.95 787.32625 l
stroke
grestore
gsave
93.95 493.154821 m
465.95 493.154821 l
stroke
grestore
gsave
93.95 787.32625 m
465.95 787.32625 l
stroke
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

266.075 802.326 translate
0 rotate
0 0 m /parenleft glyphshow
8.3252 0 m /a glyphshow
19.4214 0 m /parenright glyphshow
grestore
0.050 setlinewidth
1 setlinejoin
0 setlinecap
[0.185 0.08] 0 setdash
0.722 0.224 0.055 setrgbcolor
gsave
111.95 587.154821 m
131.95 587.154821 l
151.95 587.154821 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

0 2 m
-2 -2 l
2 -2 l
cl

gsave
0.722 0.224 0.055 setrgbcolor
fill
grestore
stroke
grestore
} bind def
131.95 587.155 o
grestore
0.000 setgray
gsave
167.95 580.155 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.546875 moveto
/less glyphshow
11.2793 0.546875 moveto
/u glyphshow
/TimesNewRomanPSMT 14.0 selectfont
21.5799 -4.60562 moveto
/x glyphshow
/TimesNewRomanPSMT 20.0 selectfont
29.4386 0.546875 moveto
/greater glyphshow
grestore
0.050 setlinewidth
1 setlinejoin
[0.185 0.08] 0 setdash
1.000 0.271 0.000 setrgbcolor
gsave
111.95 557.154821 m
131.95 557.154821 l
151.95 557.154821 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-4 -4 m
4 -4 l
4 4 l
-4 4 l
cl

gsave
grestore
stroke
grestore
} bind def
131.95 557.155 o
grestore
0.000 setgray
gsave
167.95 550.155 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.546875 moveto
/less glyphshow
11.2793 0.546875 moveto
/u glyphshow
/TimesNewRomanPSMT 14.0 selectfont
21.5799 -4.60562 moveto
/y glyphshow
/TimesNewRomanPSMT 20.0 selectfont
29.4386 0.546875 moveto
/greater glyphshow
grestore
0.050 setlinewidth
1 setlinejoin
[0.185 0.08] 0 setdash
1.000 0.514 0.000 setrgbcolor
gsave
111.95 524.154821 m
131.95 524.154821 l
151.95 524.154821 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
131.95 524.155 o
grestore
0.000 setgray
gsave
167.95 517.155 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.546875 moveto
/less glyphshow
11.2793 0.546875 moveto
/u glyphshow
/TimesNewRomanPSMT 14.0 selectfont
21.5799 -4.60562 moveto
/z glyphshow
/TimesNewRomanPSMT 20.0 selectfont
28.6525 0.546875 moveto
/greater glyphshow
grestore
gsave
614.75 493.154821 m
986.75 493.154821 l
986.75 787.32625 l
614.75 787.32625 l
cl
1.000 setgray
fill
grestore
0.031 0.659 0.659 setrgbcolor
gsave
372 294.171 614.75 493.155 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

-0 -12.247449 m
7.348469 0 l
0 12.247449 l
-7.348469 0 l
cl

gsave
0.031 0.659 0.659 setrgbcolor
fill
grestore
stroke
grestore
} bind def
668.27 512.378 o
698.27 559.608 o
722.27 629.892 o
746.27 656.318 o
grestore
0.031 0.416 0.416 setrgbcolor
gsave
372 294.171 614.75 493.155 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

8.660254 -0 m
-8.660254 8.660254 l
-8.660254 -8.660254 l
cl

gsave
0.031 0.416 0.416 setrgbcolor
fill
grestore
stroke
grestore
} bind def
698.75 669.25 o
grestore
0.031 0.235 0.235 setrgbcolor
gsave
372 294.171 614.75 493.155 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 8.660254 m
-1.944348 2.676166 l
-8.236391 2.676166 l
-3.146021 -1.022204 l
-5.09037 -7.006293 l
-0 -3.307923 l
5.09037 -7.006293 l
3.146021 -1.022204 l
8.236391 2.676166 l
1.944348 2.676166 l
cl

gsave
0.031 0.235 0.235 setrgbcolor
fill
grestore
stroke
grestore
} bind def
698.75 570.854 o
grestore
2.500 setlinewidth
0.000 setgray
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
626.75 493.155 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
626.75 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

620.5 465.795 translate
0 rotate
0 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
698.75 493.155 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
698.75 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

680 465.795 translate
0 rotate
0 0 m /three glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
770.75 493.155 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
770.75 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

752 465.795 translate
0 rotate
0 0 m /six glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
842.75 493.155 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
842.75 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

824 465.795 translate
0 rotate
0 0 m /nine glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
914.75 493.155 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
914.75 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

889.75 465.795 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /two glyphshow
25 0 m /zero glyphshow
37.5 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 493.155 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

961.75 465.795 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /five glyphshow
25 0 m /zero glyphshow
37.5 0 m /zero glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
644.75 493.155 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
644.75 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
662.75 493.155 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
662.75 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
680.75 493.155 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
680.75 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
716.75 493.155 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
716.75 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
734.75 493.155 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
734.75 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
752.75 493.155 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
752.75 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
788.75 493.155 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
788.75 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
806.75 493.155 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
806.75 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
824.75 493.155 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
824.75 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
860.75 493.155 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
860.75 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
878.75 493.155 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
878.75 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
896.75 493.155 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
896.75 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
932.75 493.155 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
932.75 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
950.75 493.155 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
950.75 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
968.75 493.155 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
968.75 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

717.289 439.092 translate
0 rotate
0 0 m /T glyphshow
13.521 0 m /e glyphshow
24.6172 0 m /m glyphshow
44.063 0 m /p glyphshow
56.563 0 m /e glyphshow
67.6592 0 m /r glyphshow
75.9844 0 m /a glyphshow
87.0806 0 m /t glyphshow
94.0264 0 m /u glyphshow
106.526 0 m /r glyphshow
114.852 0 m /e glyphshow
125.948 0 m /space glyphshow
132.198 0 m /parenleft glyphshow
140.523 0 m /K glyphshow
158.577 0 m /parenright glyphshow
grestore
2.500 setlinewidth
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.75 506.193 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 506.193 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

592.25 497.513 translate
0 rotate
0 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.75 562.42 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 562.42 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

579.75 553.74 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.75 618.646 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 618.646 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

579.75 609.967 translate
0 rotate
0 0 m /two glyphshow
12.5 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.75 674.873 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 674.873 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

579.75 666.193 translate
0 rotate
0 0 m /three glyphshow
12.5 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.75 731.1 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 731.1 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

579.75 722.42 translate
0 rotate
0 0 m /four glyphshow
12.5 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.75 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

579.75 778.647 translate
0 rotate
0 0 m /five glyphshow
12.5 0 m /zero glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.75 494.947 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 494.947 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.75 517.438 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 517.438 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.75 528.683 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 528.683 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.75 539.929 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 539.929 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.75 551.174 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 551.174 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.75 573.665 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 573.665 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.75 584.91 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 584.91 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.75 596.156 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 596.156 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.75 607.401 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 607.401 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.75 629.892 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 629.892 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.75 641.137 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 641.137 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.75 652.382 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 652.382 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.75 663.628 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 663.628 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.75 686.118 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 686.118 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.75 697.364 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 697.364 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.75 708.609 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 708.609 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.75 719.854 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 719.854 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.75 742.345 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 742.345 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.75 753.59 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 753.59 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.75 764.836 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 764.836 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.75 776.081 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 776.081 o
grestore
gsave
569.75 529.241 translate
90 rotate
/TimesNewRomanPSMT 25.0 selectfont
0 0.148438 moveto
/P glyphshow
13.9038 0.148438 moveto
/o glyphshow
26.4038 0.148438 moveto
/l glyphshow
33.3496 0.148438 moveto
/a glyphshow
44.4458 0.148438 moveto
/r glyphshow
52.771 0.148438 moveto
/i glyphshow
59.7168 0.148438 moveto
/z glyphshow
70.813 0.148438 moveto
/a glyphshow
81.9092 0.148438 moveto
/t glyphshow
88.855 0.148438 moveto
/i glyphshow
95.8008 0.148438 moveto
/o glyphshow
108.301 0.148438 moveto
/n glyphshow
120.801 0.148438 moveto
/space glyphshow
127.051 0.148438 moveto
/parenleft glyphshow
135.376 0.148438 moveto
/mu glyphshow
148.779 0.148438 moveto
/C glyphshow
165.454 0.148438 moveto
/slash glyphshow
172.4 0.148438 moveto
/c glyphshow
183.496 0.148438 moveto
/m glyphshow
/TimesNewRomanPSMT 17.5 selectfont
203.318 15.1766 moveto
/two glyphshow
/TimesNewRomanPSMT 25.0 selectfont
213.141 0.148438 moveto
/parenright glyphshow
grestore
0.050 setlinewidth
[0.185 0.08] 0 setdash
0.722 0.224 0.055 setrgbcolor
gsave
372 294.171 614.75 493.155 clipbox
981.95 509.38888 m
977.15 515.147397 l
972.35 506.54875 l
967.55 507.637014 l
962.75 516.697099 l
957.95 519.431688 l
953.15 514.115382 l
948.35 526.870533 l
943.55 511.508179 l
938.75 520.57692 l
933.95 519.116865 l
929.15 512.146506 l
924.35 549.01167 l
919.55 550.335881 l
914.75 508.893207 l
909.95 517.632176 l
905.15 685.713038 l
900.35 693.458129 l
895.55 700.402859 l
890.75 704.195729 l
885.95 707.542608 l
881.15 709.203635 l
876.35 712.961059 l
871.55 716.166537 l
866.75 717.604593 l
861.95 719.973815 l
857.15 723.230945 l
852.35 724.520398 l
847.55 726.104832 l
842.75 728.428345 l
837.95 730.037936 l
833.15 731.457212 l
828.35 733.120168 l
823.55 734.349378 l
818.75 736.094512 l
813.95 736.650026 l
809.15 738.424716 l
804.35 739.660252 l
799.55 740.706784 l
794.75 742.212471 l
789.95 743.286313 l
785.15 744.005153 l
780.35 745.341598 l
775.55 746.749932 l
770.75 747.846353 l
765.95 748.897499 l
761.15 749.889513 l
756.35 750.862878 l
751.55 751.814489 l
746.75 752.717688 l
741.95 753.406571 l
737.15 754.568822 l
732.35 755.298772 l
727.55 756.129341 l
722.75 756.937784 l
717.95 757.913329 l
713.15 758.62334 l
708.35 759.417857 l
703.55 760.346382 l
698.75 761.108682 l
693.95 761.777118 l
689.15 762.554271 l
684.35 763.313935 l
679.55 764.124613 l
674.75 764.939916 l
669.95 765.475943 l
665.15 766.114441 l
660.35 766.975568 l
655.55 767.658452 l
650.75 768.219323 l
645.95 768.854695 l
641.15 769.56478 l
636.35 770.176459 l
631.55 770.808395 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
372 294.171 614.75 493.155 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

0 2 m
-2 -2 l
2 -2 l
cl

gsave
0.722 0.224 0.055 setrgbcolor
fill
grestore
stroke
grestore
} bind def
981.95 509.389 o
977.15 515.147 o
972.35 506.549 o
967.55 507.637 o
962.75 516.697 o
957.95 519.432 o
953.15 514.115 o
948.35 526.871 o
943.55 511.508 o
938.75 520.577 o
933.95 519.117 o
929.15 512.147 o
924.35 549.012 o
919.55 550.336 o
914.75 508.893 o
909.95 517.632 o
905.15 685.713 o
900.35 693.458 o
895.55 700.403 o
890.75 704.196 o
885.95 707.543 o
881.15 709.204 o
876.35 712.961 o
871.55 716.167 o
866.75 717.605 o
861.95 719.974 o
857.15 723.231 o
852.35 724.52 o
847.55 726.105 o
842.75 728.428 o
837.95 730.038 o
833.15 731.457 o
828.35 733.12 o
823.55 734.349 o
818.75 736.095 o
813.95 736.65 o
809.15 738.425 o
804.35 739.66 o
799.55 740.707 o
794.75 742.212 o
789.95 743.286 o
785.15 744.005 o
780.35 745.342 o
775.55 746.75 o
770.75 747.846 o
765.95 748.897 o
761.15 749.89 o
756.35 750.863 o
751.55 751.814 o
746.75 752.718 o
741.95 753.407 o
737.15 754.569 o
732.35 755.299 o
727.55 756.129 o
722.75 756.938 o
717.95 757.913 o
713.15 758.623 o
708.35 759.418 o
703.55 760.346 o
698.75 761.109 o
693.95 761.777 o
689.15 762.554 o
684.35 763.314 o
679.55 764.125 o
674.75 764.94 o
669.95 765.476 o
665.15 766.114 o
660.35 766.976 o
655.55 767.658 o
650.75 768.219 o
645.95 768.855 o
641.15 769.565 o
636.35 770.176 o
631.55 770.808 o
grestore
0.050 setlinewidth
1 setlinejoin
[0.185 0.08] 0 setdash
1.000 0.271 0.000 setrgbcolor
gsave
372 294.171 614.75 493.155 clipbox
981.95 507.804311 m
977.15 508.433338 l
972.35 509.355818 l
967.55 516.566036 l
962.75 507.036347 l
957.95 507.975062 l
953.15 507.645733 l
948.35 507.939539 l
943.55 517.556886 l
938.75 507.137736 l
933.95 512.696521 l
929.15 532.950333 l
924.35 523.063094 l
919.55 527.722081 l
914.75 530.54422 l
909.95 553.524419 l
905.15 686.906157 l
900.35 694.39127 l
895.55 700.068744 l
890.75 703.826163 l
885.95 707.620098 l
881.15 710.479182 l
876.35 713.06582 l
871.55 715.157029 l
866.75 718.361745 l
861.95 720.522293 l
857.15 722.438151 l
852.35 724.839857 l
847.55 726.412804 l
842.75 727.856701 l
837.95 730.032526 l
833.15 731.725536 l
828.35 732.500728 l
823.55 733.892265 l
818.75 735.840505 l
813.95 737.135484 l
809.15 738.334909 l
804.35 740.004175 l
799.55 740.934374 l
794.75 742.13991 l
789.95 743.41318 l
785.15 744.481205 l
780.35 745.866344 l
775.55 746.676301 l
770.75 747.655327 l
765.95 748.814303 l
761.15 750.004643 l
756.35 750.616041 l
751.55 751.937861 l
746.75 752.590171 l
741.95 753.729278 l
737.15 754.493576 l
732.35 755.262796 l
727.55 756.274268 l
722.75 757.153282 l
717.95 757.898117 l
713.15 758.680955 l
708.35 759.56046 l
703.55 760.290406 l
698.75 761.109912 l
693.95 761.849959 l
689.15 762.537376 l
684.35 763.384702 l
679.55 764.089666 l
674.75 764.90952 l
669.95 765.557653 l
665.15 766.31236 l
660.35 766.852325 l
655.55 767.522701 l
650.75 768.190835 l
645.95 768.904988 l
641.15 769.566272 l
636.35 770.191811 l
631.55 770.84458 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
372 294.171 614.75 493.155 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-4 -4 m
4 -4 l
4 4 l
-4 4 l
cl

gsave
grestore
stroke
grestore
} bind def
981.95 507.804 o
977.15 508.433 o
972.35 509.356 o
967.55 516.566 o
962.75 507.036 o
957.95 507.975 o
953.15 507.646 o
948.35 507.94 o
943.55 517.557 o
938.75 507.138 o
933.95 512.697 o
929.15 532.95 o
924.35 523.063 o
919.55 527.722 o
914.75 530.544 o
909.95 553.524 o
905.15 686.906 o
900.35 694.391 o
895.55 700.069 o
890.75 703.826 o
885.95 707.62 o
881.15 710.479 o
876.35 713.066 o
871.55 715.157 o
866.75 718.362 o
861.95 720.522 o
857.15 722.438 o
852.35 724.84 o
847.55 726.413 o
842.75 727.857 o
837.95 730.033 o
833.15 731.726 o
828.35 732.501 o
823.55 733.892 o
818.75 735.841 o
813.95 737.135 o
809.15 738.335 o
804.35 740.004 o
799.55 740.934 o
794.75 742.14 o
789.95 743.413 o
785.15 744.481 o
780.35 745.866 o
775.55 746.676 o
770.75 747.655 o
765.95 748.814 o
761.15 750.005 o
756.35 750.616 o
751.55 751.938 o
746.75 752.59 o
741.95 753.729 o
737.15 754.494 o
732.35 755.263 o
727.55 756.274 o
722.75 757.153 o
717.95 757.898 o
713.15 758.681 o
708.35 759.56 o
703.55 760.29 o
698.75 761.11 o
693.95 761.85 o
689.15 762.537 o
684.35 763.385 o
679.55 764.09 o
674.75 764.91 o
669.95 765.558 o
665.15 766.312 o
660.35 766.852 o
655.55 767.523 o
650.75 768.191 o
645.95 768.905 o
641.15 769.566 o
636.35 770.192 o
631.55 770.845 o
grestore
0.050 setlinewidth
1 setlinejoin
[0.185 0.08] 0 setdash
1.000 0.514 0.000 setrgbcolor
gsave
372 294.171 614.75 493.155 clipbox
981.95 506.378143 m
977.15 512.053155 l
972.35 509.568661 l
967.55 515.248213 l
962.75 508.171669 l
957.95 506.481422 l
953.15 511.328884 l
948.35 518.799257 l
943.55 507.783711 l
938.75 519.331087 l
933.95 508.573147 l
929.15 536.202487 l
924.35 508.014195 l
919.55 591.206905 l
914.75 602.897618 l
909.95 592.773793 l
905.15 685.43961 l
900.35 694.242758 l
895.55 699.07442 l
890.75 703.83374 l
885.95 707.361707 l
881.15 710.661465 l
876.35 713.57636 l
871.55 715.972925 l
866.75 718.475424 l
861.95 719.936925 l
857.15 722.367399 l
852.35 724.563961 l
847.55 726.332873 l
842.75 727.999422 l
837.95 729.667487 l
833.15 731.406554 l
828.35 732.845978 l
823.55 734.087684 l
818.75 735.824957 l
813.95 737.356897 l
809.15 738.237658 l
804.35 739.556897 l
799.55 740.890882 l
794.75 742.243392 l
789.95 743.186478 l
785.15 744.308579 l
780.35 745.736194 l
775.55 746.639997 l
770.75 747.672102 l
765.95 748.704826 l
761.15 749.850563 l
756.35 750.616202 l
751.55 751.614936 l
746.75 752.681246 l
741.95 753.530665 l
737.15 754.364 l
732.35 755.383751 l
727.55 756.120605 l
722.75 757.046926 l
717.95 757.913115 l
713.15 758.800416 l
708.35 759.485572 l
703.55 760.355372 l
698.75 761.264093 l
693.95 761.891086 l
689.15 762.722848 l
684.35 763.356534 l
679.55 764.114834 l
674.75 764.854017 l
669.95 765.538641 l
665.15 766.247384 l
660.35 766.988272 l
655.55 767.637108 l
650.75 768.239008 l
645.95 768.941894 l
641.15 769.607434 l
636.35 770.187653 l
631.55 770.840796 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
372 294.171 614.75 493.155 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
981.95 506.378 o
977.15 512.053 o
972.35 509.569 o
967.55 515.248 o
962.75 508.172 o
957.95 506.481 o
953.15 511.329 o
948.35 518.799 o
943.55 507.784 o
938.75 519.331 o
933.95 508.573 o
929.15 536.202 o
924.35 508.014 o
919.55 591.207 o
914.75 602.898 o
909.95 592.774 o
905.15 685.44 o
900.35 694.243 o
895.55 699.074 o
890.75 703.834 o
885.95 707.362 o
881.15 710.661 o
876.35 713.576 o
871.55 715.973 o
866.75 718.475 o
861.95 719.937 o
857.15 722.367 o
852.35 724.564 o
847.55 726.333 o
842.75 727.999 o
837.95 729.667 o
833.15 731.407 o
828.35 732.846 o
823.55 734.088 o
818.75 735.825 o
813.95 737.357 o
809.15 738.238 o
804.35 739.557 o
799.55 740.891 o
794.75 742.243 o
789.95 743.186 o
785.15 744.309 o
780.35 745.736 o
775.55 746.64 o
770.75 747.672 o
765.95 748.705 o
761.15 749.851 o
756.35 750.616 o
751.55 751.615 o
746.75 752.681 o
741.95 753.531 o
737.15 754.364 o
732.35 755.384 o
727.55 756.121 o
722.75 757.047 o
717.95 757.913 o
713.15 758.8 o
708.35 759.486 o
703.55 760.355 o
698.75 761.264 o
693.95 761.891 o
689.15 762.723 o
684.35 763.357 o
679.55 764.115 o
674.75 764.854 o
669.95 765.539 o
665.15 766.247 o
660.35 766.988 o
655.55 767.637 o
650.75 768.239 o
645.95 768.942 o
641.15 769.607 o
636.35 770.188 o
631.55 770.841 o
grestore
2.500 setlinewidth
0 setlinejoin
2 setlinecap
0.000 setgray
gsave
614.75 493.154821 m
614.75 787.32625 l
stroke
grestore
gsave
986.75 493.154821 m
986.75 787.32625 l
stroke
grestore
gsave
614.75 493.154821 m
986.75 493.154821 l
stroke
grestore
gsave
614.75 787.32625 m
986.75 787.32625 l
stroke
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

786.172 802.326 translate
0 rotate
0 0 m /parenleft glyphshow
8.3252 0 m /b glyphshow
20.8252 0 m /parenright glyphshow
grestore
0.050 setlinewidth
1 setlinejoin
0 setlinecap
[0.185 0.08] 0 setdash
0.722 0.224 0.055 setrgbcolor
gsave
708.857813 576.854821 m
726.857813 576.854821 l
744.857813 576.854821 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

0 2 m
-2 -2 l
2 -2 l
cl

gsave
0.722 0.224 0.055 setrgbcolor
fill
grestore
stroke
grestore
} bind def
726.858 576.855 o
grestore
0.000 setgray
gsave
759.258 570.555 translate
0 rotate
/TimesNewRomanPSMT 18.0 selectfont
0 0.078125 moveto
/P glyphshow
/TimesNewRomanPSMT 12.6 selectfont
10.2812 -4.55913 moveto
/x glyphshow
grestore
0.050 setlinewidth
1 setlinejoin
[0.185 0.08] 0 setdash
1.000 0.271 0.000 setrgbcolor
gsave
708.857813 550.254821 m
726.857813 550.254821 l
744.857813 550.254821 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-4 -4 m
4 -4 l
4 4 l
-4 4 l
cl

gsave
grestore
stroke
grestore
} bind def
726.858 550.255 o
grestore
0.000 setgray
gsave
759.258 543.955 translate
0 rotate
/TimesNewRomanPSMT 18.0 selectfont
0 0.078125 moveto
/P glyphshow
/TimesNewRomanPSMT 12.6 selectfont
10.2812 -4.55913 moveto
/y glyphshow
grestore
0.050 setlinewidth
1 setlinejoin
[0.185 0.08] 0 setdash
1.000 0.514 0.000 setrgbcolor
gsave
708.857813 520.654821 m
726.857813 520.654821 l
744.857813 520.654821 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
726.858 520.655 o
grestore
0.000 setgray
gsave
759.258 514.355 translate
0 rotate
/TimesNewRomanPSMT 18.0 selectfont
0 0.078125 moveto
/P glyphshow
/TimesNewRomanPSMT 12.6 selectfont
10.2812 -4.55913 moveto
/z glyphshow
grestore
0.031 0.659 0.659 setrgbcolor
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

-0 -12.247449 m
7.348469 0 l
0 12.247449 l
-7.348469 0 l
cl

gsave
0.031 0.659 0.659 setrgbcolor
fill
grestore
stroke
grestore
} bind def
804.258 575.28 o
grestore
0.000 setgray
/TimesNewRomanPSMT 18.000 selectfont
gsave

836.658 570.555 translate
0 rotate
0 0 m /R glyphshow
12.0059 0 m /e glyphshow
19.9951 0 m /f glyphshow
25.9893 0 m /period glyphshow
30.4893 0 m /space glyphshow
34.9893 0 m /bracketleft glyphshow
40.9834 0 m /a glyphshow
48.9727 0 m /bracketright glyphshow
grestore
0.031 0.416 0.416 setrgbcolor
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

8.660254 -0 m
-8.660254 8.660254 l
-8.660254 -8.660254 l
cl

gsave
0.031 0.416 0.416 setrgbcolor
fill
grestore
stroke
grestore
} bind def
804.258 549.836 o
grestore
0.000 setgray
/TimesNewRomanPSMT 18.000 selectfont
gsave

836.658 545.111 translate
0 rotate
0 0 m /R glyphshow
12.0059 0 m /e glyphshow
19.9951 0 m /f glyphshow
25.9893 0 m /period glyphshow
30.4893 0 m /space glyphshow
34.9893 0 m /bracketleft glyphshow
40.9834 0 m /b glyphshow
49.9834 0 m /bracketright glyphshow
grestore
0.031 0.235 0.235 setrgbcolor
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 8.660254 m
-1.944348 2.676166 l
-8.236391 2.676166 l
-3.146021 -1.022204 l
-5.09037 -7.006293 l
-0 -3.307923 l
5.09037 -7.006293 l
3.146021 -1.022204 l
8.236391 2.676166 l
1.944348 2.676166 l
cl

gsave
0.031 0.235 0.235 setrgbcolor
fill
grestore
stroke
grestore
} bind def
804.258 524.392 o
grestore
0.000 setgray
/TimesNewRomanPSMT 18.000 selectfont
gsave

836.658 519.667 translate
0 rotate
0 0 m /R glyphshow
12.0059 0 m /e glyphshow
19.9951 0 m /f glyphshow
25.9893 0 m /period glyphshow
30.4893 0 m /space glyphshow
34.9893 0 m /bracketleft glyphshow
40.9834 0 m /c glyphshow
48.9727 0 m /bracketright glyphshow
grestore
gsave
93.95 66.60625 m
465.95 66.60625 l
465.95 360.777679 l
93.95 360.777679 l
cl
1.000 setgray
fill
grestore
2.500 setlinewidth
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
105.95 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
105.95 360.778 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

99.7 39.2469 translate
0 rotate
0 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
177.95 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
177.95 360.778 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

159.2 39.2469 translate
0 rotate
0 0 m /three glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
249.95 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
249.95 360.778 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

231.2 39.2469 translate
0 rotate
0 0 m /six glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
321.95 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
321.95 360.778 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

303.2 39.2469 translate
0 rotate
0 0 m /nine glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.95 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.95 360.778 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

368.95 39.2469 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /two glyphshow
25 0 m /zero glyphshow
37.5 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.95 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.95 360.778 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

440.95 39.2469 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /five glyphshow
25 0 m /zero glyphshow
37.5 0 m /zero glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
123.95 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
123.95 360.778 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
141.95 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
141.95 360.778 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
159.95 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
159.95 360.778 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
195.95 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
195.95 360.778 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
213.95 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
213.95 360.778 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
231.95 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
231.95 360.778 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
267.95 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
267.95 360.778 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
285.95 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
285.95 360.778 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
303.95 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
303.95 360.778 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
339.95 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
339.95 360.778 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
357.95 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
357.95 360.778 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
375.95 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
375.95 360.778 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
411.95 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
411.95 360.778 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
429.95 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
429.95 360.778 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
447.95 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
447.95 360.778 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

196.489 12.5437 translate
0 rotate
0 0 m /T glyphshow
13.521 0 m /e glyphshow
24.6172 0 m /m glyphshow
44.063 0 m /p glyphshow
56.563 0 m /e glyphshow
67.6592 0 m /r glyphshow
75.9844 0 m /a glyphshow
87.0806 0 m /t glyphshow
94.0264 0 m /u glyphshow
106.526 0 m /r glyphshow
114.852 0 m /e glyphshow
125.948 0 m /space glyphshow
132.198 0 m /parenleft glyphshow
140.523 0 m /K glyphshow
158.577 0 m /parenright glyphshow
grestore
2.500 setlinewidth
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 79.972 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.95 79.972 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

40.2 71.2923 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 169.271 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.95 169.271 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

40.2 160.591 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /two glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 258.569 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.95 258.569 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

40.2 249.889 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /four glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 347.868 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.95 347.868 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

40.2 339.188 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /six glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 102.297 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.95 102.297 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 124.621 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.95 124.621 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 146.946 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.95 146.946 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 191.595 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.95 191.595 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 213.92 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.95 213.92 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 236.244 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.95 236.244 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 280.894 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.95 280.894 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 303.218 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.95 303.218 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 325.543 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.95 325.543 o
grestore
gsave
25.2 170.692 translate
90 rotate
/TimesNewRomanPSMT 25.0 selectfont
0 0.6875 moveto
/less glyphshow
14.0991 0.6875 moveto
/omega glyphshow
/TimesNewRomanPSMT 17.5 selectfont
30.9299 -5.75313 moveto
/x glyphshow
39.6799 -5.75313 moveto
/comma glyphshow
46.7009 -5.75313 moveto
/y glyphshow
55.4509 -5.75313 moveto
/comma glyphshow
62.4719 -5.75313 moveto
/z glyphshow
/TimesNewRomanPSMT 25.0 selectfont
71.3127 0.6875 moveto
/greater glyphshow
grestore
0.050 setlinewidth
[0.185 0.08] 0 setdash
0.722 0.224 0.055 setrgbcolor
gsave
372 294.171 93.95 66.606 clipbox
461.15 80.239234 m
456.35 80.662554 l
451.55 80.04175 l
446.75 81.315862 l
441.95 80.701756 l
437.15 80.292054 l
432.35 81.105117 l
427.55 81.202854 l
422.75 80.382112 l
417.95 80.2245 l
413.15 80.192709 l
408.35 79.988394 l
403.55 80.023935 l
398.75 80.029606 l
393.95 80.461632 l
389.15 81.062656 l
384.35 211.197499 l
379.55 225.856255 l
374.75 235.096332 l
369.95 242.228874 l
365.15 248.727351 l
360.35 253.169685 l
355.55 258.577739 l
350.75 263.310739 l
345.95 266.247724 l
341.15 270.516774 l
336.35 274.312765 l
331.55 276.914478 l
326.75 279.531372 l
321.95 282.99803 l
317.15 285.287243 l
312.35 287.907842 l
307.55 290.511162 l
302.75 292.663614 l
297.95 294.8187 l
293.15 296.616637 l
288.35 299.110433 l
283.55 300.858273 l
278.75 302.609953 l
273.95 304.636092 l
269.15 306.343703 l
264.35 307.919912 l
259.55 309.78643 l
254.75 311.584947 l
249.95 313.174595 l
245.15 314.650074 l
240.35 316.046927 l
235.55 317.596301 l
230.75 319.258414 l
225.95 320.399203 l
221.15 321.714794 l
216.35 323.236664 l
211.55 324.591144 l
206.75 325.647948 l
201.95 327.053864 l
197.15 328.393788 l
192.35 329.591639 l
187.55 330.659828 l
182.75 331.83272 l
177.95 332.892157 l
173.15 334.183191 l
168.35 335.300985 l
163.55 336.383685 l
158.75 337.45433 l
153.95 338.519528 l
149.15 339.488774 l
144.35 340.513073 l
139.55 341.644173 l
134.75 342.643423 l
129.95 343.648657 l
125.15 344.516103 l
120.35 345.519729 l
115.55 346.452988 l
110.75 347.370441 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
372 294.171 93.95 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

0 2 m
-2 -2 l
2 -2 l
cl

gsave
0.722 0.224 0.055 setrgbcolor
fill
grestore
stroke
grestore
} bind def
461.15 80.2392 o
456.35 80.6626 o
451.55 80.0418 o
446.75 81.3159 o
441.95 80.7018 o
437.15 80.2921 o
432.35 81.1051 o
427.55 81.2029 o
422.75 80.3821 o
417.95 80.2245 o
413.15 80.1927 o
408.35 79.9884 o
403.55 80.0239 o
398.75 80.0296 o
393.95 80.4616 o
389.15 81.0627 o
384.35 211.197 o
379.55 225.856 o
374.75 235.096 o
369.95 242.229 o
365.15 248.727 o
360.35 253.17 o
355.55 258.578 o
350.75 263.311 o
345.95 266.248 o
341.15 270.517 o
336.35 274.313 o
331.55 276.914 o
326.75 279.531 o
321.95 282.998 o
317.15 285.287 o
312.35 287.908 o
307.55 290.511 o
302.75 292.664 o
297.95 294.819 o
293.15 296.617 o
288.35 299.11 o
283.55 300.858 o
278.75 302.61 o
273.95 304.636 o
269.15 306.344 o
264.35 307.92 o
259.55 309.786 o
254.75 311.585 o
249.95 313.175 o
245.15 314.65 o
240.35 316.047 o
235.55 317.596 o
230.75 319.258 o
225.95 320.399 o
221.15 321.715 o
216.35 323.237 o
211.55 324.591 o
206.75 325.648 o
201.95 327.054 o
197.15 328.394 o
192.35 329.592 o
187.55 330.66 o
182.75 331.833 o
177.95 332.892 o
173.15 334.183 o
168.35 335.301 o
163.55 336.384 o
158.75 337.454 o
153.95 338.52 o
149.15 339.489 o
144.35 340.513 o
139.55 341.644 o
134.75 342.643 o
129.95 343.649 o
125.15 344.516 o
120.35 345.52 o
115.55 346.453 o
110.75 347.37 o
grestore
0.050 setlinewidth
1 setlinejoin
[0.185 0.08] 0 setdash
1.000 0.271 0.000 setrgbcolor
gsave
372 294.171 93.95 66.606 clipbox
461.15 80.472169 m
456.35 79.989198 l
451.55 80.687423 l
446.75 80.362421 l
441.95 81.83232 l
437.15 80.645587 l
432.35 80.179047 l
427.55 80.947148 l
422.75 80.207488 l
417.95 80.921073 l
413.15 80.431003 l
408.35 82.218357 l
403.55 82.116378 l
398.75 83.414958 l
393.95 80.122298 l
389.15 83.957 l
384.35 211.350423 l
379.55 225.251213 l
374.75 235.265061 l
369.95 242.003797 l
365.15 248.396545 l
360.35 254.000028 l
355.55 258.661947 l
350.75 262.712082 l
345.95 267.100569 l
341.15 270.948443 l
336.35 273.772375 l
331.55 277.516216 l
326.75 279.90714 l
321.95 282.445138 l
317.15 285.239557 l
312.35 287.89735 l
307.55 290.199332 l
302.75 292.478409 l
297.95 294.700826 l
293.15 296.873995 l
288.35 298.91094 l
283.55 301.086564 l
278.75 302.793863 l
273.95 304.984981 l
269.15 306.641023 l
264.35 308.425832 l
259.55 310.057227 l
254.75 311.493371 l
249.95 313.025064 l
245.15 314.797238 l
240.35 316.177392 l
235.55 317.497581 l
230.75 319.00436 l
225.95 320.454211 l
221.15 321.995325 l
216.35 323.213491 l
211.55 324.401965 l
206.75 325.928524 l
201.95 327.147985 l
197.15 328.324984 l
192.35 329.452556 l
187.55 330.712827 l
182.75 331.868484 l
177.95 333.031151 l
173.15 334.274052 l
168.35 335.350725 l
163.55 336.388597 l
158.75 337.38499 l
153.95 338.536227 l
149.15 339.617721 l
144.35 340.57259 l
139.55 341.496428 l
134.75 342.533765 l
129.95 343.578156 l
125.15 344.532936 l
120.35 345.476955 l
115.55 346.472098 l
110.75 347.383613 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
372 294.171 93.95 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-4 -4 m
4 -4 l
4 4 l
-4 4 l
cl

gsave
grestore
stroke
grestore
} bind def
461.15 80.4722 o
456.35 79.9892 o
451.55 80.6874 o
446.75 80.3624 o
441.95 81.8323 o
437.15 80.6456 o
432.35 80.179 o
427.55 80.9471 o
422.75 80.2075 o
417.95 80.9211 o
413.15 80.431 o
408.35 82.2184 o
403.55 82.1164 o
398.75 83.415 o
393.95 80.1223 o
389.15 83.957 o
384.35 211.35 o
379.55 225.251 o
374.75 235.265 o
369.95 242.004 o
365.15 248.397 o
360.35 254 o
355.55 258.662 o
350.75 262.712 o
345.95 267.101 o
341.15 270.948 o
336.35 273.772 o
331.55 277.516 o
326.75 279.907 o
321.95 282.445 o
317.15 285.24 o
312.35 287.897 o
307.55 290.199 o
302.75 292.478 o
297.95 294.701 o
293.15 296.874 o
288.35 298.911 o
283.55 301.087 o
278.75 302.794 o
273.95 304.985 o
269.15 306.641 o
264.35 308.426 o
259.55 310.057 o
254.75 311.493 o
249.95 313.025 o
245.15 314.797 o
240.35 316.177 o
235.55 317.498 o
230.75 319.004 o
225.95 320.454 o
221.15 321.995 o
216.35 323.213 o
211.55 324.402 o
206.75 325.929 o
201.95 327.148 o
197.15 328.325 o
192.35 329.453 o
187.55 330.713 o
182.75 331.868 o
177.95 333.031 o
173.15 334.274 o
168.35 335.351 o
163.55 336.389 o
158.75 337.385 o
153.95 338.536 o
149.15 339.618 o
144.35 340.573 o
139.55 341.496 o
134.75 342.534 o
129.95 343.578 o
125.15 344.533 o
120.35 345.477 o
115.55 346.472 o
110.75 347.384 o
grestore
0.050 setlinewidth
1 setlinejoin
[0.185 0.08] 0 setdash
1.000 0.514 0.000 setrgbcolor
gsave
372 294.171 93.95 66.606 clipbox
461.15 80.337239 m
456.35 81.042831 l
451.55 80.01313 l
446.75 80.921564 l
441.95 80.621432 l
437.15 80.680101 l
432.35 80.818692 l
427.55 80.048135 l
422.75 80.005049 l
417.95 79.977679 l
413.15 80.576157 l
408.35 80.787973 l
403.55 81.599027 l
398.75 80.543653 l
393.95 82.004889 l
389.15 81.028142 l
384.35 211.048638 l
379.55 225.912112 l
374.75 234.569158 l
369.95 242.565217 l
365.15 248.605637 l
360.35 253.821431 l
355.55 259.097724 l
350.75 263.106246 l
345.95 267.100569 l
341.15 270.442656 l
336.35 273.479923 l
331.55 276.781245 l
326.75 280.111634 l
321.95 282.737055 l
317.15 284.879014 l
312.35 287.963654 l
307.55 290.363239 l
302.75 292.775014 l
297.95 294.917375 l
293.15 297.165644 l
288.35 299.000551 l
283.55 300.944714 l
278.75 303.007063 l
273.95 304.648906 l
269.15 306.315529 l
264.35 308.160616 l
259.55 309.94364 l
254.75 311.389963 l
249.95 313.022296 l
245.15 314.463931 l
240.35 316.047507 l
235.55 317.601123 l
230.75 318.895148 l
225.95 320.503325 l
221.15 321.834811 l
216.35 323.160805 l
211.55 324.51975 l
206.75 325.814713 l
201.95 327.078466 l
197.15 328.310964 l
192.35 329.640753 l
187.55 330.85115 l
182.75 331.999574 l
177.95 333.085355 l
173.15 334.137872 l
168.35 335.376264 l
163.55 336.475842 l
158.75 337.510499 l
153.95 338.45943 l
149.15 339.62174 l
144.35 340.610587 l
139.55 341.621178 l
134.75 342.610428 l
129.95 343.592265 l
125.15 344.582898 l
120.35 345.534419 l
115.55 346.449997 l
110.75 347.40625 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
372 294.171 93.95 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
461.15 80.3372 o
456.35 81.0428 o
451.55 80.0131 o
446.75 80.9216 o
441.95 80.6214 o
437.15 80.6801 o
432.35 80.8187 o
427.55 80.0481 o
422.75 80.005 o
417.95 79.9777 o
413.15 80.5762 o
408.35 80.788 o
403.55 81.599 o
398.75 80.5437 o
393.95 82.0049 o
389.15 81.0281 o
384.35 211.049 o
379.55 225.912 o
374.75 234.569 o
369.95 242.565 o
365.15 248.606 o
360.35 253.821 o
355.55 259.098 o
350.75 263.106 o
345.95 267.101 o
341.15 270.443 o
336.35 273.48 o
331.55 276.781 o
326.75 280.112 o
321.95 282.737 o
317.15 284.879 o
312.35 287.964 o
307.55 290.363 o
302.75 292.775 o
297.95 294.917 o
293.15 297.166 o
288.35 299.001 o
283.55 300.945 o
278.75 303.007 o
273.95 304.649 o
269.15 306.316 o
264.35 308.161 o
259.55 309.944 o
254.75 311.39 o
249.95 313.022 o
245.15 314.464 o
240.35 316.048 o
235.55 317.601 o
230.75 318.895 o
225.95 320.503 o
221.15 321.835 o
216.35 323.161 o
211.55 324.52 o
206.75 325.815 o
201.95 327.078 o
197.15 328.311 o
192.35 329.641 o
187.55 330.851 o
182.75 332 o
177.95 333.085 o
173.15 334.138 o
168.35 335.376 o
163.55 336.476 o
158.75 337.51 o
153.95 338.459 o
149.15 339.622 o
144.35 340.611 o
139.55 341.621 o
134.75 342.61 o
129.95 343.592 o
125.15 344.583 o
120.35 345.534 o
115.55 346.45 o
110.75 347.406 o
grestore
2.500 setlinewidth
0 setlinejoin
2 setlinecap
0.000 setgray
gsave
93.95 66.60625 m
93.95 360.777679 l
stroke
grestore
gsave
465.95 66.60625 m
465.95 360.777679 l
stroke
grestore
gsave
93.95 66.60625 m
465.95 66.60625 l
stroke
grestore
gsave
93.95 360.777679 m
465.95 360.777679 l
stroke
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

266.075 375.778 translate
0 rotate
0 0 m /parenleft glyphshow
8.3252 0 m /c glyphshow
19.4214 0 m /parenright glyphshow
grestore
0.050 setlinewidth
1 setlinejoin
0 setlinecap
[0.185 0.08] 0 setdash
0.722 0.224 0.055 setrgbcolor
gsave
111.95 160.60625 m
131.95 160.60625 l
151.95 160.60625 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

0 2 m
-2 -2 l
2 -2 l
cl

gsave
0.722 0.224 0.055 setrgbcolor
fill
grestore
stroke
grestore
} bind def
131.95 160.606 o
grestore
0.000 setgray
gsave
167.95 153.606 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
3.024 0.546875 moveto
/less glyphshow
17.3273 0.546875 moveto
/omega glyphshow
/TimesNewRomanPSMT 14.0 selectfont
30.7919 -4.60562 moveto
/x glyphshow
/TimesNewRomanPSMT 20.0 selectfont
41.6747 0.546875 moveto
/greater glyphshow
grestore
0.050 setlinewidth
1 setlinejoin
[0.185 0.08] 0 setdash
1.000 0.271 0.000 setrgbcolor
gsave
111.95 130.60625 m
131.95 130.60625 l
151.95 130.60625 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-4 -4 m
4 -4 l
4 4 l
-4 4 l
cl

gsave
grestore
stroke
grestore
} bind def
131.95 130.606 o
grestore
0.000 setgray
gsave
167.95 123.606 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
3.024 0.546875 moveto
/less glyphshow
17.3273 0.546875 moveto
/omega glyphshow
/TimesNewRomanPSMT 14.0 selectfont
30.7919 -4.60562 moveto
/y glyphshow
/TimesNewRomanPSMT 20.0 selectfont
38.6507 0.546875 moveto
/greater glyphshow
grestore
0.050 setlinewidth
1 setlinejoin
[0.185 0.08] 0 setdash
1.000 0.514 0.000 setrgbcolor
gsave
111.95 97.60625 m
131.95 97.60625 l
151.95 97.60625 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
131.95 97.6062 o
grestore
0.000 setgray
gsave
167.95 90.6062 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
3.024 0.546875 moveto
/less glyphshow
17.3273 0.546875 moveto
/omega glyphshow
/TimesNewRomanPSMT 14.0 selectfont
30.7919 -4.60562 moveto
/z glyphshow
/TimesNewRomanPSMT 20.0 selectfont
40.8885 0.546875 moveto
/greater glyphshow
grestore
gsave
614.75 66.60625 m
986.75 66.60625 l
986.75 360.777679 l
614.75 360.777679 l
cl
1.000 setgray
fill
grestore
0 setlinejoin
0.902 0.902 0.980 setrgbcolor
gsave
372 294.171 614.75 66.606 clipbox
614.75 66.60625 m
614.75 360.777679 l
909.95 360.777679 l
909.95 66.60625 l
cl
gsave
fill
grestore
stroke
grestore
0.800 setgray
gsave
372 294.171 614.75 66.606 clipbox
909.95 66.60625 m
909.95 360.777679 l
986.75 360.777679 l
986.75 66.60625 l
cl
gsave
fill
grestore
stroke
grestore
2.500 setlinewidth
1 setlinejoin
0.000 setgray
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
626.75 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
626.75 360.778 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

620.5 39.2469 translate
0 rotate
0 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
698.75 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
698.75 360.778 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

680 39.2469 translate
0 rotate
0 0 m /three glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
770.75 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
770.75 360.778 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

752 39.2469 translate
0 rotate
0 0 m /six glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
842.75 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
842.75 360.778 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

824 39.2469 translate
0 rotate
0 0 m /nine glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
914.75 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
914.75 360.778 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

889.75 39.2469 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /two glyphshow
25 0 m /zero glyphshow
37.5 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 360.778 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

961.75 39.2469 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /five glyphshow
25 0 m /zero glyphshow
37.5 0 m /zero glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
644.75 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
644.75 360.778 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
662.75 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
662.75 360.778 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
680.75 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
680.75 360.778 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
716.75 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
716.75 360.778 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
734.75 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
734.75 360.778 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
752.75 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
752.75 360.778 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
788.75 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
788.75 360.778 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
806.75 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
806.75 360.778 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
824.75 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
824.75 360.778 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
860.75 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
860.75 360.778 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
878.75 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
878.75 360.778 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
896.75 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
896.75 360.778 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
932.75 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
932.75 360.778 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
950.75 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
950.75 360.778 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
968.75 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
968.75 360.778 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

717.289 12.5437 translate
0 rotate
0 0 m /T glyphshow
13.521 0 m /e glyphshow
24.6172 0 m /m glyphshow
44.063 0 m /p glyphshow
56.563 0 m /e glyphshow
67.6592 0 m /r glyphshow
75.9844 0 m /a glyphshow
87.0806 0 m /t glyphshow
94.0264 0 m /u glyphshow
106.526 0 m /r glyphshow
114.852 0 m /e glyphshow
125.948 0 m /space glyphshow
132.198 0 m /parenleft glyphshow
140.523 0 m /K glyphshow
158.577 0 m /parenright glyphshow
grestore
2.500 setlinewidth
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.75 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 66.6063 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

561 57.9266 translate
0 rotate
0 0 m /seven glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /eight glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.75 125.441 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 125.441 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

561 116.761 translate
0 rotate
0 0 m /seven glyphshow
12.5 0 m /period glyphshow
18.75 0 m /one glyphshow
31.25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.75 184.275 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 184.275 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

561 175.595 translate
0 rotate
0 0 m /seven glyphshow
12.5 0 m /period glyphshow
18.75 0 m /one glyphshow
31.25 0 m /two glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.75 243.109 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 243.109 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

561 234.429 translate
0 rotate
0 0 m /seven glyphshow
12.5 0 m /period glyphshow
18.75 0 m /one glyphshow
31.25 0 m /four glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.75 301.943 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 301.943 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

561 293.264 translate
0 rotate
0 0 m /seven glyphshow
12.5 0 m /period glyphshow
18.75 0 m /one glyphshow
31.25 0 m /six glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.75 360.778 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 360.778 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

561 352.098 translate
0 rotate
0 0 m /seven glyphshow
12.5 0 m /period glyphshow
18.75 0 m /one glyphshow
31.25 0 m /eight glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.75 81.3148 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 81.3148 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.75 96.0234 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 96.0234 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.75 110.732 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 110.732 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.75 140.149 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 140.149 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.75 154.858 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 154.858 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.75 169.566 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 169.566 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.75 198.983 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 198.983 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.75 213.692 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 213.692 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.75 228.401 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 228.401 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.75 257.818 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 257.818 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.75 272.526 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 272.526 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.75 287.235 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 287.235 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.75 316.652 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 316.652 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.75 331.361 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 331.361 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.75 346.069 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 346.069 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

551.656 90.8248 translate
90 rotate
0 0 m /L glyphshow
15.271 0 m /a glyphshow
26.3672 0 m /t glyphshow
33.313 0 m /t glyphshow
40.2588 0 m /i glyphshow
47.2046 0 m /c glyphshow
58.3008 0 m /e glyphshow
69.397 0 m /space glyphshow
75.647 0 m /P glyphshow
89.5508 0 m /a glyphshow
100.647 0 m /r glyphshow
108.972 0 m /a glyphshow
120.068 0 m /m glyphshow
139.514 0 m /e glyphshow
150.61 0 m /t glyphshow
157.556 0 m /e glyphshow
168.652 0 m /r glyphshow
176.978 0 m /s glyphshow
186.707 0 m /space glyphshow
192.957 0 m /parenleft glyphshow
201.282 0 m /a glyphshow
212.378 0 m /period glyphshow
218.628 0 m /u glyphshow
231.128 0 m /period glyphshow
237.378 0 m /parenright glyphshow
grestore
0.050 setlinewidth
[0.185 0.08] 0 setdash
0.722 0.224 0.055 setrgbcolor
gsave
372 294.171 614.75 66.606 clipbox
981.95 102.859937 m
977.15 102.174517 l
972.35 101.553816 l
967.55 101.324362 l
962.75 100.66836 l
957.95 100.312412 l
953.15 100.006474 l
948.35 99.565217 l
943.55 99.332821 l
938.75 99.018058 l
933.95 98.826847 l
929.15 99.479907 l
924.35 100.097667 l
919.55 100.388897 l
914.75 101.721493 l
909.95 103.96308 l
905.15 157.696433 l
900.35 170.489948 l
895.55 179.782824 l
890.75 187.343029 l
885.95 194.108972 l
881.15 199.565852 l
876.35 205.734627 l
871.55 210.959112 l
866.75 215.648204 l
861.95 220.393189 l
857.15 224.961672 l
852.35 229.259516 l
847.55 232.97196 l
842.75 236.952099 l
837.95 240.426264 l
833.15 244.441704 l
828.35 247.895276 l
823.55 251.145871 l
818.75 254.575909 l
813.95 257.555866 l
809.15 260.844703 l
804.35 263.907027 l
799.55 266.751665 l
794.75 269.931658 l
789.95 272.546842 l
785.15 275.247336 l
780.35 278.392028 l
775.55 280.916019 l
770.75 283.478252 l
765.95 286.119912 l
761.15 288.767455 l
756.35 291.026691 l
751.55 293.674234 l
746.75 295.989363 l
741.95 298.398627 l
737.15 300.746115 l
732.35 303.058303 l
727.55 305.358723 l
722.75 307.647377 l
717.95 309.950739 l
713.15 312.086424 l
708.35 314.213283 l
703.55 316.331317 l
698.75 318.393459 l
693.95 320.573269 l
689.15 322.644236 l
684.35 324.738737 l
679.55 326.630259 l
674.75 328.695343 l
669.95 330.598632 l
665.15 332.487212 l
660.35 334.50817 l
655.55 336.3791 l
650.75 338.235322 l
645.95 340.118019 l
641.15 341.988949 l
636.35 343.786337 l
631.55 345.616083 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
372 294.171 614.75 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

0 2 m
-2 -2 l
2 -2 l
cl

gsave
0.722 0.224 0.055 setrgbcolor
fill
grestore
stroke
grestore
} bind def
981.95 102.86 o
977.15 102.175 o
972.35 101.554 o
967.55 101.324 o
962.75 100.668 o
957.95 100.312 o
953.15 100.006 o
948.35 99.5652 o
943.55 99.3328 o
938.75 99.0181 o
933.95 98.8268 o
929.15 99.4799 o
924.35 100.098 o
919.55 100.389 o
914.75 101.721 o
909.95 103.963 o
905.15 157.696 o
900.35 170.49 o
895.55 179.783 o
890.75 187.343 o
885.95 194.109 o
881.15 199.566 o
876.35 205.735 o
871.55 210.959 o
866.75 215.648 o
861.95 220.393 o
857.15 224.962 o
852.35 229.26 o
847.55 232.972 o
842.75 236.952 o
837.95 240.426 o
833.15 244.442 o
828.35 247.895 o
823.55 251.146 o
818.75 254.576 o
813.95 257.556 o
809.15 260.845 o
804.35 263.907 o
799.55 266.752 o
794.75 269.932 o
789.95 272.547 o
785.15 275.247 o
780.35 278.392 o
775.55 280.916 o
770.75 283.478 o
765.95 286.12 o
761.15 288.767 o
756.35 291.027 o
751.55 293.674 o
746.75 295.989 o
741.95 298.399 o
737.15 300.746 o
732.35 303.058 o
727.55 305.359 o
722.75 307.647 o
717.95 309.951 o
713.15 312.086 o
708.35 314.213 o
703.55 316.331 o
698.75 318.393 o
693.95 320.573 o
689.15 322.644 o
684.35 324.739 o
679.55 326.63 o
674.75 328.695 o
669.95 330.599 o
665.15 332.487 o
660.35 334.508 o
655.55 336.379 o
650.75 338.235 o
645.95 340.118 o
641.15 341.989 o
636.35 343.786 o
631.55 345.616 o
grestore
0.050 setlinewidth
1 setlinejoin
[0.185 0.08] 0 setdash
1.000 0.271 0.000 setrgbcolor
gsave
372 294.171 614.75 66.606 clipbox
981.95 102.859937 m
977.15 102.174517 l
972.35 101.553816 l
967.55 101.324362 l
962.75 100.66836 l
957.95 100.312412 l
953.15 100.006474 l
948.35 99.565217 l
943.55 99.332821 l
938.75 99.018058 l
933.95 98.826847 l
929.15 99.479907 l
924.35 100.097667 l
919.55 100.388897 l
914.75 101.721493 l
909.95 103.96308 l
905.15 157.696433 l
900.35 170.489948 l
895.55 179.782824 l
890.75 187.343029 l
885.95 194.108972 l
881.15 199.565852 l
876.35 205.734627 l
871.55 210.959112 l
866.75 215.648204 l
861.95 220.393189 l
857.15 224.961672 l
852.35 229.259516 l
847.55 232.97196 l
842.75 236.952099 l
837.95 240.426264 l
833.15 244.441704 l
828.35 247.895276 l
823.55 251.145871 l
818.75 254.575909 l
813.95 257.555866 l
809.15 260.844703 l
804.35 263.907027 l
799.55 266.751665 l
794.75 269.931658 l
789.95 272.546842 l
785.15 275.247336 l
780.35 278.392028 l
775.55 280.916019 l
770.75 283.478252 l
765.95 286.119912 l
761.15 288.767455 l
756.35 291.026691 l
751.55 293.674234 l
746.75 295.989363 l
741.95 298.398627 l
737.15 300.746115 l
732.35 303.058303 l
727.55 305.358723 l
722.75 307.647377 l
717.95 309.950739 l
713.15 312.086424 l
708.35 314.213283 l
703.55 316.331317 l
698.75 318.393459 l
693.95 320.573269 l
689.15 322.644236 l
684.35 324.738737 l
679.55 326.630259 l
674.75 328.695343 l
669.95 330.598632 l
665.15 332.487212 l
660.35 334.50817 l
655.55 336.3791 l
650.75 338.235322 l
645.95 340.118019 l
641.15 341.988949 l
636.35 343.786337 l
631.55 345.616083 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
372 294.171 614.75 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-4 -4 m
4 -4 l
4 4 l
-4 4 l
cl

gsave
grestore
stroke
grestore
} bind def
981.95 102.86 o
977.15 102.175 o
972.35 101.554 o
967.55 101.324 o
962.75 100.668 o
957.95 100.312 o
953.15 100.006 o
948.35 99.5652 o
943.55 99.3328 o
938.75 99.0181 o
933.95 98.8268 o
929.15 99.4799 o
924.35 100.098 o
919.55 100.389 o
914.75 101.721 o
909.95 103.963 o
905.15 157.696 o
900.35 170.49 o
895.55 179.783 o
890.75 187.343 o
885.95 194.109 o
881.15 199.566 o
876.35 205.735 o
871.55 210.959 o
866.75 215.648 o
861.95 220.393 o
857.15 224.962 o
852.35 229.26 o
847.55 232.972 o
842.75 236.952 o
837.95 240.426 o
833.15 244.442 o
828.35 247.895 o
823.55 251.146 o
818.75 254.576 o
813.95 257.556 o
809.15 260.845 o
804.35 263.907 o
799.55 266.752 o
794.75 269.932 o
789.95 272.547 o
785.15 275.247 o
780.35 278.392 o
775.55 280.916 o
770.75 283.478 o
765.95 286.12 o
761.15 288.767 o
756.35 291.027 o
751.55 293.674 o
746.75 295.989 o
741.95 298.399 o
737.15 300.746 o
732.35 303.058 o
727.55 305.359 o
722.75 307.647 o
717.95 309.951 o
713.15 312.086 o
708.35 314.213 o
703.55 316.331 o
698.75 318.393 o
693.95 320.573 o
689.15 322.644 o
684.35 324.739 o
679.55 326.63 o
674.75 328.695 o
669.95 330.599 o
665.15 332.487 o
660.35 334.508 o
655.55 336.379 o
650.75 338.235 o
645.95 340.118 o
641.15 341.989 o
636.35 343.786 o
631.55 345.616 o
grestore
0.050 setlinewidth
1 setlinejoin
[0.185 0.08] 0 setdash
1.000 0.514 0.000 setrgbcolor
gsave
372 294.171 614.75 66.606 clipbox
981.95 102.859937 m
977.15 102.174517 l
972.35 101.553816 l
967.55 101.324362 l
962.75 100.66836 l
957.95 100.312412 l
953.15 100.006474 l
948.35 99.565217 l
943.55 99.332821 l
938.75 99.018058 l
933.95 98.826847 l
929.15 99.479907 l
924.35 100.097667 l
919.55 100.388897 l
914.75 101.721493 l
909.95 103.96308 l
905.15 157.696433 l
900.35 170.489948 l
895.55 179.782824 l
890.75 187.343029 l
885.95 194.108972 l
881.15 199.565852 l
876.35 205.734627 l
871.55 210.959112 l
866.75 215.648204 l
861.95 220.393189 l
857.15 224.961672 l
852.35 229.259516 l
847.55 232.97196 l
842.75 236.952099 l
837.95 240.426264 l
833.15 244.441704 l
828.35 247.895276 l
823.55 251.145871 l
818.75 254.575909 l
813.95 257.555866 l
809.15 260.844703 l
804.35 263.907027 l
799.55 266.751665 l
794.75 269.931658 l
789.95 272.546842 l
785.15 275.247336 l
780.35 278.392028 l
775.55 280.916019 l
770.75 283.478252 l
765.95 286.119912 l
761.15 288.767455 l
756.35 291.026691 l
751.55 293.674234 l
746.75 295.989363 l
741.95 298.398627 l
737.15 300.746115 l
732.35 303.058303 l
727.55 305.358723 l
722.75 307.647377 l
717.95 309.950739 l
713.15 312.086424 l
708.35 314.213283 l
703.55 316.331317 l
698.75 318.393459 l
693.95 320.573269 l
689.15 322.644236 l
684.35 324.738737 l
679.55 326.630259 l
674.75 328.695343 l
669.95 330.598632 l
665.15 332.487212 l
660.35 334.50817 l
655.55 336.3791 l
650.75 338.235322 l
645.95 340.118019 l
641.15 341.988949 l
636.35 343.786337 l
631.55 345.616083 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
372 294.171 614.75 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
981.95 102.86 o
977.15 102.175 o
972.35 101.554 o
967.55 101.324 o
962.75 100.668 o
957.95 100.312 o
953.15 100.006 o
948.35 99.5652 o
943.55 99.3328 o
938.75 99.0181 o
933.95 98.8268 o
929.15 99.4799 o
924.35 100.098 o
919.55 100.389 o
914.75 101.721 o
909.95 103.963 o
905.15 157.696 o
900.35 170.49 o
895.55 179.783 o
890.75 187.343 o
885.95 194.109 o
881.15 199.566 o
876.35 205.735 o
871.55 210.959 o
866.75 215.648 o
861.95 220.393 o
857.15 224.962 o
852.35 229.26 o
847.55 232.972 o
842.75 236.952 o
837.95 240.426 o
833.15 244.442 o
828.35 247.895 o
823.55 251.146 o
818.75 254.576 o
813.95 257.556 o
809.15 260.845 o
804.35 263.907 o
799.55 266.752 o
794.75 269.932 o
789.95 272.547 o
785.15 275.247 o
780.35 278.392 o
775.55 280.916 o
770.75 283.478 o
765.95 286.12 o
761.15 288.767 o
756.35 291.027 o
751.55 293.674 o
746.75 295.989 o
741.95 298.399 o
737.15 300.746 o
732.35 303.058 o
727.55 305.359 o
722.75 307.647 o
717.95 309.951 o
713.15 312.086 o
708.35 314.213 o
703.55 316.331 o
698.75 318.393 o
693.95 320.573 o
689.15 322.644 o
684.35 324.739 o
679.55 326.63 o
674.75 328.695 o
669.95 330.599 o
665.15 332.487 o
660.35 334.508 o
655.55 336.379 o
650.75 338.235 o
645.95 340.118 o
641.15 341.989 o
636.35 343.786 o
631.55 345.616 o
grestore
2.500 setlinewidth
0 setlinejoin
2 setlinecap
0.000 setgray
gsave
614.75 66.60625 m
614.75 360.777679 l
stroke
grestore
gsave
986.75 66.60625 m
986.75 360.777679 l
stroke
grestore
gsave
614.75 66.60625 m
986.75 66.60625 l
stroke
grestore
gsave
614.75 360.777679 m
986.75 360.777679 l
stroke
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

723.828 213.692 translate
0 rotate
0 0 m /R glyphshow
16.6748 0 m /three glyphshow
29.1748 0 m /C glyphshow
grestore
gsave
910.55 213.692 translate
0 rotate
/TimesNewRomanPSMT 25.0 selectfont
0 0.921875 moveto
/P glyphshow
13.9038 0.921875 moveto
/m glyphshow
33.3496 0.546875 moveto
/three glyphshow
45.8496 0.921875 moveto
/m glyphshow
33.349609375 20.875 12.5 1.5625 rectfill
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

786.172 375.778 translate
0 rotate
0 0 m /parenleft glyphshow
8.3252 0 m /d glyphshow
20.8252 0 m /parenright glyphshow
grestore
0.050 setlinewidth
1 setlinejoin
0 setlinecap
[0.185 0.08] 0 setdash
0.722 0.224 0.055 setrgbcolor
gsave
632.75 152.45 m
652.75 152.45 l
672.75 152.45 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

0 2 m
-2 -2 l
2 -2 l
cl

gsave
0.722 0.224 0.055 setrgbcolor
fill
grestore
stroke
grestore
} bind def
652.75 152.45 o
grestore
0.000 setgray
gsave
688.75 145.45 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.796875 moveto
/a glyphshow
grestore
0.050 setlinewidth
1 setlinejoin
[0.185 0.08] 0 setdash
1.000 0.271 0.000 setrgbcolor
gsave
632.75 124.16875 m
652.75 124.16875 l
672.75 124.16875 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-4 -4 m
4 -4 l
4 4 l
-4 4 l
cl

gsave
grestore
stroke
grestore
} bind def
652.75 124.169 o
grestore
0.000 setgray
gsave
688.75 117.169 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.109375 moveto
/b glyphshow
grestore
0.050 setlinewidth
1 setlinejoin
[0.185 0.08] 0 setdash
1.000 0.514 0.000 setrgbcolor
gsave
632.75 95.8875 m
652.75 95.8875 l
672.75 95.8875 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
652.75 95.8875 o
grestore
0.000 setgray
gsave
688.75 88.8875 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.796875 moveto
/c glyphshow
grestore

end
showpage
