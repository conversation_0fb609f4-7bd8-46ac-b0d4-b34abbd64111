This is BibTeX, Version 0.99d (TeX Live 2017/Debian)
Capacity: max_strings=100000, hash_size=100000, hash_prime=85009
The top-level auxiliary file: BAO.aux
The style file: apsrev4-2.bst
Reallocated singl_function (elt_size=4) to 100 items from 50.
Reallocated singl_function (elt_size=4) to 100 items from 50.
Reallocated singl_function (elt_size=4) to 100 items from 50.
Reallocated singl_function (elt_size=4) to 100 items from 50.
Reallocated singl_function (elt_size=4) to 100 items from 50.
Reallocated singl_function (elt_size=4) to 100 items from 50.
Reallocated singl_function (elt_size=4) to 100 items from 50.
Reallocated wiz_functions (elt_size=4) to 6000 items from 3000.
Reallocated singl_function (elt_size=4) to 100 items from 50.
Database file #1: BAONotes.bib
Database file #2: BAO.bib
Repeated entry---line 1432 of file BAO.bib
 : @article{mani_atomistic_2013
 :                             ,
I'm skipping whatever remains of this entry
I was expecting a `,' or a `}'---line 1720 of file BAO.bib
 : 
 : @Article{korus-22,
(<PERSON><PERSON><PERSON> may have been on previous line)
I'm skipping whatever remains of this entry
Warning--I didn't find a database entry for "zylberberg_bismuth_2007"
Warning--I didn't find a database entry for "son_epitaxial_2008"
Warning--I didn't find a database entry for ""
Too many commas in name 1 of "JUN-KI CHUNG, WON-JEONG KIM, JONG KUK KIM, SANG SU KIM and TAE KWON SONG" for entry JUN-KI_2007
while executing---line 3309 of file apsrev4-2.bst
Reallocated singl_function (elt_size=4) to 100 items from 50.
Reallocated wiz_functions (elt_size=4) to 9000 items from 6000.
apsrev4-2.bst 2019-01-14 (MD) hand-edited version of apsrev4-1.bst
Control: key (0) 
Control: author (72) initials jnrlst
Control: editor formatted (1) identically to author
Control: production of article title (-1) disabled
Control: page (0) single
Control: year (1) truncated
Control: production of eprint (0) enabled
Too many commas in name 1 of "JUN-KI CHUNG, WON-JEONG KIM, JONG KUK KIM, SANG SU KIM and TAE KWON SONG" for entry JUN-KI_2007
while executing---line 3515 of file apsrev4-2.bst
Too many commas in name 1 of "JUN-KI CHUNG, WON-JEONG KIM, JONG KUK KIM, SANG SU KIM and TAE KWON SONG" for entry JUN-KI_2007
while executing---line 3515 of file apsrev4-2.bst
Too many commas in name 1 of "JUN-KI CHUNG, WON-JEONG KIM, JONG KUK KIM, SANG SU KIM and TAE KWON SONG" for entry JUN-KI_2007
while executing---line 3515 of file apsrev4-2.bst
Too many commas in name 1 of "JUN-KI CHUNG, WON-JEONG KIM, JONG KUK KIM, SANG SU KIM and TAE KWON SONG" for entry JUN-KI_2007
while executing---line 3515 of file apsrev4-2.bst
You've used 35 entries,
            6079 wiz_defined-function locations,
            1965 strings with 25192 characters,
and the built_in function-call counts, 39202 in all, are:
= -- 2810
> -- 1103
< -- 221
+ -- 343
- -- 266
* -- 6053
:= -- 3607
add.period$ -- 35
call.type$ -- 35
change.case$ -- 140
chr.to.int$ -- 34
cite$ -- 35
duplicate$ -- 3437
empty$ -- 2705
format.name$ -- 653
if$ -- 8087
int.to.chr$ -- 2
int.to.str$ -- 43
missing$ -- 553
newline$ -- 157
num.names$ -- 105
pop$ -- 1418
preamble$ -- 1
purify$ -- 175
quote$ -- 0
skip$ -- 1748
stack$ -- 0
substring$ -- 932
swap$ -- 3406
text.length$ -- 132
text.prefix$ -- 0
top$ -- 8
type$ -- 518
warning$ -- 0
while$ -- 105
width$ -- 0
write$ -- 335
(There were 7 error messages)
