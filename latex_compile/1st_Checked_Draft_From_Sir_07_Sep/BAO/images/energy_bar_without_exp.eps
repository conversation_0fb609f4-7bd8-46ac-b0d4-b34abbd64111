%!PS-Adobe-3.0 EPSF-3.0
%%Title: energy_bar_without_exp.eps
%%Creator: Mat<PERSON>lotlib v3.7.1, https://matplotlib.org/
%%CreationDate: Wed Apr 24 12:54:34 2024
%%Orientation: portrait
%%BoundingBox: 30 173 582 619
%%HiResBoundingBox: 30.803125 173.928594 581.196875 618.071406
%%EndComments
%%BeginProlog
/mpldict 11 dict def
mpldict begin
/_d { bind def } bind def
/m { moveto } _d
/l { lineto } _d
/r { rlineto } _d
/c { curveto } _d
/cl { closepath } _d
/ce { closepath eofill } _d
/box {
      m
      1 index 0 r
      0 exch r
      neg 0 r
      cl
    } _d
/clipbox {
      box
      clip
      newpath
    } _d
/sc { setcachedevice } _d
%!PS-TrueTypeFont-1.0-2.53740
%%Title: unknown
%%Creator: Converted from TrueType to type 42 by PPR
15 dict begin
/FontName /TimesNewRomanPSMT def
/PaintType 0 def
/FontMatrix[1 0 0 1 0 0]def
/FontBBox[-568 -307 2028 1007]def
/FontType 42 def
/Encoding StandardEncoding def
/FontInfo 10 dict dup begin
/FamilyName (unknown) def
/FullName (unknown) def
/Weight (unknown) def
/Version (unknown) def
/ItalicAngle 0.0 def
/isFixedPitch false def
/UnderlinePosition -223 def
/UnderlineThickness 100 def
end readonly def
/sfnts[<00010000000900800003001063767420F34DDA810000009C000007C46670676D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>]def
/CharStrings 28 dict dup begin
/.notdef 0 def
/minus 27 def
/Gamma 28 def
/Delta 29 def
/space 3 def
/parenleft 4 def
/parenright 5 def
/plus 6 def
/zero 7 def
/one 8 def
/two 9 def
/five 10 def
/D 11 def
/E 12 def
/M 13 def
/R 14 def
/V 15 def
/d 16 def
/e 17 def
/g 18 def
/i 19 def
/m 20 def
/n 21 def
/o 22 def
/r 23 def
/s 24 def
/t 25 def
/y 26 def
end readonly def

systemdict/resourcestatus known
 {42 /FontType resourcestatus
   {pop pop false}{true}ifelse}
 {true}ifelse
{/TrueDict where{pop}{(%%[ Error: no TrueType rasterizer ]%%)= flush}ifelse
/FontType 3 def
 /TrueState 271 string def
 TrueDict begin sfnts save
 72 0 matrix defaultmatrix dtransform dup
 mul exch dup mul add sqrt cvi 0 72 matrix
 defaultmatrix dtransform dup mul exch dup
 mul add sqrt cvi 3 -1 roll restore
 TrueState initer end
 /BuildGlyph{exch begin
  CharStrings dup 2 index known
    {exch}{exch pop /.notdef}ifelse
  get dup xcheck
    {currentdict systemdict begin begin exec end end}
    {TrueDict begin /bander load cvlit exch TrueState render end}
    ifelse
 end}bind def
 /BuildChar{
  1 index /Encoding get exch get
  1 index /BuildGlyph get exec
 }bind def
}if

FontName currentdict end definefont pop
end
%%EndProlog
mpldict begin
30.803 173.929 translate
550.394 444.143 0 0 clipbox
gsave
0 0 m
550.39375 0 l
550.39375 444.142812 l
0 444.142812 l
cl
1.000 setgray
fill
grestore
gsave
96.79375 67.903125 m
543.19375 67.903125 l
543.19375 428.263125 l
96.79375 428.263125 l
cl
1.000 setgray
fill
grestore
gsave
446.4 360.36 96.794 67.903 clipbox
117.084659 428.263125 m
170.017465 428.263125 l
170.017465 145.376633 l
117.084659 145.376633 l
cl
0.016 0.459 0.459 setrgbcolor
fill
grestore
gsave
446.4 360.36 96.794 67.903 clipbox
205.306003 428.263125 m
258.238809 428.263125 l
258.238809 169.218848 l
205.306003 169.218848 l
cl
0.016 0.459 0.459 setrgbcolor
fill
grestore
gsave
446.4 360.36 96.794 67.903 clipbox
293.527347 428.263125 m
346.460153 428.263125 l
346.460153 281.625688 l
293.527347 281.625688 l
cl
0.016 0.459 0.459 setrgbcolor
fill
grestore
gsave
446.4 360.36 96.794 67.903 clipbox
381.748691 428.263125 m
434.681497 428.263125 l
434.681497 85.063125 l
381.748691 85.063125 l
cl
0.016 0.459 0.459 setrgbcolor
fill
grestore
gsave
446.4 360.36 96.794 67.903 clipbox
469.970035 428.263125 m
522.902841 428.263125 l
522.902841 135.434456 l
469.970035 135.434456 l
cl
0.016 0.459 0.459 setrgbcolor
fill
grestore
2.500 setlinewidth
1 setlinejoin
0 setlinecap
[] 0 setdash
0.000 setgray
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 10 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
143.551 67.9031 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -10 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
143.551 428.263 o
grestore
gsave
136.051 40.5438 translate
0 rotate
/TimesNewRomanPSMT 25.0 selectfont
0 0.453125 moveto
/Gamma glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 10 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
231.772 67.9031 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -10 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
231.772 428.263 o
grestore
gsave
213.772 40.9031 translate
0 rotate
/TimesNewRomanPSMT 25.0 selectfont
0 0.453125 moveto
/R glyphshow
/TimesNewRomanPSMT 17.5 selectfont
17.0505 -5.9875 moveto
/two glyphshow
25.8005 -5.9875 moveto
/five glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 10 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
319.994 67.9031 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -10 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
319.994 428.263 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

308.877 40.5438 translate
0 rotate
0 0 m /M glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 10 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
408.215 67.9031 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -10 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
408.215 428.263 o
grestore
gsave
375.715 40.9031 translate
0 rotate
/TimesNewRomanPSMT 25.0 selectfont
0 0.453125 moveto
/Gamma glyphshow
14.4531 0.453125 moveto
/plus glyphshow
28.5522 0.453125 moveto
/R glyphshow
/TimesNewRomanPSMT 17.5 selectfont
45.6028 -5.9875 moveto
/two glyphshow
54.3528 -5.9875 moveto
/five glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 10 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
496.436 67.9031 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -10 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
496.436 428.263 o
grestore
gsave
470.936 40.5438 translate
0 rotate
/TimesNewRomanPSMT 25.0 selectfont
0 0.453125 moveto
/Gamma glyphshow
14.4531 0.453125 moveto
/plus glyphshow
28.5522 0.453125 moveto
/M glyphshow
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

233.509 12.5438 translate
0 rotate
0 0 m /D glyphshow
18.0542 0 m /i glyphshow
25 0 m /s glyphshow
34.729 0 m /t glyphshow
41.6748 0 m /o glyphshow
54.1748 0 m /r glyphshow
62.5 0 m /t glyphshow
69.4458 0 m /i glyphshow
76.3916 0 m /o glyphshow
88.8916 0 m /n glyphshow
101.392 0 m /space glyphshow
107.642 0 m /m glyphshow
127.087 0 m /o glyphshow
139.587 0 m /d glyphshow
152.087 0 m /e glyphshow
163.184 0 m /s glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
10 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
96.7938 106.093 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-10 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
543.194 106.093 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

35.2 97.4131 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /two glyphshow
26.5991 0 m /five glyphshow
39.0991 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
10 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
96.7938 170.527 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-10 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
543.194 170.527 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

35.2 161.847 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /two glyphshow
26.5991 0 m /zero glyphshow
39.0991 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
10 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
96.7938 234.961 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-10 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
543.194 234.961 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

35.2 226.281 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /one glyphshow
26.5991 0 m /five glyphshow
39.0991 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
10 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
96.7938 299.395 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-10 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
543.194 299.395 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

35.2 290.715 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /one glyphshow
26.5991 0 m /zero glyphshow
39.0991 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
10 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
96.7938 363.829 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-10 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
543.194 363.829 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

47.7 355.149 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /five glyphshow
26.5991 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
10 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
96.7938 428.263 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-10 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
543.194 428.263 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

74.2938 419.583 translate
0 rotate
0 0 m /zero glyphshow
grestore
1.000 setlinewidth
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
5 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
96.7938 80.3192 o
grestore
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-5 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
543.194 80.3192 o
grestore
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
5 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
96.7938 93.206 o
grestore
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-5 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
543.194 93.206 o
grestore
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
5 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
96.7938 118.98 o
grestore
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-5 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
543.194 118.98 o
grestore
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
5 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
96.7938 131.866 o
grestore
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-5 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
543.194 131.866 o
grestore
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
5 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
96.7938 144.753 o
grestore
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-5 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
543.194 144.753 o
grestore
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
5 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
96.7938 157.64 o
grestore
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-5 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
543.194 157.64 o
grestore
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
5 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
96.7938 183.414 o
grestore
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-5 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
543.194 183.414 o
grestore
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
5 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
96.7938 196.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-5 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
543.194 196.3 o
grestore
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
5 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
96.7938 209.187 o
grestore
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-5 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
543.194 209.187 o
grestore
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
5 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
96.7938 222.074 o
grestore
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-5 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
543.194 222.074 o
grestore
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
5 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
96.7938 247.848 o
grestore
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-5 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
543.194 247.848 o
grestore
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
5 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
96.7938 260.735 o
grestore
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-5 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
543.194 260.735 o
grestore
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
5 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
96.7938 273.621 o
grestore
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-5 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
543.194 273.621 o
grestore
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
5 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
96.7938 286.508 o
grestore
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-5 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
543.194 286.508 o
grestore
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
5 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
96.7938 312.282 o
grestore
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-5 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
543.194 312.282 o
grestore
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
5 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
96.7938 325.169 o
grestore
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-5 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
543.194 325.169 o
grestore
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
5 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
96.7938 338.055 o
grestore
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-5 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
543.194 338.055 o
grestore
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
5 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
96.7938 350.942 o
grestore
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-5 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
543.194 350.942 o
grestore
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
5 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
96.7938 376.716 o
grestore
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-5 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
543.194 376.716 o
grestore
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
5 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
96.7938 389.603 o
grestore
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-5 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
543.194 389.603 o
grestore
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
5 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
96.7938 402.489 o
grestore
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-5 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
543.194 402.489 o
grestore
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
5 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
96.7938 415.376 o
grestore
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-5 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
543.194 415.376 o
grestore
gsave
25.2 199.583 translate
90 rotate
/TimesNewRomanPSMT 25.0 selectfont
0 0.640625 moveto
/Delta glyphshow
16.0767 0.640625 moveto
/E glyphshow
31.3477 0.640625 moveto
/parenleft glyphshow
39.6729 0.640625 moveto
/m glyphshow
59.1187 0.640625 moveto
/e glyphshow
70.2148 0.640625 moveto
/V glyphshow
88.269 0.640625 moveto
/parenright glyphshow
grestore
2.500 setlinewidth
0 setlinejoin
2 setlinecap
gsave
96.79375 67.903125 m
96.79375 428.263125 l
stroke
grestore
gsave
543.19375 67.903125 m
543.19375 428.263125 l
stroke
grestore
gsave
96.79375 67.903125 m
543.19375 67.903125 l
stroke
grestore
gsave
96.79375 428.263125 m
543.19375 428.263125 l
stroke
grestore
gsave
114.79375 90.215625 m
154.79375 90.215625 l
154.79375 104.215625 l
114.79375 104.215625 l
cl
0.016 0.459 0.459 setrgbcolor
fill
grestore
/TimesNewRomanPSMT 20.000 selectfont
gsave

170.794 90.2156 translate
0 rotate
0 0 m /E glyphshow
12.2168 0 m /n glyphshow
22.2168 0 m /e glyphshow
31.0938 0 m /r glyphshow
37.3789 0 m /g glyphshow
47.3789 0 m /y glyphshow
grestore

end
showpage
