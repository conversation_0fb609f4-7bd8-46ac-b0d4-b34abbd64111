%!PS-Adobe-3.0 EPSF-3.0
%%Title: temp_vs_strain_111.eps
%%Creator: Mat<PERSON>lotlib v3.7.1, https://matplotlib.org/
%%CreationDate: Wed Apr 24 13:24:21 2024
%%Orientation: portrait
%%BoundingBox: 25 178 587 614
%%HiResBoundingBox: 25.998438 178.916875 586.001562 613.083125
%%EndComments
%%BeginProlog
/mpldict 11 dict def
mpldict begin
/_d { bind def } bind def
/m { moveto } _d
/l { lineto } _d
/r { rlineto } _d
/c { curveto } _d
/cl { closepath } _d
/ce { closepath eofill } _d
/box {
      m
      1 index 0 r
      0 exch r
      neg 0 r
      cl
    } _d
/clipbox {
      box
      clip
      newpath
    } _d
/sc { setcachedevice } _d
%!PS-TrueTypeFont-1.0-2.53740
%%Title: unknown
%%Creator: Converted from TrueType to type 42 by PPR
15 dict begin
/FontName /TimesNewRomanPSMT def
/PaintType 0 def
/FontMatrix[1 0 0 1 0 0]def
/FontBBox[-568 -307 2028 1007]def
/FontType 42 def
/Encoding StandardEncoding def
/FontInfo 10 dict dup begin
/FamilyName (unknown) def
/FullName (unknown) def
/Weight (unknown) def
/Version (unknown) def
/ItalicAngle 0.0 def
/isFixedPitch false def
/UnderlinePosition -223 def
/UnderlineThickness 100 def
end readonly def
/sfnts[<00010000000900800003001063767420F34DDA810000009C000007C46670676D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>]def
/CharStrings 31 dict dup begin
/.notdef 0 def
/minus 31 def
/space 3 def
/parenleft 4 def
/parenright 5 def
/period 6 def
/slash 7 def
/zero 8 def
/one 9 def
/two 10 def
/three 11 def
/four 12 def
/seven 13 def
/eight 14 def
/nine 15 def
/eta 32 def
/E 16 def
/F 17 def
/M 18 def
/S 19 def
/V 20 def
/a 21 def
/c 22 def
/d 23 def
/e 24 def
/i 25 def
/l 26 def
/m 27 def
/n 28 def
/r 29 def
/t 30 def
end readonly def

systemdict/resourcestatus known
 {42 /FontType resourcestatus
   {pop pop false}{true}ifelse}
 {true}ifelse
{/TrueDict where{pop}{(%%[ Error: no TrueType rasterizer ]%%)= flush}ifelse
/FontType 3 def
 /TrueState 271 string def
 TrueDict begin sfnts save
 72 0 matrix defaultmatrix dtransform dup
 mul exch dup mul add sqrt cvi 0 72 matrix
 defaultmatrix dtransform dup mul exch dup
 mul add sqrt cvi 3 -1 roll restore
 TrueState initer end
 /BuildGlyph{exch begin
  CharStrings dup 2 index known
    {exch}{exch pop /.notdef}ifelse
  get dup xcheck
    {currentdict systemdict begin begin exec end end}
    {TrueDict begin /bander load cvlit exch TrueState render end}
    ifelse
 end}bind def
 /BuildChar{
  1 index /Encoding get exch get
  1 index /BuildGlyph get exec
 }bind def
}if

FontName currentdict end definefont pop
end
%%EndProlog
mpldict begin
25.998 178.917 translate
560.003 434.166 0 0 clipbox
gsave
0 -0 m
560.003125 -0 l
560.003125 434.16625 l
0 434.16625 l
cl
1.000 setgray
fill
grestore
gsave
100.153125 66.60625 m
546.553125 66.60625 l
546.553125 426.96625 l
100.153125 426.96625 l
cl
1.000 setgray
fill
grestore
2.500 setlinewidth
1 setlinejoin
0 setlinecap
[] 0 setdash
0.000 setgray
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

86.8563 39.2469 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /four glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
211.753 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
211.753 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

198.456 39.2469 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /two glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
323.353 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
323.353 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

317.103 39.2469 translate
0 rotate
0 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
434.953 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
434.953 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

428.703 39.2469 translate
0 rotate
0 0 m /two glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

540.303 39.2469 translate
0 rotate
0 0 m /four glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
128.053 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
128.053 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
155.953 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
155.953 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
183.853 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
183.853 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
239.653 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
239.653 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
267.553 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
267.553 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
295.453 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
295.453 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
351.253 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
351.253 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
379.153 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
379.153 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
407.053 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
407.053 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
462.853 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
462.853 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
490.753 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
490.753 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
518.653 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
518.653 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

205.306 12.5438 translate
0 rotate
0 0 m /E glyphshow
15.271 0 m /l glyphshow
22.2168 0 m /e glyphshow
33.313 0 m /c glyphshow
44.4092 0 m /t glyphshow
51.355 0 m /r glyphshow
59.6802 0 m /i glyphshow
66.626 0 m /c glyphshow
77.7222 0 m /space glyphshow
83.9722 0 m /F glyphshow
97.876 0 m /i glyphshow
104.822 0 m /e glyphshow
115.918 0 m /l glyphshow
122.864 0 m /d glyphshow
135.364 0 m /space glyphshow
141.614 0 m /parenleft glyphshow
149.939 0 m /M glyphshow
172.168 0 m /V glyphshow
190.222 0 m /slash glyphshow
197.168 0 m /c glyphshow
208.264 0 m /m glyphshow
227.71 0 m /parenright glyphshow
grestore
2.500 setlinewidth
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 95.0824 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 95.0824 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

33.9031 86.4027 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /one glyphshow
43.75 0 m /seven glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 174.662 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 174.662 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

33.9031 165.982 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /one glyphshow
43.75 0 m /eight glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 254.242 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 254.242 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

33.9031 245.562 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /one glyphshow
43.75 0 m /nine glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 333.821 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 333.821 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

33.9031 325.142 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /two glyphshow
43.75 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 413.401 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 413.401 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

33.9031 404.721 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /two glyphshow
43.75 0 m /one glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 79.1664 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 79.1664 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 110.998 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 110.998 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 126.914 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 126.914 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 142.83 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 142.83 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 158.746 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 158.746 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 190.578 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 190.578 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 206.494 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 206.494 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 222.41 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 222.41 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 238.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 238.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 270.158 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 270.158 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 286.074 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 286.074 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 301.989 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 301.989 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 317.905 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 317.905 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 349.737 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 349.737 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 365.653 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 365.653 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 381.569 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 381.569 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.153 397.485 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
546.553 397.485 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

24.5594 216.919 translate
90 rotate
0 0 m /S glyphshow
13.9038 0 m /t glyphshow
20.8496 0 m /r glyphshow
29.1748 0 m /a glyphshow
40.271 0 m /i glyphshow
47.2168 0 m /n glyphshow
grestore
0.500 setlinewidth
[1.85 0.8] 0 setdash
0.031 0.235 0.235 setrgbcolor
gsave
446.4 360.36 100.153 66.606 clipbox
535.393125 403.940553 m
524.233125 395.95553 l
513.073125 389.239007 l
501.913125 381.743399 l
490.753125 373.820449 l
479.593125 367.661779 l
468.433125 360.663544 l
457.273125 352.809828 l
446.113125 344.038558 l
434.953125 337.712771 l
423.793125 329.684776 l
412.633125 322.774874 l
401.473125 313.621622 l
390.313125 305.516434 l
379.153125 296.744369 l
367.993125 288.355081 l
356.833125 279.069727 l
345.673125 270.403502 l
334.513125 261.843915 l
323.353125 251.422163 l
312.193125 241.166733 l
301.033125 231.489846 l
289.873125 221.404717 l
278.713125 210.070982 l
267.553125 197.880174 l
256.393125 185.713241 l
245.233125 173.837569 l
234.073125 158.04419 l
222.913125 144.433682 l
211.753125 129.120168 l
200.593125 108.898978 l
189.433125 84.322392 l
178.273125 358.798197 l
167.113125 367.36813 l
155.953125 375.392943 l
144.793125 382.454046 l
133.633125 389.205584 l
122.473125 396.56511 l
111.313125 402.77869 l
100.153125 410.356265 l
111.313125 403.189321 l
122.473125 395.973834 l
133.633125 388.737655 l
144.793125 382.274992 l
155.953125 375.132717 l
167.113125 367.937125 l
178.273125 360.922974 l
189.433125 352.713537 l
200.593125 344.71419 l
211.753125 338.276991 l
222.913125 329.73889 l
234.073125 321.539002 l
245.233125 313.335931 l
256.393125 306.067125 l
267.553125 296.777792 l
278.713125 288.341553 l
289.873125 279.434998 l
301.033125 270.158397 l
312.193125 261.042548 l
323.353125 252.651669 l
334.513125 242.158295 l
345.673125 232.081919 l
356.833125 222.688336 l
367.993125 210.043129 l
379.153125 199.811573 l
390.313125 185.621724 l
401.473125 173.733319 l
412.633125 159.590423 l
423.793125 143.421428 l
434.953125 128.411114 l
446.113125 110.742042 l
457.273125 84.545215 l
468.433125 360.661157 l
479.593125 367.835263 l
490.753125 374.667176 l
501.913125 380.985005 l
513.073125 389.37668 l
524.233125 395.380169 l
535.393125 402.543134 l
546.553125 409.994177 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
446.4 360.36 100.153 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

0 2 m
-2 -2 l
2 -2 l
cl

gsave
0.031 0.235 0.235 setrgbcolor
fill
grestore
stroke
grestore
} bind def
535.393 403.941 o
524.233 395.956 o
513.073 389.239 o
501.913 381.743 o
490.753 373.82 o
479.593 367.662 o
468.433 360.664 o
457.273 352.81 o
446.113 344.039 o
434.953 337.713 o
423.793 329.685 o
412.633 322.775 o
401.473 313.622 o
390.313 305.516 o
379.153 296.744 o
367.993 288.355 o
356.833 279.07 o
345.673 270.404 o
334.513 261.844 o
323.353 251.422 o
312.193 241.167 o
301.033 231.49 o
289.873 221.405 o
278.713 210.071 o
267.553 197.88 o
256.393 185.713 o
245.233 173.838 o
234.073 158.044 o
222.913 144.434 o
211.753 129.12 o
200.593 108.899 o
189.433 84.3224 o
178.273 358.798 o
167.113 367.368 o
155.953 375.393 o
144.793 382.454 o
133.633 389.206 o
122.473 396.565 o
111.313 402.779 o
100.153 410.356 o
111.313 403.189 o
122.473 395.974 o
133.633 388.738 o
144.793 382.275 o
155.953 375.133 o
167.113 367.937 o
178.273 360.923 o
189.433 352.714 o
200.593 344.714 o
211.753 338.277 o
222.913 329.739 o
234.073 321.539 o
245.233 313.336 o
256.393 306.067 o
267.553 296.778 o
278.713 288.342 o
289.873 279.435 o
301.033 270.158 o
312.193 261.043 o
323.353 252.652 o
334.513 242.158 o
345.673 232.082 o
356.833 222.688 o
367.993 210.043 o
379.153 199.812 o
390.313 185.622 o
401.473 173.733 o
412.633 159.59 o
423.793 143.421 o
434.953 128.411 o
446.113 110.742 o
457.273 84.5452 o
468.433 360.661 o
479.593 367.835 o
490.753 374.667 o
501.913 380.985 o
513.073 389.377 o
524.233 395.38 o
535.393 402.543 o
546.553 409.994 o
grestore
0.500 setlinewidth
1 setlinejoin
[1.85 0.8] 0 setdash
0.031 0.416 0.416 setrgbcolor
gsave
446.4 360.36 100.153 66.606 clipbox
535.393125 402.565416 m
524.233125 396.208594 l
513.073125 388.473451 l
501.913125 382.617184 l
490.753125 375.347582 l
479.593125 367.068911 l
468.433125 359.777823 l
457.273125 352.914873 l
446.113125 345.560121 l
434.953125 337.238477 l
423.793125 329.828815 l
412.633125 321.307425 l
401.473125 312.768528 l
390.313125 304.708701 l
379.153125 296.488918 l
367.993125 287.79882 l
356.833125 278.526994 l
345.673125 269.533697 l
334.513125 260.453658 l
323.353125 251.554265 l
312.193125 241.611583 l
301.033125 231.371273 l
289.873125 220.394851 l
278.713125 210.432274 l
267.553125 197.852322 l
256.393125 187.658963 l
245.233125 172.609655 l
234.073125 160.710904 l
222.913125 146.015725 l
211.753125 126.18925 l
200.593125 108.051454 l
189.433125 84.225305 l
178.273125 361.347929 l
167.113125 367.970548 l
155.953125 375.290285 l
144.793125 380.679419 l
133.633125 389.480134 l
122.473125 396.186311 l
111.313125 402.982414 l
100.153125 409.748276 l
111.313125 403.061994 l
122.473125 395.877542 l
133.633125 388.921484 l
144.793125 382.66334 l
155.953125 374.499263 l
167.113125 368.153582 l
178.273125 360.141502 l
189.433125 351.824632 l
200.593125 344.052087 l
211.753125 337.384903 l
222.913125 329.74446 l
234.073125 320.923056 l
245.233125 312.926892 l
256.393125 306.153071 l
267.553125 297.016531 l
278.713125 288.003339 l
289.873125 278.867595 l
301.033125 269.728667 l
312.193125 261.781047 l
323.353125 250.914445 l
334.513125 240.680501 l
345.673125 231.250312 l
356.833125 220.416337 l
367.993125 210.238099 l
379.153125 197.17112 l
390.313125 185.884337 l
401.473125 173.53437 l
412.633125 159.700243 l
423.793125 144.012705 l
434.953125 127.340767 l
446.113125 106.745552 l
457.273125 84.078879 l
468.433125 359.786576 l
479.593125 367.435773 l
490.753125 375.49003 l
501.913125 382.4437 l
513.073125 389.480134 l
524.233125 397.122168 l
535.393125 402.855086 l
546.553125 409.727586 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
446.4 360.36 100.153 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-5 -5 m
5 -5 l
5 5 l
-5 5 l
cl

gsave
grestore
stroke
grestore
} bind def
535.393 402.565 o
524.233 396.209 o
513.073 388.473 o
501.913 382.617 o
490.753 375.348 o
479.593 367.069 o
468.433 359.778 o
457.273 352.915 o
446.113 345.56 o
434.953 337.238 o
423.793 329.829 o
412.633 321.307 o
401.473 312.769 o
390.313 304.709 o
379.153 296.489 o
367.993 287.799 o
356.833 278.527 o
345.673 269.534 o
334.513 260.454 o
323.353 251.554 o
312.193 241.612 o
301.033 231.371 o
289.873 220.395 o
278.713 210.432 o
267.553 197.852 o
256.393 187.659 o
245.233 172.61 o
234.073 160.711 o
222.913 146.016 o
211.753 126.189 o
200.593 108.051 o
189.433 84.2253 o
178.273 361.348 o
167.113 367.971 o
155.953 375.29 o
144.793 380.679 o
133.633 389.48 o
122.473 396.186 o
111.313 402.982 o
100.153 409.748 o
111.313 403.062 o
122.473 395.878 o
133.633 388.921 o
144.793 382.663 o
155.953 374.499 o
167.113 368.154 o
178.273 360.142 o
189.433 351.825 o
200.593 344.052 o
211.753 337.385 o
222.913 329.744 o
234.073 320.923 o
245.233 312.927 o
256.393 306.153 o
267.553 297.017 o
278.713 288.003 o
289.873 278.868 o
301.033 269.729 o
312.193 261.781 o
323.353 250.914 o
334.513 240.681 o
345.673 231.25 o
356.833 220.416 o
367.993 210.238 o
379.153 197.171 o
390.313 185.884 o
401.473 173.534 o
412.633 159.7 o
423.793 144.013 o
434.953 127.341 o
446.113 106.746 o
457.273 84.0789 o
468.433 359.787 o
479.593 367.436 o
490.753 375.49 o
501.913 382.444 o
513.073 389.48 o
524.233 397.122 o
535.393 402.855 o
546.553 409.728 o
grestore
0.500 setlinewidth
1 setlinejoin
[1.85 0.8] 0 setdash
0.031 0.659 0.659 setrgbcolor
gsave
446.4 360.36 100.153 66.606 clipbox
535.393125 402.163539 m
524.233125 395.412797 l
513.073125 389.573242 l
501.913125 381.369375 l
490.753125 374.529504 l
479.593125 367.797065 l
468.433125 359.975976 l
457.273125 353.263432 l
446.113125 346.757795 l
434.953125 338.109874 l
423.793125 329.331442 l
412.633125 321.218296 l
401.473125 313.246006 l
390.313125 304.391974 l
379.153125 297.782883 l
367.993125 287.946042 l
356.833125 279.529697 l
345.673125 270.343022 l
334.513125 259.911721 l
323.353125 251.161937 l
312.193125 241.660922 l
301.033125 230.914485 l
289.873125 219.748664 l
278.713125 209.254495 l
267.553125 198.046496 l
256.393125 185.06785 l
245.233125 173.924311 l
234.073125 160.800829 l
222.913125 144.615123 l
211.753125 126.972313 l
200.593125 109.288122 l
189.433125 82.98625 l
178.273125 360.550541 l
167.113125 367.080848 l
155.953125 374.909894 l
144.793125 381.800697 l
133.633125 388.908752 l
122.473125 396.281011 l
111.313125 403.541063 l
100.153125 410.58625 l
111.313125 403.811634 l
122.473125 395.628458 l
133.633125 389.022551 l
144.793125 381.874706 l
155.953125 375.195585 l
167.113125 367.138941 l
178.273125 359.924249 l
189.433125 353.276165 l
200.593125 346.354326 l
211.753125 337.260759 l
222.913125 329.419775 l
234.073125 321.579588 l
245.233125 314.02907 l
256.393125 305.014287 l
267.553125 296.918648 l
278.713125 288.992514 l
289.873125 278.960703 l
301.033125 270.107466 l
312.193125 259.692081 l
323.353125 252.110527 l
334.513125 242.046884 l
345.673125 231.232804 l
356.833125 220.145766 l
367.993125 210.001748 l
379.153125 198.890836 l
390.313125 185.678226 l
401.473125 173.536758 l
412.633125 158.510527 l
423.793125 145.433998 l
434.953125 128.778772 l
446.113125 107.585118 l
457.273125 83.506701 l
468.433125 360.173334 l
479.593125 366.838926 l
490.753125 375.360315 l
501.913125 382.461208 l
513.073125 389.200809 l
524.233125 396.249975 l
535.393125 402.750837 l
546.553125 409.50715 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
446.4 360.36 100.153 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -5 m
1.326016 -5 2.597899 -4.473168 3.535534 -3.535534 c
4.473168 -2.597899 5 -1.326016 5 0 c
5 1.326016 4.473168 2.597899 3.535534 3.535534 c
2.597899 4.473168 1.326016 5 0 5 c
-1.326016 5 -2.597899 4.473168 -3.535534 3.535534 c
-4.473168 2.597899 -5 1.326016 -5 0 c
-5 -1.326016 -4.473168 -2.597899 -3.535534 -3.535534 c
-2.597899 -4.473168 -1.326016 -5 0 -5 c
cl

gsave
grestore
stroke
grestore
} bind def
535.393 402.164 o
524.233 395.413 o
513.073 389.573 o
501.913 381.369 o
490.753 374.53 o
479.593 367.797 o
468.433 359.976 o
457.273 353.263 o
446.113 346.758 o
434.953 338.11 o
423.793 329.331 o
412.633 321.218 o
401.473 313.246 o
390.313 304.392 o
379.153 297.783 o
367.993 287.946 o
356.833 279.53 o
345.673 270.343 o
334.513 259.912 o
323.353 251.162 o
312.193 241.661 o
301.033 230.914 o
289.873 219.749 o
278.713 209.254 o
267.553 198.046 o
256.393 185.068 o
245.233 173.924 o
234.073 160.801 o
222.913 144.615 o
211.753 126.972 o
200.593 109.288 o
189.433 82.9863 o
178.273 360.551 o
167.113 367.081 o
155.953 374.91 o
144.793 381.801 o
133.633 388.909 o
122.473 396.281 o
111.313 403.541 o
100.153 410.586 o
111.313 403.812 o
122.473 395.628 o
133.633 389.023 o
144.793 381.875 o
155.953 375.196 o
167.113 367.139 o
178.273 359.924 o
189.433 353.276 o
200.593 346.354 o
211.753 337.261 o
222.913 329.42 o
234.073 321.58 o
245.233 314.029 o
256.393 305.014 o
267.553 296.919 o
278.713 288.993 o
289.873 278.961 o
301.033 270.107 o
312.193 259.692 o
323.353 252.111 o
334.513 242.047 o
345.673 231.233 o
356.833 220.146 o
367.993 210.002 o
379.153 198.891 o
390.313 185.678 o
401.473 173.537 o
412.633 158.511 o
423.793 145.434 o
434.953 128.779 o
446.113 107.585 o
457.273 83.5067 o
468.433 360.173 o
479.593 366.839 o
490.753 375.36 o
501.913 382.461 o
513.073 389.201 o
524.233 396.25 o
535.393 402.751 o
546.553 409.507 o
grestore
2.500 setlinewidth
0 setlinejoin
2 setlinecap
0.000 setgray
gsave
100.153125 66.60625 m
100.153125 426.96625 l
stroke
grestore
gsave
546.553125 66.60625 m
546.553125 426.96625 l
stroke
grestore
gsave
100.153125 66.60625 m
546.553125 66.60625 l
stroke
grestore
gsave
100.153125 426.96625 m
546.553125 426.96625 l
stroke
grestore
0.500 setlinewidth
1 setlinejoin
0 setlinecap
[1.85 0.8] 0 setdash
0.031 0.235 0.235 setrgbcolor
gsave
285.853125 157.60625 m
305.853125 157.60625 l
325.853125 157.60625 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

0 2 m
-2 -2 l
2 -2 l
cl

gsave
0.031 0.235 0.235 setrgbcolor
fill
grestore
stroke
grestore
} bind def
305.853 157.606 o
grestore
0.000 setgray
gsave
341.853 150.606 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.796875 moveto
/eta glyphshow
/TimesNewRomanPSMT 14.0 selectfont
10.7595 -4.35562 moveto
/one glyphshow
grestore
0.500 setlinewidth
1 setlinejoin
[1.85 0.8] 0 setdash
0.031 0.416 0.416 setrgbcolor
gsave
285.853125 127.60625 m
305.853125 127.60625 l
325.853125 127.60625 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-5 -5 m
5 -5 l
5 5 l
-5 5 l
cl

gsave
grestore
stroke
grestore
} bind def
305.853 127.606 o
grestore
0.000 setgray
gsave
341.853 120.606 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.796875 moveto
/eta glyphshow
/TimesNewRomanPSMT 14.0 selectfont
10.7595 -4.35562 moveto
/two glyphshow
grestore
0.500 setlinewidth
1 setlinejoin
[1.85 0.8] 0 setdash
0.031 0.659 0.659 setrgbcolor
gsave
285.853125 97.60625 m
305.853125 97.60625 l
325.853125 97.60625 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -5 m
1.326016 -5 2.597899 -4.473168 3.535534 -3.535534 c
4.473168 -2.597899 5 -1.326016 5 0 c
5 1.326016 4.473168 2.597899 3.535534 3.535534 c
2.597899 4.473168 1.326016 5 0 5 c
-1.326016 5 -2.597899 4.473168 -3.535534 3.535534 c
-4.473168 2.597899 -5 1.326016 -5 0 c
-5 -1.326016 -4.473168 -2.597899 -3.535534 -3.535534 c
-2.597899 -4.473168 -1.326016 -5 0 -5 c
cl

gsave
grestore
stroke
grestore
} bind def
305.853 97.6062 o
grestore
0.000 setgray
gsave
341.853 90.6062 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.796875 moveto
/eta glyphshow
/TimesNewRomanPSMT 14.0 selectfont
10.7595 -4.35562 moveto
/three glyphshow
grestore

end
showpage
