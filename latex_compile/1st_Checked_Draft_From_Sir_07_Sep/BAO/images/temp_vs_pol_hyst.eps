%!PS-Adobe-3.0 EPSF-3.0
%%Title: temp_vs_pol_hyst.eps
%%Creator: Mat<PERSON>lotlib v3.7.1, https://matplotlib.org/
%%CreationDate: Wed Apr 24 13:25:59 2024
%%Orientation: portrait
%%BoundingBox: 32 178 580 614
%%HiResBoundingBox: 32.553125 178.916875 579.446875 613.083125
%%EndComments
%%BeginProlog
/mpldict 11 dict def
mpldict begin
/_d { bind def } bind def
/m { moveto } _d
/l { lineto } _d
/r { rlineto } _d
/c { curveto } _d
/cl { closepath } _d
/ce { closepath eofill } _d
/box {
      m
      1 index 0 r
      0 exch r
      neg 0 r
      cl
    } _d
/clipbox {
      box
      clip
      newpath
    } _d
/sc { setcachedevice } _d
%!PS-TrueTypeFont-1.0-2.53740
%%Title: unknown
%%Creator: Converted from TrueType to type 42 by PPR
15 dict begin
/FontName /TimesNewRomanPSMT def
/PaintType 0 def
/FontMatrix[1 0 0 1 0 0]def
/FontBBox[-568 -307 2028 1007]def
/FontType 42 def
/Encoding StandardEncoding def
/FontInfo 10 dict dup begin
/FamilyName (unknown) def
/FullName (unknown) def
/Weight (unknown) def
/Version (unknown) def
/ItalicAngle 0.0 def
/isFixedPitch false def
/UnderlinePosition -223 def
/UnderlineThickness 100 def
end readonly def
/sfnts[<00010000000900800003001063767420F34DDA810000009C000007C46670676D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>]def
/CharStrings 32 dict dup begin
/.notdef 0 def
/minus 32 def
/space 3 def
/parenleft 4 def
/parenright 5 def
/slash 6 def
/zero 7 def
/one 8 def
/two 9 def
/four 10 def
/six 11 def
/eight 12 def
/mu 33 def
/C 13 def
/E 14 def
/F 15 def
/K 16 def
/M 17 def
/P 18 def
/V 19 def
/a 20 def
/c 21 def
/d 22 def
/e 23 def
/i 24 def
/l 25 def
/m 26 def
/n 27 def
/o 28 def
/r 29 def
/t 30 def
/z 31 def
end readonly def

systemdict/resourcestatus known
 {42 /FontType resourcestatus
   {pop pop false}{true}ifelse}
 {true}ifelse
{/TrueDict where{pop}{(%%[ Error: no TrueType rasterizer ]%%)= flush}ifelse
/FontType 3 def
 /TrueState 271 string def
 TrueDict begin sfnts save
 72 0 matrix defaultmatrix dtransform dup
 mul exch dup mul add sqrt cvi 0 72 matrix
 defaultmatrix dtransform dup mul exch dup
 mul add sqrt cvi 3 -1 roll restore
 TrueState initer end
 /BuildGlyph{exch begin
  CharStrings dup 2 index known
    {exch}{exch pop /.notdef}ifelse
  get dup xcheck
    {currentdict systemdict begin begin exec end end}
    {TrueDict begin /bander load cvlit exch TrueState render end}
    ifelse
 end}bind def
 /BuildChar{
  1 index /Encoding get exch get
  1 index /BuildGlyph get exec
 }bind def
}if

FontName currentdict end definefont pop
end
%%EndProlog
mpldict begin
32.553 178.917 translate
546.894 434.166 0 0 clipbox
gsave
0 -0 m
546.89375 -0 l
546.89375 434.16625 l
0 434.16625 l
cl
1.000 setgray
fill
grestore
gsave
93.29375 66.60625 m
539.69375 66.60625 l
539.69375 426.96625 l
93.29375 426.96625 l
cl
1.000 setgray
fill
grestore
2.500 setlinewidth
1 setlinejoin
0 setlinecap
[] 0 setdash
0.000 setgray
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
113.585 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
113.585 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

100.288 39.2469 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /four glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
215.039 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
215.039 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

201.742 39.2469 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /two glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
316.494 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
316.494 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

310.244 39.2469 translate
0 rotate
0 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
417.948 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
417.948 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

411.698 39.2469 translate
0 rotate
0 0 m /two glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
519.403 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
519.403 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

513.153 39.2469 translate
0 rotate
0 0 m /four glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
138.948 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
138.948 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
164.312 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
164.312 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
189.676 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
189.676 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
240.403 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
240.403 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
265.766 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
265.766 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
291.13 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
291.13 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
341.857 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
341.857 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
367.221 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
367.221 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
392.585 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
392.585 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
443.312 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
443.312 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
468.676 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
468.676 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
494.039 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
494.039 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

198.447 12.5438 translate
0 rotate
0 0 m /E glyphshow
15.271 0 m /l glyphshow
22.2168 0 m /e glyphshow
33.313 0 m /c glyphshow
44.4092 0 m /t glyphshow
51.355 0 m /r glyphshow
59.6802 0 m /i glyphshow
66.626 0 m /c glyphshow
77.7222 0 m /space glyphshow
83.9722 0 m /F glyphshow
97.876 0 m /i glyphshow
104.822 0 m /e glyphshow
115.918 0 m /l glyphshow
122.864 0 m /d glyphshow
135.364 0 m /space glyphshow
141.614 0 m /parenleft glyphshow
149.939 0 m /M glyphshow
172.168 0 m /V glyphshow
190.222 0 m /slash glyphshow
197.168 0 m /c glyphshow
208.264 0 m /m glyphshow
227.71 0 m /parenright glyphshow
grestore
2.500 setlinewidth
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 79.2268 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
539.694 79.2268 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

44.2 70.5471 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /six glyphshow
26.5991 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 135.087 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
539.694 135.087 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

44.2 126.407 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /four glyphshow
26.5991 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 190.946 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
539.694 190.946 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

44.2 182.267 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /two glyphshow
26.5991 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 246.806 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
539.694 246.806 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

70.7938 238.126 translate
0 rotate
0 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 302.666 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
539.694 302.666 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

58.2938 293.986 translate
0 rotate
0 0 m /two glyphshow
12.5 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 358.525 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
539.694 358.525 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

58.2938 349.846 translate
0 rotate
0 0 m /four glyphshow
12.5 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 414.385 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
539.694 414.385 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

58.2938 405.705 translate
0 rotate
0 0 m /six glyphshow
12.5 0 m /zero glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 93.1918 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
539.694 93.1918 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 107.157 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
539.694 107.157 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 121.122 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
539.694 121.122 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 149.051 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
539.694 149.051 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 163.016 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
539.694 163.016 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 176.981 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
539.694 176.981 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 204.911 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
539.694 204.911 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 218.876 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
539.694 218.876 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 232.841 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
539.694 232.841 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 260.771 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
539.694 260.771 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 274.736 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
539.694 274.736 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 288.701 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
539.694 288.701 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 316.631 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
539.694 316.631 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 330.595 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
539.694 330.595 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 344.56 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
539.694 344.56 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 372.49 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
539.694 372.49 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 386.455 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
539.694 386.455 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 400.42 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
539.694 400.42 o
grestore
gsave
34.2 135.786 translate
90 rotate
/TimesNewRomanPSMT 25.0 selectfont
0 0.148438 moveto
/P glyphshow
13.9038 0.148438 moveto
/o glyphshow
26.4038 0.148438 moveto
/l glyphshow
33.3496 0.148438 moveto
/a glyphshow
44.4458 0.148438 moveto
/r glyphshow
52.771 0.148438 moveto
/i glyphshow
59.7168 0.148438 moveto
/z glyphshow
70.813 0.148438 moveto
/a glyphshow
81.9092 0.148438 moveto
/t glyphshow
88.855 0.148438 moveto
/i glyphshow
95.8008 0.148438 moveto
/o glyphshow
108.301 0.148438 moveto
/n glyphshow
120.801 0.148438 moveto
/space glyphshow
127.051 0.148438 moveto
/parenleft glyphshow
135.376 0.148438 moveto
/mu glyphshow
148.779 0.148438 moveto
/C glyphshow
165.454 0.148438 moveto
/slash glyphshow
172.4 0.148438 moveto
/c glyphshow
183.496 0.148438 moveto
/m glyphshow
/TimesNewRomanPSMT 17.5 selectfont
203.318 15.1766 moveto
/two glyphshow
/TimesNewRomanPSMT 25.0 selectfont
213.141 0.148438 moveto
/parenright glyphshow
grestore
2 setlinecap
0.031 0.235 0.235 setrgbcolor
gsave
446.4 360.36 93.294 66.606 clipbox
519.402841 410.580627 m
509.257386 409.546 l
499.111932 408.50575 l
488.966477 407.440197 l
478.821023 406.324037 l
468.675568 405.196631 l
458.530114 404.055167 l
448.384659 402.908081 l
438.239205 401.679462 l
428.09375 400.507072 l
417.948295 399.267206 l
407.802841 398.021718 l
397.657386 396.697508 l
387.511932 395.376109 l
377.366477 393.987235 l
367.221023 392.618041 l
357.075568 391.17856 l
346.930114 389.680038 l
336.784659 388.159024 l
326.639205 386.519928 l
316.49375 384.894889 l
306.348295 383.219243 l
296.202841 381.439572 l
286.057386 379.530573 l
275.911932 377.635631 l
265.766477 375.605738 l
255.621023 373.480255 l
245.475568 371.157967 l
235.330114 368.785073 l
225.184659 366.156333 l
215.039205 363.257691 l
204.89375 360.091957 l
194.748295 356.577599 l
184.602841 352.329444 l
174.457386 347.192858 l
164.311932 339.374115 l
154.166477 87.270955 l
144.021023 86.143549 l
133.875568 85.137037 l
123.730114 84.04618 l
113.584659 82.98625 l
123.730114 84.065861 l
133.875568 85.083619 l
144.021023 86.222271 l
154.166477 87.279389 l
164.311932 88.418041 l
174.457386 89.567939 l
184.602841 90.655984 l
194.748295 91.864924 l
204.89375 93.082297 l
215.039205 94.296859 l
225.184659 95.570462 l
235.330114 96.92841 l
245.475568 98.207637 l
255.621023 99.602134 l
265.766477 100.988196 l
275.911932 102.436112 l
286.057386 103.931823 l
296.202841 105.416287 l
306.348295 107.041326 l
316.49375 108.711349 l
326.639205 110.364503 l
336.784659 112.135739 l
346.930114 114.022247 l
357.075568 115.967795 l
367.221023 118.006123 l
377.366477 120.097868 l
387.511932 122.425779 l
397.657386 124.868961 l
407.802841 127.525815 l
417.948295 130.359793 l
428.09375 133.514281 l
438.239205 137.008958 l
448.384659 141.299286 l
458.530114 146.45274 l
468.675568 154.24618 l
478.821023 406.357775 l
488.966477 407.420516 l
499.111932 408.508562 l
509.257386 409.551623 l
519.402841 410.58625 l
519.402841 410.58625 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
0 setlinecap
gsave
446.4 360.36 93.294 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

0 5 m
-5 -5 l
5 -5 l
cl

gsave
0.031 0.235 0.235 setrgbcolor
fill
grestore
stroke
grestore
} bind def
519.403 410.581 o
509.257 409.546 o
499.112 408.506 o
488.966 407.44 o
478.821 406.324 o
468.676 405.197 o
458.53 404.055 o
448.385 402.908 o
438.239 401.679 o
428.094 400.507 o
417.948 399.267 o
407.803 398.022 o
397.657 396.698 o
387.512 395.376 o
377.366 393.987 o
367.221 392.618 o
357.076 391.179 o
346.93 389.68 o
336.785 388.159 o
326.639 386.52 o
316.494 384.895 o
306.348 383.219 o
296.203 381.44 o
286.057 379.531 o
275.912 377.636 o
265.766 375.606 o
255.621 373.48 o
245.476 371.158 o
235.33 368.785 o
225.185 366.156 o
215.039 363.258 o
204.894 360.092 o
194.748 356.578 o
184.603 352.329 o
174.457 347.193 o
164.312 339.374 o
154.166 87.271 o
144.021 86.1435 o
133.876 85.137 o
123.73 84.0462 o
113.585 82.9863 o
123.73 84.0659 o
133.876 85.0836 o
144.021 86.2223 o
154.166 87.2794 o
164.312 88.418 o
174.457 89.5679 o
184.603 90.656 o
194.748 91.8649 o
204.894 93.0823 o
215.039 94.2969 o
225.185 95.5705 o
235.33 96.9284 o
245.476 98.2076 o
255.621 99.6021 o
265.766 100.988 o
275.912 102.436 o
286.057 103.932 o
296.203 105.416 o
306.348 107.041 o
316.494 108.711 o
326.639 110.365 o
336.785 112.136 o
346.93 114.022 o
357.076 115.968 o
367.221 118.006 o
377.366 120.098 o
387.512 122.426 o
397.657 124.869 o
407.803 127.526 o
417.948 130.36 o
428.094 133.514 o
438.239 137.009 o
448.385 141.299 o
458.53 146.453 o
468.676 154.246 o
478.821 406.358 o
488.966 407.421 o
499.112 408.509 o
509.257 409.552 o
519.403 410.586 o
519.403 410.586 o
grestore
1.500 setlinewidth
1 setlinejoin
[5.55 2.4] 0 setdash
0.031 0.416 0.416 setrgbcolor
gsave
446.4 360.36 93.294 66.606 clipbox
519.402841 407.178729 m
509.257386 405.961355 l
499.111932 404.878933 l
488.966477 403.647502 l
478.821023 402.491981 l
468.675568 401.356141 l
458.530114 400.020685 l
448.384659 398.794877 l
438.239205 397.484724 l
428.09375 396.098661 l
417.948295 394.71541 l
407.802841 393.495225 l
397.657386 391.917981 l
387.511932 390.503804 l
377.366477 388.864707 l
367.221023 387.383054 l
357.075568 385.665236 l
346.930114 383.981156 l
336.784659 382.178993 l
326.639205 380.480855 l
316.49375 378.40879 l
306.348295 376.443561 l
296.202841 374.244979 l
286.057386 372.108249 l
275.911932 369.63133 l
265.766477 366.808598 l
255.621023 364.134874 l
245.475568 361.053485 l
235.330114 357.277659 l
225.184659 353.186947 l
215.039205 347.867615 l
204.89375 340.138839 l
194.748295 96.062473 l
184.602841 94.760754 l
174.457386 93.577119 l
164.311932 92.337253 l
154.166477 91.13956 l
144.021023 89.94749 l
133.875568 88.657018 l
123.730114 87.63926 l
113.584659 86.497796 l
123.730114 87.540858 l
133.875568 88.777912 l
144.021023 89.922187 l
154.166477 91.069273 l
164.311932 92.300704 l
174.457386 93.551815 l
184.602841 94.831042 l
194.748295 96.149629 l
204.89375 97.490708 l
215.039205 98.812106 l
225.184659 100.271267 l
235.330114 101.696691 l
245.475568 103.096811 l
255.621023 104.727473 l
265.766477 106.166954 l
275.911932 107.822919 l
286.057386 109.656009 l
296.202841 111.272613 l
306.348295 113.209727 l
316.49375 115.177767 l
326.639205 117.224529 l
336.784659 119.350013 l
346.930114 121.573899 l
357.075568 124.112671 l
367.221023 126.58959 l
377.366477 129.457306 l
387.511932 132.665212 l
397.657386 136.247045 l
407.802841 140.430537 l
417.948295 145.550253 l
428.09375 153.177816 l
438.239205 397.448175 l
448.384659 398.822992 l
458.530114 400.003816 l
468.675568 401.305534 l
478.821023 402.444186 l
488.966477 403.714978 l
499.111932 404.867687 l
509.257386 406.031642 l
519.402841 407.077515 l
519.402841 407.077515 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
446.4 360.36 93.294 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-5 -5 m
5 -5 l
5 5 l
-5 5 l
cl

gsave
grestore
stroke
grestore
} bind def
519.403 407.179 o
509.257 405.961 o
499.112 404.879 o
488.966 403.648 o
478.821 402.492 o
468.676 401.356 o
458.53 400.021 o
448.385 398.795 o
438.239 397.485 o
428.094 396.099 o
417.948 394.715 o
407.803 393.495 o
397.657 391.918 o
387.512 390.504 o
377.366 388.865 o
367.221 387.383 o
357.076 385.665 o
346.93 383.981 o
336.785 382.179 o
326.639 380.481 o
316.494 378.409 o
306.348 376.444 o
296.203 374.245 o
286.057 372.108 o
275.912 369.631 o
265.766 366.809 o
255.621 364.135 o
245.476 361.053 o
235.33 357.278 o
225.185 353.187 o
215.039 347.868 o
204.894 340.139 o
194.748 96.0625 o
184.603 94.7608 o
174.457 93.5771 o
164.312 92.3373 o
154.166 91.1396 o
144.021 89.9475 o
133.876 88.657 o
123.73 87.6393 o
113.585 86.4978 o
123.73 87.5409 o
133.876 88.7779 o
144.021 89.9222 o
154.166 91.0693 o
164.312 92.3007 o
174.457 93.5518 o
184.603 94.831 o
194.748 96.1496 o
204.894 97.4907 o
215.039 98.8121 o
225.185 100.271 o
235.33 101.697 o
245.476 103.097 o
255.621 104.727 o
265.766 106.167 o
275.912 107.823 o
286.057 109.656 o
296.203 111.273 o
306.348 113.21 o
316.494 115.178 o
326.639 117.225 o
336.785 119.35 o
346.93 121.574 o
357.076 124.113 o
367.221 126.59 o
377.366 129.457 o
387.512 132.665 o
397.657 136.247 o
407.803 140.431 o
417.948 145.55 o
428.094 153.178 o
438.239 397.448 o
448.385 398.823 o
458.53 400.004 o
468.676 401.306 o
478.821 402.444 o
488.966 403.715 o
499.112 404.868 o
509.257 406.032 o
519.403 407.078 o
519.403 407.078 o
grestore
1.500 setlinewidth
1 setlinejoin
[1.5 2.475] 0 setdash
0.031 0.659 0.659 setrgbcolor
gsave
446.4 360.36 93.294 66.606 clipbox
519.402841 404.617465 m
509.257386 403.478813 l
499.111932 402.177095 l
488.966477 400.906303 l
478.821023 399.638323 l
468.675568 398.409703 l
458.530114 397.074247 l
448.384659 395.738791 l
438.239205 394.285253 l
428.09375 392.826091 l
417.948295 391.316323 l
407.802841 389.775629 l
397.657386 388.187139 l
387.511932 386.63801 l
377.366477 384.97361 l
367.221023 383.132087 l
357.075568 381.478933 l
346.930114 379.457474 l
336.784659 377.483811 l
326.639205 375.434238 l
316.49375 373.140065 l
306.348295 370.618162 l
296.202841 368.293062 l
286.057386 365.366305 l
275.911932 362.332712 l
265.766477 359.003912 l
255.621023 354.795117 l
245.475568 350.254567 l
235.330114 343.757222 l
225.184659 103.723773 l
215.039205 102.194324 l
204.89375 100.701425 l
194.748295 99.346288 l
184.602841 97.926488 l
174.457386 96.655696 l
164.311932 95.182477 l
154.166477 93.847021 l
144.021023 92.716804 l
133.875568 91.451635 l
123.730114 90.324229 l
113.584659 88.949412 l
123.730114 90.208958 l
133.875568 91.462881 l
144.021023 92.753353 l
154.166477 94.060694 l
164.311932 95.275256 l
174.457386 96.551671 l
184.602841 98.008021 l
194.748295 99.323797 l
204.89375 100.5974 l
215.039205 102.28148 l
225.184659 103.844666 l
235.330114 105.236352 l
245.475568 106.996343 l
255.621023 108.559529 l
265.766477 110.41511 l
275.911932 112.1751 l
286.057386 114.084099 l
296.202841 116.074632 l
306.348295 118.273214 l
316.49375 120.525214 l
326.639205 122.861559 l
336.784659 125.428446 l
346.930114 128.287728 l
357.075568 131.189182 l
367.221023 134.726031 l
377.366477 138.496234 l
387.511932 143.492245 l
397.657386 150.180771 l
407.802841 389.952752 l
417.948295 391.352873 l
428.09375 392.938551 l
438.239205 394.293687 l
448.384659 395.60384 l
458.530114 397.029263 l
468.675568 398.446252 l
478.821023 399.649569 l
488.966477 401.055312 l
499.111932 402.129299 l
509.257386 403.293255 l
519.402841 404.586538 l
519.402841 404.586538 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
446.4 360.36 93.294 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -5 m
1.326016 -5 2.597899 -4.473168 3.535534 -3.535534 c
4.473168 -2.597899 5 -1.326016 5 0 c
5 1.326016 4.473168 2.597899 3.535534 3.535534 c
2.597899 4.473168 1.326016 5 0 5 c
-1.326016 5 -2.597899 4.473168 -3.535534 3.535534 c
-4.473168 2.597899 -5 1.326016 -5 0 c
-5 -1.326016 -4.473168 -2.597899 -3.535534 -3.535534 c
-2.597899 -4.473168 -1.326016 -5 0 -5 c
cl

gsave
grestore
stroke
grestore
} bind def
519.403 404.617 o
509.257 403.479 o
499.112 402.177 o
488.966 400.906 o
478.821 399.638 o
468.676 398.41 o
458.53 397.074 o
448.385 395.739 o
438.239 394.285 o
428.094 392.826 o
417.948 391.316 o
407.803 389.776 o
397.657 388.187 o
387.512 386.638 o
377.366 384.974 o
367.221 383.132 o
357.076 381.479 o
346.93 379.457 o
336.785 377.484 o
326.639 375.434 o
316.494 373.14 o
306.348 370.618 o
296.203 368.293 o
286.057 365.366 o
275.912 362.333 o
265.766 359.004 o
255.621 354.795 o
245.476 350.255 o
235.33 343.757 o
225.185 103.724 o
215.039 102.194 o
204.894 100.701 o
194.748 99.3463 o
184.603 97.9265 o
174.457 96.6557 o
164.312 95.1825 o
154.166 93.847 o
144.021 92.7168 o
133.876 91.4516 o
123.73 90.3242 o
113.585 88.9494 o
123.73 90.209 o
133.876 91.4629 o
144.021 92.7534 o
154.166 94.0607 o
164.312 95.2753 o
174.457 96.5517 o
184.603 98.008 o
194.748 99.3238 o
204.894 100.597 o
215.039 102.281 o
225.185 103.845 o
235.33 105.236 o
245.476 106.996 o
255.621 108.56 o
265.766 110.415 o
275.912 112.175 o
286.057 114.084 o
296.203 116.075 o
306.348 118.273 o
316.494 120.525 o
326.639 122.862 o
336.785 125.428 o
346.93 128.288 o
357.076 131.189 o
367.221 134.726 o
377.366 138.496 o
387.512 143.492 o
397.657 150.181 o
407.803 389.953 o
417.948 391.353 o
428.094 392.939 o
438.239 394.294 o
448.385 395.604 o
458.53 397.029 o
468.676 398.446 o
478.821 399.65 o
488.966 401.055 o
499.112 402.129 o
509.257 403.293 o
519.403 404.587 o
519.403 404.587 o
grestore
1.500 setlinewidth
[9.6 2.4 1.5 2.4] 0 setdash
0.031 0.659 0.255 setrgbcolor
gsave
446.4 360.36 93.294 66.606 clipbox
519.402841 401.716011 m
509.257386 400.439596 l
499.111932 399.070402 l
488.966477 397.942996 l
478.821023 396.458532 l
468.675568 394.99937 l
458.530114 393.590816 l
448.384659 392.269417 l
438.239205 390.706231 l
428.09375 389.202086 l
417.948295 387.52644 l
407.802841 385.833925 l
397.657386 383.972721 l
387.511932 382.17337 l
377.366477 380.269994 l
367.221023 378.330068 l
357.075568 376.162412 l
346.930114 374.034117 l
336.784659 371.63592 l
326.639205 369.02686 l
316.49375 366.35876 l
306.348295 363.266125 l
296.202841 359.976686 l
286.057386 356.043417 l
275.911932 351.800884 l
265.766477 346.054206 l
255.621023 337.670354 l
245.475568 111.427245 l
235.330114 109.501377 l
225.184659 107.800428 l
215.039205 106.186634 l
204.89375 104.592522 l
194.748295 102.967483 l
184.602841 101.457715 l
174.457386 99.942324 l
164.311932 98.469105 l
154.166477 97.057739 l
144.021023 95.862857 l
133.875568 94.423376 l
123.730114 93.023256 l
113.584659 91.95208 l
123.730114 93.132904 l
133.875568 94.294048 l
144.021023 95.798193 l
154.166477 97.038058 l
164.311932 98.474728 l
174.457386 99.835487 l
184.602841 101.31714 l
194.748295 102.944991 l
204.89375 104.508177 l
215.039205 106.192257 l
225.184659 107.870715 l
235.330114 109.546361 l
245.475568 111.390696 l
255.621023 113.305318 l
265.766477 115.329588 l
275.911932 117.280759 l
286.057386 119.569309 l
296.202841 122.040605 l
306.348295 124.323532 l
316.49375 127.21374 l
326.639205 130.210784 l
336.784659 133.458051 l
346.930114 137.281672 l
357.075568 141.667591 l
367.221023 147.445195 l
377.366477 155.744702 l
387.511932 382.38142 l
397.657386 384.009271 l
407.802841 385.831114 l
417.948295 387.467399 l
428.09375 389.15429 l
438.239205 390.838371 l
448.384659 392.19913 l
458.530114 393.551455 l
468.675568 395.134322 l
478.821023 396.382622 l
488.966477 397.838971 l
499.111932 399.168804 l
509.257386 400.50426 l
519.402841 401.682273 l
519.402841 401.682273 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
446.4 360.36 93.294 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

5 -0 m
-5 5 l
-5 -5 l
cl

gsave
grestore
stroke
grestore
} bind def
519.403 401.716 o
509.257 400.44 o
499.112 399.07 o
488.966 397.943 o
478.821 396.459 o
468.676 394.999 o
458.53 393.591 o
448.385 392.269 o
438.239 390.706 o
428.094 389.202 o
417.948 387.526 o
407.803 385.834 o
397.657 383.973 o
387.512 382.173 o
377.366 380.27 o
367.221 378.33 o
357.076 376.162 o
346.93 374.034 o
336.785 371.636 o
326.639 369.027 o
316.494 366.359 o
306.348 363.266 o
296.203 359.977 o
286.057 356.043 o
275.912 351.801 o
265.766 346.054 o
255.621 337.67 o
245.476 111.427 o
235.33 109.501 o
225.185 107.8 o
215.039 106.187 o
204.894 104.593 o
194.748 102.967 o
184.603 101.458 o
174.457 99.9423 o
164.312 98.4691 o
154.166 97.0577 o
144.021 95.8629 o
133.876 94.4234 o
123.73 93.0233 o
113.585 91.9521 o
123.73 93.1329 o
133.876 94.294 o
144.021 95.7982 o
154.166 97.0381 o
164.312 98.4747 o
174.457 99.8355 o
184.603 101.317 o
194.748 102.945 o
204.894 104.508 o
215.039 106.192 o
225.185 107.871 o
235.33 109.546 o
245.476 111.391 o
255.621 113.305 o
265.766 115.33 o
275.912 117.281 o
286.057 119.569 o
296.203 122.041 o
306.348 124.324 o
316.494 127.214 o
326.639 130.211 o
336.785 133.458 o
346.93 137.282 o
357.076 141.668 o
367.221 147.445 o
377.366 155.745 o
387.512 382.381 o
397.657 384.009 o
407.803 385.831 o
417.948 387.467 o
428.094 389.154 o
438.239 390.838 o
448.385 392.199 o
458.53 393.551 o
468.676 395.134 o
478.821 396.383 o
488.966 397.839 o
499.112 399.169 o
509.257 400.504 o
519.403 401.682 o
519.403 401.682 o
grestore
0.500 setlinewidth
1 setlinejoin
[1.85 0.8] 0 setdash
0.000 0.000 1.000 setrgbcolor
gsave
446.4 360.36 93.294 66.606 clipbox
316.49375 66.60625 m
316.49375 426.96625 l
stroke
grestore
gsave
446.4 360.36 93.294 66.606 clipbox
93.29375 246.80593 m
539.69375 246.80593 l
stroke
grestore
2.500 setlinewidth
0 setlinejoin
2 setlinecap
[] 0 setdash
0.000 setgray
gsave
93.29375 66.60625 m
93.29375 426.96625 l
stroke
grestore
gsave
539.69375 66.60625 m
539.69375 426.96625 l
stroke
grestore
gsave
93.29375 66.60625 m
539.69375 66.60625 l
stroke
grestore
gsave
93.29375 426.96625 m
539.69375 426.96625 l
stroke
grestore
1.500 setlinewidth
1 setlinejoin
0.031 0.235 0.235 setrgbcolor
gsave
263.775 291.34875 m
283.775 291.34875 l
303.775 291.34875 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
0 setlinecap
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

0 5 m
-5 -5 l
5 -5 l
cl

gsave
0.031 0.235 0.235 setrgbcolor
fill
grestore
stroke
grestore
} bind def
283.775 291.349 o
grestore
0.000 setgray
/TimesNewRomanPSMT 20.000 selectfont
gsave

319.775 284.349 translate
0 rotate
0 0 m /one glyphshow
10 0 m /zero glyphshow
20 0 m /zero glyphshow
30 0 m /space glyphshow
35 0 m /K glyphshow
grestore
1.500 setlinewidth
1 setlinejoin
[5.55 2.4] 0 setdash
0.031 0.416 0.416 setrgbcolor
gsave
263.775 263.0675 m
283.775 263.0675 l
303.775 263.0675 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-5 -5 m
5 -5 l
5 5 l
-5 5 l
cl

gsave
grestore
stroke
grestore
} bind def
283.775 263.067 o
grestore
0.000 setgray
/TimesNewRomanPSMT 20.000 selectfont
gsave

319.775 256.067 translate
0 rotate
0 0 m /four glyphshow
10 0 m /zero glyphshow
20 0 m /zero glyphshow
30 0 m /space glyphshow
35 0 m /K glyphshow
grestore
1.500 setlinewidth
1 setlinejoin
[1.5 2.475] 0 setdash
0.031 0.659 0.659 setrgbcolor
gsave
263.775 234.78625 m
283.775 234.78625 l
303.775 234.78625 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -5 m
1.326016 -5 2.597899 -4.473168 3.535534 -3.535534 c
4.473168 -2.597899 5 -1.326016 5 0 c
5 1.326016 4.473168 2.597899 3.535534 3.535534 c
2.597899 4.473168 1.326016 5 0 5 c
-1.326016 5 -2.597899 4.473168 -3.535534 3.535534 c
-4.473168 2.597899 -5 1.326016 -5 0 c
-5 -1.326016 -4.473168 -2.597899 -3.535534 -3.535534 c
-2.597899 -4.473168 -1.326016 -5 0 -5 c
cl

gsave
grestore
stroke
grestore
} bind def
283.775 234.786 o
grestore
0.000 setgray
/TimesNewRomanPSMT 20.000 selectfont
gsave

319.775 227.786 translate
0 rotate
0 0 m /six glyphshow
10 0 m /zero glyphshow
20 0 m /zero glyphshow
30 0 m /space glyphshow
35 0 m /K glyphshow
grestore
1.500 setlinewidth
[9.6 2.4 1.5 2.4] 0 setdash
0.031 0.659 0.255 setrgbcolor
gsave
263.775 206.505 m
283.775 206.505 l
303.775 206.505 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

5 -0 m
-5 5 l
-5 -5 l
cl

gsave
grestore
stroke
grestore
} bind def
283.775 206.505 o
grestore
0.000 setgray
/TimesNewRomanPSMT 20.000 selectfont
gsave

319.775 199.505 translate
0 rotate
0 0 m /eight glyphshow
10 0 m /zero glyphshow
20 0 m /zero glyphshow
30 0 m /space glyphshow
35 0 m /K glyphshow
grestore

end
showpage
