%!PS-Adobe-3.0 EPSF-3.0
%%BoundingBox: 0 0 576 468
%%LanguageLevel: 2
%%Creator: OriginLab Corporation
%%Title: (C:\Users\<USER>\OneDrive - IIT Delhi\Documents\OriginLab\User Files\energy.eps)
%%CreationDate: Sat Mar 16 15:18:14 2024
%%EndComments

/OriginLab::PSL1 128 dict dup begin def
/Save0 save def
%%BeginResource: procset OriginLab::PSL1
/wd{1 index where{pop pop pop}{bind def}ifelse}bind def
/setcmykcolor{1 sub 4 1 roll 3{3 index add neg dup 0 lt{pop 0}if 3 1 roll}repeat setrgbcolor pop}wd
/selectfont{dup type/arraytype eq{exch findfont exch makefont setfont}{exch findfont exch scalefont setfont}ifelse}wd
/xshow{1 index length 0 le{}{currentpoint exch 3 index length 1 sub 0 exch 1 exch{dup 5 index exch 1 getinterval show 3 index exch get add 2 copy exch moveto}for pop pop}ifelse pop pop}wd
/grayimage/image load def/rgbtogray{cvx exec dup length 3 idiv 1 sub 0 exch 1 exch{dup 3 mul dup 3 index exch get 255 div .3 mul exch
 1 add dup 4 index exch get 255 div .59 mul exch 1 add 4 index exch get 255 div .11 mul add add
 255 mul dup 255 gt{255}if round cvi 2 index 3 1 roll put}for dup length 3 idiv 0 exch getinterval}bind def
/colorimage{0 setgray pop pop cvlit/rgbtogray cvx 2 packedarray cvx grayimage}wd
/bd{bind def}bind def/ld{load def}bd/ed{exch def}bd/xd{cvx def}bd
/np/newpath ld/a/arc ld/m/moveto ld/l/lineto ld/c/curveto ld/rm/rmoveto ld/rl/rlineto ld/rc/rcurveto ld/cp/closepath ld/cpt/currentpoint ld
/tr/translate ld/sc/scale ld/ro/rotate ld/t/transform ld/it/itransform ld/dt/dtransform ld/idt/idtransform ld
/mtx/matrix ld/cmtx/currentmatrix ld/dmtx/defaultmatrix ld/smtx/setmatrix ld/catmtx/concatmatrix ld
/sw/setlinewidth ld/scap/setlinecap ld/sj/setlinejoin ld/sm/setmiterlimit ld/sd/setdash ld/rgb/setrgbcolor ld/crgb/currentrgbcolor ld/cmyk/setcmykcolor ld/gs/gsave ld/gr/grestore ld
/st/stroke ld/fi/fill ld/eofi/eofill ld/cl/clip ld/eocl/eoclip ld
/glyphshowEmulation{matrix currentmatrix exch currentfont dup/FontMatrix get concat currentpoint translate
 dup/Metrics get 2 index get 0 4 2 roll gsave/CharProcs get exch 2 copy known not{pop/.notdef}if
 get exec fill grestore moveto setmatrix} bind def
/isWord2010 false def
currentsystemparams /PrinterName known{currentsystemparams /PrinterName get (Access Softek) eq {/isWord2010 true def} if } if
isWord2010{/glyphshowCommand/glyphshowEmulation ld}{/glyphshowCommand/glyphshow ld}ifelse
/df/definefont ld/ff/findfont ld/sf/setfont ld/scf/scalefont ld/self/selectfont ld/cf/currentfont ld
/s/show ld/xs/xshow ld/gls/glyphshowCommand ld/xgls{cpt 3 1 roll add exch 3 -1 roll gls m}bd
/xglsh{cpt 3 1 roll add exch 3 -1 roll glyphshowCommand m}bd
/cd/currentdict ld/i/index ld
/DrawBox{4 2 roll m 1 index 0 rl 0 exch rl neg 0 rl cp}bd
/plus.re { 6 { 3 index } repeat moveto 0 exch rlineto 0 rlineto pop pop neg 0 exch rlineto pop lineto closepath } def
/DrawBcc{4 2 roll m 2 copy 0 lt exch 0 lt xor{dup 0 exch rl exch 0 rl neg 0 exch rl}{exch dup 0 rl exch 0 exch rl neg 0 rl}ifelse cp}bd
/rectline{dup type/arraytype eq{dup length 0 exch 4 exch 4 sub{0 1 3{3 copy add get 4 1 roll pop}for pop 5 1 roll DrawBox}for pop}{DrawBox}ifelse}bd
/rectpath{dup type/arraytype eq{dup length 0 exch 4 exch 4 sub{0 1 3{3 copy add get 4 1 roll pop}for pop 5 1 roll DrawBcc}for pop}{DrawBcc}ifelse}bd
/pathproc{[{/m cvx}{/l cvx}{/c cvx}{/cp cvx}pathforall]cvx}bd
/L1img/image ld/L1idx[{0}
 {dup 7 and 3 1 roll -3 bitshift get exch 7 sub 1 mul bitshift  1 and}bind 1 index dup
 {dup 1 and 3 1 roll -1 bitshift get exch 1 sub 4 mul bitshift 15 and}bind 1 index dup dup
 {get}bind]def
/image{dup type/dicttype eq {dup/ImgCS get 0 get/Indexed eq}{false}ifelse
 {begin Width Height 8 ImageMatrix {rgb_string rgb_lookup
 cd/DataSource get dup type/filetype eq
 {mystring readstring pop}{exec}ifelse
 dup length 0 ne
 {L1idx BitsPerComponent get 0 1 Width 1 sub
 {3 copy exch exec exch 3 mul exch
 3 mul dup 5 index exch get exch 1 add dup 6 index exch get exch 1 add 6 index exch get
 7 index exch 4 index 2 add exch put 6 index exch 3 index 1 add exch put 5 index exch 2 index exch put pop
 }for pop pop pop }{pop pop pop ()}ifelse }false 3 colorimage end} {dup type/dicttype eq
 {{dup mark exch L1img pop pop}stopped{cleartomark begin Width Height BitsPerComponent ImageMatrix
 {cd/DataSource get dup type/filetype eq {mystring readstring pop}{exec}ifelse
 }false 3 colorimage end} if}{L1img}ifelse}ifelse
 }bd
/L1imgmask/imagemask ld/imagemask{dup type/dicttype eq
 {{dup mark exch L1imgmask pop pop}stopped {cleartomark begin Width Height polarityflag ImageMatrix
 {cd/DataSource get dup type/filetype eq {mystring readstring pop}{exec}ifelse
 }L1imgmask end}if}{L1imgmask}ifelse
 }bd
% Gradient function
/GradientStep 1 150 div def
/GradientStartRGB { /R1 exch def /G1 exch def /B1 exch def } def
/GradientEndRGB { /R2 exch def /G2 exch def /B2 exch def
/GradientSteps GradientLength GradientStep div def
/GradientRStep R2 R1 sub GradientSteps div def
/GradientGStep G2 G1 sub GradientSteps div def
/GradientBStep B2 B1 sub GradientSteps div def
} def
/GradientStepRGB { /R1 R1 GradientRStep add def
/G1 G1 GradientGStep add def
/B1 B1 GradientBStep add def
R1 G1 B1 setrgbcolor
} def
%%EndResource
/rectclip{np rectpath clip np}wd
/rectfill{gs np rectpath fi gr}wd
/rectstroke{gs np dup type/arraytype eq{dup length 6 eq}{false}ifelse{1 index type/arraytype eq{exch}{5 1 roll}ifelse rectline concat st}{rectline st}ifelse gr}wd
%%BeginResource: procset OriginLab::Image
/OriginLab::Image 32 dict dup begin
/BinProc{[currentfile mystring /readstring cvx /pop cvx] cvx bind}bd
/HexProc{[currentfile mystring /readhexstring cvx /pop cvx] cvx bind}bd
/Ascii85Proc{currentfile /ASCII85Decode filter}bd
/LZWA85{currentfile /ASCII85Decode filter /LZWDecode filter}bd
/ImgProcArray[/BinProc /HexProc /Ascii85Proc /LZWA85]def
/ImgDict{/ImgDict 16 dict def ImgDict dup begin /ImageType 1 def /MultipleDataSource false def end}bd
/L2Dec{{[1 0]}{[0 1]}ifelse}bd
/BegImg{ /Img_sv save def
 /ImgDir ed ImgProcArray exch get /ImgProc exch ld
 dup /polarityflag ed L2Dec /polarity ed /smoothflag ed
 tr /dx 2 index def /dy 1 index abs def sc
 string /mystring ed /bpc ed /ih ed /iw ed
 }bd
/EndImg {Img_sv restore}bd
/DoXImg{ /rgb_lookup ed /hival ed ImgDict dup begin
 [/Indexed /DeviceRGB hival rgb_lookup] /L1img where{pop/ImgCS ed}{setcolorspace}ifelse
 /rgb_string iw 3 mul string def
 /Width iw def
 /Height ih def
 /Decode [0 1 bpc{2 mul}repeat 1 sub] def
 /ImageMatrix
  ImgDir 0 eq{[iw 0 0 ih 0 0]}if
  ImgDir 1 eq{[iw neg 0 0 ih iw 0]}if
  ImgDir 2 eq{[iw 0 0 ih neg 0 ih]}if
  ImgDir 3 eq{[iw neg 0 0 ih neg iw ih]}if
  def
 /DataSource ImgProc def
 /BitsPerComponent bpc def
 /Interpolate smoothflag def
 end image}bd
/DoImg{ ImgDict dup begin
 [/DeviceRGB] /L1img where{pop/ImgCS ed}{setcolorspace}ifelse
 /rgb_string iw 3 mul string def
 /Width iw def
 /Height ih def
 /Decode [0 1 0 1 0 1] def
 /ImageMatrix
  ImgDir 0 eq{[iw 0 0 ih 0 0]}if
  ImgDir 1 eq{[iw neg 0 0 ih iw 0]}if
  ImgDir 2 eq{[iw 0 0 ih neg 0 ih]}if
  ImgDir 3 eq{[iw neg 0 0 ih neg iw ih]}if
  def
 /DataSource ImgProc def
 /BitsPerComponent 8 def
 /Interpolate smoothflag def
 end image}bd
end def
%%EndResource
/initmtx mtx cmtx def
%%BeginFont: /MC1_ArialBold
12 dict begin
/FontInfo 8 dict dup begin
/ItalicAngle 0 def
/UnderlinePosition -217.00 def
/UnderlineThickness 215.00 def
/StrikeoutPosition 530.00 def
/StrikeoutThickness 102.00 def
/isFixedPitch false def
end readonly def
/FontName /MC1_ArialBold def
/FontType 3 def
/FontMatrix [1 2048 div 0 0 1 2048 div 0 0] def
/FontBBox [-1286 -771 4096 2162] def
/Encoding 256 array def 0 1 255{Encoding exch/.notdef put}for
Encoding 32/s32 put
Encoding 45/s45 put
Encoding 46/s46 put
Encoding 48/s48 put
Encoding 49/s49 put
Encoding 50/s50 put
Encoding 52/s52 put
Encoding 53/s53 put
Encoding 54/s54 put
Encoding 57/s57 put
Encoding 68/s68 put
Encoding 77/s77 put
Encoding 82/s82 put
Encoding 105/s105 put
Encoding 110/s110 put
Encoding 111/s111 put
Encoding 114/s114 put
Encoding 115/s115 put
Encoding 116/s116 put
/BoundingBoxes 20 dict def
BoundingBoxes begin
/s32 [0 -434 569 1854] def
/s45 [0 -434 682 1854] def
/s46 [0 -434 569 1854] def
/s48 [0 -434 1139 1854] def
/s49 [0 -434 1139 1854] def
/s50 [0 -434 1139 1854] def
/s52 [0 -434 1139 1854] def
/s53 [0 -434 1139 1854] def
/s54 [0 -434 1139 1854] def
/s57 [0 -434 1139 1854] def
/s68 [0 -434 1479 1854] def
/s77 [0 -434 1706 1854] def
/s82 [0 -434 1479 1854] def
/s105 [0 -434 569 1854] def
/s110 [0 -434 1251 1854] def
/s111 [0 -434 1251 1854] def
/s114 [0 -434 797 1854] def
/s115 [0 -434 1139 1854] def
/s116 [0 -434 682 1854] def
end
/Metrics 20 dict def
 Metrics begin
/s32 569 def
/s45 682 def
/s46 569 def
/s48 1139 def
/s49 1139 def
/s50 1139 def
/s52 1139 def
/s53 1139 def
/s54 1139 def
/s57 1139 def
/s68 1479 def
/s77 1706 def
/s82 1479 def
/s105 569 def
/s110 1251 def
/s111 1251 def
/s114 797 def
/s115 1139 def
/s116 682 def
end
/CharProcs 20 dict def
CharProcs begin
/.notdef {} def
/s45 {
115 391 m
115 672 115 391 115 672 c
667 672 115 672 667 672 c
667 391 667 672 667 391 c
115 391 667 391 115 391 c
closepath } bind def
/s46 {
147 0 m
147 281 147 0 147 281 c
428 281 147 281 428 281 c
428 0 428 281 428 0 c
147 0 428 0 147 0 c
closepath } bind def
/s48 {
562 1472 m
704 1472 815 1421.33 895 1320 c
990.33 1200 1038 1001 1038 723 c
1038 445.67 990 246.33 894 125 c
814.67 25 704 -25 562 -25 c
419.33 -25 304.33 29.83 217 139.5 c
129.67 249.17 86 444.67 86 726 c
86 1002 134 1200.67 230 1322 c
309.33 1422 420 1472 562 1472 c
closepath 562 1239 m
528 1239 497.67 1228.17 471 1206.5 c
444.33 1184.83 423.67 1146 409 1090 c
389.67 1017.33 380 895 380 723 c
380 551 388.67 432.83 406 368.5 c
423.33 304.17 445.17 261.33 471.5 240 c
497.83 218.67 528 208 562 208 c
596 208 626.33 218.83 653 240.5 c
679.67 262.17 700.33 301 715 357 c
734.33 429 744 551 744 723 c
744 895 735.33 1013.17 718 1077.5 c
700.67 1141.83 678.83 1184.83 652.5 1206.5 c
626.17 1228.17 596 1239 562 1239 c
closepath } bind def
/s49 {
806 0 m
525 0 806 0 525 0 c
525 1059 525 0 525 1059 c
422.33 963 301.33 892 162 846 c
162 1101 162 846 162 1101 c
235.33 1125 315 1170.5 401 1237.5 c
487 1304.5 546 1382.67 578 1472 c
806 1472 578 1472 806 1472 c
806 0 806 1472 806 0 c
closepath } bind def
/s50 {
1036 261 m
1036 0 1036 261 1036 0 c
51 0 1036 0 51 0 c
61.67 98.67 93.67 192.17 147 280.5 c
200.33 368.83 305.67 486 463 632 c
589.67 750 667.33 830 696 872 c
734.67 930 754 987.33 754 1044 c
754 1106.67 737.17 1154.83 703.5 1188.5 c
669.83 1222.17 623.33 1239 564 1239 c
505.33 1239 458.67 1221.33 424 1186 c
389.33 1150.67 369.33 1092 364 1010 c
84 1038 364 1010 84 1038 c
100.67 1192.67 153 1303.67 241 1371 c
329 1438.33 439 1472 571 1472 c
715.67 1472 829.33 1433 912 1355 c
994.67 1277 1036 1180 1036 1064 c
1036 998 1024.17 935.17 1000.5 875.5 c
976.83 815.83 939.33 753.33 888 688 c
854 644.67 792.67 582.33 704 501 c
615.33 419.67 559.17 365.67 535.5 339 c
511.83 312.33 492.67 286.33 478 261 c
1036 261 478 261 1036 261 c
closepath } bind def
/s52 {
638 0 m
638 295 638 0 638 295 c
38 295 638 295 38 295 c
38 541 38 295 38 541 c
674 1472 38 541 674 1472 c
910 1472 674 1472 910 1472 c
910 542 910 1472 910 542 c
1092 542 910 542 1092 542 c
1092 295 1092 542 1092 295 c
910 295 1092 295 910 295 c
910 0 910 295 910 0 c
638 0 910 0 638 0 c
closepath 638 542 m
638 1043 638 542 638 1043 c
301 542 638 1043 301 542 c
638 542 301 542 638 542 c
closepath } bind def
/s53 {
91 377 m
371 406 91 377 371 406 c
379 342.67 402.67 292.5 442 255.5 c
481.33 218.5 526.67 200 578 200 c
636.67 200 686.33 223.83 727 271.5 c
767.67 319.17 788 391 788 487 c
788 577 767.83 644.5 727.5 689.5 c
687.17 734.5 634.67 757 570 757 c
489.33 757 417 721.33 353 650 c
125 683 353 650 125 683 c
269 1446 125 683 269 1446 c
1012 1446 269 1446 1012 1446 c
1012 1183 1012 1446 1012 1183 c
482 1183 1012 1183 482 1183 c
438 934 482 1183 438 934 c
500.67 965.33 564.67 981 630 981 c
754.67 981 860.33 935.67 947 845 c
1033.67 754.33 1077 636.67 1077 492 c
1077 371.33 1042 263.67 972 169 c
876.67 39.67 744.33 -25 575 -25 c
439.67 -25 329.33 11.33 244 84 c
158.67 156.67 107.67 254.33 91 377 c
closepath } bind def
/s54 {
1039 1107 m
767 1077 1039 1107 767 1077 c
760.33 1133 743 1174.33 715 1201 c
687 1227.67 650.67 1241 606 1241 c
546.67 1241 496.5 1214.33 455.5 1161 c
414.5 1107.67 388.67 996.67 378 828 c
448 910.67 535 952 639 952 c
756.33 952 856.83 907.33 940.5 818 c
1024.17 728.67 1066 613.33 1066 472 c
1066 322 1022 201.67 934 111 c
846 20.33 733 -25 595 -25 c
447 -25 325.33 32.5 230 147.5 c
134.67 262.5 87 451 87 713 c
87 981.67 136.67 1175.33 236 1294 c
335.33 1412.67 464.33 1472 623 1472 c
734.33 1472 826.5 1440.83 899.5 1378.5 c
972.5 1316.17 1019 1225.67 1039 1107 c
closepath 402 494 m
402 402.67 423 332.17 465 282.5 c
507 232.83 555 208 609 208 c
661 208 704.33 228.33 739 269 c
773.67 309.67 791 376.33 791 469 c
791 564.33 772.33 634.17 735 678.5 c
697.67 722.83 651 745 595 745 c
541 745 495.33 723.83 458 681.5 c
420.67 639.17 402 576.67 402 494 c
closepath } bind def
/s57 {
93 339 m
365 369 93 339 365 369 c
371.67 313.67 389 272.67 417 246 c
445 219.33 482 206 528 206 c
586 206 635.33 232.67 676 286 c
716.67 339.33 742.67 450 754 618 c
683.33 536 595 495 489 495 c
373.67 495 274.17 539.5 190.5 628.5 c
106.83 717.5 65 833.33 65 976 c
65 1124.67 109.17 1244.5 197.5 1335.5 c
285.83 1426.5 398.33 1472 535 1472 c
683.67 1472 805.67 1414.5 901 1299.5 c
996.33 1184.5 1044 995.33 1044 732 c
1044 464 994.33 270.67 895 152 c
795.67 33.33 666.33 -26 507 -26 c
392.33 -26 299.67 4.5 229 65.5 c
158.33 126.5 113 217.67 93 339 c
closepath 729 953 m
729 1043.67 708.17 1114 666.5 1164 c
624.83 1214 576.67 1239 522 1239 c
470 1239 426.83 1218.5 392.5 1177.5 c
358.17 1136.5 341 1069.33 341 976 c
341 881.33 359.67 811.83 397 767.5 c
434.33 723.17 481 701 537 701 c
591 701 636.5 722.33 673.5 765 c
710.5 807.67 729 870.33 729 953 c
closepath } bind def
/s68 {
148 1466 m
689 1466 148 1466 689 1466 c
811 1466 904 1456.67 968 1438 c
1054 1412.67 1127.67 1367.67 1189 1303 c
1250.33 1238.33 1297 1159.17 1329 1065.5 c
1361 971.83 1377 856.33 1377 719 c
1377 598.33 1362 494.33 1332 407 c
1295.33 300.33 1243 214 1175 148 c
1123.67 98 1054.33 59 967 31 c
901.67 10.33 814.33 0 705 0 c
148 0 705 0 148 0 c
148 1466 148 0 148 1466 c
closepath 444 1218 m
444 247 444 1218 444 247 c
665 247 444 247 665 247 c
747.67 247 807.33 251.67 844 261 c
892 273 931.83 293.33 963.5 322 c
995.17 350.67 1021 397.83 1041 463.5 c
1061 529.17 1071 618.67 1071 732 c
1071 845.33 1061 932.33 1041 993 c
1021 1053.67 993 1101 957 1135 c
921 1169 875.33 1192 820 1204 c
778.67 1213.33 697.67 1218 577 1218 c
444 1218 577 1218 444 1218 c
closepath } bind def
/s77 {
145 0 m
145 1466 145 0 145 1466 c
588 1466 145 1466 588 1466 c
854 466 588 1466 854 466 c
1117 1466 854 466 1117 1466 c
1561 1466 1117 1466 1561 1466 c
1561 0 1561 1466 1561 0 c
1286 0 1561 0 1286 0 c
1286 1154 1286 0 1286 1154 c
995 0 1286 1154 995 0 c
710 0 995 0 710 0 c
420 1154 710 0 420 1154 c
420 0 420 1154 420 0 c
145 0 420 0 145 0 c
closepath } bind def
/s82 {
150 0 m
150 1466 150 0 150 1466 c
773 1466 150 1466 773 1466 c
929.67 1466 1043.5 1452.83 1114.5 1426.5 c
1185.5 1400.17 1242.33 1353.33 1285 1286 c
1327.67 1218.67 1349 1141.67 1349 1055 c
1349 945 1316.67 854.17 1252 782.5 c
1187.33 710.83 1090.67 665.67 962 647 c
1026 609.67 1078.83 568.67 1120.5 524 c
1162.17 479.33 1218.33 400 1289 286 c
1468 0 1289 286 1468 0 c
1114 0 1468 0 1114 0 c
900 319 1114 0 900 319 c
824 433 772 504.83 744 534.5 c
716 564.17 686.33 584.5 655 595.5 c
623.67 606.5 574 612 506 612 c
446 612 506 612 446 612 c
446 0 446 612 446 0 c
150 0 446 0 150 0 c
closepath 446 846 m
665 846 446 846 665 846 c
807 846 895.67 852 931 864 c
966.33 876 994 896.67 1014 926 c
1034 955.33 1044 992 1044 1036 c
1044 1085.33 1030.83 1125.17 1004.5 1155.5 c
978.17 1185.83 941 1205 893 1213 c
869 1216.33 797 1218 677 1218 c
446 1218 677 1218 446 1218 c
446 846 446 1218 446 846 c
closepath } bind def
/s105 {
147 1206 m
147 1466 147 1206 147 1466 c
428 1466 147 1466 428 1466 c
428 1206 428 1466 428 1206 c
147 1206 428 1206 147 1206 c
closepath 147 0 m
147 1062 147 0 147 1062 c
428 1062 147 1062 428 1062 c
428 0 428 1062 428 0 c
147 0 428 0 147 0 c
closepath } bind def
/s110 {
1113 0 m
832 0 1113 0 832 0 c
832 542 832 0 832 542 c
832 656.67 826 730.83 814 764.5 c
802 798.17 782.5 824.33 755.5 843 c
728.5 861.67 696 871 658 871 c
609.33 871 565.67 857.67 527 831 c
488.33 804.33 461.83 769 447.5 725 c
433.17 681 426 599.67 426 481 c
426 0 426 481 426 0 c
145 0 426 0 145 0 c
145 1062 145 0 145 1062 c
406 1062 145 1062 406 1062 c
406 906 406 1062 406 906 c
498.67 1026 615.33 1086 756 1086 c
818 1086 874.67 1074.83 926 1052.5 c
977.33 1030.17 1016.17 1001.67 1042.5 967 c
1068.83 932.33 1087.17 893 1097.5 849 c
1107.83 805 1113 742 1113 660 c
1113 0 1113 660 1113 0 c
closepath } bind def
/s111 {
82 546 m
82 639.33 105 729.67 151 817 c
197 904.33 262.17 971 346.5 1017 c
430.83 1063 525 1086 629 1086 c
789.67 1086 921.33 1033.83 1024 929.5 c
1126.67 825.17 1178 693.33 1178 534 c
1178 373.33 1126.17 240.17 1022.5 134.5 c
918.83 28.83 788.33 -24 631 -24 c
533.67 -24 440.83 -2 352.5 42 c
264.17 86 197 150.5 151 235.5 c
105 320.5 82 424 82 546 c
closepath 370 531 m
370 425.67 395 345 445 289 c
495 233 556.67 205 630 205 c
703.33 205 764.83 233 814.5 289 c
864.17 345 889 426.33 889 533 c
889 637 864.17 717 814.5 773 c
764.83 829 703.33 857 630 857 c
556.67 857 495 829 445 773 c
395 717 370 636.33 370 531 c
closepath } bind def
/s114 {
416 0 m
135 0 416 0 135 0 c
135 1062 135 0 135 1062 c
396 1062 135 1062 396 1062 c
396 911 396 1062 396 911 c
440.67 982.33 480.83 1029.33 516.5 1052 c
552.17 1074.67 592.67 1086 638 1086 c
702 1086 763.67 1068.33 823 1033 c
736 788 823 1033 736 788 c
688.67 818.67 644.67 834 604 834 c
564.67 834 531.33 823.17 504 801.5 c
476.67 779.83 455.17 740.67 439.5 684 c
423.83 627.33 416 508.67 416 328 c
416 0 416 328 416 0 c
closepath } bind def
/s115 {
48 303 m
330 346 48 303 330 346 c
342 291.33 366.33 249.83 403 221.5 c
439.67 193.17 491 179 557 179 c
629.67 179 684.33 192.33 721 219 c
745.67 237.67 758 262.67 758 294 c
758 315.33 751.33 333 738 347 c
724 360.33 692.67 372.67 644 384 c
417.33 434 273.67 479.67 213 521 c
129 578.33 87 658 87 760 c
87 852 123.33 929.33 196 992 c
268.67 1054.67 381.33 1086 534 1086 c
679.33 1086 787.33 1062.33 858 1015 c
928.67 967.67 977.33 897.67 1004 805 c
739 756 1004 805 739 756 c
727.67 797.33 706.17 829 674.5 851 c
642.83 873 597.67 884 539 884 c
465 884 412 873.67 380 853 c
358.67 838.33 348 819.33 348 796 c
348 776 357.33 759 376 745 c
401.33 726.33 488.83 700 638.5 666 c
788.17 632 892.67 590.33 952 541 c
1010.67 491 1040 421.33 1040 332 c
1040 234.67 999.33 151 918 81 c
836.67 11 716.33 -24 557 -24 c
412.33 -24 297.83 5.33 213.5 64 c
129.17 122.67 74 202.33 48 303 c
closepath } bind def
/s116 {
634 1062 m
634 838 634 1062 634 838 c
442 838 634 838 442 838 c
442 410 442 838 442 410 c
442 323.33 443.83 272.83 447.5 258.5 c
451.17 244.17 459.5 232.33 472.5 223 c
485.5 213.67 501.33 209 520 209 c
546 209 583.67 218 633 236 c
657 18 633 236 657 18 c
591.67 -10 517.67 -24 435 -24 c
384.33 -24 338.67 -15.5 298 1.5 c
257.33 18.5 227.5 40.5 208.5 67.5 c
189.5 94.5 176.33 131 169 177 c
163 209.67 160 275.67 160 375 c
160 838 160 375 160 838 c
31 838 160 838 31 838 c
31 1062 31 838 31 1062 c
160 1062 31 1062 160 1062 c
160 1273 160 1062 160 1273 c
442 1437 160 1273 442 1437 c
442 1062 442 1437 442 1062 c
634 1062 442 1062 634 1062 c
closepath } bind def
end
/BuildGlyph {
 exch dup /Metrics get 2 index get 0 setcharwidth
/CharProcs get exch 2 copy known not{pop/.notdef}if get exec fill}bind def
/BuildChar {
1 index /Encoding get exch get
1 index /BuildGlyph get exec
} bind def
currentdict end /MC1_ArialBold exch definefont pop
%%EndFont
%%BeginFont: /MC2_ArialBold
12 dict begin
/FontInfo 8 dict dup begin
/ItalicAngle 0 def
/UnderlinePosition -217.00 def
/UnderlineThickness 215.00 def
/StrikeoutPosition 530.00 def
/StrikeoutThickness 102.00 def
/isFixedPitch false def
end readonly def
/FontName /MC2_ArialBold def
/FontType 3 def
/FontMatrix [1 2048 div 0 0 1 2048 div 0 0] def
/FontBBox [-1286 -771 4096 2162] def
/Encoding 256 array def 0 1 255{Encoding exch/.notdef put}for
Encoding 32/s32 put
Encoding 40/s40 put
Encoding 41/s41 put
Encoding 69/s69 put
Encoding 86/s86 put
Encoding 101/s101 put
Encoding 103/s103 put
Encoding 110/s110 put
Encoding 114/s114 put
Encoding 121/s121 put
/BoundingBoxes 11 dict def
BoundingBoxes begin
/s32 [0 -434 569 1854] def
/s40 [0 -434 682 1854] def
/s41 [0 -434 682 1854] def
/s69 [0 -434 1366 1854] def
/s86 [-1 -434 1366 1854] def
/s101 [0 -434 1139 1854] def
/s103 [0 -434 1251 1854] def
/s110 [0 -434 1251 1854] def
/s114 [0 -434 797 1854] def
/s121 [0 -434 1139 1854] def
end
/Metrics 11 dict def
 Metrics begin
/s32 569 def
/s40 682 def
/s41 682 def
/s69 1366 def
/s86 1366 def
/s101 1139 def
/s103 1251 def
/s110 1251 def
/s114 797 def
/s121 1139 def
end
/CharProcs 11 dict def
CharProcs begin
/.notdef {} def
/s40 {
613 -431 m
420 -431 613 -431 420 -431 c
318 -277 240.33 -117 187 49 c
133.67 215 107 375.67 107 531 c
107 723.67 140 906 206 1078 c
263.33 1227.33 336 1365 424 1491 c
616 1491 424 1491 616 1491 c
524.67 1289 461.83 1117.17 427.5 975.5 c
393.17 833.83 376 683.67 376 525 c
376 415.67 386.17 303.67 406.5 189 c
426.83 74.33 454.67 -34.67 490 -138 c
513.33 -206 554.33 -303.67 613 -431 c
closepath } bind def
/s41 {
69 -431 m
124.33 -312.33 163.33 -221.33 186 -158 c
208.67 -94.67 229.67 -21.67 249 61 c
268.33 143.67 282.67 222.17 292 296.5 c
301.33 370.83 306 447 306 525 c
306 683.67 289 833.83 255 975.5 c
221 1117.17 158.33 1289 67 1491 c
258 1491 67 1491 258 1491 c
358.67 1347.67 436.83 1195.67 492.5 1035 c
548.17 874.33 576 711.33 576 546 c
576 406.67 554 257.33 510 98 c
460 -80.67 377.67 -257 263 -431 c
69 -431 263 -431 69 -431 c
closepath } bind def
/s69 {
149 0 m
149 1466 149 0 149 1466 c
1236 1466 149 1466 1236 1466 c
1236 1218 1236 1466 1236 1218 c
445 1218 1236 1218 445 1218 c
445 893 445 1218 445 893 c
1181 893 445 893 1181 893 c
1181 646 1181 893 1181 646 c
445 646 1181 646 445 646 c
445 247 445 646 445 247 c
1264 247 445 247 1264 247 c
1264 0 1264 247 1264 0 c
149 0 1264 0 149 0 c
closepath } bind def
/s86 {
523 0 m
-1 1466 523 0 -1 1466 c
320 1466 -1 1466 320 1466 c
691 381 320 1466 691 381 c
1050 1466 691 381 1050 1466 c
1364 1466 1050 1466 1364 1466 c
839 0 1364 1466 839 0 c
523 0 839 0 523 0 c
closepath } bind def
/s101 {
762 338 m
1042 291 762 338 1042 291 c
1006 188.33 949.17 110.17 871.5 56.5 c
793.83 2.83 696.67 -24 580 -24 c
395.33 -24 258.67 36.33 170 157 c
100 253.67 65 375.67 65 523 c
65 699 111 836.83 203 936.5 c
295 1036.17 411.33 1086 552 1086 c
710 1086 834.67 1033.83 926 929.5 c
1017.33 825.17 1061 665.33 1057 450 c
353 450 1057 450 353 450 c
355 366.67 377.67 301.83 421 255.5 c
464.33 209.17 518.33 186 583 186 c
627 186 664 198 694 222 c
724 246 746.67 284.67 762 338 c
closepath 778 622 m
776 703.33 755 765.17 715 807.5 c
675 849.83 626.33 871 569 871 c
507.67 871 457 848.67 417 804 c
377 759.33 357.33 698.67 358 622 c
778 622 358 622 778 622 c
closepath } bind def
/s103 {
121 -70 m
442 -109 121 -70 442 -109 c
447.33 -146.33 459.67 -172 479 -186 c
505.67 -206 547.67 -216 605 -216 c
678.33 -216 733.33 -205 770 -183 c
794.67 -168.33 813.33 -144.67 826 -112 c
834.67 -88.67 839 -45.67 839 17 c
839 172 839 17 839 172 c
755 57.33 649 0 521 0 c
378.33 0 265.33 60.33 182 181 c
116.67 276.33 84 395 84 537 c
84 715 126.83 851 212.5 945 c
298.17 1039 404.67 1086 532 1086 c
663.33 1086 771.67 1028.33 857 913 c
857 1062 857 913 857 1062 c
1120 1062 857 1062 1120 1062 c
1120 109 1120 1062 1120 109 c
1120 -16.33 1109.67 -110 1089 -172 c
1068.33 -234 1039.33 -282.67 1002 -318 c
964.67 -353.33 914.83 -381 852.5 -401 c
790.17 -421 711.33 -431 616 -431 c
436 -431 308.33 -400.17 233 -338.5 c
157.67 -276.83 120 -198.67 120 -104 c
120 -94.67 120.33 -83.33 121 -70 c
closepath 372 553 m
372 440.33 393.83 357.83 437.5 305.5 c
481.17 253.17 535 227 599 227 c
667.67 227 725.67 253.83 773 307.5 c
820.33 361.17 844 440.67 844 546 c
844 656 821.33 737.67 776 791 c
730.67 844.33 673.33 871 604 871 c
536.67 871 481.17 844.83 437.5 792.5 c
393.83 740.17 372 660.33 372 553 c
closepath } bind def
/s110 {
1113 0 m
832 0 1113 0 832 0 c
832 542 832 0 832 542 c
832 656.67 826 730.83 814 764.5 c
802 798.17 782.5 824.33 755.5 843 c
728.5 861.67 696 871 658 871 c
609.33 871 565.67 857.67 527 831 c
488.33 804.33 461.83 769 447.5 725 c
433.17 681 426 599.67 426 481 c
426 0 426 481 426 0 c
145 0 426 0 145 0 c
145 1062 145 0 145 1062 c
406 1062 145 1062 406 1062 c
406 906 406 1062 406 906 c
498.67 1026 615.33 1086 756 1086 c
818 1086 874.67 1074.83 926 1052.5 c
977.33 1030.17 1016.17 1001.67 1042.5 967 c
1068.83 932.33 1087.17 893 1097.5 849 c
1107.83 805 1113 742 1113 660 c
1113 0 1113 660 1113 0 c
closepath } bind def
/s114 {
416 0 m
135 0 416 0 135 0 c
135 1062 135 0 135 1062 c
396 1062 135 1062 396 1062 c
396 911 396 1062 396 911 c
440.67 982.33 480.83 1029.33 516.5 1052 c
552.17 1074.67 592.67 1086 638 1086 c
702 1086 763.67 1068.33 823 1033 c
736 788 823 1033 736 788 c
688.67 818.67 644.67 834 604 834 c
564.67 834 531.33 823.17 504 801.5 c
476.67 779.83 455.17 740.67 439.5 684 c
423.83 627.33 416 508.67 416 328 c
416 0 416 328 416 0 c
closepath } bind def
/s121 {
14 1062 m
313 1062 14 1062 313 1062 c
567 308 313 1062 567 308 c
815 1062 567 308 815 1062 c
1106 1062 815 1062 1106 1062 c
731 40 1106 1062 731 40 c
664 -145 731 40 664 -145 c
639.33 -207 615.83 -254.33 593.5 -287 c
571.17 -319.67 545.5 -346.17 516.5 -366.5 c
487.5 -386.83 451.83 -402.67 409.5 -414 c
367.17 -425.33 319.33 -431 266 -431 c
212 -431 159 -425.33 107 -414 c
82 -194 107 -414 82 -194 c
126 -202.67 165.67 -207 201 -207 c
266.33 -207 314.67 -187.83 346 -149.5 c
377.33 -111.17 401.33 -62.33 418 -3 c
14 1062 418 -3 14 1062 c
closepath } bind def
end
/BuildGlyph {
 exch dup /Metrics get 2 index get 0 setcharwidth
/CharProcs get exch 2 copy known not{pop/.notdef}if get exec fill}bind def
/BuildChar {
1 index /Encoding get exch get
1 index /BuildGlyph get exec
} bind def
currentdict end /MC2_ArialBold exch definefont pop
%%EndFont
%%BeginFont: /MC3_Arial
12 dict begin
/FontInfo 8 dict dup begin
/ItalicAngle 0 def
/UnderlinePosition -217.00 def
/UnderlineThickness 150.00 def
/StrikeoutPosition 530.00 def
/StrikeoutThickness 102.00 def
/isFixedPitch false def
end readonly def
/FontName /MC3_Arial def
/FontType 3 def
/FontMatrix [1 2048 div 0 0 1 2048 div 0 0] def
/FontBBox [-1361 -665 4096 2129] def
/Encoding 256 array def 0 1 255{Encoding exch/.notdef put}for
Encoding 32/s32 put
/BoundingBoxes 2 dict def
BoundingBoxes begin
/s32 [0 -434 569 1854] def
end
/Metrics 2 dict def
 Metrics begin
/s32 569 def
end
/CharProcs 2 dict def
CharProcs begin
/.notdef {} def
end
/BuildGlyph {
 exch dup /Metrics get 2 index get 0 setcharwidth
/CharProcs get exch 2 copy known not{pop/.notdef}if get exec fill}bind def
/BuildChar {
1 index /Encoding get exch get
1 index /BuildGlyph get exec
} bind def
currentdict end /MC3_Arial exch definefont pop
%%EndFont
%%BeginFont: /MC4_SymbolBold
12 dict begin
/FontInfo 8 dict dup begin
/ItalicAngle 0 def
/UnderlinePosition -223.00 def
/UnderlineThickness 100.00 def
/StrikeoutPosition 530.00 def
/StrikeoutThickness 102.00 def
/isFixedPitch false def
end readonly def
/FontName /MC4_SymbolBold def
/FontType 3 def
/FontMatrix [1 2048 div 0 0 1 2048 div 0 0] def
/FontBBox [-1 -450 2279 2059] def
/Encoding 256 array def 0 1 255{Encoding exch/.notdef put}for
Encoding 32/s32 put
Encoding 71/s71 put
/BoundingBoxes 3 dict def
BoundingBoxes begin
/s32 [0 -450 513 2059] def
/s71 [0 -450 1236 2059] def
end
/Metrics 3 dict def
 Metrics begin
/s32 513 def
/s71 1236 def
end
/CharProcs 3 dict def
CharProcs begin
/.notdef {} def
/s71 {
1188.06 1369 m
1199 1011 1188.06 1369 1199 1011 c
1123.53 1011 1199 1011 1123.53 1011 c
1115.75 1103.38 1094.13 1165.47 1058.66 1197.28 c
1023.2 1229.09 959.31 1245 867 1245 c
460 1245 867 1245 460 1245 c
460 211 460 1245 460 211 c
460 159.82 469.84 124.53 489.52 105.12 c
509.19 85.71 544.03 76 594.02 76 c
668 76 594.02 76 668 76 c
668 0 668 76 668 0 c
34 0 668 0 34 0 c
34 76 34 0 34 76 c
88.3 76 34 76 88.3 76 c
140.31 76 176.59 85.88 197.16 105.64 c
217.72 125.4 228 159.52 228 208 c
228 1161 228 208 228 1161 c
228 1209.53 217.87 1243.66 197.6 1263.4 c
177.34 1283.13 140.9 1293 88.3 1293 c
34 1293 88.3 1293 34 1293 c
34 1369 34 1293 34 1369 c
1188.06 1369 34 1369 1188.06 1369 c
closepath } bind def
end
/BuildGlyph {
 exch dup /Metrics get 2 index get 0 setcharwidth
/CharProcs get exch 2 copy known not{pop/.notdef}if get exec fill}bind def
/BuildChar {
1 index /Encoding get exch get
1 index /BuildGlyph get exec
} bind def
currentdict end /MC4_SymbolBold exch definefont pop
%%EndFont
/clipproc{clippath{[{/m cvx}{/l cvx}{/c cvx}{/cp cvx}pathforall}stopped
{cleartomark[pathbbox exch 3 i sub exch 2 i sub/rectpath cvx}if]np}bd
/iclmtx mtx cmtx def/iclpath clipproc xd/clinit{pathproc initclip np iclpath cl np exec}bd
/clmove{pathproc 3 1 roll clippath pathproc 2 i neg 2 i neg tr initclip np exec cl tr np exec}bd
/clrect[gs np iclpath{pathbbox}stopped{0 0 576 468 }if gr]def
/cloper{mtx cmtx iclmtx smtx exch exec smtx}bd
/cland{{exch cvx exec}cloper}bd
/clcopy{{exch clinit cvx exec}cloper}bd
/cldiff{{exch pathbbox 4 -1 roll clrect 0 get 2 copy gt{exch}if pop 4 1 roll
 3 -1 roll clrect 1 get 2 copy gt{exch}if pop 3 1 roll
 2 -1 roll clrect 2 get 2 copy lt{exch}if pop 2 1 roll
 1 -1 roll clrect 3 get 2 copy lt{exch}if pop 1 1 roll
 exch 3 i sub exch 2 i sub rectpath cvx exec}cloper}bd
/icl{{clinit}cloper}bd
0.170756 0.170598 sc

/initmtx mtx cmtx def
initmtx smtx
[1.0000 0.0000 0.0000 1.0000 0.0000 0.0000 ] concat
 [0.702756 -0 0 -0.703412 0 2743.29] concat
gs
859 3252 m
4132 3252 l
17 sw
1 sj
2 scap
st
1268 3252 m
1268 3218 l
0 scap
st
2086 3252 m
2086 3218 l
st
2905 3252 m
2905 3218 l
st
3723 3252 m
3723 3218 l
st
859 3252 m
859 3183 l
st
1677 3252 m
1677 3183 l
st
2496 3252 m
2496 3183 l
st
3314 3252 m
3314 3183 l
st
4132 3252 m
4132 3183 l
st
859 452 m
4132 452 l
2 scap
st
1268 452 m
1268 486 l
0 scap
st
2086 452 m
2086 486 l
st
2905 452 m
2905 486 l
st
3723 452 m
3723 486 l
st
859 452 m
859 521 l
st
1677 452 m
1677 521 l
st
2496 452 m
2496 521 l
st
3314 452 m
3314 521 l
st
4132 452 m
4132 521 l
st
859 3252 m
859 452 l
2 scap
st
859 3051 m
893 3051 l
0 scap
st
859 2649 m
893 2649 l
st
859 2246 m
893 2246 l
st
859 1844 m
893 1844 l
st
859 1442 m
893 1442 l
st
859 1039 m
893 1039 l
st
859 637 m
893 637 l
st
859 3252 m
928 3252 l
st
859 2850 m
928 2850 l
st
859 2447 m
928 2447 l
st
859 2045 m
928 2045 l
st
859 1643 m
928 1643 l
st
859 1241 m
928 1241 l
st
859 838 m
928 838 l
st
4132 3252 m
4132 452 l
2 scap
st
4132 3051 m
4098 3051 l
0 scap
st
4132 2649 m
4098 2649 l
st
4132 2246 m
4098 2246 l
st
4132 1844 m
4098 1844 l
st
4132 1442 m
4098 1442 l
st
4132 1039 m
4098 1039 l
st
4132 637 m
4098 637 l
st
4132 3252 m
4063 3252 l
st
4132 2850 m
4063 2850 l
st
4132 2447 m
4063 2447 l
st
4132 2045 m
4063 2045 l
st
4132 1643 m
4063 1643 l
st
4132 1241 m
4063 1241 l
st
4132 838 m
4063 838 l
st
gs
gr
gs
gr
gs
1 -1 sc
/MC1_ArialBold 150 self 751 -3443 m
(-20)[49 82 82 ] xs
1 -1 sc
gr
gs
gr
gs
gr
gs
1 -1 sc
/MC1_ArialBold 150 self 1569 -3443 m
(-10)[49 82 82 ] xs
1 -1 sc
gr
gs
gr
gs
gr
gs
1 -1 sc
/MC1_ArialBold 150 self 2455 -3443 m
(0)[82 ] xs
1 -1 sc
gr
gs
gr
gs
gr
gs
1 -1 sc
/MC1_ArialBold 150 self 3231 -3443 m
(10)[82 82 ] xs
1 -1 sc
gr
gs
gr
gs
gr
gs
1 -1 sc
/MC1_ArialBold 150 self 4049 -3443 m
(20)[82 82 ] xs
1 -1 sc
gr
gs
gr
gs
gr
gs
1 -1 sc
/MC1_ArialBold 150 self 360 -3296 m
(-0.165)[49 82 41 82 82 82 ] xs
1 -1 sc
gr
gs
gr
gs
gr
gs
1 -1 sc
/MC1_ArialBold 150 self 443 -2894 m
(-0.14)[49 82 41 82 82 ] xs
1 -1 sc
gr
gs
gr
gs
gr
gs
1 -1 sc
/MC1_ArialBold 150 self 360 -2491 m
(-0.115)[49 82 41 82 82 82 ] xs
1 -1 sc
gr
gs
gr
gs
gr
gs
1 -1 sc
/MC1_ArialBold 150 self 443 -2089 m
(-0.09)[49 82 41 82 82 ] xs
1 -1 sc
gr
gs
gr
gs
gr
gs
1 -1 sc
/MC1_ArialBold 150 self 360 -1687 m
(-0.065)[49 82 41 82 82 82 ] xs
1 -1 sc
gr
gs
gr
gs
gr
gs
1 -1 sc
/MC1_ArialBold 150 self 443 -1285 m
(-0.04)[49 82 41 82 82 ] xs
1 -1 sc
gr
gs
gr
gs
gr
gs
1 -1 sc
/MC1_ArialBold 150 self 360 -882 m
(-0.015)[49 82 41 82 82 82 ] xs
1 -1 sc
gr
gs
gr
gs
gr
gs
gr
gs
gr
gs
gr
gs
gr
gs
gr
gs
gr
gs
gr
gs
gr
gs
gr
gs
gr
gs
gr
gs
gr
gs
gr
gs
gr
gs
gr
gs
gr
gs
gr
gs
gr
gs
gr
gs
gr
gs
gr
gs
gr
gs
gr
gs
gr
gs
gr
gs
gr
gs
gr
gs
gr
gs
gr
gs
pathproc 859 452 3274 2801 np rectpath
/eocl cland
np exec
859 1362 m
941 1822 l
1023 2162 l
1104 2397 l
1186 2537 l
1268 2594 l
1350 2581 l
1432 2509 l
1514 2388 l
1595 2230 l
1677 2045 l
1759 1843 l
1841 1634 l
1923 1426 l
2005 1229 l
2086 1048 l
2168 891 l
2250 763 l
2332 669 l
2414 611 l
2496 591 l
2577 611 l
2659 669 l
2741 763 l
2823 891 l
2905 1048 l
2986 1229 l
3068 1426 l
3150 1634 l
3232 1843 l
3314 2045 l
3396 2230 l
3477 2388 l
3559 2509 l
3641 2581 l
3723 2594 l
3805 2537 l
3887 2397 l
3968 2162 l
4050 1822 l
4132 1362 l
0.165 0.416 0.667 rgb
1 scap
st
821 1324 77 77 rectfill
903 1784 77 77 rectfill
985 2124 77 77 rectfill
1066 2359 77 77 rectfill
1148 2499 77 77 rectfill
1230 2556 77 77 rectfill
1312 2543 77 77 rectfill
1394 2471 77 77 rectfill
1476 2350 77 77 rectfill
1557 2192 77 77 rectfill
1639 2007 77 77 rectfill
1721 1805 77 77 rectfill
1803 1596 77 77 rectfill
1885 1388 77 77 rectfill
1967 1191 77 77 rectfill
2048 1010 77 77 rectfill
2130 853 77 77 rectfill
2212 725 77 77 rectfill
2294 631 77 77 rectfill
2376 573 77 77 rectfill
2458 553 77 77 rectfill
2539 573 77 77 rectfill
2621 631 77 77 rectfill
2703 725 77 77 rectfill
2785 853 77 77 rectfill
2867 1010 77 77 rectfill
2948 1191 77 77 rectfill
3030 1388 77 77 rectfill
3112 1596 77 77 rectfill
3194 1805 77 77 rectfill
3276 2007 77 77 rectfill
3358 2192 77 77 rectfill
3439 2350 77 77 rectfill
3521 2471 77 77 rectfill
3603 2543 77 77 rectfill
3685 2556 77 77 rectfill
3767 2499 77 77 rectfill
3849 2359 77 77 rectfill
3930 2124 77 77 rectfill
4012 1784 77 77 rectfill
4094 1324 77 77 rectfill
gr
gs
pathproc 859 452 3274 2801 np rectpath
/eocl cland
np exec
859 -3956 m
941 -2234 l
1023 -811 l
1104 340 l
1186 1244 l
1268 1924 l
1350 2405 l
1432 2709 l
1514 2860 l
1595 2879 l
1677 2789 l
1759 2612 l
1841 2370 l
1923 2085 l
2005 1779 l
2086 1474 l
2168 1188 l
2250 942 l
2332 753 l
2414 633 l
2496 591 l
2577 633 l
2659 753 l
2741 942 l
2823 1188 l
2905 1474 l
2986 1779 l
3068 2085 l
3150 2370 l
3232 2612 l
3314 2789 l
3396 2879 l
3477 2860 l
3559 2709 l
3641 2405 l
3723 1924 l
3805 1244 l
3887 340 l
3968 -811 l
4050 -2234 l
4132 -3956 l
0.008 0.753 0 rgb
1 scap
st
np
859 -3956 41 0 360  a
cp
gs
eofi
gr
6 sw
st
np
941 -2234 41 0 360  a
cp
gs
eofi
gr
st
np
1023 -811 41 0 360  a
cp
gs
eofi
gr
st
np
1104 340 41 0 360  a
cp
gs
eofi
gr
st
np
1186 1244 41 0 360  a
cp
gs
eofi
gr
st
np
1268 1924 41 0 360  a
cp
gs
eofi
gr
st
np
1350 2405 41 0 360  a
cp
gs
eofi
gr
st
np
1432 2709 41 0 360  a
cp
gs
eofi
gr
st
np
1514 2860 41 0 360  a
cp
gs
eofi
gr
st
np
1595 2879 41 0 360  a
cp
gs
eofi
gr
st
np
1677 2789 41 0 360  a
cp
gs
eofi
gr
st
np
1759 2612 41 0 360  a
cp
gs
eofi
gr
st
np
1841 2370 41 0 360  a
cp
gs
eofi
gr
st
np
1923 2085 41 0 360  a
cp
gs
eofi
gr
st
np
2005 1779 41 0 360  a
cp
gs
eofi
gr
st
np
2086 1474 41 0 360  a
cp
gs
eofi
gr
st
np
2168 1188 41 0 360  a
cp
gs
eofi
gr
st
np
2250 942 41 0 360  a
cp
gs
eofi
gr
st
np
2332 753 41 0 360  a
cp
gs
eofi
gr
st
np
2414 633 41 0 360  a
cp
gs
eofi
gr
st
np
2496 591 41 0 360  a
cp
gs
eofi
gr
st
np
2577 633 41 0 360  a
cp
gs
eofi
gr
st
np
2659 753 41 0 360  a
cp
gs
eofi
gr
st
np
2741 942 41 0 360  a
cp
gs
eofi
gr
st
np
2823 1188 41 0 360  a
cp
gs
eofi
gr
st
np
2905 1474 41 0 360  a
cp
gs
eofi
gr
st
np
2986 1779 41 0 360  a
cp
gs
eofi
gr
st
np
3068 2085 41 0 360  a
cp
gs
eofi
gr
st
np
3150 2370 41 0 360  a
cp
gs
eofi
gr
st
np
3232 2612 41 0 360  a
cp
gs
eofi
gr
st
np
3314 2789 41 0 360  a
cp
gs
eofi
gr
st
np
3396 2879 41 0 360  a
cp
gs
eofi
gr
st
np
3477 2860 41 0 360  a
cp
gs
eofi
gr
st
np
3559 2709 41 0 360  a
cp
gs
eofi
gr
st
np
3641 2405 41 0 360  a
cp
gs
eofi
gr
st
np
3723 1924 41 0 360  a
cp
gs
eofi
gr
st
np
3805 1244 41 0 360  a
cp
gs
eofi
gr
st
np
3887 340 41 0 360  a
cp
gs
eofi
gr
st
np
3968 -811 41 0 360  a
cp
gs
eofi
gr
st
np
4050 -2234 41 0 360  a
cp
gs
eofi
gr
st
np
4132 -3956 41 0 360  a
cp
gs
eofi
gr
st
gr
gs
pathproc 859 452 3274 2801 np rectpath
/eocl cland
np exec
859 -5873 m
941 -4024 l
1023 -2467 l
1104 -1177 l
1186 -132 l
1268 691 l
1350 1313 l
1432 1755 l
1514 2038 l
1595 2183 l
1677 2211 l
1759 2142 l
1841 1998 l
1923 1800 l
2005 1570 l
2086 1329 l
2168 1096 l
2250 890 l
2332 729 l
2414 627 l
2496 591 l
2577 627 l
2659 729 l
2741 890 l
2823 1096 l
2905 1329 l
2986 1570 l
3068 1800 l
3150 1998 l
3232 2142 l
3314 2211 l
3396 2183 l
3477 2038 l
3559 1755 l
3641 1313 l
3723 691 l
3805 -132 l
3887 -1177 l
3968 -2467 l
4050 -4024 l
4132 -5873 l
0.675 0.918 0.169 rgb
1 scap
st
859 -5929 m
907 -5845 l
810 -5845 l
cp
gs
eofi
gr
6 sw
st
941 -4080 m
989 -3996 l
892 -3996 l
cp
gs
eofi
gr
st
1023 -2523 m
1071 -2439 l
974 -2439 l
cp
gs
eofi
gr
st
1104 -1233 m
1152 -1149 l
1055 -1149 l
cp
gs
eofi
gr
st
1186 -188 m
1234 -104 l
1137 -104 l
cp
gs
eofi
gr
st
1268 635 m
1316 719 l
1219 719 l
cp
gs
eofi
gr
st
1350 1257 m
1398 1341 l
1301 1341 l
cp
gs
eofi
gr
st
1432 1699 m
1480 1783 l
1383 1783 l
cp
gs
eofi
gr
st
1514 1982 m
1562 2066 l
1465 2066 l
cp
gs
eofi
gr
st
1595 2127 m
1643 2211 l
1546 2211 l
cp
gs
eofi
gr
st
1677 2155 m
1725 2239 l
1628 2239 l
cp
gs
eofi
gr
st
1759 2086 m
1807 2170 l
1710 2170 l
cp
gs
eofi
gr
st
1841 1942 m
1889 2026 l
1792 2026 l
cp
gs
eofi
gr
st
1923 1744 m
1971 1828 l
1874 1828 l
cp
gs
eofi
gr
st
2005 1514 m
2053 1598 l
1956 1598 l
cp
gs
eofi
gr
st
2086 1273 m
2134 1357 l
2037 1357 l
cp
gs
eofi
gr
st
2168 1040 m
2216 1124 l
2119 1124 l
cp
gs
eofi
gr
st
2250 834 m
2298 918 l
2201 918 l
cp
gs
eofi
gr
st
2332 673 m
2380 757 l
2283 757 l
cp
gs
eofi
gr
st
2414 571 m
2462 655 l
2365 655 l
cp
gs
eofi
gr
st
2496 535 m
2544 619 l
2447 619 l
cp
gs
eofi
gr
st
2577 571 m
2625 655 l
2528 655 l
cp
gs
eofi
gr
st
2659 673 m
2707 757 l
2610 757 l
cp
gs
eofi
gr
st
2741 834 m
2789 918 l
2692 918 l
cp
gs
eofi
gr
st
2823 1040 m
2871 1124 l
2774 1124 l
cp
gs
eofi
gr
st
2905 1273 m
2953 1357 l
2856 1357 l
cp
gs
eofi
gr
st
2986 1514 m
3034 1598 l
2937 1598 l
cp
gs
eofi
gr
st
3068 1744 m
3116 1828 l
3019 1828 l
cp
gs
eofi
gr
st
3150 1942 m
3198 2026 l
3101 2026 l
cp
gs
eofi
gr
st
3232 2086 m
3280 2170 l
3183 2170 l
cp
gs
eofi
gr
st
3314 2155 m
3362 2239 l
3265 2239 l
cp
gs
eofi
gr
st
3396 2127 m
3444 2211 l
3347 2211 l
cp
gs
eofi
gr
st
3477 1982 m
3525 2066 l
3428 2066 l
cp
gs
eofi
gr
st
3559 1699 m
3607 1783 l
3510 1783 l
cp
gs
eofi
gr
st
3641 1257 m
3689 1341 l
3592 1341 l
cp
gs
eofi
gr
st
3723 635 m
3771 719 l
3674 719 l
cp
gs
eofi
gr
st
3805 -188 m
3853 -104 l
3756 -104 l
cp
gs
eofi
gr
st
3887 -1233 m
3935 -1149 l
3838 -1149 l
cp
gs
eofi
gr
st
3968 -2523 m
4016 -2439 l
3919 -2439 l
cp
gs
eofi
gr
st
4050 -4080 m
4098 -3996 l
4001 -3996 l
cp
gs
eofi
gr
st
4132 -5929 m
4180 -5845 l
4083 -5845 l
cp
gs
eofi
gr
st
gr
gs
gs
1 -1 sc
283 -2362 tr
90 ro
/MC2_ArialBold 184 self 0 0 m
(Energy \(eV\))[122 111 101 71 111 101 50 60 101 122 60 ] xs
1 -1 sc
gr
gr
gs
1 -1 sc
/MC1_ArialBold 184 self 2062 -3708 m
(Distortion)[132 50 101 60 111 71 60 50 111 111 ] xs
1 -1 sc
gr
3474 466 m
4115 466 l
4115 1105 l
3474 1105 l
cp
gs
1 1 1 rgb
eofi
gr
4 sw
0 sj
2 scap
st
gs
gs
gr
gs
gs
3495 587 m
3831 587 l
17 sw
0.165 0.416 0.667 rgb
1 sj
1 scap
st
gr
0.165 0.416 0.667 rgb
3624 548 79 79 rectfill
gr
1 -1 sc
/MC3_Arial 150 self 3831 -638 m
( )[41 ] xs
1 -1 sc
1 -1 sc
/MC4_SymbolBold 150 self 3873 -638 m
(G)[90 ] xs
1 -1 sc
gs
gr
gs
gs
3495 777 m
3831 777 l
17 sw
0.008 0.753 0 rgb
1 sj
1 scap
st
gr
np
3663 777 41 0 360  a
cp
gs
0.008 0.753 0 rgb
eofi
gr
6 sw
0.008 0.753 0 rgb
1 sj
1 scap
st
gr
1 -1 sc
/MC3_Arial 150 self 3831 -828 m
( )[41 ] xs
1 -1 sc
1 -1 sc
/MC1_ArialBold 150 self 3873 -828 m
(R)[107 ] xs
1 -1 sc
1 -1 sc
/MC1_ArialBold 102 self 3981 -869 m
(25)[56 56 ] xs
1 -1 sc
gs
gr
gs
gs
3495 1000 m
3831 1000 l
17 sw
0.675 0.918 0.169 rgb
1 sj
1 scap
st
gr
3663 944 m
3711 1028 l
3614 1028 l
cp
gs
0.675 0.918 0.169 rgb
eofi
gr
6 sw
0.675 0.918 0.169 rgb
1 sj
1 scap
st
gr
1 -1 sc
/MC3_Arial 150 self 3831 -1051 m
( )[41 ] xs
1 -1 sc
1 -1 sc
/MC1_ArialBold 150 self 3873 -1051 m
(M)[124 ] xs
1 -1 sc
gr
gr
gs
gr
Save0 restore end
showpage
%%EOF
