%!PS-Adobe-3.0 EPSF-3.0
%%Title: pressure_vs_Tc_Tc0.eps
%%Creator: Mat<PERSON>lotlib v3.7.1, https://matplotlib.org/
%%CreationDate: Wed Apr 24 19:18:40 2024
%%Orientation: portrait
%%BoundingBox: 40 177 572 615
%%HiResBoundingBox: 40.475000 177.399574 571.525000 614.600426
%%EndComments
%%BeginProlog
/mpldict 11 dict def
mpldict begin
/_d { bind def } bind def
/m { moveto } _d
/l { lineto } _d
/r { rlineto } _d
/c { curveto } _d
/cl { closepath } _d
/ce { closepath eofill } _d
/box {
      m
      1 index 0 r
      0 exch r
      neg 0 r
      cl
    } _d
/clipbox {
      box
      clip
      newpath
    } _d
/sc { setcachedevice } _d
%!PS-TrueTypeFont-1.0-2.53740
%%Title: unknown
%%Creator: Converted from TrueType to type 42 by PPR
15 dict begin
/FontName /TimesNewRomanPSMT def
/PaintType 0 def
/FontMatrix[1 0 0 1 0 0]def
/FontBBox[-568 -307 2028 1007]def
/FontType 42 def
/Encoding StandardEncoding def
/FontInfo 10 dict dup begin
/FamilyName (unknown) def
/FullName (unknown) def
/Weight (unknown) def
/Version (unknown) def
/ItalicAngle 0.0 def
/isFixedPitch false def
/UnderlinePosition -223 def
/UnderlineThickness 100 def
end readonly def
/sfnts[<00010000000900800003001063767420F34DDA810000009C000007C46670676D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>]def
/CharStrings 25 dict dup begin
/.notdef 0 def
/minus 26 def
/space 3 def
/parenleft 4 def
/parenright 5 def
/period 6 def
/slash 7 def
/zero 8 def
/one 9 def
/two 10 def
/three 11 def
/four 12 def
/five 13 def
/six 14 def
/eight 15 def
/nine 16 def
/C 17 def
/G 18 def
/P 19 def
/T 20 def
/a 21 def
/e 22 def
/r 23 def
/s 24 def
/u 25 def
end readonly def

systemdict/resourcestatus known
 {42 /FontType resourcestatus
   {pop pop false}{true}ifelse}
 {true}ifelse
{/TrueDict where{pop}{(%%[ Error: no TrueType rasterizer ]%%)= flush}ifelse
/FontType 3 def
 /TrueState 271 string def
 TrueDict begin sfnts save
 72 0 matrix defaultmatrix dtransform dup
 mul exch dup mul add sqrt cvi 0 72 matrix
 defaultmatrix dtransform dup mul exch dup
 mul add sqrt cvi 3 -1 roll restore
 TrueState initer end
 /BuildGlyph{exch begin
  CharStrings dup 2 index known
    {exch}{exch pop /.notdef}ifelse
  get dup xcheck
    {currentdict systemdict begin begin exec end end}
    {TrueDict begin /bander load cvlit exch TrueState render end}
    ifelse
 end}bind def
 /BuildChar{
  1 index /Encoding get exch get
  1 index /BuildGlyph get exec
 }bind def
}if

FontName currentdict end definefont pop
end
%%EndProlog
mpldict begin
40.475 177.4 translate
531.05 437.201 0 0 clipbox
gsave
0 -0 m
531.05 -0 l
531.05 437.200853 l
0 437.200853 l
cl
1.000 setgray
fill
grestore
gsave
77.45 66.60625 m
523.85 66.60625 l
523.85 426.96625 l
77.45 426.96625 l
cl
1.000 setgray
fill
grestore
2.500 setlinewidth
1 setlinejoin
0 setlinecap
[] 0 setdash
0.000 setgray
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
80.8318 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
80.8318 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

67.5349 39.2469 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /six glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
131.559 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
131.559 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

118.262 39.2469 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /three glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
182.286 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
182.286 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

176.036 39.2469 translate
0 rotate
0 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
233.014 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
233.014 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

226.764 39.2469 translate
0 rotate
0 0 m /three glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
283.741 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
283.741 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

277.491 39.2469 translate
0 rotate
0 0 m /six glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
334.468 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
334.468 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

328.218 39.2469 translate
0 rotate
0 0 m /nine glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
385.195 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
385.195 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

372.695 39.2469 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /two glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
435.923 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
435.923 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

423.423 39.2469 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /five glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
486.65 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
486.65 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

474.15 39.2469 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /eight glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.5136 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.5136 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
106.195 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
106.195 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
118.877 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
118.877 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
144.241 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
144.241 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
156.923 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
156.923 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
169.605 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
169.605 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
194.968 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
194.968 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
207.65 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
207.65 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
220.332 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
220.332 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
245.695 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
245.695 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
258.377 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
258.377 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
271.059 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
271.059 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
296.423 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
296.423 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
309.105 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
309.105 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
321.786 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
321.786 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
347.15 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
347.15 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
359.832 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
359.832 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
372.514 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
372.514 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
397.877 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
397.877 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
410.559 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
410.559 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
423.241 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
423.241 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
448.605 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
448.605 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
461.286 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
461.286 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
473.968 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
473.968 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
499.332 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
499.332 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
512.014 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
512.014 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

225.306 12.5438 translate
0 rotate
0 0 m /P glyphshow
13.9038 0 m /r glyphshow
22.229 0 m /e glyphshow
33.3252 0 m /s glyphshow
43.0542 0 m /s glyphshow
52.7832 0 m /u glyphshow
65.2832 0 m /r glyphshow
73.6084 0 m /e glyphshow
84.7046 0 m /space glyphshow
90.9546 0 m /parenleft glyphshow
99.2798 0 m /G glyphshow
117.334 0 m /P glyphshow
131.238 0 m /a glyphshow
142.334 0 m /parenright glyphshow
grestore
2.500 setlinewidth
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
77.45 74.8425 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
523.85 74.8425 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

36.2 66.1628 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /four glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
77.45 161.462 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
523.85 161.462 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

36.2 152.782 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /six glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
77.45 248.082 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
523.85 248.082 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

36.2 239.402 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /eight glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
77.45 334.702 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
523.85 334.702 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

36.2 326.022 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
77.45 421.321 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
523.85 421.321 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

36.2 412.641 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /period glyphshow
18.75 0 m /two glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
77.45 96.4974 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
523.85 96.4974 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
77.45 118.152 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
523.85 118.152 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
77.45 139.807 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
523.85 139.807 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
77.45 183.117 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
523.85 183.117 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
77.45 204.772 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
523.85 204.772 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
77.45 226.427 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
523.85 226.427 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
77.45 269.737 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
523.85 269.737 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
77.45 291.392 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
523.85 291.392 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
77.45 313.047 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
523.85 313.047 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
77.45 356.356 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
523.85 356.356 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
77.45 378.011 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
523.85 378.011 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
77.45 399.666 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
523.85 399.666 o
grestore
gsave
25.2 200.286 translate
90 rotate
/TimesNewRomanPSMT 25.0 selectfont
0 0.640625 moveto
/T glyphshow
/TimesNewRomanPSMT 17.5 selectfont
15.6467 -5.8 moveto
/C glyphshow
/TimesNewRomanPSMT 25.0 selectfont
28.3925 0.640625 moveto
/slash glyphshow
35.3383 0.640625 moveto
/T glyphshow
/TimesNewRomanPSMT 17.5 selectfont
50.985 -5.8 moveto
/C glyphshow
/TimesNewRomanPSMT 25.0 selectfont
63.7308 0.640625 moveto
/parenleft glyphshow
72.056 0.640625 moveto
/zero glyphshow
84.556 0.640625 moveto
/parenright glyphshow
grestore
[9.6 2.4 1.5 2.4] 0 setdash
0.031 0.659 0.255 setrgbcolor
gsave
446.4 360.36 77.45 66.606 clipbox
503.559091 82.98625 m
486.65 94.091335 l
469.740909 105.196419 l
452.831818 116.301504 l
435.922727 129.257436 l
419.013636 142.213369 l
402.104545 153.318453 l
385.195455 164.423538 l
368.286364 179.230318 l
351.377273 190.335403 l
334.468182 205.142182 l
317.559091 218.098114 l
300.65 231.054047 l
283.740909 245.860826 l
266.831818 258.816758 l
249.922727 273.623538 l
233.013636 288.430318 l
216.104545 305.087945 l
199.195455 318.043877 l
182.286364 334.701504 l
165.377273 347.657436 l
148.468182 364.315064 l
131.559091 380.972691 l
114.65 393.928623 l
97.740909 410.58625 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
446.4 360.36 77.45 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
503.559 82.9862 o
486.65 94.0913 o
469.741 105.196 o
452.832 116.302 o
435.923 129.257 o
419.014 142.213 o
402.105 153.318 o
385.195 164.424 o
368.286 179.23 o
351.377 190.335 o
334.468 205.142 o
317.559 218.098 o
300.65 231.054 o
283.741 245.861 o
266.832 258.817 o
249.923 273.624 o
233.014 288.43 o
216.105 305.088 o
199.195 318.044 o
182.286 334.702 o
165.377 347.657 o
148.468 364.315 o
131.559 380.973 o
114.65 393.929 o
97.7409 410.586 o
grestore
2.500 setlinewidth
0 setlinejoin
2 setlinecap
0.000 setgray
gsave
77.45 66.60625 m
77.45 426.96625 l
stroke
grestore
gsave
523.85 66.60625 m
523.85 426.96625 l
stroke
grestore
gsave
77.45 66.60625 m
523.85 66.60625 l
stroke
grestore
gsave
77.45 426.96625 m
523.85 426.96625 l
stroke
grestore

end
showpage
