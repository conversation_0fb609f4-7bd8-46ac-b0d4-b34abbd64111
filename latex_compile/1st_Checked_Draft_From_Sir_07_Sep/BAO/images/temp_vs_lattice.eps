%!PS-Adobe-3.0 EPSF-3.0
%%Title: temp_vs_lattice.eps
%%Creator: Matplotlib v3.7.1, https://matplotlib.org/
%%CreationDate: Wed Apr 24 13:12:28 2024
%%Orientation: portrait
%%BoundingBox: 22 178 590 614
%%HiResBoundingBox: 22.873438 178.916875 589.126562 613.083125
%%EndComments
%%BeginProlog
/mpldict 11 dict def
mpldict begin
/_d { bind def } bind def
/m { moveto } _d
/l { lineto } _d
/r { rlineto } _d
/c { curveto } _d
/cl { closepath } _d
/ce { closepath eofill } _d
/box {
      m
      1 index 0 r
      0 exch r
      neg 0 r
      cl
    } _d
/clipbox {
      box
      clip
      newpath
    } _d
/sc { setcachedevice } _d
%!PS-TrueTypeFont-1.0-2.53740
%%Title: unknown
%%Creator: Converted from TrueType to type 42 by PPR
15 dict begin
/FontName /TimesNewRomanPSMT def
/PaintType 0 def
/FontMatrix[1 0 0 1 0 0]def
/FontBBox[-568 -307 2028 1007]def
/FontType 42 def
/Encoding StandardEncoding def
/FontInfo 10 dict dup begin
/FamilyName (unknown) def
/FullName (unknown) def
/Weight (unknown) def
/Version (unknown) def
/ItalicAngle 0.0 def
/isFixedPitch false def
/UnderlinePosition -223 def
/UnderlineThickness 100 def
end readonly def
/sfnts[<00010000000900800003001063767420F34DDA810000009C000007C46670676D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>]def
/CharStrings 32 dict dup begin
/.notdef 0 def
/space 3 def
/parenleft 4 def
/parenright 5 def
/period 6 def
/zero 7 def
/one 8 def
/two 9 def
/three 10 def
/four 11 def
/five 12 def
/six 13 def
/seven 14 def
/eight 15 def
/nine 16 def
/C 17 def
/K 18 def
/L 19 def
/P 20 def
/R 21 def
/T 22 def
/a 23 def
/b 24 def
/c 25 def
/e 26 def
/i 27 def
/m 28 def
/p 29 def
/r 30 def
/s 31 def
/t 32 def
/u 33 def
end readonly def

systemdict/resourcestatus known
 {42 /FontType resourcestatus
   {pop pop false}{true}ifelse}
 {true}ifelse
{/TrueDict where{pop}{(%%[ Error: no TrueType rasterizer ]%%)= flush}ifelse
/FontType 3 def
 /TrueState 271 string def
 TrueDict begin sfnts save
 72 0 matrix defaultmatrix dtransform dup
 mul exch dup mul add sqrt cvi 0 72 matrix
 defaultmatrix dtransform dup mul exch dup
 mul add sqrt cvi 3 -1 roll restore
 TrueState initer end
 /BuildGlyph{exch begin
  CharStrings dup 2 index known
    {exch}{exch pop /.notdef}ifelse
  get dup xcheck
    {currentdict systemdict begin begin exec end end}
    {TrueDict begin /bander load cvlit exch TrueState render end}
    ifelse
 end}bind def
 /BuildChar{
  1 index /Encoding get exch get
  1 index /BuildGlyph get exec
 }bind def
}if

FontName currentdict end definefont pop
end
%%EndProlog
mpldict begin
22.873 178.917 translate
566.253 434.166 0 0 clipbox
gsave
0 -0 m
566.253125 -0 l
566.253125 434.16625 l
0 434.16625 l
cl
1.000 setgray
fill
grestore
gsave
87.653125 66.60625 m
534.053125 66.60625 l
534.053125 426.96625 l
87.653125 426.96625 l
cl
1.000 setgray
fill
grestore
1.000 setlinewidth
0 setlinejoin
0 setlinecap
[] 0 setdash
0.902 0.902 0.980 setrgbcolor
gsave
446.4 360.36 87.653 66.606 clipbox
87.653125 66.60625 m
87.653125 426.96625 l
438.821125 426.96625 l
438.821125 66.60625 l
cl
gsave
fill
grestore
stroke
grestore
0.678 0.847 0.902 setrgbcolor
gsave
446.4 360.36 87.653 66.606 clipbox
438.821125 66.60625 m
438.821125 426.96625 l
534.053125 426.96625 l
534.053125 66.60625 l
cl
gsave
fill
grestore
stroke
grestore
2.500 setlinewidth
1 setlinejoin
0.000 setgray
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
87.6531 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
87.6531 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

81.4031 39.2469 translate
0 rotate
0 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
176.933 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
176.933 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

158.183 39.2469 translate
0 rotate
0 0 m /three glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
266.213 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
266.213 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

247.463 39.2469 translate
0 rotate
0 0 m /six glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
355.493 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
355.493 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

336.743 39.2469 translate
0 rotate
0 0 m /nine glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
444.773 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
444.773 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

419.773 39.2469 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /two glyphshow
25 0 m /zero glyphshow
37.5 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
534.053 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
534.053 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

509.053 39.2469 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /five glyphshow
25 0 m /zero glyphshow
37.5 0 m /zero glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
109.973 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
109.973 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
132.293 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
132.293 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
154.613 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
154.613 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
199.253 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
199.253 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
221.573 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
221.573 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
243.893 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
243.893 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
288.533 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
288.533 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
310.853 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
310.853 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
333.173 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
333.173 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
377.813 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
377.813 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
400.133 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
400.133 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
422.453 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
422.453 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
467.093 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
467.093 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
489.413 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
489.413 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
511.733 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
511.733 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

227.392 12.5438 translate
0 rotate
0 0 m /T glyphshow
13.521 0 m /e glyphshow
24.6172 0 m /m glyphshow
44.063 0 m /p glyphshow
56.563 0 m /e glyphshow
67.6592 0 m /r glyphshow
75.9844 0 m /a glyphshow
87.0806 0 m /t glyphshow
94.0264 0 m /u glyphshow
106.526 0 m /r glyphshow
114.852 0 m /e glyphshow
125.948 0 m /space glyphshow
132.198 0 m /parenleft glyphshow
140.523 0 m /K glyphshow
158.577 0 m /parenright glyphshow
grestore
2.500 setlinewidth
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
87.6531 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
534.053 66.6063 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

33.9031 57.9266 translate
0 rotate
0 0 m /seven glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /eight glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
87.6531 139.376 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
534.053 139.376 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

33.9031 130.697 translate
0 rotate
0 0 m /seven glyphshow
12.5 0 m /period glyphshow
18.75 0 m /one glyphshow
31.25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
87.6531 212.146 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
534.053 212.146 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

33.9031 203.467 translate
0 rotate
0 0 m /seven glyphshow
12.5 0 m /period glyphshow
18.75 0 m /one glyphshow
31.25 0 m /two glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
87.6531 284.917 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
534.053 284.917 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

33.9031 276.237 translate
0 rotate
0 0 m /seven glyphshow
12.5 0 m /period glyphshow
18.75 0 m /one glyphshow
31.25 0 m /four glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
87.6531 357.687 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
534.053 357.687 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

33.9031 349.007 translate
0 rotate
0 0 m /seven glyphshow
12.5 0 m /period glyphshow
18.75 0 m /one glyphshow
31.25 0 m /six glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
87.6531 84.7988 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
534.053 84.7988 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
87.6531 102.991 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
534.053 102.991 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
87.6531 121.184 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
534.053 121.184 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
87.6531 157.569 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
534.053 157.569 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
87.6531 175.761 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
534.053 175.761 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
87.6531 193.954 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
534.053 193.954 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
87.6531 230.339 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
534.053 230.339 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
87.6531 248.532 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
534.053 248.532 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
87.6531 266.724 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
534.053 266.724 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
87.6531 303.109 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
534.053 303.109 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
87.6531 321.302 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
534.053 321.302 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
87.6531 339.494 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
534.053 339.494 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
87.6531 375.879 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
534.053 375.879 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
87.6531 394.072 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
534.053 394.072 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
87.6531 412.264 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
534.053 412.264 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

24.5594 123.919 translate
90 rotate
0 0 m /L glyphshow
15.271 0 m /a glyphshow
26.3672 0 m /t glyphshow
33.313 0 m /t glyphshow
40.2588 0 m /i glyphshow
47.2046 0 m /c glyphshow
58.3008 0 m /e glyphshow
69.397 0 m /space glyphshow
75.647 0 m /P glyphshow
89.5508 0 m /a glyphshow
100.647 0 m /r glyphshow
108.972 0 m /a glyphshow
120.068 0 m /m glyphshow
139.514 0 m /e glyphshow
150.61 0 m /t glyphshow
157.556 0 m /e glyphshow
168.652 0 m /r glyphshow
176.978 0 m /s glyphshow
186.707 0 m /space glyphshow
192.957 0 m /parenleft glyphshow
201.282 0 m /a glyphshow
212.378 0 m /period glyphshow
218.628 0 m /u glyphshow
231.128 0 m /period glyphshow
237.378 0 m /parenright glyphshow
grestore
0.050 setlinewidth
[0.185 0.08] 0 setdash
0.031 0.235 0.235 setrgbcolor
gsave
446.4 360.36 87.653 66.606 clipbox
528.101125 111.447198 m
522.149125 110.599426 l
516.197125 109.831701 l
510.245125 109.547898 l
504.293125 108.736511 l
498.341125 108.296252 l
492.389125 107.917847 l
486.437125 107.372071 l
480.485125 107.084629 l
474.533125 106.695309 l
468.581125 106.458806 l
462.629125 107.266555 l
456.677125 108.030641 l
450.725125 108.390853 l
444.773125 110.039096 l
438.821125 112.811638 l
432.869125 179.272589 l
426.917125 195.096451 l
420.965125 206.590492 l
415.013125 215.941452 l
409.061125 224.310016 l
403.109125 231.059445 l
397.157125 238.689392 l
391.205125 245.151378 l
385.253125 250.951157 l
379.301125 256.820067 l
373.349125 262.470667 l
367.397125 267.786524 l
361.445125 272.378319 l
355.493125 277.301217 l
349.541125 281.598293 l
343.589125 286.564854 l
337.637125 290.83646 l
331.685125 294.857009 l
325.733125 299.099507 l
319.781125 302.785313 l
313.829125 306.853163 l
307.877125 310.640848 l
301.925125 314.159283 l
295.973125 318.092508 l
290.021125 321.32714 l
284.069125 324.667289 l
278.117125 328.556851 l
272.165125 331.67869 l
266.213125 334.847828 l
260.261125 338.115207 l
254.309125 341.389862 l
248.357125 344.184235 l
242.405125 347.45889 l
236.453125 350.322394 l
230.501125 353.302331 l
224.549125 356.205859 l
218.597125 359.065724 l
212.645125 361.911036 l
206.693125 364.741794 l
200.741125 367.590744 l
194.789125 370.232299 l
188.837125 372.862939 l
182.885125 375.482663 l
176.933125 378.033256 l
170.981125 380.729389 l
165.029125 383.290897 l
159.077125 385.881514 l
153.125125 388.221073 l
147.173125 390.775304 l
141.221125 393.129418 l
135.269125 395.465338 l
129.317125 397.964992 l
123.365125 400.279082 l
117.413125 402.574979 l
111.461125 404.903623 l
105.509125 407.217713 l
99.557125 409.44084 l
93.605125 411.703991 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
446.4 360.36 87.653 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

0 2 m
-2 -2 l
2 -2 l
cl

gsave
0.031 0.235 0.235 setrgbcolor
fill
grestore
stroke
grestore
} bind def
528.101 111.447 o
522.149 110.599 o
516.197 109.832 o
510.245 109.548 o
504.293 108.737 o
498.341 108.296 o
492.389 107.918 o
486.437 107.372 o
480.485 107.085 o
474.533 106.695 o
468.581 106.459 o
462.629 107.267 o
456.677 108.031 o
450.725 108.391 o
444.773 110.039 o
438.821 112.812 o
432.869 179.273 o
426.917 195.096 o
420.965 206.59 o
415.013 215.941 o
409.061 224.31 o
403.109 231.059 o
397.157 238.689 o
391.205 245.151 o
385.253 250.951 o
379.301 256.82 o
373.349 262.471 o
367.397 267.787 o
361.445 272.378 o
355.493 277.301 o
349.541 281.598 o
343.589 286.565 o
337.637 290.836 o
331.685 294.857 o
325.733 299.1 o
319.781 302.785 o
313.829 306.853 o
307.877 310.641 o
301.925 314.159 o
295.973 318.093 o
290.021 321.327 o
284.069 324.667 o
278.117 328.557 o
272.165 331.679 o
266.213 334.848 o
260.261 338.115 o
254.309 341.39 o
248.357 344.184 o
242.405 347.459 o
236.453 350.322 o
230.501 353.302 o
224.549 356.206 o
218.597 359.066 o
212.645 361.911 o
206.693 364.742 o
200.741 367.591 o
194.789 370.232 o
188.837 372.863 o
182.885 375.483 o
176.933 378.033 o
170.981 380.729 o
165.029 383.291 o
159.077 385.882 o
153.125 388.221 o
147.173 390.775 o
141.221 393.129 o
135.269 395.465 o
129.317 397.965 o
123.365 400.279 o
117.413 402.575 o
111.461 404.904 o
105.509 407.218 o
99.5571 409.441 o
93.6051 411.704 o
grestore
0.050 setlinewidth
1 setlinejoin
[0.185 0.08] 0 setdash
0.031 0.416 0.416 setrgbcolor
gsave
446.4 360.36 87.653 66.606 clipbox
528.101125 111.447198 m
522.149125 110.599426 l
516.197125 109.831701 l
510.245125 109.547898 l
504.293125 108.736511 l
498.341125 108.296252 l
492.389125 107.917847 l
486.437125 107.372071 l
480.485125 107.084629 l
474.533125 106.695309 l
468.581125 106.458806 l
462.629125 107.266555 l
456.677125 108.030641 l
450.725125 108.390853 l
444.773125 110.039096 l
438.821125 112.811638 l
432.869125 179.272589 l
426.917125 195.096451 l
420.965125 206.590492 l
415.013125 215.941452 l
409.061125 224.310016 l
403.109125 231.059445 l
397.157125 238.689392 l
391.205125 245.151378 l
385.253125 250.951157 l
379.301125 256.820067 l
373.349125 262.470667 l
367.397125 267.786524 l
361.445125 272.378319 l
355.493125 277.301217 l
349.541125 281.598293 l
343.589125 286.564854 l
337.637125 290.83646 l
331.685125 294.857009 l
325.733125 299.099507 l
319.781125 302.785313 l
313.829125 306.853163 l
307.877125 310.640848 l
301.925125 314.159283 l
295.973125 318.092508 l
290.021125 321.32714 l
284.069125 324.667289 l
278.117125 328.556851 l
272.165125 331.67869 l
266.213125 334.847828 l
260.261125 338.115207 l
254.309125 341.389862 l
248.357125 344.184235 l
242.405125 347.45889 l
236.453125 350.322394 l
230.501125 353.302331 l
224.549125 356.205859 l
218.597125 359.065724 l
212.645125 361.911036 l
206.693125 364.741794 l
200.741125 367.590744 l
194.789125 370.232299 l
188.837125 372.862939 l
182.885125 375.482663 l
176.933125 378.033256 l
170.981125 380.729389 l
165.029125 383.290897 l
159.077125 385.881514 l
153.125125 388.221073 l
147.173125 390.775304 l
141.221125 393.129418 l
135.269125 395.465338 l
129.317125 397.964992 l
123.365125 400.279082 l
117.413125 402.574979 l
111.461125 404.903623 l
105.509125 407.217713 l
99.557125 409.44084 l
93.605125 411.703991 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
446.4 360.36 87.653 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-4 -4 m
4 -4 l
4 4 l
-4 4 l
cl

gsave
grestore
stroke
grestore
} bind def
528.101 111.447 o
522.149 110.599 o
516.197 109.832 o
510.245 109.548 o
504.293 108.737 o
498.341 108.296 o
492.389 107.918 o
486.437 107.372 o
480.485 107.085 o
474.533 106.695 o
468.581 106.459 o
462.629 107.267 o
456.677 108.031 o
450.725 108.391 o
444.773 110.039 o
438.821 112.812 o
432.869 179.273 o
426.917 195.096 o
420.965 206.59 o
415.013 215.941 o
409.061 224.31 o
403.109 231.059 o
397.157 238.689 o
391.205 245.151 o
385.253 250.951 o
379.301 256.82 o
373.349 262.471 o
367.397 267.787 o
361.445 272.378 o
355.493 277.301 o
349.541 281.598 o
343.589 286.565 o
337.637 290.836 o
331.685 294.857 o
325.733 299.1 o
319.781 302.785 o
313.829 306.853 o
307.877 310.641 o
301.925 314.159 o
295.973 318.093 o
290.021 321.327 o
284.069 324.667 o
278.117 328.557 o
272.165 331.679 o
266.213 334.848 o
260.261 338.115 o
254.309 341.39 o
248.357 344.184 o
242.405 347.459 o
236.453 350.322 o
230.501 353.302 o
224.549 356.206 o
218.597 359.066 o
212.645 361.911 o
206.693 364.742 o
200.741 367.591 o
194.789 370.232 o
188.837 372.863 o
182.885 375.483 o
176.933 378.033 o
170.981 380.729 o
165.029 383.291 o
159.077 385.882 o
153.125 388.221 o
147.173 390.775 o
141.221 393.129 o
135.269 395.465 o
129.317 397.965 o
123.365 400.279 o
117.413 402.575 o
111.461 404.904 o
105.509 407.218 o
99.5571 409.441 o
93.6051 411.704 o
grestore
0.050 setlinewidth
1 setlinejoin
[0.185 0.08] 0 setdash
0.031 0.659 0.659 setrgbcolor
gsave
446.4 360.36 87.653 66.606 clipbox
528.101125 111.447198 m
522.149125 110.599426 l
516.197125 109.831701 l
510.245125 109.547898 l
504.293125 108.736511 l
498.341125 108.296252 l
492.389125 107.917847 l
486.437125 107.372071 l
480.485125 107.084629 l
474.533125 106.695309 l
468.581125 106.458806 l
462.629125 107.266555 l
456.677125 108.030641 l
450.725125 108.390853 l
444.773125 110.039096 l
438.821125 112.811638 l
432.869125 179.272589 l
426.917125 195.096451 l
420.965125 206.590492 l
415.013125 215.941452 l
409.061125 224.310016 l
403.109125 231.059445 l
397.157125 238.689392 l
391.205125 245.151378 l
385.253125 250.951157 l
379.301125 256.820067 l
373.349125 262.470667 l
367.397125 267.786524 l
361.445125 272.378319 l
355.493125 277.301217 l
349.541125 281.598293 l
343.589125 286.564854 l
337.637125 290.83646 l
331.685125 294.857009 l
325.733125 299.099507 l
319.781125 302.785313 l
313.829125 306.853163 l
307.877125 310.640848 l
301.925125 314.159283 l
295.973125 318.092508 l
290.021125 321.32714 l
284.069125 324.667289 l
278.117125 328.556851 l
272.165125 331.67869 l
266.213125 334.847828 l
260.261125 338.115207 l
254.309125 341.389862 l
248.357125 344.184235 l
242.405125 347.45889 l
236.453125 350.322394 l
230.501125 353.302331 l
224.549125 356.205859 l
218.597125 359.065724 l
212.645125 361.911036 l
206.693125 364.741794 l
200.741125 367.590744 l
194.789125 370.232299 l
188.837125 372.862939 l
182.885125 375.482663 l
176.933125 378.033256 l
170.981125 380.729389 l
165.029125 383.290897 l
159.077125 385.881514 l
153.125125 388.221073 l
147.173125 390.775304 l
141.221125 393.129418 l
135.269125 395.465338 l
129.317125 397.964992 l
123.365125 400.279082 l
117.413125 402.574979 l
111.461125 404.903623 l
105.509125 407.217713 l
99.557125 409.44084 l
93.605125 411.703991 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
446.4 360.36 87.653 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
528.101 111.447 o
522.149 110.599 o
516.197 109.832 o
510.245 109.548 o
504.293 108.737 o
498.341 108.296 o
492.389 107.918 o
486.437 107.372 o
480.485 107.085 o
474.533 106.695 o
468.581 106.459 o
462.629 107.267 o
456.677 108.031 o
450.725 108.391 o
444.773 110.039 o
438.821 112.812 o
432.869 179.273 o
426.917 195.096 o
420.965 206.59 o
415.013 215.941 o
409.061 224.31 o
403.109 231.059 o
397.157 238.689 o
391.205 245.151 o
385.253 250.951 o
379.301 256.82 o
373.349 262.471 o
367.397 267.787 o
361.445 272.378 o
355.493 277.301 o
349.541 281.598 o
343.589 286.565 o
337.637 290.836 o
331.685 294.857 o
325.733 299.1 o
319.781 302.785 o
313.829 306.853 o
307.877 310.641 o
301.925 314.159 o
295.973 318.093 o
290.021 321.327 o
284.069 324.667 o
278.117 328.557 o
272.165 331.679 o
266.213 334.848 o
260.261 338.115 o
254.309 341.39 o
248.357 344.184 o
242.405 347.459 o
236.453 350.322 o
230.501 353.302 o
224.549 356.206 o
218.597 359.066 o
212.645 361.911 o
206.693 364.742 o
200.741 367.591 o
194.789 370.232 o
188.837 372.863 o
182.885 375.483 o
176.933 378.033 o
170.981 380.729 o
165.029 383.291 o
159.077 385.882 o
153.125 388.221 o
147.173 390.775 o
141.221 393.129 o
135.269 395.465 o
129.317 397.965 o
123.365 400.279 o
117.413 402.575 o
111.461 404.904 o
105.509 407.218 o
99.5571 409.441 o
93.6051 411.704 o
grestore
2.500 setlinewidth
0 setlinejoin
2 setlinecap
0.000 setgray
gsave
87.653125 66.60625 m
87.653125 426.96625 l
stroke
grestore
gsave
534.053125 66.60625 m
534.053125 426.96625 l
stroke
grestore
gsave
87.653125 66.60625 m
534.053125 66.60625 l
stroke
grestore
gsave
87.653125 426.96625 m
534.053125 426.96625 l
stroke
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

213.531 246.786 translate
0 rotate
0 0 m /R glyphshow
16.6748 0 m /three glyphshow
29.1748 0 m /C glyphshow
grestore
gsave
447.485 246.786 translate
0 rotate
/TimesNewRomanPSMT 25.0 selectfont
0 0.921875 moveto
/P glyphshow
13.9038 0.921875 moveto
/m glyphshow
33.3496 0.546875 moveto
/three glyphshow
45.8496 0.921875 moveto
/m glyphshow
33.349609375 20.875 12.5 1.5625 rectfill
grestore
0.050 setlinewidth
1 setlinejoin
0 setlinecap
[0.185 0.08] 0 setdash
0.031 0.235 0.235 setrgbcolor
gsave
450.053125 401.96625 m
470.053125 401.96625 l
490.053125 401.96625 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

0 2 m
-2 -2 l
2 -2 l
cl

gsave
0.031 0.235 0.235 setrgbcolor
fill
grestore
stroke
grestore
} bind def
470.053 401.966 o
grestore
0.000 setgray
gsave
506.053 394.966 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.796875 moveto
/a glyphshow
grestore
0.050 setlinewidth
1 setlinejoin
[0.185 0.08] 0 setdash
0.031 0.416 0.416 setrgbcolor
gsave
450.053125 373.685 m
470.053125 373.685 l
490.053125 373.685 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-4 -4 m
4 -4 l
4 4 l
-4 4 l
cl

gsave
grestore
stroke
grestore
} bind def
470.053 373.685 o
grestore
0.000 setgray
gsave
506.053 366.685 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.109375 moveto
/b glyphshow
grestore
0.050 setlinewidth
1 setlinejoin
[0.185 0.08] 0 setdash
0.031 0.659 0.659 setrgbcolor
gsave
450.053125 345.40375 m
470.053125 345.40375 l
490.053125 345.40375 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
470.053 345.404 o
grestore
0.000 setgray
gsave
506.053 338.404 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.796875 moveto
/c glyphshow
grestore

end
showpage
