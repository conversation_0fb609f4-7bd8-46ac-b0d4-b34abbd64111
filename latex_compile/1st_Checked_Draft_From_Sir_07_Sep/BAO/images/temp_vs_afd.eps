%!PS-Adobe-3.0 EPSF-3.0
%%Title: temp_vs_afd.eps
%%Creator: Mat<PERSON>lotlib v3.7.1, https://matplotlib.org/
%%CreationDate: Wed Apr 24 13:11:23 2024
%%Orientation: portrait
%%BoundingBox: 21 178 591 614
%%HiResBoundingBox: 21.225000 178.916875 590.775000 613.083125
%%EndComments
%%BeginProlog
/mpldict 11 dict def
mpldict begin
/_d { bind def } bind def
/m { moveto } _d
/l { lineto } _d
/r { rlineto } _d
/c { curveto } _d
/cl { closepath } _d
/ce { closepath eofill } _d
/box {
      m
      1 index 0 r
      0 exch r
      neg 0 r
      cl
    } _d
/clipbox {
      box
      clip
      newpath
    } _d
/sc { setcachedevice } _d
%!PS-TrueTypeFont-1.0-2.53740
%%Title: unknown
%%Creator: Converted from TrueType to type 42 by PPR
15 dict begin
/FontName /TimesNewRomanPSMT def
/PaintType 0 def
/FontMatrix[1 0 0 1 0 0]def
/FontBBox[-568 -307 2028 1007]def
/FontType 42 def
/Encoding StandardEncoding def
/FontInfo 10 dict dup begin
/FamilyName (unknown) def
/FullName (unknown) def
/Weight (unknown) def
/Version (unknown) def
/ItalicAngle 0.0 def
/isFixedPitch false def
/UnderlinePosition -223 def
/UnderlineThickness 100 def
end readonly def
/sfnts[<00010000000900800003001063767420F34DDA810000009C000007C46670676D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>]def
/CharStrings 29 dict dup begin
/.notdef 0 def
/space 3 def
/parenleft 4 def
/parenright 5 def
/comma 6 def
/period 7 def
/zero 8 def
/one 9 def
/two 10 def
/three 11 def
/four 12 def
/five 13 def
/six 14 def
/nine 15 def
/less 16 def
/greater 17 def
/omega 30 def
/K 18 def
/T 19 def
/a 20 def
/e 21 def
/m 22 def
/p 23 def
/r 24 def
/t 25 def
/u 26 def
/x 27 def
/y 28 def
/z 29 def
end readonly def

systemdict/resourcestatus known
 {42 /FontType resourcestatus
   {pop pop false}{true}ifelse}
 {true}ifelse
{/TrueDict where{pop}{(%%[ Error: no TrueType rasterizer ]%%)= flush}ifelse
/FontType 3 def
 /TrueState 271 string def
 TrueDict begin sfnts save
 72 0 matrix defaultmatrix dtransform dup
 mul exch dup mul add sqrt cvi 0 72 matrix
 defaultmatrix dtransform dup mul exch dup
 mul add sqrt cvi 3 -1 roll restore
 TrueState initer end
 /BuildGlyph{exch begin
  CharStrings dup 2 index known
    {exch}{exch pop /.notdef}ifelse
  get dup xcheck
    {currentdict systemdict begin begin exec end end}
    {TrueDict begin /bander load cvlit exch TrueState render end}
    ifelse
 end}bind def
 /BuildChar{
  1 index /Encoding get exch get
  1 index /BuildGlyph get exec
 }bind def
}if

FontName currentdict end definefont pop
end
%%EndProlog
mpldict begin
21.225 178.917 translate
569.55 434.166 0 0 clipbox
gsave
0 -0 m
569.55 -0 l
569.55 434.16625 l
0 434.16625 l
cl
1.000 setgray
fill
grestore
gsave
90.95 66.60625 m
537.35 66.60625 l
537.35 426.96625 l
90.95 426.96625 l
cl
1.000 setgray
fill
grestore
2.500 setlinewidth
1 setlinejoin
0 setlinecap
[] 0 setdash
0.000 setgray
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
90.95 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
90.95 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

84.7 39.2469 translate
0 rotate
0 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
180.23 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
180.23 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

161.48 39.2469 translate
0 rotate
0 0 m /three glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
269.51 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
269.51 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

250.76 39.2469 translate
0 rotate
0 0 m /six glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
358.79 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
358.79 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

340.04 39.2469 translate
0 rotate
0 0 m /nine glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
448.07 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
448.07 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

423.07 39.2469 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /two glyphshow
25 0 m /zero glyphshow
37.5 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
537.35 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
537.35 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

512.35 39.2469 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /five glyphshow
25 0 m /zero glyphshow
37.5 0 m /zero glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
113.27 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
113.27 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
135.59 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
135.59 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
157.91 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
157.91 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
202.55 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
202.55 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
224.87 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
224.87 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
247.19 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
247.19 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
291.83 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
291.83 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
314.15 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
314.15 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
336.47 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
336.47 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
381.11 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
381.11 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
403.43 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
403.43 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
425.75 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
425.75 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
470.39 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
470.39 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
492.71 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
492.71 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
515.03 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
515.03 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

230.689 12.5438 translate
0 rotate
0 0 m /T glyphshow
13.521 0 m /e glyphshow
24.6172 0 m /m glyphshow
44.063 0 m /p glyphshow
56.563 0 m /e glyphshow
67.6592 0 m /r glyphshow
75.9844 0 m /a glyphshow
87.0806 0 m /t glyphshow
94.0264 0 m /u glyphshow
106.526 0 m /r glyphshow
114.852 0 m /e glyphshow
125.948 0 m /space glyphshow
132.198 0 m /parenleft glyphshow
140.523 0 m /K glyphshow
158.577 0 m /parenright glyphshow
grestore
2.500 setlinewidth
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
90.95 82.9793 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
537.35 82.9793 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

37.2 74.2996 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
90.95 137.675 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
537.35 137.675 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

37.2 128.995 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /one glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
90.95 192.37 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
537.35 192.37 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

37.2 183.69 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /two glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
90.95 247.065 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
537.35 247.065 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

37.2 238.386 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /three glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
90.95 301.761 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
537.35 301.761 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

37.2 293.081 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /four glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
90.95 356.456 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
537.35 356.456 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

37.2 347.776 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /five glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
90.95 411.151 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
537.35 411.151 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

37.2 402.472 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /six glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
90.95 72.0402 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
537.35 72.0402 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
90.95 93.9184 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
537.35 93.9184 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
90.95 104.857 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
537.35 104.857 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
90.95 115.797 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
537.35 115.797 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
90.95 126.736 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
537.35 126.736 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
90.95 148.614 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
537.35 148.614 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
90.95 159.553 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
537.35 159.553 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
90.95 170.492 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
537.35 170.492 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
90.95 181.431 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
537.35 181.431 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
90.95 203.309 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
537.35 203.309 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
90.95 214.248 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
537.35 214.248 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
90.95 225.187 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
537.35 225.187 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
90.95 236.126 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
537.35 236.126 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
90.95 258.004 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
537.35 258.004 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
90.95 268.944 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
537.35 268.944 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
90.95 279.883 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
537.35 279.883 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
90.95 290.822 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
537.35 290.822 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
90.95 312.7 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
537.35 312.7 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
90.95 323.639 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
537.35 323.639 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
90.95 334.578 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
537.35 334.578 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
90.95 345.517 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
537.35 345.517 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
90.95 367.395 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
537.35 367.395 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
90.95 378.334 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
537.35 378.334 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
90.95 389.273 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
537.35 389.273 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
90.95 400.212 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
537.35 400.212 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
90.95 422.09 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
537.35 422.09 o
grestore
gsave
22.2 203.786 translate
90 rotate
/TimesNewRomanPSMT 25.0 selectfont
0 0.6875 moveto
/less glyphshow
14.0991 0.6875 moveto
/omega glyphshow
/TimesNewRomanPSMT 17.5 selectfont
30.9299 -5.75313 moveto
/x glyphshow
39.6799 -5.75313 moveto
/comma glyphshow
46.7009 -5.75313 moveto
/y glyphshow
55.4509 -5.75313 moveto
/comma glyphshow
62.4719 -5.75313 moveto
/z glyphshow
/TimesNewRomanPSMT 25.0 selectfont
71.3127 0.6875 moveto
/greater glyphshow
grestore
0.050 setlinewidth
[0.185 0.08] 0 setdash
0.031 0.235 0.235 setrgbcolor
gsave
446.4 360.36 90.95 66.606 clipbox
531.398 83.306655 m
525.446 83.825222 l
519.494 83.064738 l
513.542 84.625524 l
507.59 83.873245 l
501.638 83.37136 l
495.686 84.367362 l
489.734 84.48709 l
483.782 83.481681 l
477.83 83.288606 l
471.878 83.249663 l
465.926 82.999377 l
459.974 83.042914 l
454.022 83.049861 l
448.07 83.579093 l
442.118 84.315347 l
436.166 243.73053 l
430.214 261.687506 l
424.262 273.0066 l
418.31 281.743964 l
412.358 289.704599 l
406.406 295.146458 l
400.454 301.771324 l
394.502 307.56925 l
388.55 311.167055 l
382.598 316.396642 l
376.646 321.046731 l
370.694 324.23383 l
364.742 327.439524 l
358.79 331.686181 l
352.838 334.490466 l
346.886 337.7007 l
340.934 340.889768 l
334.982 343.526521 l
329.03 346.166502 l
323.078 348.368974 l
317.126 351.423874 l
311.174 353.564978 l
305.222 355.710786 l
299.27 358.192806 l
293.318 360.28463 l
287.366 362.215485 l
281.414 364.50197 l
275.462 366.705153 l
269.51 368.652472 l
263.558 370.459935 l
257.606 372.171079 l
251.654 374.069062 l
245.702 376.105151 l
239.75 377.502618 l
233.798 379.114216 l
227.846 380.978507 l
221.894 382.637745 l
215.942 383.93233 l
209.99 385.654577 l
204.038 387.295985 l
198.086 388.763351 l
192.134 390.071883 l
186.182 391.508675 l
180.23 392.806487 l
174.278 394.388003 l
168.326 395.757301 l
162.374 397.083608 l
156.422 398.395148 l
150.47 399.700015 l
144.518 400.887342 l
138.566 402.142108 l
132.614 403.527705 l
126.662 404.751787 l
120.71 405.983199 l
114.758 407.04582 l
108.806 408.275262 l
102.854 409.418504 l
96.902 410.542384 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
446.4 360.36 90.95 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

0 2 m
-2 -2 l
2 -2 l
cl

gsave
0.031 0.235 0.235 setrgbcolor
fill
grestore
stroke
grestore
} bind def
531.398 83.3067 o
525.446 83.8252 o
519.494 83.0647 o
513.542 84.6255 o
507.59 83.8732 o
501.638 83.3714 o
495.686 84.3674 o
489.734 84.4871 o
483.782 83.4817 o
477.83 83.2886 o
471.878 83.2497 o
465.926 82.9994 o
459.974 83.0429 o
454.022 83.0499 o
448.07 83.5791 o
442.118 84.3153 o
436.166 243.731 o
430.214 261.688 o
424.262 273.007 o
418.31 281.744 o
412.358 289.705 o
406.406 295.146 o
400.454 301.771 o
394.502 307.569 o
388.55 311.167 o
382.598 316.397 o
376.646 321.047 o
370.694 324.234 o
364.742 327.44 o
358.79 331.686 o
352.838 334.49 o
346.886 337.701 o
340.934 340.89 o
334.982 343.527 o
329.03 346.167 o
323.078 348.369 o
317.126 351.424 o
311.174 353.565 o
305.222 355.711 o
299.27 358.193 o
293.318 360.285 o
287.366 362.215 o
281.414 364.502 o
275.462 366.705 o
269.51 368.652 o
263.558 370.46 o
257.606 372.171 o
251.654 374.069 o
245.702 376.105 o
239.75 377.503 o
233.798 379.114 o
227.846 380.979 o
221.894 382.638 o
215.942 383.932 o
209.99 385.655 o
204.038 387.296 o
198.086 388.763 o
192.134 390.072 o
186.182 391.509 o
180.23 392.806 o
174.278 394.388 o
168.326 395.757 o
162.374 397.084 o
156.422 398.395 o
150.47 399.7 o
144.518 400.887 o
138.566 402.142 o
132.614 403.528 o
126.662 404.752 o
120.71 405.983 o
114.758 407.046 o
108.806 408.275 o
102.854 409.419 o
96.902 410.542 o
grestore
0.050 setlinewidth
1 setlinejoin
[0.185 0.08] 0 setdash
0.031 0.416 0.416 setrgbcolor
gsave
446.4 360.36 90.95 66.606 clipbox
531.398 83.592001 m
525.446 83.000361 l
519.494 83.855687 l
513.542 83.45756 l
507.59 85.258186 l
501.638 83.804438 l
495.686 83.232926 l
489.734 84.17385 l
483.782 83.267767 l
477.83 84.141908 l
471.878 83.541572 l
465.926 85.731082 l
459.974 85.606157 l
454.022 87.196917 l
448.07 83.163408 l
442.118 87.860919 l
436.166 243.917862 l
430.214 260.94633 l
424.262 273.213294 l
418.31 281.468245 l
412.358 289.299361 l
406.406 296.163628 l
400.454 301.874479 l
394.502 306.835894 l
388.55 312.211791 l
382.598 316.925437 l
376.646 320.384754 l
370.694 324.970959 l
364.742 327.89984 l
358.79 331.008888 l
352.838 334.432051 l
346.886 337.687847 l
340.934 340.507775 l
334.982 343.299645 l
329.03 346.022106 l
323.078 348.684238 l
317.126 351.179495 l
311.174 353.844635 l
305.222 355.936076 l
299.27 358.620196 l
293.318 360.648846 l
287.366 362.835238 l
281.414 364.833697 l
275.462 366.592973 l
269.51 368.469297 l
263.558 370.640211 l
257.606 372.330899 l
251.654 373.948131 l
245.702 375.793935 l
239.75 377.570002 l
233.798 379.457867 l
227.846 380.95012 l
221.894 382.406001 l
215.942 384.276035 l
209.99 385.769875 l
204.038 387.211699 l
198.086 388.592975 l
192.134 390.136806 l
186.182 391.552486 l
180.23 392.976753 l
174.278 394.499308 l
168.326 395.818231 l
162.374 397.089625 l
156.422 398.310206 l
150.47 399.720471 l
144.518 401.045302 l
138.566 402.215017 l
132.614 403.346718 l
126.662 404.617456 l
120.71 405.896835 l
114.758 407.06644 l
108.806 408.222864 l
102.854 409.441914 l
96.902 410.558519 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
446.4 360.36 90.95 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-4 -4 m
4 -4 l
4 4 l
-4 4 l
cl

gsave
grestore
stroke
grestore
} bind def
531.398 83.592 o
525.446 83.0004 o
519.494 83.8557 o
513.542 83.4576 o
507.59 85.2582 o
501.638 83.8044 o
495.686 83.2329 o
489.734 84.1739 o
483.782 83.2678 o
477.83 84.1419 o
471.878 83.5416 o
465.926 85.7311 o
459.974 85.6062 o
454.022 87.1969 o
448.07 83.1634 o
442.118 87.8609 o
436.166 243.918 o
430.214 260.946 o
424.262 273.213 o
418.31 281.468 o
412.358 289.299 o
406.406 296.164 o
400.454 301.874 o
394.502 306.836 o
388.55 312.212 o
382.598 316.925 o
376.646 320.385 o
370.694 324.971 o
364.742 327.9 o
358.79 331.009 o
352.838 334.432 o
346.886 337.688 o
340.934 340.508 o
334.982 343.3 o
329.03 346.022 o
323.078 348.684 o
317.126 351.179 o
311.174 353.845 o
305.222 355.936 o
299.27 358.62 o
293.318 360.649 o
287.366 362.835 o
281.414 364.834 o
275.462 366.593 o
269.51 368.469 o
263.558 370.64 o
257.606 372.331 o
251.654 373.948 o
245.702 375.794 o
239.75 377.57 o
233.798 379.458 o
227.846 380.95 o
221.894 382.406 o
215.942 384.276 o
209.99 385.77 o
204.038 387.212 o
198.086 388.593 o
192.134 390.137 o
186.182 391.552 o
180.23 392.977 o
174.278 394.499 o
168.326 395.818 o
162.374 397.09 o
156.422 398.31 o
150.47 399.72 o
144.518 401.045 o
138.566 402.215 o
132.614 403.347 o
126.662 404.617 o
120.71 405.897 o
114.758 407.066 o
108.806 408.223 o
102.854 409.442 o
96.902 410.559 o
grestore
0.050 setlinewidth
1 setlinejoin
[0.185 0.08] 0 setdash
0.031 0.659 0.659 setrgbcolor
gsave
446.4 360.36 90.95 66.606 clipbox
531.398 83.426712 m
525.446 84.291062 l
519.494 83.029678 l
513.542 84.14251 l
507.59 83.774848 l
501.638 83.846717 l
495.686 84.016492 l
489.734 83.072559 l
483.782 83.019778 l
477.83 82.98625 l
471.878 83.719387 l
465.926 83.978861 l
459.974 84.972402 l
454.022 83.679568 l
448.07 85.469583 l
442.118 84.273068 l
436.166 243.548176 l
430.214 261.75593 l
424.262 272.360812 l
418.31 282.155984 l
412.358 289.555499 l
406.406 295.944846 l
400.454 302.408306 l
394.502 307.318745 l
388.55 312.211791 l
382.598 316.305848 l
376.646 320.026499 l
370.694 324.070619 l
364.742 328.150345 l
358.79 331.366486 l
352.838 333.990386 l
346.886 337.76907 l
340.934 340.708562 l
334.982 343.662986 l
329.03 346.287378 l
323.078 349.041508 l
317.126 351.289268 l
311.174 353.670868 l
305.222 356.197246 l
299.27 358.208504 l
293.318 360.250117 l
287.366 362.510348 l
281.414 364.694552 l
275.462 366.466299 l
269.51 368.465906 l
263.558 370.23191 l
257.606 372.17179 l
251.654 374.074969 l
245.702 375.66015 l
239.75 377.630167 l
233.798 379.261237 l
227.846 380.88558 l
221.894 382.550288 l
215.942 384.136617 l
209.99 385.684714 l
204.038 387.194525 l
198.086 388.823516 l
192.134 390.306253 l
186.182 391.713072 l
180.23 393.043153 l
174.278 394.332487 l
168.326 395.849517 l
162.374 397.1965 l
156.422 398.463955 l
150.47 399.626395 l
144.518 401.050225 l
138.566 402.261563 l
132.614 403.499537 l
126.662 404.711368 l
120.71 405.914118 l
114.758 407.127644 l
108.806 408.293257 l
102.854 409.41484 l
96.902 410.58625 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
446.4 360.36 90.95 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
531.398 83.4267 o
525.446 84.2911 o
519.494 83.0297 o
513.542 84.1425 o
507.59 83.7748 o
501.638 83.8467 o
495.686 84.0165 o
489.734 83.0726 o
483.782 83.0198 o
477.83 82.9863 o
471.878 83.7194 o
465.926 83.9789 o
459.974 84.9724 o
454.022 83.6796 o
448.07 85.4696 o
442.118 84.2731 o
436.166 243.548 o
430.214 261.756 o
424.262 272.361 o
418.31 282.156 o
412.358 289.555 o
406.406 295.945 o
400.454 302.408 o
394.502 307.319 o
388.55 312.212 o
382.598 316.306 o
376.646 320.026 o
370.694 324.071 o
364.742 328.15 o
358.79 331.366 o
352.838 333.99 o
346.886 337.769 o
340.934 340.709 o
334.982 343.663 o
329.03 346.287 o
323.078 349.042 o
317.126 351.289 o
311.174 353.671 o
305.222 356.197 o
299.27 358.209 o
293.318 360.25 o
287.366 362.51 o
281.414 364.695 o
275.462 366.466 o
269.51 368.466 o
263.558 370.232 o
257.606 372.172 o
251.654 374.075 o
245.702 375.66 o
239.75 377.63 o
233.798 379.261 o
227.846 380.886 o
221.894 382.55 o
215.942 384.137 o
209.99 385.685 o
204.038 387.195 o
198.086 388.824 o
192.134 390.306 o
186.182 391.713 o
180.23 393.043 o
174.278 394.332 o
168.326 395.85 o
162.374 397.196 o
156.422 398.464 o
150.47 399.626 o
144.518 401.05 o
138.566 402.262 o
132.614 403.5 o
126.662 404.711 o
120.71 405.914 o
114.758 407.128 o
108.806 408.293 o
102.854 409.415 o
96.902 410.586 o
grestore
2.500 setlinewidth
0 setlinejoin
2 setlinecap
0.000 setgray
gsave
90.95 66.60625 m
90.95 426.96625 l
stroke
grestore
gsave
537.35 66.60625 m
537.35 426.96625 l
stroke
grestore
gsave
90.95 66.60625 m
537.35 66.60625 l
stroke
grestore
gsave
90.95 426.96625 m
537.35 426.96625 l
stroke
grestore
0.050 setlinewidth
1 setlinejoin
0 setlinecap
[0.185 0.08] 0 setdash
0.031 0.235 0.235 setrgbcolor
gsave
407.35 401.96625 m
427.35 401.96625 l
447.35 401.96625 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

0 2 m
-2 -2 l
2 -2 l
cl

gsave
0.031 0.235 0.235 setrgbcolor
fill
grestore
stroke
grestore
} bind def
427.35 401.966 o
grestore
0.000 setgray
gsave
463.35 394.966 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
3.024 0.546875 moveto
/less glyphshow
17.3273 0.546875 moveto
/omega glyphshow
/TimesNewRomanPSMT 14.0 selectfont
30.7919 -4.60562 moveto
/x glyphshow
/TimesNewRomanPSMT 20.0 selectfont
41.6747 0.546875 moveto
/greater glyphshow
grestore
0.050 setlinewidth
1 setlinejoin
[0.185 0.08] 0 setdash
0.031 0.416 0.416 setrgbcolor
gsave
407.35 371.96625 m
427.35 371.96625 l
447.35 371.96625 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-4 -4 m
4 -4 l
4 4 l
-4 4 l
cl

gsave
grestore
stroke
grestore
} bind def
427.35 371.966 o
grestore
0.000 setgray
gsave
463.35 364.966 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
3.024 0.546875 moveto
/less glyphshow
17.3273 0.546875 moveto
/omega glyphshow
/TimesNewRomanPSMT 14.0 selectfont
30.7919 -4.60562 moveto
/y glyphshow
/TimesNewRomanPSMT 20.0 selectfont
38.6507 0.546875 moveto
/greater glyphshow
grestore
0.050 setlinewidth
1 setlinejoin
[0.185 0.08] 0 setdash
0.031 0.659 0.659 setrgbcolor
gsave
407.35 338.96625 m
427.35 338.96625 l
447.35 338.96625 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
427.35 338.966 o
grestore
0.000 setgray
gsave
463.35 331.966 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
3.024 0.546875 moveto
/less glyphshow
17.3273 0.546875 moveto
/omega glyphshow
/TimesNewRomanPSMT 14.0 selectfont
30.7919 -4.60562 moveto
/z glyphshow
/TimesNewRomanPSMT 20.0 selectfont
40.8885 0.546875 moveto
/greater glyphshow
grestore

end
showpage
