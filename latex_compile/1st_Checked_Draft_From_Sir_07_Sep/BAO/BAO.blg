This is BibTeX, Version 0.99d (TeX Live 2022/dev/Debian)
Capacity: max_strings=200000, hash_size=200000, hash_prime=170003
The top-level auxiliary file: BAO.aux
The style file: apsrev4-2.bst
Reallocated singl_function (elt_size=4) to 100 items from 50.
Reallocated singl_function (elt_size=4) to 100 items from 50.
Reallocated singl_function (elt_size=4) to 100 items from 50.
Reallocated singl_function (elt_size=4) to 100 items from 50.
Reallocated singl_function (elt_size=4) to 100 items from 50.
Reallocated singl_function (elt_size=4) to 100 items from 50.
Reallocated singl_function (elt_size=4) to 100 items from 50.
Reallocated wiz_functions (elt_size=4) to 6000 items from 3000.
Reallocated singl_function (elt_size=4) to 100 items from 50.
Database file #1: BAONotes.bib
Database file #2: BAO.bib
Repeated entry---line 1463 of file BAO.bib
 : @article{mani_atomistic_2013
 :                             ,
I'm skipping whatever remains of this entry
Warning--I didn't find a database entry for "baettig_theoretical_2005"
Warning--I didn't find a database entry for ""
Warning--I didn't find a database entry for "McCash-pto"
Warning--I didn't find a database entry for "mani-15-pzo"
Reallocated singl_function (elt_size=4) to 100 items from 50.
Reallocated wiz_functions (elt_size=4) to 9000 items from 6000.
apsrev4-2.bst 2019-01-14 (MD) hand-edited version of apsrev4-1.bst
Control: key (0) 
Control: author (72) initials jnrlst
Control: editor formatted (1) identically to author
Control: production of article title (-1) disabled
Control: page (0) single
Control: year (1) truncated
Control: production of eprint (0) enabled
You've used 27 entries,
            6079 wiz_defined-function locations,
            1878 strings with 22098 characters,
and the built_in function-call counts, 29352 in all, are:
= -- 2147
> -- 790
< -- 164
+ -- 249
- -- 188
* -- 4472
:= -- 2730
add.period$ -- 27
call.type$ -- 27
change.case$ -- 108
chr.to.int$ -- 24
cite$ -- 27
duplicate$ -- 2577
empty$ -- 2025
format.name$ -- 467
if$ -- 6046
int.to.chr$ -- 4
int.to.str$ -- 35
missing$ -- 418
newline$ -- 133
num.names$ -- 81
pop$ -- 1075
preamble$ -- 1
purify$ -- 135
quote$ -- 0
skip$ -- 1321
stack$ -- 0
substring$ -- 707
swap$ -- 2531
text.length$ -- 94
text.prefix$ -- 0
top$ -- 8
type$ -- 391
warning$ -- 0
while$ -- 81
width$ -- 0
write$ -- 269
(There was 1 error message)
