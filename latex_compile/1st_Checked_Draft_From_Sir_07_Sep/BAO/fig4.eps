%!PS-Adobe-3.0 EPSF-3.0
%%Title: hysteresis_n_derivatives.eps
%%Creator: Matplotlib v3.7.1, https://matplotlib.org/
%%CreationDate: Thu Aug 29 17:52:50 2024
%%Orientation: portrait
%%BoundingBox: -194 -14 807 807
%%HiResBoundingBox: -194.345796 -14.643160 806.345796 806.643160
%%EndComments
%%BeginProlog
/mpldict 11 dict def
mpldict begin
/_d { bind def } bind def
/m { moveto } _d
/l { lineto } _d
/r { rlineto } _d
/c { curveto } _d
/cl { closepath } _d
/ce { closepath eofill } _d
/box {
      m
      1 index 0 r
      0 exch r
      neg 0 r
      cl
    } _d
/clipbox {
      box
      clip
      newpath
    } _d
/sc { setcachedevice } _d
%!PS-TrueTypeFont-1.0-2.53740
%%Title: unknown
%%Creator: Converted from TrueType to type 42 by PPR
15 dict begin
/FontName /TimesNewRomanPSMT def
/PaintType 0 def
/FontMatrix[1 0 0 1 0 0]def
/FontBBox[-568 -307 2028 1007]def
/FontType 42 def
/Encoding StandardEncoding def
/FontInfo 10 dict dup begin
/FamilyName (unknown) def
/FullName (unknown) def
/Weight (unknown) def
/Version (unknown) def
/ItalicAngle 0.0 def
/isFixedPitch false def
/UnderlinePosition -223 def
/UnderlineThickness 100 def
end readonly def
/sfnts[<00010000000900800003001063767420F34DDA810000009C000007C46670676D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>]def
/CharStrings 47 dict dup begin
/.notdef 0 def
/minus 47 def
/space 3 def
/parenleft 4 def
/parenright 5 def
/period 6 def
/slash 7 def
/zero 8 def
/one 9 def
/two 10 def
/three 11 def
/four 12 def
/five 13 def
/six 14 def
/eight 15 def
/mu 48 def
/C 16 def
/E 17 def
/F 18 def
/K 19 def
/M 20 def
/P 21 def
/R 22 def
/S 23 def
/T 24 def
/V 25 def
/bracketleft 26 def
/bracketright 27 def
/a 28 def
/b 29 def
/c 30 def
/d 31 def
/e 32 def
/f 33 def
/i 34 def
/l 35 def
/m 36 def
/n 37 def
/o 38 def
/p 39 def
/r 40 def
/s 41 def
/t 42 def
/u 43 def
/v 44 def
/x 45 def
/z 46 def
end readonly def

systemdict/resourcestatus known
 {42 /FontType resourcestatus
   {pop pop false}{true}ifelse}
 {true}ifelse
{/TrueDict where{pop}{(%%[ Error: no TrueType rasterizer ]%%)= flush}ifelse
/FontType 3 def
 /TrueState 271 string def
 TrueDict begin sfnts save
 72 0 matrix defaultmatrix dtransform dup
 mul exch dup mul add sqrt cvi 0 72 matrix
 defaultmatrix dtransform dup mul exch dup
 mul add sqrt cvi 3 -1 roll restore
 TrueState initer end
 /BuildGlyph{exch begin
  CharStrings dup 2 index known
    {exch}{exch pop /.notdef}ifelse
  get dup xcheck
    {currentdict systemdict begin begin exec end end}
    {TrueDict begin /bander load cvlit exch TrueState render end}
    ifelse
 end}bind def
 /BuildChar{
  1 index /Encoding get exch get
  1 index /BuildGlyph get exec
 }bind def
}if

FontName currentdict end definefont pop
end
%%EndProlog
mpldict begin
-194.346 -14.643 translate
1000.692 821.286 0 0 clipbox
gsave
0 0 m
1000.691592 0 l
1000.691592 821.286321 l
0 821.286321 l
cl
1.000 setgray
fill
grestore
gsave
93.29375 473.969728 m
481.467663 473.969728 l
481.467663 787.32625 l
93.29375 787.32625 l
cl
1.000 setgray
fill
grestore
2.500 setlinewidth
1 setlinejoin
0 setlinecap
[] 0 setdash
0.000 setgray
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
110.938 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
110.938 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

97.6411 446.61 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /four glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
199.159 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
199.159 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

185.862 446.61 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /two glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
287.381 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
287.381 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

281.131 446.61 translate
0 rotate
0 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
375.602 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
375.602 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

369.352 446.61 translate
0 rotate
0 0 m /two glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
463.823 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
463.823 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

457.573 446.61 translate
0 rotate
0 0 m /four glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
132.993 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
132.993 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
155.049 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
155.049 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
177.104 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
177.104 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
221.215 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
221.215 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
243.27 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
243.27 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
265.325 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
265.325 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
309.436 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
309.436 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
331.491 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
331.491 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
353.547 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
353.547 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
397.657 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
397.657 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
419.713 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
419.713 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
441.768 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
441.768 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

169.334 419.907 translate
0 rotate
0 0 m /E glyphshow
15.271 0 m /l glyphshow
22.2168 0 m /e glyphshow
33.313 0 m /c glyphshow
44.4092 0 m /t glyphshow
51.355 0 m /r glyphshow
59.6802 0 m /i glyphshow
66.626 0 m /c glyphshow
77.7222 0 m /space glyphshow
83.9722 0 m /F glyphshow
97.876 0 m /i glyphshow
104.822 0 m /e glyphshow
115.918 0 m /l glyphshow
122.864 0 m /d glyphshow
135.364 0 m /space glyphshow
141.614 0 m /parenleft glyphshow
149.939 0 m /M glyphshow
172.168 0 m /V glyphshow
190.222 0 m /slash glyphshow
197.168 0 m /c glyphshow
208.264 0 m /m glyphshow
227.71 0 m /parenright glyphshow
grestore
2.500 setlinewidth
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 484.944 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
481.468 484.944 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

44.2 476.264 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /six glyphshow
26.5991 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 533.518 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
481.468 533.518 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

44.2 524.838 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /four glyphshow
26.5991 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 582.091 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
481.468 582.091 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

44.2 573.412 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /two glyphshow
26.5991 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 630.665 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
481.468 630.665 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

70.7938 621.985 translate
0 rotate
0 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 679.239 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
481.468 679.239 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

58.2938 670.559 translate
0 rotate
0 0 m /two glyphshow
12.5 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 727.812 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
481.468 727.812 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

58.2938 719.133 translate
0 rotate
0 0 m /four glyphshow
12.5 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 776.386 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
481.468 776.386 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

58.2938 767.706 translate
0 rotate
0 0 m /six glyphshow
12.5 0 m /zero glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 497.088 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
481.468 497.088 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 509.231 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
481.468 509.231 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 521.374 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
481.468 521.374 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 545.661 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
481.468 545.661 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 557.805 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
481.468 557.805 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 569.948 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
481.468 569.948 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 594.235 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
481.468 594.235 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 606.378 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
481.468 606.378 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 618.522 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
481.468 618.522 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 642.809 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
481.468 642.809 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 654.952 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
481.468 654.952 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 667.095 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
481.468 667.095 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 691.382 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
481.468 691.382 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 703.526 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
481.468 703.526 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 715.669 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
481.468 715.669 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 739.956 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
481.468 739.956 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 752.099 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
481.468 752.099 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 764.243 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
481.468 764.243 o
grestore
gsave
34.2 519.648 translate
90 rotate
/TimesNewRomanPSMT 25.0 selectfont
0 0.148438 moveto
/P glyphshow
13.9038 0.148438 moveto
/o glyphshow
26.4038 0.148438 moveto
/l glyphshow
33.3496 0.148438 moveto
/a glyphshow
44.4458 0.148438 moveto
/r glyphshow
52.771 0.148438 moveto
/i glyphshow
59.7168 0.148438 moveto
/z glyphshow
70.813 0.148438 moveto
/a glyphshow
81.9092 0.148438 moveto
/t glyphshow
88.855 0.148438 moveto
/i glyphshow
95.8008 0.148438 moveto
/o glyphshow
108.301 0.148438 moveto
/n glyphshow
120.801 0.148438 moveto
/space glyphshow
127.051 0.148438 moveto
/parenleft glyphshow
135.376 0.148438 moveto
/mu glyphshow
148.779 0.148438 moveto
/C glyphshow
165.454 0.148438 moveto
/slash glyphshow
172.4 0.148438 moveto
/c glyphshow
183.496 0.148438 moveto
/m glyphshow
/TimesNewRomanPSMT 17.5 selectfont
203.318 15.1766 moveto
/two glyphshow
/TimesNewRomanPSMT 25.0 selectfont
213.141 0.148438 moveto
/parenright glyphshow
grestore
2 setlinecap
0.749 0.212 0.047 setrgbcolor
gsave
388.174 313.357 93.294 473.97 clipbox
463.823394 773.077882 m
455.00126 772.178207 l
446.179125 771.273641 l
437.356991 770.347073 l
428.534857 769.376499 l
419.712722 768.396146 l
410.890588 767.40357 l
402.068454 766.406103 l
393.246319 765.337738 l
384.424185 764.318269 l
375.60205 763.240125 l
366.779916 762.157092 l
357.957782 761.005605 l
349.135647 759.856563 l
340.313513 758.648846 l
331.491378 757.458243 l
322.669244 756.20652 l
313.84711 754.903457 l
305.024975 753.580836 l
296.202841 752.155535 l
287.380707 750.742458 l
278.558572 749.285374 l
269.736438 747.737834 l
260.914303 746.077835 l
252.092169 744.43006 l
243.270035 742.664935 l
234.4479 740.816689 l
225.625766 738.797308 l
216.803631 736.733922 l
207.981497 734.448061 l
199.159363 731.927503 l
190.337228 729.174691 l
181.515094 726.118728 l
172.692959 722.424679 l
163.870825 717.958083 l
155.048691 711.159176 l
146.226556 491.939037 l
137.404422 490.958684 l
128.582288 490.083456 l
119.760153 489.134885 l
110.938019 488.213207 l
119.760153 489.151998 l
128.582288 490.037005 l
137.404422 491.027138 l
146.226556 491.946371 l
155.048691 492.936503 l
163.870825 493.936415 l
172.692959 494.882541 l
181.515094 495.933792 l
190.337228 496.992378 l
199.159363 498.048519 l
207.981497 499.156 l
216.803631 500.336824 l
225.625766 501.449195 l
234.4479 502.661801 l
243.270035 503.867073 l
252.092169 505.12613 l
260.914303 506.426748 l
269.736438 507.717587 l
278.558572 509.130664 l
287.380707 510.582858 l
296.202841 512.020383 l
305.024975 513.560589 l
313.84711 515.20103 l
322.669244 516.892811 l
331.491378 518.66527 l
340.313513 520.484179 l
349.135647 522.508449 l
357.957782 524.632955 l
366.779916 526.943263 l
375.60205 529.407592 l
384.424185 532.150625 l
393.246319 535.189474 l
402.068454 538.920194 l
410.890588 543.401459 l
419.712722 550.178363 l
428.534857 769.405837 l
437.356991 770.32996 l
446.179125 771.276086 l
455.00126 772.183096 l
463.823394 773.082772 l
463.823394 773.082772 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
0 setlinecap
gsave
388.174 313.357 93.294 473.97 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

0 5 m
-5 -5 l
5 -5 l
cl

gsave
0.749 0.212 0.047 setrgbcolor
fill
grestore
stroke
grestore
} bind def
463.823 773.078 o
455.001 772.178 o
446.179 771.274 o
437.357 770.347 o
428.535 769.376 o
419.713 768.396 o
410.891 767.404 o
402.068 766.406 o
393.246 765.338 o
384.424 764.318 o
375.602 763.24 o
366.78 762.157 o
357.958 761.006 o
349.136 759.857 o
340.314 758.649 o
331.491 757.458 o
322.669 756.207 o
313.847 754.903 o
305.025 753.581 o
296.203 752.156 o
287.381 750.742 o
278.559 749.285 o
269.736 747.738 o
260.914 746.078 o
252.092 744.43 o
243.27 742.665 o
234.448 740.817 o
225.626 738.797 o
216.804 736.734 o
207.981 734.448 o
199.159 731.928 o
190.337 729.175 o
181.515 726.119 o
172.693 722.425 o
163.871 717.958 o
155.049 711.159 o
146.227 491.939 o
137.404 490.959 o
128.582 490.083 o
119.76 489.135 o
110.938 488.213 o
119.76 489.152 o
128.582 490.037 o
137.404 491.027 o
146.227 491.946 o
155.049 492.937 o
163.871 493.936 o
172.693 494.883 o
181.515 495.934 o
190.337 496.992 o
199.159 498.049 o
207.981 499.156 o
216.804 500.337 o
225.626 501.449 o
234.448 502.662 o
243.27 503.867 o
252.092 505.126 o
260.914 506.427 o
269.736 507.718 o
278.559 509.131 o
287.381 510.583 o
296.203 512.02 o
305.025 513.561 o
313.847 515.201 o
322.669 516.893 o
331.491 518.665 o
340.314 520.484 o
349.136 522.508 o
357.958 524.633 o
366.78 526.943 o
375.602 529.408 o
384.424 532.151 o
393.246 535.189 o
402.068 538.92 o
410.891 543.401 o
419.713 550.178 o
428.535 769.406 o
437.357 770.33 o
446.179 771.276 o
455.001 772.183 o
463.823 773.083 o
463.823 773.083 o
grestore
1.500 setlinewidth
1 setlinejoin
[5.55 2.4] 0 setdash
0.847 0.263 0.082 setrgbcolor
gsave
388.174 313.357 93.294 473.97 clipbox
463.823394 770.11971 m
455.00126 769.061124 l
446.179125 768.119887 l
437.356991 767.049078 l
428.534857 766.044277 l
419.712722 765.05659 l
410.890588 763.895324 l
402.068454 762.829404 l
393.246319 761.690141 l
384.424185 760.484869 l
375.60205 759.282041 l
366.779916 758.221011 l
357.957782 756.849495 l
349.135647 755.619775 l
340.313513 754.194474 l
331.491378 752.90608 l
322.669244 751.412325 l
313.84711 749.947907 l
305.024975 748.380809 l
296.202841 746.904168 l
287.380707 745.102372 l
278.558572 743.393477 l
269.736438 741.481666 l
260.914303 739.623641 l
252.092169 737.469798 l
243.270035 735.015248 l
234.4479 732.690271 l
225.625766 730.010802 l
216.803631 726.727476 l
207.981497 723.170334 l
199.159363 718.544828 l
190.337228 711.824154 l
181.515094 499.583835 l
172.692959 498.451906 l
163.870825 497.422658 l
155.048691 496.344514 l
146.226556 495.303041 l
137.404422 494.266459 l
128.582288 493.144309 l
119.760153 492.259302 l
110.938019 491.266725 l
119.760153 492.173735 l
128.582288 493.249434 l
137.404422 494.244456 l
146.226556 495.241922 l
155.048691 496.312732 l
163.870825 497.400655 l
172.692959 498.513025 l
181.515094 499.659623 l
190.337228 500.825778 l
199.159363 501.97482 l
207.981497 503.243656 l
216.803631 504.483155 l
225.625766 505.700651 l
234.4479 507.118618 l
243.270035 508.37034 l
252.092169 509.81031 l
260.914303 511.404301 l
269.736438 512.810044 l
278.558572 514.494491 l
287.380707 516.20583 l
296.202841 517.985623 l
305.024975 519.83387 l
313.84711 521.767684 l
322.669244 523.975312 l
331.491378 526.129154 l
340.313513 528.622821 l
349.135647 531.412304 l
357.957782 534.526942 l
366.779916 538.16476 l
375.60205 542.616688 l
384.424185 549.24935 l
393.246319 761.658359 l
402.068454 762.853851 l
410.890588 763.880655 l
419.712722 765.012584 l
428.534857 766.002716 l
437.356991 767.107752 l
446.179125 768.110108 l
455.00126 769.122243 l
463.823394 770.031698 l
463.823394 770.031698 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
388.174 313.357 93.294 473.97 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-5 -5 m
5 -5 l
5 5 l
-5 5 l
cl

gsave
grestore
stroke
grestore
} bind def
463.823 770.12 o
455.001 769.061 o
446.179 768.12 o
437.357 767.049 o
428.535 766.044 o
419.713 765.057 o
410.891 763.895 o
402.068 762.829 o
393.246 761.69 o
384.424 760.485 o
375.602 759.282 o
366.78 758.221 o
357.958 756.849 o
349.136 755.62 o
340.314 754.194 o
331.491 752.906 o
322.669 751.412 o
313.847 749.948 o
305.025 748.381 o
296.203 746.904 o
287.381 745.102 o
278.559 743.393 o
269.736 741.482 o
260.914 739.624 o
252.092 737.47 o
243.27 735.015 o
234.448 732.69 o
225.626 730.011 o
216.804 726.727 o
207.981 723.17 o
199.159 718.545 o
190.337 711.824 o
181.515 499.584 o
172.693 498.452 o
163.871 497.423 o
155.049 496.345 o
146.227 495.303 o
137.404 494.266 o
128.582 493.144 o
119.76 492.259 o
110.938 491.267 o
119.76 492.174 o
128.582 493.249 o
137.404 494.244 o
146.227 495.242 o
155.049 496.313 o
163.871 497.401 o
172.693 498.513 o
181.515 499.66 o
190.337 500.826 o
199.159 501.975 o
207.981 503.244 o
216.804 504.483 o
225.626 505.701 o
234.448 507.119 o
243.27 508.37 o
252.092 509.81 o
260.914 511.404 o
269.736 512.81 o
278.559 514.494 o
287.381 516.206 o
296.203 517.986 o
305.025 519.834 o
313.847 521.768 o
322.669 523.975 o
331.491 526.129 o
340.314 528.623 o
349.136 531.412 o
357.958 534.527 o
366.78 538.165 o
375.602 542.617 o
384.424 549.249 o
393.246 761.658 o
402.068 762.854 o
410.891 763.881 o
419.713 765.013 o
428.535 766.003 o
437.357 767.108 o
446.179 768.11 o
455.001 769.122 o
463.823 770.032 o
463.823 770.032 o
grestore
1.500 setlinewidth
1 setlinejoin
[1.5 2.475] 0 setdash
0.937 0.424 0.000 setrgbcolor
gsave
388.174 313.357 93.294 473.97 clipbox
463.823394 767.892524 m
455.00126 766.902392 l
446.179125 765.770463 l
437.356991 764.665426 l
428.534857 763.562835 l
419.712722 762.49447 l
410.890588 761.333204 l
402.068454 760.171938 l
393.246319 758.907992 l
384.424185 757.639156 l
375.60205 756.326314 l
366.779916 754.986579 l
357.957782 753.605284 l
349.135647 752.258215 l
340.313513 750.810911 l
331.491378 749.209586 l
322.669244 747.772061 l
313.84711 746.014271 l
305.024975 744.298042 l
296.202841 742.515804 l
287.380707 740.520872 l
278.558572 738.327912 l
269.736438 736.306087 l
260.914303 733.761081 l
252.092169 731.123173 l
243.270035 728.228565 l
234.4479 724.568743 l
225.625766 720.620438 l
216.803631 714.970573 l
207.981497 506.245835 l
199.159363 504.91588 l
190.337228 503.617706 l
181.515094 502.439327 l
172.692959 501.204718 l
163.870825 500.099681 l
155.048691 498.818622 l
146.226556 497.657356 l
137.404422 496.674558 l
128.582288 495.574411 l
119.760153 494.594058 l
110.938019 493.398565 l
119.760153 494.493822 l
128.582288 495.58419 l
137.404422 496.70634 l
146.226556 497.843158 l
155.048691 498.899299 l
163.870825 500.009225 l
172.692959 501.275616 l
181.515094 502.419769 l
190.337228 503.52725 l
199.159363 504.991667 l
207.981497 506.35096 l
216.803631 507.561121 l
225.625766 509.091548 l
234.4479 510.45084 l
243.270035 512.064389 l
252.092169 513.594815 l
260.914303 515.254815 l
269.736438 516.985712 l
278.558572 518.897523 l
287.380707 520.855784 l
296.202841 522.887389 l
305.024975 525.119464 l
313.84711 527.605796 l
322.669244 530.128799 l
331.491378 533.204321 l
340.313513 536.482758 l
349.135647 540.827116 l
357.957782 546.643225 l
366.779916 755.1406 l
375.60205 756.358096 l
384.424185 757.736946 l
393.246319 758.915326 l
402.068454 760.054589 l
410.890588 761.294088 l
419.712722 762.526252 l
428.534857 763.572614 l
437.356991 764.794999 l
446.179125 765.728902 l
455.00126 766.741037 l
463.823394 767.865631 l
463.823394 767.865631 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
388.174 313.357 93.294 473.97 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -5 m
1.326016 -5 2.597899 -4.473168 3.535534 -3.535534 c
4.473168 -2.597899 5 -1.326016 5 0 c
5 1.326016 4.473168 2.597899 3.535534 3.535534 c
2.597899 4.473168 1.326016 5 0 5 c
-1.326016 5 -2.597899 4.473168 -3.535534 3.535534 c
-4.473168 2.597899 -5 1.326016 -5 0 c
-5 -1.326016 -4.473168 -2.597899 -3.535534 -3.535534 c
-2.597899 -4.473168 -1.326016 -5 0 -5 c
cl

gsave
grestore
stroke
grestore
} bind def
463.823 767.893 o
455.001 766.902 o
446.179 765.77 o
437.357 764.665 o
428.535 763.563 o
419.713 762.494 o
410.891 761.333 o
402.068 760.172 o
393.246 758.908 o
384.424 757.639 o
375.602 756.326 o
366.78 754.987 o
357.958 753.605 o
349.136 752.258 o
340.314 750.811 o
331.491 749.21 o
322.669 747.772 o
313.847 746.014 o
305.025 744.298 o
296.203 742.516 o
287.381 740.521 o
278.559 738.328 o
269.736 736.306 o
260.914 733.761 o
252.092 731.123 o
243.27 728.229 o
234.448 724.569 o
225.626 720.62 o
216.804 714.971 o
207.981 506.246 o
199.159 504.916 o
190.337 503.618 o
181.515 502.439 o
172.693 501.205 o
163.871 500.1 o
155.049 498.819 o
146.227 497.657 o
137.404 496.675 o
128.582 495.574 o
119.76 494.594 o
110.938 493.399 o
119.76 494.494 o
128.582 495.584 o
137.404 496.706 o
146.227 497.843 o
155.049 498.899 o
163.871 500.009 o
172.693 501.276 o
181.515 502.42 o
190.337 503.527 o
199.159 504.992 o
207.981 506.351 o
216.804 507.561 o
225.626 509.092 o
234.448 510.451 o
243.27 512.064 o
252.092 513.595 o
260.914 515.255 o
269.736 516.986 o
278.559 518.898 o
287.381 520.856 o
296.203 522.887 o
305.025 525.119 o
313.847 527.606 o
322.669 530.129 o
331.491 533.204 o
340.314 536.483 o
349.136 540.827 o
357.958 546.643 o
366.78 755.141 o
375.602 756.358 o
384.424 757.737 o
393.246 758.915 o
402.068 760.055 o
410.891 761.294 o
419.713 762.526 o
428.535 763.573 o
437.357 764.795 o
446.179 765.729 o
455.001 766.741 o
463.823 767.866 o
463.823 767.866 o
grestore
1.500 setlinewidth
[9.6 2.4 1.5 2.4] 0 setdash
1.000 0.702 0.000 setrgbcolor
gsave
388.174 313.357 93.294 473.97 clipbox
463.823394 765.36952 m
455.00126 764.259594 l
446.179125 763.068991 l
437.356991 762.088638 l
428.534857 760.797799 l
419.712722 759.528963 l
410.890588 758.304133 l
402.068454 757.155091 l
393.246319 755.795799 l
384.424185 754.487846 l
375.60205 753.030763 l
366.779916 751.559011 l
357.957782 749.940573 l
349.135647 748.37592 l
340.313513 746.72081 l
331.491378 745.033918 l
322.669244 743.149 l
313.84711 741.298309 l
305.024975 739.212919 l
296.202841 736.944172 l
287.380707 734.624085 l
278.558572 731.934837 l
269.736438 729.074455 l
260.914303 725.654221 l
252.092169 721.965062 l
243.270035 716.967951 l
234.4479 709.677645 l
225.625766 512.944506 l
216.803631 511.269838 l
207.981497 509.790752 l
199.159363 508.387454 l
190.337228 507.001269 l
181.515094 505.588192 l
172.692959 504.27535 l
163.870825 502.957618 l
155.048691 501.676558 l
146.226556 500.449284 l
137.404422 499.410256 l
128.582288 498.158534 l
119.760153 496.941038 l
110.938019 496.00958 l
119.760153 497.036384 l
128.582288 498.046074 l
137.404422 499.354026 l
146.226556 500.43217 l
155.048691 501.681448 l
163.870825 502.864717 l
172.692959 504.153111 l
181.515094 505.568633 l
190.337228 506.927926 l
199.159363 508.392343 l
207.981497 509.851872 l
216.803631 511.308955 l
225.625766 512.912724 l
234.4479 514.577613 l
243.270035 516.337848 l
252.092169 518.034519 l
260.914303 520.024562 l
269.736438 522.173516 l
278.558572 524.158669 l
287.380707 526.671894 l
296.202841 529.278019 l
305.024975 532.101729 l
313.84711 535.426617 l
322.669244 539.240459 l
331.491378 544.264463 l
340.313513 551.481426 l
349.135647 748.556833 l
357.957782 749.972355 l
366.779916 751.556566 l
375.60205 752.979423 l
384.424185 754.446285 l
393.246319 755.910703 l
402.068454 757.093972 l
410.890588 758.269906 l
419.712722 759.646312 l
428.534857 760.73179 l
437.356991 761.998182 l
446.179125 763.154558 l
455.00126 764.315824 l
463.823394 765.340183 l
463.823394 765.340183 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
388.174 313.357 93.294 473.97 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

5 -0 m
-5 5 l
-5 -5 l
cl

gsave
grestore
stroke
grestore
} bind def
463.823 765.37 o
455.001 764.26 o
446.179 763.069 o
437.357 762.089 o
428.535 760.798 o
419.713 759.529 o
410.891 758.304 o
402.068 757.155 o
393.246 755.796 o
384.424 754.488 o
375.602 753.031 o
366.78 751.559 o
357.958 749.941 o
349.136 748.376 o
340.314 746.721 o
331.491 745.034 o
322.669 743.149 o
313.847 741.298 o
305.025 739.213 o
296.203 736.944 o
287.381 734.624 o
278.559 731.935 o
269.736 729.074 o
260.914 725.654 o
252.092 721.965 o
243.27 716.968 o
234.448 709.678 o
225.626 512.945 o
216.804 511.27 o
207.981 509.791 o
199.159 508.387 o
190.337 507.001 o
181.515 505.588 o
172.693 504.275 o
163.871 502.958 o
155.049 501.677 o
146.227 500.449 o
137.404 499.41 o
128.582 498.159 o
119.76 496.941 o
110.938 496.01 o
119.76 497.036 o
128.582 498.046 o
137.404 499.354 o
146.227 500.432 o
155.049 501.681 o
163.871 502.865 o
172.693 504.153 o
181.515 505.569 o
190.337 506.928 o
199.159 508.392 o
207.981 509.852 o
216.804 511.309 o
225.626 512.913 o
234.448 514.578 o
243.27 516.338 o
252.092 518.035 o
260.914 520.025 o
269.736 522.174 o
278.559 524.159 o
287.381 526.672 o
296.203 529.278 o
305.025 532.102 o
313.847 535.427 o
322.669 539.24 o
331.491 544.264 o
340.314 551.481 o
349.136 748.557 o
357.958 749.972 o
366.78 751.557 o
375.602 752.979 o
384.424 754.446 o
393.246 755.911 o
402.068 757.094 o
410.891 758.27 o
419.713 759.646 o
428.535 760.732 o
437.357 761.998 o
446.179 763.155 o
455.001 764.316 o
463.823 765.34 o
463.823 765.34 o
grestore
0.500 setlinewidth
1 setlinejoin
[1.85 0.8] 0 setdash
0.000 0.000 1.000 setrgbcolor
gsave
388.174 313.357 93.294 473.97 clipbox
287.380707 473.969728 m
287.380707 787.32625 l
stroke
grestore
gsave
388.174 313.357 93.294 473.97 clipbox
93.29375 630.665103 m
481.467663 630.665103 l
stroke
grestore
2.500 setlinewidth
0 setlinejoin
2 setlinecap
[] 0 setdash
0.000 setgray
gsave
93.29375 473.969728 m
93.29375 787.32625 l
stroke
grestore
gsave
481.467663 473.969728 m
481.467663 787.32625 l
stroke
grestore
gsave
93.29375 473.969728 m
481.467663 473.969728 l
stroke
grestore
gsave
93.29375 787.32625 m
481.467663 787.32625 l
stroke
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

93.2938 796.727 translate
0 rotate
0 0 m /parenleft glyphshow
8.3252 0 m /a glyphshow
19.4214 0 m /parenright glyphshow
grestore
1.500 setlinewidth
1 setlinejoin
0.749 0.212 0.047 setrgbcolor
gsave
234.661957 675.210489 m
254.661957 675.210489 l
274.661957 675.210489 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
0 setlinecap
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

0 5 m
-5 -5 l
5 -5 l
cl

gsave
0.749 0.212 0.047 setrgbcolor
fill
grestore
stroke
grestore
} bind def
254.662 675.21 o
grestore
0.000 setgray
/TimesNewRomanPSMT 20.000 selectfont
gsave

290.662 668.21 translate
0 rotate
0 0 m /one glyphshow
10 0 m /zero glyphshow
20 0 m /zero glyphshow
30 0 m /space glyphshow
35 0 m /K glyphshow
grestore
1.500 setlinewidth
1 setlinejoin
[5.55 2.4] 0 setdash
0.847 0.263 0.082 setrgbcolor
gsave
234.661957 646.929239 m
254.661957 646.929239 l
274.661957 646.929239 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-5 -5 m
5 -5 l
5 5 l
-5 5 l
cl

gsave
grestore
stroke
grestore
} bind def
254.662 646.929 o
grestore
0.000 setgray
/TimesNewRomanPSMT 20.000 selectfont
gsave

290.662 639.929 translate
0 rotate
0 0 m /four glyphshow
10 0 m /zero glyphshow
20 0 m /zero glyphshow
30 0 m /space glyphshow
35 0 m /K glyphshow
grestore
1.500 setlinewidth
1 setlinejoin
[1.5 2.475] 0 setdash
0.937 0.424 0.000 setrgbcolor
gsave
234.661957 618.647989 m
254.661957 618.647989 l
274.661957 618.647989 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -5 m
1.326016 -5 2.597899 -4.473168 3.535534 -3.535534 c
4.473168 -2.597899 5 -1.326016 5 0 c
5 1.326016 4.473168 2.597899 3.535534 3.535534 c
2.597899 4.473168 1.326016 5 0 5 c
-1.326016 5 -2.597899 4.473168 -3.535534 3.535534 c
-4.473168 2.597899 -5 1.326016 -5 0 c
-5 -1.326016 -4.473168 -2.597899 -3.535534 -3.535534 c
-2.597899 -4.473168 -1.326016 -5 0 -5 c
cl

gsave
grestore
stroke
grestore
} bind def
254.662 618.648 o
grestore
0.000 setgray
/TimesNewRomanPSMT 20.000 selectfont
gsave

290.662 611.648 translate
0 rotate
0 0 m /six glyphshow
10 0 m /zero glyphshow
20 0 m /zero glyphshow
30 0 m /space glyphshow
35 0 m /K glyphshow
grestore
1.500 setlinewidth
[9.6 2.4 1.5 2.4] 0 setdash
1.000 0.702 0.000 setrgbcolor
gsave
234.661957 590.366739 m
254.661957 590.366739 l
274.661957 590.366739 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

5 -0 m
-5 5 l
-5 -5 l
cl

gsave
grestore
stroke
grestore
} bind def
254.662 590.367 o
grestore
0.000 setgray
/TimesNewRomanPSMT 20.000 selectfont
gsave

290.662 583.367 translate
0 rotate
0 0 m /eight glyphshow
10 0 m /zero glyphshow
20 0 m /zero glyphshow
30 0 m /space glyphshow
35 0 m /K glyphshow
grestore
gsave
597.919837 473.969728 m
986.09375 473.969728 l
986.09375 787.32625 l
597.919837 787.32625 l
cl
1.000 setgray
fill
grestore
1 setlinejoin
0.031 0.659 0.255 setrgbcolor
gsave
388.174 313.357 597.92 473.97 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

-8.660254 -8.660254 m
8.660254 -8.660254 l
8.660254 8.660254 l
-8.660254 8.660254 l
cl

gsave
0.031 0.659 0.255 setrgbcolor
fill
grestore
stroke
grestore
} bind def
662.029 488.213 o
708.35 544.33 o
745.407 600.447 o
782.465 625.954 o
grestore
2.500 setlinewidth
0.031 0.416 0.416 setrgbcolor
gsave
388.174 313.357 597.92 473.97 clipbox
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-11.18034 0 m
11.18034 0 l
0 -11.18034 m
0 11.18034 l

gsave
0.031 0.416 0.416 setrgbcolor
fill
grestore
stroke
grestore
} bind def
709.091 641.259 o
grestore
1.000 setlinewidth
0.031 0.659 0.659 setrgbcolor
gsave
388.174 313.357 597.92 473.97 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 13.228757 m
-2.970041 4.08791 l
-12.581295 4.087911 l
-4.805627 -1.561443 l
-7.775668 -10.702289 l
-0 -5.052935 l
7.775668 -10.702289 l
4.805627 -1.561443 l
12.581295 4.087911 l
2.970041 4.08791 l
cl

gsave
0.031 0.659 0.659 setrgbcolor
fill
grestore
stroke
grestore
} bind def
709.091 554.533 o
grestore
2.500 setlinewidth
0.000 setgray
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
597.92 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
597.92 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

591.67 446.61 translate
0 rotate
0 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
672.034 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
672.034 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

653.284 446.61 translate
0 rotate
0 0 m /two glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
746.149 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
746.149 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

727.399 446.61 translate
0 rotate
0 0 m /four glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
820.263 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
820.263 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

801.513 446.61 translate
0 rotate
0 0 m /six glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
894.377 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
894.377 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

875.627 446.61 translate
0 rotate
0 0 m /eight glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
968.492 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
968.492 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

943.492 446.61 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
37.5 0 m /zero glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
616.448 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
616.448 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
634.977 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
634.977 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
653.506 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
653.506 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
690.563 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
690.563 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
709.091 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
709.091 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
727.62 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
727.62 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
764.677 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
764.677 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
783.206 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
783.206 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
801.734 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
801.734 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
838.791 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
838.791 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
857.32 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
857.32 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
875.849 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
875.849 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
912.906 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
912.906 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
931.434 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
931.434 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
949.963 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
949.963 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

708.546 419.907 translate
0 rotate
0 0 m /T glyphshow
13.521 0 m /e glyphshow
24.6172 0 m /m glyphshow
44.063 0 m /p glyphshow
56.563 0 m /e glyphshow
67.6592 0 m /r glyphshow
75.9844 0 m /a glyphshow
87.0806 0 m /t glyphshow
94.0264 0 m /u glyphshow
106.526 0 m /r glyphshow
114.852 0 m /e glyphshow
125.948 0 m /space glyphshow
132.198 0 m /parenleft glyphshow
140.523 0 m /K glyphshow
158.577 0 m /parenright glyphshow
grestore
2.500 setlinewidth
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
597.92 574.939 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.094 574.939 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

562.92 566.259 translate
0 rotate
0 0 m /two glyphshow
12.5 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
597.92 676.969 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.094 676.969 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

562.92 668.29 translate
0 rotate
0 0 m /four glyphshow
12.5 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
597.92 778.999 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.094 778.999 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

562.92 770.32 translate
0 rotate
0 0 m /six glyphshow
12.5 0 m /zero glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
597.92 498.416 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.094 498.416 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
597.92 523.924 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.094 523.924 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
597.92 549.431 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.094 549.431 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
597.92 600.447 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.094 600.447 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
597.92 625.954 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.094 625.954 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
597.92 651.462 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.094 651.462 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
597.92 702.477 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.094 702.477 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
597.92 727.984 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.094 727.984 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
597.92 753.492 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.094 753.492 o
grestore
gsave
552.92 519.648 translate
90 rotate
/TimesNewRomanPSMT 25.0 selectfont
0 0.148438 moveto
/P glyphshow
13.9038 0.148438 moveto
/o glyphshow
26.4038 0.148438 moveto
/l glyphshow
33.3496 0.148438 moveto
/a glyphshow
44.4458 0.148438 moveto
/r glyphshow
52.771 0.148438 moveto
/i glyphshow
59.7168 0.148438 moveto
/z glyphshow
70.813 0.148438 moveto
/a glyphshow
81.9092 0.148438 moveto
/t glyphshow
88.855 0.148438 moveto
/i glyphshow
95.8008 0.148438 moveto
/o glyphshow
108.301 0.148438 moveto
/n glyphshow
120.801 0.148438 moveto
/space glyphshow
127.051 0.148438 moveto
/parenleft glyphshow
135.376 0.148438 moveto
/mu glyphshow
148.779 0.148438 moveto
/C glyphshow
165.454 0.148438 moveto
/slash glyphshow
172.4 0.148438 moveto
/c glyphshow
183.496 0.148438 moveto
/m glyphshow
/TimesNewRomanPSMT 17.5 selectfont
203.318 15.1766 moveto
/two glyphshow
/TimesNewRomanPSMT 25.0 selectfont
213.141 0.148438 moveto
/parenright glyphshow
grestore
[9.6 2.4 1.5 2.4] 0 setdash
0.937 0.424 0.000 setrgbcolor
gsave
388.174 313.357 597.92 473.97 clipbox
616.448425 773.082772 m
634.977012 772.13274 l
672.034188 770.088888 l
709.091363 767.973141 l
746.148539 765.718741 l
783.205714 763.541371 l
820.26289 761.240754 l
857.320065 758.601206 l
894.377241 755.756246 l
968.491592 749.965776 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
388.174 313.357 597.92 473.97 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
616.448 773.083 o
634.977 772.133 o
672.034 770.089 o
709.091 767.973 o
746.149 765.719 o
783.206 763.541 o
820.263 761.241 o
857.32 758.601 o
894.377 755.756 o
968.492 749.966 o
grestore
2.500 setlinewidth
0 setlinejoin
2 setlinecap
0.000 setgray
gsave
597.919837 473.969728 m
597.919837 787.32625 l
stroke
grestore
gsave
986.09375 473.969728 m
986.09375 787.32625 l
stroke
grestore
gsave
597.919837 473.969728 m
986.09375 473.969728 l
stroke
grestore
gsave
597.919837 787.32625 m
986.09375 787.32625 l
stroke
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

597.92 796.727 translate
0 rotate
0 0 m /parenleft glyphshow
8.3252 0 m /b glyphshow
20.8252 0 m /parenright glyphshow
grestore
1.500 setlinewidth
1 setlinejoin
0 setlinecap
[9.6 2.4 1.5 2.4] 0 setdash
0.937 0.424 0.000 setrgbcolor
gsave
854.90625 589.813478 m
874.90625 589.813478 l
894.90625 589.813478 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
874.906 589.813 o
grestore
0.000 setgray
gsave
910.906 582.813 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.75 moveto
/P glyphshow
/TimesNewRomanPSMT 14.0 selectfont
11.4236 -4.4025 moveto
/s glyphshow
grestore
0.031 0.659 0.255 setrgbcolor
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

-8.660254 -8.660254 m
8.660254 -8.660254 l
8.660254 8.660254 l
-8.660254 8.660254 l
cl

gsave
0.031 0.659 0.255 setrgbcolor
fill
grestore
stroke
grestore
} bind def
874.906 558.063 o
grestore
0.000 setgray
/TimesNewRomanPSMT 20.000 selectfont
gsave

910.906 552.813 translate
0 rotate
0 0 m /R glyphshow
13.3398 0 m /e glyphshow
22.2168 0 m /f glyphshow
28.877 0 m /period glyphshow
33.877 0 m /bracketleft glyphshow
40.5371 0 m /a glyphshow
49.4141 0 m /bracketright glyphshow
grestore
2.500 setlinewidth
0.031 0.416 0.416 setrgbcolor
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-11.18034 0 m
11.18034 0 l
0 -11.18034 m
0 11.18034 l

gsave
0.031 0.416 0.416 setrgbcolor
fill
grestore
stroke
grestore
} bind def
874.906 529.782 o
grestore
0.000 setgray
/TimesNewRomanPSMT 20.000 selectfont
gsave

910.906 524.532 translate
0 rotate
0 0 m /R glyphshow
13.3398 0 m /e glyphshow
22.2168 0 m /f glyphshow
28.877 0 m /period glyphshow
33.877 0 m /bracketleft glyphshow
40.5371 0 m /b glyphshow
50.5371 0 m /bracketright glyphshow
grestore
1.000 setlinewidth
0.031 0.659 0.659 setrgbcolor
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 13.228757 m
-2.970041 4.08791 l
-12.581295 4.087911 l
-4.805627 -1.561443 l
-7.775668 -10.702289 l
-0 -5.052935 l
7.775668 -10.702289 l
4.805627 -1.561443 l
12.581295 4.087911 l
2.970041 4.08791 l
cl

gsave
0.031 0.659 0.659 setrgbcolor
fill
grestore
stroke
grestore
} bind def
874.906 501.501 o
grestore
0.000 setgray
/TimesNewRomanPSMT 20.000 selectfont
gsave

910.906 496.251 translate
0 rotate
0 0 m /R glyphshow
13.3398 0 m /e glyphshow
22.2168 0 m /f glyphshow
28.877 0 m /period glyphshow
33.877 0 m /bracketleft glyphshow
40.5371 0 m /c glyphshow
49.4141 0 m /bracketright glyphshow
grestore
gsave
93.29375 66.60625 m
481.467663 66.60625 l
481.467663 379.962772 l
93.29375 379.962772 l
cl
1.000 setgray
fill
grestore
2.500 setlinewidth
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
107.374 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
107.374 379.963 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

101.124 39.2469 translate
0 rotate
0 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
178.663 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
178.663 379.963 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

159.913 39.2469 translate
0 rotate
0 0 m /two glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
249.953 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
249.953 379.963 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

231.203 39.2469 translate
0 rotate
0 0 m /four glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
321.243 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
321.243 379.963 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

302.493 39.2469 translate
0 rotate
0 0 m /six glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
392.533 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
392.533 379.963 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

373.783 39.2469 translate
0 rotate
0 0 m /eight glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
463.823 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
463.823 379.963 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

438.823 39.2469 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
37.5 0 m /zero glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
125.196 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
125.196 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
143.019 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
143.019 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
160.841 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
160.841 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
196.486 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
196.486 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
214.308 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
214.308 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
232.131 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
232.131 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
267.776 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
267.776 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
285.598 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
285.598 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
303.421 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
303.421 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
339.066 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
339.066 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
356.888 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
356.888 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
374.711 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
374.711 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
410.356 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
410.356 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
428.178 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
428.178 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
446.001 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
446.001 379.963 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

203.92 12.5437 translate
0 rotate
0 0 m /T glyphshow
13.521 0 m /e glyphshow
24.6172 0 m /m glyphshow
44.063 0 m /p glyphshow
56.563 0 m /e glyphshow
67.6592 0 m /r glyphshow
75.9844 0 m /a glyphshow
87.0806 0 m /t glyphshow
94.0264 0 m /u glyphshow
106.526 0 m /r glyphshow
114.852 0 m /e glyphshow
125.948 0 m /space glyphshow
132.198 0 m /parenleft glyphshow
140.523 0 m /K glyphshow
158.577 0 m /parenright glyphshow
grestore
2.500 setlinewidth
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 106.171 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
481.468 106.171 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

70.7938 97.4918 translate
0 rotate
0 0 m /one glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 169.476 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
481.468 169.476 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

70.7938 160.796 translate
0 rotate
0 0 m /two glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 232.78 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
481.468 232.78 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

70.7938 224.1 translate
0 rotate
0 0 m /three glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 296.085 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
481.468 296.085 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

70.7938 287.405 translate
0 rotate
0 0 m /four glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 359.389 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
481.468 359.389 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

70.7938 350.709 translate
0 rotate
0 0 m /five glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 68.1889 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
481.468 68.1889 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 80.8497 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
481.468 80.8497 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 93.5106 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
481.468 93.5106 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 118.832 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
481.468 118.832 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 131.493 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
481.468 131.493 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 144.154 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
481.468 144.154 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 156.815 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
481.468 156.815 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 182.137 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
481.468 182.137 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 194.798 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
481.468 194.798 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 207.458 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
481.468 207.458 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 220.119 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
481.468 220.119 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 245.441 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
481.468 245.441 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 258.102 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
481.468 258.102 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 270.763 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
481.468 270.763 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 283.424 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
481.468 283.424 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 308.745 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
481.468 308.745 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 321.406 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
481.468 321.406 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 334.067 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
481.468 334.067 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 346.728 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
481.468 346.728 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 372.05 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
481.468 372.05 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

61.45 98.9876 translate
90 rotate
0 0 m /C glyphshow
16.6748 0 m /o glyphshow
29.1748 0 m /e glyphshow
40.271 0 m /r glyphshow
48.5962 0 m /c glyphshow
59.6924 0 m /i glyphshow
66.6382 0 m /v glyphshow
79.1382 0 m /e glyphshow
90.2344 0 m /space glyphshow
96.4844 0 m /F glyphshow
110.388 0 m /i glyphshow
117.334 0 m /e glyphshow
128.43 0 m /l glyphshow
135.376 0 m /d glyphshow
147.876 0 m /space glyphshow
154.126 0 m /parenleft glyphshow
162.451 0 m /M glyphshow
184.68 0 m /V glyphshow
202.734 0 m /slash glyphshow
209.68 0 m /c glyphshow
220.776 0 m /m glyphshow
240.222 0 m /parenright glyphshow
grestore
[9.6 2.4 1.5 2.4] 0 setdash
0.749 0.212 0.047 setrgbcolor
gsave
388.174 313.357 93.294 66.606 clipbox
110.938019 251.771467 m
125.196014 245.441033 l
143.018507 232.780163 l
178.663495 220.119293 l
214.308482 194.797554 l
249.95347 182.136685 l
285.598457 156.814946 l
321.243445 144.154076 l
356.888432 131.493207 l
392.533419 118.832337 l
463.823394 80.849728 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
388.174 313.357 93.294 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
110.938 251.771 o
125.196 245.441 o
143.019 232.78 o
178.663 220.119 o
214.308 194.798 o
249.953 182.137 o
285.598 156.815 o
321.243 144.154 o
356.888 131.493 o
392.533 118.832 o
463.823 80.8497 o
grestore
1.500 setlinewidth
[9.6 2.4 1.5 2.4] 0 setdash
0.937 0.424 0.000 setrgbcolor
gsave
388.174 313.357 93.294 66.606 clipbox
110.938019 346.727989 m
125.196014 327.736685 l
143.018507 327.736685 l
178.663495 289.754076 l
214.308482 270.762772 l
249.95347 270.762772 l
285.598457 194.797554 l
321.243445 194.797554 l
356.888432 169.475815 l
392.533419 144.154076 l
463.823394 93.510598 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
388.174 313.357 93.294 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-8 -8 m
8 -8 l
8 8 l
-8 8 l
cl

gsave
grestore
stroke
grestore
} bind def
110.938 346.728 o
125.196 327.737 o
143.019 327.737 o
178.663 289.754 o
214.308 270.763 o
249.953 270.763 o
285.598 194.798 o
321.243 194.798 o
356.888 169.476 o
392.533 144.154 o
463.823 93.5106 o
grestore
1.500 setlinewidth
1 setlinejoin
[9.6 2.4 1.5 2.4] 0 setdash
1.000 0.702 0.000 setrgbcolor
gsave
388.174 313.357 93.294 66.606 clipbox
110.938019 365.719293 m
125.196014 365.719293 l
143.018507 365.719293 l
178.663495 327.736685 l
214.308482 283.423641 l
249.95347 283.423641 l
285.598457 270.762772 l
321.243445 232.780163 l
356.888432 169.475815 l
392.533419 169.475815 l
463.823394 118.832337 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
388.174 313.357 93.294 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-8 0 m
8 -8 l
8 8 l
cl

gsave
grestore
stroke
grestore
} bind def
110.938 365.719 o
125.196 365.719 o
143.019 365.719 o
178.663 327.737 o
214.308 283.424 o
249.953 283.424 o
285.598 270.763 o
321.243 232.78 o
356.888 169.476 o
392.533 169.476 o
463.823 118.832 o
grestore
2.500 setlinewidth
2 setlinecap
0.000 setgray
gsave
93.29375 66.60625 m
93.29375 379.962772 l
stroke
grestore
gsave
481.467663 66.60625 m
481.467663 379.962772 l
stroke
grestore
gsave
93.29375 66.60625 m
481.467663 66.60625 l
stroke
grestore
gsave
93.29375 379.962772 m
481.467663 379.962772 l
stroke
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

93.2938 389.363 translate
0 rotate
0 0 m /parenleft glyphshow
8.3252 0 m /c glyphshow
19.4214 0 m /parenright glyphshow
grestore
1.500 setlinewidth
1 setlinejoin
0 setlinecap
[9.6 2.4 1.5 2.4] 0 setdash
0.749 0.212 0.047 setrgbcolor
gsave
344.467663 354.962772 m
364.467663 354.962772 l
384.467663 354.962772 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
364.468 354.963 o
grestore
0.000 setgray
gsave
400.468 347.963 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.109375 moveto
/E glyphshow
/TimesNewRomanPSMT 14.0 selectfont
12.5174 -5.04312 moveto
/c glyphshow
/TimesNewRomanPSMT 20.0 selectfont
19.59 0.109375 moveto
/parenleft glyphshow
26.2501 0.109375 moveto
/one glyphshow
36.2501 0.109375 moveto
/one glyphshow
46.2501 0.109375 moveto
/one glyphshow
56.2501 0.109375 moveto
/parenright glyphshow
grestore
1.500 setlinewidth
[9.6 2.4 1.5 2.4] 0 setdash
0.937 0.424 0.000 setrgbcolor
gsave
344.467663 324.962772 m
364.467663 324.962772 l
384.467663 324.962772 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-8 -8 m
8 -8 l
8 8 l
-8 8 l
cl

gsave
grestore
stroke
grestore
} bind def
364.468 324.963 o
grestore
0.000 setgray
gsave
400.468 317.963 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.109375 moveto
/E glyphshow
/TimesNewRomanPSMT 14.0 selectfont
12.5174 -5.04312 moveto
/c glyphshow
/TimesNewRomanPSMT 20.0 selectfont
19.59 0.109375 moveto
/parenleft glyphshow
26.2501 0.109375 moveto
/one glyphshow
36.2501 0.109375 moveto
/one glyphshow
46.2501 0.109375 moveto
/zero glyphshow
56.2501 0.109375 moveto
/parenright glyphshow
grestore
1.500 setlinewidth
1 setlinejoin
[9.6 2.4 1.5 2.4] 0 setdash
1.000 0.702 0.000 setrgbcolor
gsave
344.467663 294.962772 m
364.467663 294.962772 l
384.467663 294.962772 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-8 0 m
8 -8 l
8 8 l
cl

gsave
grestore
stroke
grestore
} bind def
364.468 294.963 o
grestore
0.000 setgray
gsave
400.468 287.963 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.109375 moveto
/E glyphshow
/TimesNewRomanPSMT 14.0 selectfont
12.5174 -5.04312 moveto
/c glyphshow
/TimesNewRomanPSMT 20.0 selectfont
19.59 0.109375 moveto
/parenleft glyphshow
26.2501 0.109375 moveto
/zero glyphshow
36.2501 0.109375 moveto
/zero glyphshow
46.2501 0.109375 moveto
/one glyphshow
56.2501 0.109375 moveto
/parenright glyphshow
grestore
gsave
597.919837 66.60625 m
986.09375 66.60625 l
986.09375 379.962772 l
597.919837 379.962772 l
cl
1.000 setgray
fill
grestore
2.500 setlinewidth
1 setlinejoin
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
597.92 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
597.92 379.963 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

591.67 39.2469 translate
0 rotate
0 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
672.034 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
672.034 379.963 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

653.284 39.2469 translate
0 rotate
0 0 m /two glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
746.149 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
746.149 379.963 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

727.399 39.2469 translate
0 rotate
0 0 m /four glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
820.263 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
820.263 379.963 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

801.513 39.2469 translate
0 rotate
0 0 m /six glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
894.377 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
894.377 379.963 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

875.627 39.2469 translate
0 rotate
0 0 m /eight glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
968.492 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
968.492 379.963 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

943.492 39.2469 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
37.5 0 m /zero glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
616.448 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
616.448 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
634.977 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
634.977 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
653.506 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
653.506 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
690.563 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
690.563 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
709.091 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
709.091 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
727.62 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
727.62 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
764.677 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
764.677 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
783.206 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
783.206 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
801.734 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
801.734 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
838.791 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
838.791 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
857.32 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
857.32 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
875.849 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
875.849 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
912.906 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
912.906 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
931.434 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
931.434 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
949.963 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
949.963 379.963 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

708.546 12.5437 translate
0 rotate
0 0 m /T glyphshow
13.521 0 m /e glyphshow
24.6172 0 m /m glyphshow
44.063 0 m /p glyphshow
56.563 0 m /e glyphshow
67.6592 0 m /r glyphshow
75.9844 0 m /a glyphshow
87.0806 0 m /t glyphshow
94.0264 0 m /u glyphshow
106.526 0 m /r glyphshow
114.852 0 m /e glyphshow
125.948 0 m /space glyphshow
132.198 0 m /parenleft glyphshow
140.523 0 m /K glyphshow
158.577 0 m /parenright glyphshow
grestore
2.500 setlinewidth
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
597.92 82.555 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.094 82.555 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

531.67 73.8753 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /one glyphshow
43.75 0 m /four glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
597.92 153.078 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.094 153.078 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

531.67 144.398 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /one glyphshow
43.75 0 m /six glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
597.92 223.601 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.094 223.601 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

531.67 214.922 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /one glyphshow
43.75 0 m /eight glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
597.92 294.125 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.094 294.125 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

531.67 285.445 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /two glyphshow
43.75 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
597.92 364.648 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.094 364.648 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

531.67 355.968 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /two glyphshow
43.75 0 m /two glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
597.92 100.186 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.094 100.186 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
597.92 117.817 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.094 117.817 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
597.92 135.447 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.094 135.447 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
597.92 170.709 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.094 170.709 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
597.92 188.34 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.094 188.34 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
597.92 205.971 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.094 205.971 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
597.92 241.232 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.094 241.232 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
597.92 258.863 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.094 258.863 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
597.92 276.494 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.094 276.494 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
597.92 311.755 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.094 311.755 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
597.92 329.386 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.094 329.386 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
597.92 347.017 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.094 347.017 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

522.326 193.417 translate
90 rotate
0 0 m /S glyphshow
13.9038 0 m /t glyphshow
20.8496 0 m /r glyphshow
29.1748 0 m /a glyphshow
40.271 0 m /i glyphshow
47.2168 0 m /n glyphshow
grestore
[9.6 2.4 1.5 2.4] 0 setdash
0.749 0.212 0.047 setrgbcolor
gsave
388.174 313.357 597.92 66.606 clipbox
616.448425 299.207121 m
634.977012 291.605427 l
672.034188 274.732051 l
709.091363 257.613608 l
746.148539 238.857263 l
783.205714 219.387576 l
820.26289 198.617442 l
857.320065 175.184706 l
894.377241 148.783296 l
968.491592 80.849728 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
388.174 313.357 597.92 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
616.448 299.207 o
634.977 291.605 o
672.034 274.732 o
709.091 257.614 o
746.149 238.857 o
783.206 219.388 o
820.263 198.617 o
857.32 175.185 o
894.377 148.783 o
968.492 80.8497 o
grestore
1.500 setlinewidth
[5.55 2.4] 0 setdash
1.000 0.702 0.000 setrgbcolor
gsave
388.174 313.357 597.92 66.606 clipbox
616.448425 365.719293 m
634.977012 358.254062 l
672.034188 343.472757 l
709.091363 328.036996 l
746.148539 310.990486 l
783.205714 294.337495 l
820.26289 275.464082 l
857.320065 255.286343 l
894.377241 233.069778 l
968.491592 181.316343 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
388.174 313.357 597.92 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-8 -8 m
8 -8 l
8 8 l
-8 8 l
cl

gsave
grestore
stroke
grestore
} bind def
616.448 365.719 o
634.977 358.254 o
672.034 343.473 o
709.091 328.037 o
746.149 310.99 o
783.206 294.337 o
820.263 275.464 o
857.32 255.286 o
894.377 233.07 o
968.492 181.316 o
grestore
2.500 setlinewidth
2 setlinecap
0.000 setgray
gsave
597.919837 66.60625 m
597.919837 379.962772 l
stroke
grestore
gsave
986.09375 66.60625 m
986.09375 379.962772 l
stroke
grestore
gsave
597.919837 66.60625 m
986.09375 66.60625 l
stroke
grestore
gsave
597.919837 379.962772 m
986.09375 379.962772 l
stroke
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

597.92 389.363 translate
0 rotate
0 0 m /parenleft glyphshow
8.3252 0 m /d glyphshow
20.8252 0 m /parenright glyphshow
grestore
1.500 setlinewidth
1 setlinejoin
0 setlinecap
[9.6 2.4 1.5 2.4] 0 setdash
0.749 0.212 0.047 setrgbcolor
gsave
875.09375 354.962772 m
895.09375 354.962772 l
915.09375 354.962772 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
895.094 354.963 o
grestore
0.000 setgray
gsave
931.094 347.963 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.453125 moveto
/S glyphshow
/TimesNewRomanPSMT 14.0 selectfont
11.4236 -4.69937 moveto
/r glyphshow
16.0857 -4.69937 moveto
/e glyphshow
22.2996 -4.69937 moveto
/m glyphshow
grestore
1.500 setlinewidth
[5.55 2.4] 0 setdash
1.000 0.702 0.000 setrgbcolor
gsave
875.09375 324.962772 m
895.09375 324.962772 l
915.09375 324.962772 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-8 -8 m
8 -8 l
8 8 l
-8 8 l
cl

gsave
grestore
stroke
grestore
} bind def
895.094 324.963 o
grestore
0.000 setgray
gsave
931.094 317.963 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.453125 moveto
/S glyphshow
/TimesNewRomanPSMT 14.0 selectfont
11.4236 -4.69937 moveto
/m glyphshow
22.3133 -4.69937 moveto
/a glyphshow
28.5271 -4.69937 moveto
/x glyphshow
grestore

end
showpage
