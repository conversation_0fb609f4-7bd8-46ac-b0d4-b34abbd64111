This is pdfTeX, Version 3.141592653-2.6-1.40.22 (TeX Live 2022/dev/Debian) (preloaded format=pdflatex 2024.3.17)  26 JUL 2024 06:46
entering extended mode
 restricted \write18 enabled.
 %&-line parsing enabled.
**main.tex
(/usr/share/texlive/texmf-dist/tex/latex/tools/x.tex
LaTeX2e <2021-11-15> patch level 1
L3 programming layer <2022-01-21>
! .
l.38   \batchmode \errmessage{}
                               \csname @@end\endcsname \end
This error message was generated by an \errmessage
command, so I can't give any explicit help.
Pretend that you're <PERSON><PERSON> Poirot: Examine all clues,
and deduce the truth by order and method.

 ) 
Here is how much of TeX's memory you used:
 18 strings out of 478287
 514 string characters out of 5849289
 289007 words of memory out of 5000000
 18313 multiletter control sequences out of 15000+600000
 469259 words of font info for 28 fonts, out of 8000000 for 9000
 1141 hyphenation exceptions out of 8191
 13i,0n,12p,88b,10s stack positions out of 5000i,500n,10000p,200000b,80000s

No pages of output.
PDF statistics:
 0 PDF objects out of 1000 (max. 8388607)
 0 named destinations out of 1000 (max. 500000)
 1 words of extra memory for PDF output out of 10000 (max. 10000000)

