%!PS-Adobe-3.0 EPSF-3.0
%%Title: Fig2_with_exp.eps
%%Creator: Mat<PERSON>lotlib v3.7.1, https://matplotlib.org/
%%CreationDate: Wed Aug 28 18:45:06 2024
%%Orientation: portrait
%%BoundingBox: -203 -12 816 805
%%HiResBoundingBox: -203.475000 -12.942812 815.475000 804.942812
%%EndComments
%%BeginProlog
/mpldict 11 dict def
mpldict begin
/_d { bind def } bind def
/m { moveto } _d
/l { lineto } _d
/r { rlineto } _d
/c { curveto } _d
/cl { closepath } _d
/ce { closepath eofill } _d
/box {
      m
      1 index 0 r
      0 exch r
      neg 0 r
      cl
    } _d
/clipbox {
      box
      clip
      newpath
    } _d
/sc { setcachedevice } _d
%!PS-TrueTypeFont-1.0-2.53740
%%Title: unknown
%%Creator: Converted from TrueType to type 42 by PPR
15 dict begin
/FontName /TimesNewRomanPSMT def
/PaintType 0 def
/FontMatrix[1 0 0 1 0 0]def
/FontBBox[-568 -307 2028 1007]def
/FontType 42 def
/Encoding StandardEncoding def
/FontInfo 10 dict dup begin
/FamilyName (unknown) def
/FullName (unknown) def
/Weight (unknown) def
/Version (unknown) def
/ItalicAngle 0.0 def
/isFixedPitch false def
/UnderlinePosition -223 def
/UnderlineThickness 100 def
end readonly def
/sfnts[<00010000000900800003001063767420F34DDA810000009C000007C46670676D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>]def
/CharStrings 49 dict dup begin
/.notdef 0 def
/Gamma 48 def
/space 3 def
/parenleft 4 def
/parenright 5 def
/comma 6 def
/period 7 def
/slash 8 def
/zero 9 def
/one 10 def
/two 11 def
/three 12 def
/four 13 def
/five 14 def
/six 15 def
/seven 16 def
/eight 17 def
/nine 18 def
/less 19 def
/mu 49 def
/greater 20 def
/C 21 def
/omega 50 def
/K 22 def
/L 23 def
/P 24 def
/R 25 def
/T 26 def
/bracketleft 27 def
/bracketright 28 def
/a 29 def
/b 30 def
/c 31 def
/d 32 def
/e 33 def
/f 34 def
/i 35 def
/l 36 def
/m 37 def
/n 38 def
/o 39 def
/p 40 def
/r 41 def
/s 42 def
/t 43 def
/u 44 def
/x 45 def
/y 46 def
/z 47 def
end readonly def

systemdict/resourcestatus known
 {42 /FontType resourcestatus
   {pop pop false}{true}ifelse}
 {true}ifelse
{/TrueDict where{pop}{(%%[ Error: no TrueType rasterizer ]%%)= flush}ifelse
/FontType 3 def
 /TrueState 271 string def
 TrueDict begin sfnts save
 72 0 matrix defaultmatrix dtransform dup
 mul exch dup mul add sqrt cvi 0 72 matrix
 defaultmatrix dtransform dup mul exch dup
 mul add sqrt cvi 3 -1 roll restore
 TrueState initer end
 /BuildGlyph{exch begin
  CharStrings dup 2 index known
    {exch}{exch pop /.notdef}ifelse
  get dup xcheck
    {currentdict systemdict begin begin exec end end}
    {TrueDict begin /bander load cvlit exch TrueState render end}
    ifelse
 end}bind def
 /BuildChar{
  1 index /Encoding get exch get
  1 index /BuildGlyph get exec
 }bind def
}if

FontName currentdict end definefont pop
end
%%EndProlog
mpldict begin
-203.475 -12.943 translate
1018.95 817.886 0 0 clipbox
gsave
0 0 m
1018.95 0 l
1018.95 817.885625 l
0 817.885625 l
cl
1.000 setgray
fill
grestore
gsave
93.95 473.969728 m
482.123913 473.969728 l
482.123913 787.32625 l
93.95 787.32625 l
cl
1.000 setgray
fill
grestore
2.500 setlinewidth
1 setlinejoin
0 setlinecap
[] 0 setdash
0.000 setgray
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
106.472 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
106.472 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

100.222 446.61 translate
0 rotate
0 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
181.602 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
181.602 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

162.852 446.61 translate
0 rotate
0 0 m /three glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
256.733 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
256.733 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

237.983 446.61 translate
0 rotate
0 0 m /six glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
331.863 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
331.863 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

313.113 446.61 translate
0 rotate
0 0 m /nine glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
406.993 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
406.993 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

381.993 446.61 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /two glyphshow
25 0 m /zero glyphshow
37.5 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

457.124 446.61 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /five glyphshow
25 0 m /zero glyphshow
37.5 0 m /zero glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
125.254 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
125.254 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
144.037 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
144.037 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
162.82 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
162.82 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
200.385 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
200.385 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
219.167 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
219.167 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
237.95 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
237.95 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
275.515 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
275.515 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
294.298 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
294.298 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
313.08 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
313.08 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
350.646 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
350.646 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
369.428 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
369.428 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
388.211 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
388.211 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
425.776 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
425.776 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
444.559 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
444.559 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
463.341 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
463.341 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

204.576 419.907 translate
0 rotate
0 0 m /T glyphshow
13.521 0 m /e glyphshow
24.6172 0 m /m glyphshow
44.063 0 m /p glyphshow
56.563 0 m /e glyphshow
67.6592 0 m /r glyphshow
75.9844 0 m /a glyphshow
87.0806 0 m /t glyphshow
94.0264 0 m /u glyphshow
106.526 0 m /r glyphshow
114.852 0 m /e glyphshow
125.948 0 m /space glyphshow
132.198 0 m /parenleft glyphshow
140.523 0 m /K glyphshow
158.577 0 m /parenright glyphshow
grestore
2.500 setlinewidth
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 488.02 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 488.02 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

40.2 479.341 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 545.421 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 545.421 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

40.2 536.742 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /one glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 602.823 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 602.823 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

40.2 594.143 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /two glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 660.224 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 660.224 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

40.2 651.544 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /three glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 717.625 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 717.625 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

40.2 708.945 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /four glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 775.026 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 775.026 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

40.2 766.346 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /five glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 476.54 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 476.54 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 499.501 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 499.501 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 510.981 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 510.981 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 522.461 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 522.461 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 533.941 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 533.941 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 556.902 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 556.902 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 568.382 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 568.382 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 579.862 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 579.862 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 591.342 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 591.342 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 614.303 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 614.303 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 625.783 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 625.783 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 637.263 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 637.263 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 648.743 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 648.743 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 671.704 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 671.704 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 683.184 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 683.184 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 694.664 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 694.664 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 706.144 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 706.144 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 729.105 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 729.105 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 740.585 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 740.585 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 752.065 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 752.065 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 763.545 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 763.545 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 786.506 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 786.506 o
grestore
gsave
25.2 574.148 translate
90 rotate
/TimesNewRomanPSMT 25.0 selectfont
0 0.640625 moveto
/less glyphshow
14.0991 0.640625 moveto
/u glyphshow
/TimesNewRomanPSMT 17.5 selectfont
26.9748 -5.8 moveto
/x glyphshow
35.7248 -5.8 moveto
/comma glyphshow
42.7458 -5.8 moveto
/y glyphshow
51.4958 -5.8 moveto
/comma glyphshow
58.5168 -5.8 moveto
/z glyphshow
/TimesNewRomanPSMT 25.0 selectfont
67.3576 0.640625 moveto
/parenleft glyphshow
75.6828 0.640625 moveto
/Gamma glyphshow
90.1359 0.640625 moveto
/parenright glyphshow
98.4611 0.640625 moveto
/greater glyphshow
grestore
0.050 setlinewidth
[0.185 0.08] 0 setdash
0.722 0.224 0.055 setrgbcolor
gsave
388.174 313.357 93.95 473.97 clipbox
477.115217 491.345525 m
472.106522 497.33567 l
467.097826 488.390633 l
462.08913 489.522582 l
457.080435 498.945425 l
452.071739 501.788844 l
447.063043 496.259515 l
442.054348 509.522947 l
437.045652 493.547602 l
432.036957 502.977103 l
427.028261 501.458558 l
422.019565 494.211502 l
417.01087 532.550702 l
412.002174 533.929762 l
406.993478 490.829375 l
401.984783 499.923596 l
396.976087 676.266647 l
391.967391 684.748744 l
386.958696 692.31627 l
381.95 696.531803 l
376.941304 700.261781 l
371.932609 702.179895 l
366.923913 706.336306 l
361.915217 709.886619 l
356.906522 711.556013 l
351.897826 714.214543 l
346.88913 717.810834 l
341.880435 719.318071 l
336.871739 721.120235 l
331.863043 723.717116 l
326.854348 725.542068 l
321.845652 727.185173 l
316.836957 729.069593 l
311.828261 730.487801 l
306.819565 732.462053 l
301.81087 733.15695 l
296.802174 735.160706 l
291.793478 736.584195 l
286.784783 737.799719 l
281.776087 739.517503 l
276.767391 740.755989 l
271.758696 741.619932 l
266.75 743.159428 l
261.741304 744.754374 l
256.732609 746.018747 l
251.723913 747.23852 l
246.715217 748.396299 l
241.706522 749.520499 l
236.697826 750.636605 l
231.68913 751.689283 l
226.680435 752.516834 l
221.671739 753.849227 l
216.663043 754.718279 l
211.654348 755.69513 l
206.645652 756.648505 l
201.636957 757.782118 l
196.628261 758.625225 l
191.619565 759.55914 l
186.61087 760.637017 l
181.602174 761.534999 l
176.593478 762.337294 l
171.584783 763.252439 l
166.576087 764.150364 l
161.567391 765.095529 l
156.558696 766.053036 l
151.55 766.704366 l
146.541304 767.465734 l
141.532609 768.472147 l
136.523913 769.281329 l
131.515217 769.959064 l
126.506522 770.718365 l
121.497826 771.558027 l
116.48913 772.289202 l
111.480435 773.043796 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
388.174 313.357 93.95 473.97 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

0 2 m
-2 -2 l
2 -2 l
cl

gsave
0.722 0.224 0.055 setrgbcolor
fill
grestore
stroke
grestore
} bind def
477.115 491.346 o
472.107 497.336 o
467.098 488.391 o
462.089 489.523 o
457.08 498.945 o
452.072 501.789 o
447.063 496.26 o
442.054 509.523 o
437.046 493.548 o
432.037 502.977 o
427.028 501.459 o
422.02 494.212 o
417.011 532.551 o
412.002 533.93 o
406.993 490.829 o
401.985 499.924 o
396.976 676.267 o
391.967 684.749 o
386.959 692.316 o
381.95 696.532 o
376.941 700.262 o
371.933 702.18 o
366.924 706.336 o
361.915 709.887 o
356.907 711.556 o
351.898 714.215 o
346.889 717.811 o
341.88 719.318 o
336.872 721.12 o
331.863 723.717 o
326.854 725.542 o
321.846 727.185 o
316.837 729.07 o
311.828 730.488 o
306.82 732.462 o
301.811 733.157 o
296.802 735.161 o
291.793 736.584 o
286.785 737.8 o
281.776 739.518 o
276.767 740.756 o
271.759 741.62 o
266.75 743.159 o
261.741 744.754 o
256.733 746.019 o
251.724 747.239 o
246.715 748.396 o
241.707 749.52 o
236.698 750.637 o
231.689 751.689 o
226.68 752.517 o
221.672 753.849 o
216.663 754.718 o
211.654 755.695 o
206.646 756.649 o
201.637 757.782 o
196.628 758.625 o
191.62 759.559 o
186.611 760.637 o
181.602 761.535 o
176.593 762.337 o
171.585 763.252 o
166.576 764.15 o
161.567 765.096 o
156.559 766.053 o
151.55 766.704 o
146.541 767.466 o
141.533 768.472 o
136.524 769.281 o
131.515 769.959 o
126.507 770.718 o
121.498 771.558 o
116.489 772.289 o
111.48 773.044 o
grestore
0.050 setlinewidth
1 setlinejoin
[0.185 0.08] 0 setdash
1.000 0.271 0.000 setrgbcolor
gsave
388.174 313.357 93.95 473.97 clipbox
477.115217 489.696966 m
472.106522 490.351166 l
467.097826 491.31051 l
462.08913 498.810131 l
457.080435 488.897714 l
452.071739 489.873934 l
447.063043 489.531364 l
442.054348 489.836795 l
437.045652 499.83738 l
432.036957 489.00293 l
427.028261 494.782815 l
422.019565 515.844927 l
417.01087 505.564972 l
412.002174 510.411114 l
406.993478 513.351024 l
401.984783 537.271192 l
396.976087 677.517761 l
391.967391 685.729039 l
386.958696 691.964803 l
381.95 696.142624 l
376.941304 700.343463 l
371.932609 703.525491 l
366.923913 706.446918 l
361.915217 708.819935 l
356.906522 712.356586 l
351.897826 714.794868 l
346.88913 716.971459 l
341.880435 719.656508 l
336.871739 721.446675 l
331.863043 723.110846 l
326.854348 725.536328 l
321.845652 727.470055 l
316.836957 728.411604 l
311.828261 730.002015 l
306.819565 732.191981 l
301.81087 733.67333 l
296.802174 735.065133 l
291.793478 736.950356 l
286.784783 738.042124 l
281.776087 739.440184 l
276.767391 740.891226 l
271.758696 742.127587 l
266.75 743.719261 l
261.741304 744.675792 l
256.732609 745.814801 l
251.723913 747.149663 l
246.715217 748.51931 l
241.706522 749.256683 l
236.697826 750.768513 l
231.68913 751.552898 l
226.680435 752.862101 l
221.671739 753.768694 l
216.663043 754.679763 l
211.654348 755.850343 l
206.645652 756.879372 l
201.636957 757.765816 l
196.628261 758.686988 l
191.619565 759.712056 l
186.61087 760.576976 l
181.602174 761.536319 l
176.593478 762.415474 l
171.584783 763.2343 l
166.576087 764.226363 l
161.567391 765.057989 l
156.558696 766.020375 l
151.55 766.79219 l
146.541304 767.67852 l
141.532609 768.339608 l
136.523913 769.135301 l
131.515217 769.928411 l
126.506522 770.772494 l
121.497826 771.559635 l
116.48913 772.305734 l
111.480435 773.082772 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
388.174 313.357 93.95 473.97 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-4 -4 m
4 -4 l
4 4 l
-4 4 l
cl

gsave
grestore
stroke
grestore
} bind def
477.115 489.697 o
472.107 490.351 o
467.098 491.311 o
462.089 498.81 o
457.08 488.898 o
452.072 489.874 o
447.063 489.531 o
442.054 489.837 o
437.046 499.837 o
432.037 489.003 o
427.028 494.783 o
422.02 515.845 o
417.011 505.565 o
412.002 510.411 o
406.993 513.351 o
401.985 537.271 o
396.976 677.518 o
391.967 685.729 o
386.959 691.965 o
381.95 696.143 o
376.941 700.343 o
371.933 703.525 o
366.924 706.447 o
361.915 708.82 o
356.907 712.357 o
351.898 714.795 o
346.889 716.971 o
341.88 719.657 o
336.872 721.447 o
331.863 723.111 o
326.854 725.536 o
321.846 727.47 o
316.837 728.412 o
311.828 730.002 o
306.82 732.192 o
301.811 733.673 o
296.802 735.065 o
291.793 736.95 o
286.785 738.042 o
281.776 739.44 o
276.767 740.891 o
271.759 742.128 o
266.75 743.719 o
261.741 744.676 o
256.733 745.815 o
251.724 747.15 o
246.715 748.519 o
241.707 749.257 o
236.698 750.769 o
231.689 751.553 o
226.68 752.862 o
221.672 753.769 o
216.663 754.68 o
211.654 755.85 o
206.646 756.879 o
201.637 757.766 o
196.628 758.687 o
191.62 759.712 o
186.611 760.577 o
181.602 761.536 o
176.593 762.415 o
171.585 763.234 o
166.576 764.226 o
161.567 765.058 o
156.559 766.02 o
151.55 766.792 o
146.541 767.679 o
141.533 768.34 o
136.524 769.135 o
131.515 769.928 o
126.507 770.772 o
121.498 771.56 o
116.489 772.306 o
111.48 773.083 o
grestore
0.050 setlinewidth
1 setlinejoin
[0.185 0.08] 0 setdash
1.000 0.514 0.000 setrgbcolor
gsave
388.174 313.357 93.95 473.97 clipbox
477.115217 488.213207 m
472.106522 494.11679 l
467.097826 491.531906 l
462.08913 497.439393 l
457.080435 490.078511 l
452.071739 488.320546 l
447.063043 493.36168 l
442.054348 501.129707 l
437.045652 489.674695 l
432.036957 501.681676 l
427.028261 490.495415 l
422.019565 519.226768 l
417.01087 489.914574 l
412.002174 576.436163 l
406.993478 588.613912 l
401.984783 578.112045 l
396.976087 675.979929 l
391.967391 685.573023 l
386.958696 690.918841 l
381.95 696.150603 l
376.941304 700.071095 l
371.932609 703.717784 l
366.923913 706.985971 l
361.915217 709.682041 l
356.906522 712.476784 l
351.897826 714.175511 l
346.88913 716.89655 l
341.880435 719.364222 l
336.871739 721.361951 l
331.863043 723.262213 l
326.854348 725.148986 l
321.845652 727.131389 l
316.836957 728.77834 l
311.828261 730.209692 l
306.819565 732.175449 l
301.81087 733.908846 l
296.802174 734.961639 l
291.793478 736.474157 l
286.784783 737.995801 l
281.776087 739.550452 l
276.767391 740.649567 l
271.758696 741.943502 l
266.75 743.580408 l
261.741304 744.637046 l
256.732609 745.83271 l
251.723913 747.032737 l
246.715217 748.354683 l
241.706522 749.256856 l
236.697826 750.423245 l
231.68913 751.650308 l
226.680435 752.649603 l
221.671739 753.630013 l
216.663043 754.80926 l
211.654348 755.685774 l
206.645652 756.765431 l
201.636957 757.781889 l
196.628261 758.81505 l
191.619565 759.631752 l
186.61087 760.64666 l
181.602174 761.701749 l
176.593478 762.459615 l
171.584783 763.433424 l
166.576087 764.196112 l
161.567391 765.085025 l
156.558696 765.960736 l
151.55 766.771755 l
146.541304 767.608663 l
141.532609 768.485808 l
136.523913 769.258369 l
131.515217 769.980245 l
126.506522 770.812216 l
121.497826 771.603948 l
116.48913 772.301256 l
111.480435 773.078696 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
388.174 313.357 93.95 473.97 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
477.115 488.213 o
472.107 494.117 o
467.098 491.532 o
462.089 497.439 o
457.08 490.079 o
452.072 488.321 o
447.063 493.362 o
442.054 501.13 o
437.046 489.675 o
432.037 501.682 o
427.028 490.495 o
422.02 519.227 o
417.011 489.915 o
412.002 576.436 o
406.993 588.614 o
401.985 578.112 o
396.976 675.98 o
391.967 685.573 o
386.959 690.919 o
381.95 696.151 o
376.941 700.071 o
371.933 703.718 o
366.924 706.986 o
361.915 709.682 o
356.907 712.477 o
351.898 714.176 o
346.889 716.897 o
341.88 719.364 o
336.872 721.362 o
331.863 723.262 o
326.854 725.149 o
321.846 727.131 o
316.837 728.778 o
311.828 730.21 o
306.82 732.175 o
301.811 733.909 o
296.802 734.962 o
291.793 736.474 o
286.785 737.996 o
281.776 739.55 o
276.767 740.65 o
271.759 741.944 o
266.75 743.58 o
261.741 744.637 o
256.733 745.833 o
251.724 747.033 o
246.715 748.355 o
241.707 749.257 o
236.698 750.423 o
231.689 751.65 o
226.68 752.65 o
221.672 753.63 o
216.663 754.809 o
211.654 755.686 o
206.646 756.765 o
201.637 757.782 o
196.628 758.815 o
191.62 759.632 o
186.611 760.647 o
181.602 761.702 o
176.593 762.46 o
171.585 763.433 o
166.576 764.196 o
161.567 765.085 o
156.559 765.961 o
151.55 766.772 o
146.541 767.609 o
141.533 768.486 o
136.524 769.258 o
131.515 769.98 o
126.507 770.812 o
121.498 771.604 o
116.489 772.301 o
111.48 773.079 o
grestore
2.500 setlinewidth
0 setlinejoin
2 setlinecap
0.000 setgray
gsave
93.95 473.969728 m
93.95 787.32625 l
stroke
grestore
gsave
482.123913 473.969728 m
482.123913 787.32625 l
stroke
grestore
gsave
93.95 473.969728 m
482.123913 473.969728 l
stroke
grestore
gsave
93.95 787.32625 m
482.123913 787.32625 l
stroke
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

93.95 793.326 translate
0 rotate
0 0 m /parenleft glyphshow
8.3252 0 m /a glyphshow
19.4214 0 m /parenright glyphshow
grestore
0.050 setlinewidth
1 setlinejoin
0 setlinecap
[0.185 0.08] 0 setdash
0.722 0.224 0.055 setrgbcolor
gsave
367.123913 762.32625 m
387.123913 762.32625 l
407.123913 762.32625 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

0 2 m
-2 -2 l
2 -2 l
cl

gsave
0.722 0.224 0.055 setrgbcolor
fill
grestore
stroke
grestore
} bind def
387.124 762.326 o
grestore
0.000 setgray
gsave
423.124 755.326 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.546875 moveto
/less glyphshow
11.2793 0.546875 moveto
/u glyphshow
/TimesNewRomanPSMT 14.0 selectfont
21.5799 -4.60562 moveto
/x glyphshow
/TimesNewRomanPSMT 20.0 selectfont
29.4386 0.546875 moveto
/greater glyphshow
grestore
0.050 setlinewidth
1 setlinejoin
[0.185 0.08] 0 setdash
1.000 0.271 0.000 setrgbcolor
gsave
367.123913 732.32625 m
387.123913 732.32625 l
407.123913 732.32625 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-4 -4 m
4 -4 l
4 4 l
-4 4 l
cl

gsave
grestore
stroke
grestore
} bind def
387.124 732.326 o
grestore
0.000 setgray
gsave
423.124 725.326 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.546875 moveto
/less glyphshow
11.2793 0.546875 moveto
/u glyphshow
/TimesNewRomanPSMT 14.0 selectfont
21.5799 -4.60562 moveto
/y glyphshow
/TimesNewRomanPSMT 20.0 selectfont
29.4386 0.546875 moveto
/greater glyphshow
grestore
0.050 setlinewidth
1 setlinejoin
[0.185 0.08] 0 setdash
1.000 0.514 0.000 setrgbcolor
gsave
367.123913 699.32625 m
387.123913 699.32625 l
407.123913 699.32625 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
387.124 699.326 o
grestore
0.000 setgray
gsave
423.124 692.326 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.546875 moveto
/less glyphshow
11.2793 0.546875 moveto
/u glyphshow
/TimesNewRomanPSMT 14.0 selectfont
21.5799 -4.60562 moveto
/z glyphshow
/TimesNewRomanPSMT 20.0 selectfont
28.6525 0.546875 moveto
/greater glyphshow
grestore
gsave
598.576087 473.969728 m
986.75 473.969728 l
986.75 787.32625 l
598.576087 787.32625 l
cl
1.000 setgray
fill
grestore
0.031 0.659 0.659 setrgbcolor
gsave
388.174 313.357 598.576 473.97 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

-0 -12.247449 m
7.348469 0 l
0 12.247449 l
-7.348469 0 l
cl

gsave
0.031 0.659 0.659 setrgbcolor
fill
grestore
stroke
grestore
} bind def
654.423 494.446 o
685.727 544.757 o
710.771 619.624 o
735.814 647.774 o
grestore
0.031 0.416 0.416 setrgbcolor
gsave
388.174 313.357 598.576 473.97 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

8.660254 -0 m
-8.660254 8.660254 l
-8.660254 -8.660254 l
cl

gsave
0.031 0.416 0.416 setrgbcolor
fill
grestore
stroke
grestore
} bind def
686.228 661.55 o
grestore
0.031 0.235 0.235 setrgbcolor
gsave
388.174 313.357 598.576 473.97 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 8.660254 m
-1.944348 2.676166 l
-8.236391 2.676166 l
-3.146021 -1.022204 l
-5.09037 -7.006293 l
-0 -3.307923 l
5.09037 -7.006293 l
3.146021 -1.022204 l
8.236391 2.676166 l
1.944348 2.676166 l
cl

gsave
0.031 0.235 0.235 setrgbcolor
fill
grestore
stroke
grestore
} bind def
686.228 556.736 o
grestore
2.500 setlinewidth
0.000 setgray
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
611.098 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
611.098 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

604.848 446.61 translate
0 rotate
0 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
686.228 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
686.228 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

667.478 446.61 translate
0 rotate
0 0 m /three glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
761.359 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
761.359 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

742.609 446.61 translate
0 rotate
0 0 m /six glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
836.489 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
836.489 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

817.739 446.61 translate
0 rotate
0 0 m /nine glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
911.62 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
911.62 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

886.62 446.61 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /two glyphshow
25 0 m /zero glyphshow
37.5 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

961.75 446.61 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /five glyphshow
25 0 m /zero glyphshow
37.5 0 m /zero glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
629.88 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
629.88 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
648.663 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
648.663 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
667.446 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
667.446 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
705.011 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
705.011 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
723.793 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
723.793 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
742.576 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
742.576 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
780.141 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
780.141 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
798.924 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
798.924 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
817.707 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
817.707 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
855.272 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
855.272 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
874.054 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
874.054 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
892.837 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
892.837 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
930.402 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
930.402 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
949.185 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
949.185 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
967.967 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
967.967 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

709.202 419.907 translate
0 rotate
0 0 m /T glyphshow
13.521 0 m /e glyphshow
24.6172 0 m /m glyphshow
44.063 0 m /p glyphshow
56.563 0 m /e glyphshow
67.6592 0 m /r glyphshow
75.9844 0 m /a glyphshow
87.0806 0 m /t glyphshow
94.0264 0 m /u glyphshow
106.526 0 m /r glyphshow
114.852 0 m /e glyphshow
125.948 0 m /space glyphshow
132.198 0 m /parenleft glyphshow
140.523 0 m /K glyphshow
158.577 0 m /parenright glyphshow
grestore
2.500 setlinewidth
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 487.858 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 487.858 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

576.076 479.178 translate
0 rotate
0 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 547.752 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 547.752 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

563.576 539.072 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 607.645 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 607.645 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

563.576 598.966 translate
0 rotate
0 0 m /two glyphshow
12.5 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 667.539 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 667.539 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

563.576 658.859 translate
0 rotate
0 0 m /three glyphshow
12.5 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 727.433 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 727.433 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

563.576 718.753 translate
0 rotate
0 0 m /four glyphshow
12.5 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

563.576 778.647 translate
0 rotate
0 0 m /five glyphshow
12.5 0 m /zero glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 475.879 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 475.879 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 499.837 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 499.837 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 511.815 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 511.815 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 523.794 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 523.794 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 535.773 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 535.773 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 559.73 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 559.73 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 571.709 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 571.709 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 583.688 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 583.688 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 595.667 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 595.667 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 619.624 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 619.624 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 631.603 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 631.603 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 643.582 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 643.582 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 655.56 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 655.56 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 679.518 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 679.518 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 691.496 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 691.496 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 703.475 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 703.475 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 715.454 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 715.454 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 739.411 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 739.411 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 751.39 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 751.39 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 763.369 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 763.369 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 775.348 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 775.348 o
grestore
gsave
553.576 519.648 translate
90 rotate
/TimesNewRomanPSMT 25.0 selectfont
0 0.148438 moveto
/P glyphshow
13.9038 0.148438 moveto
/o glyphshow
26.4038 0.148438 moveto
/l glyphshow
33.3496 0.148438 moveto
/a glyphshow
44.4458 0.148438 moveto
/r glyphshow
52.771 0.148438 moveto
/i glyphshow
59.7168 0.148438 moveto
/z glyphshow
70.813 0.148438 moveto
/a glyphshow
81.9092 0.148438 moveto
/t glyphshow
88.855 0.148438 moveto
/i glyphshow
95.8008 0.148438 moveto
/o glyphshow
108.301 0.148438 moveto
/n glyphshow
120.801 0.148438 moveto
/space glyphshow
127.051 0.148438 moveto
/parenleft glyphshow
135.376 0.148438 moveto
/mu glyphshow
148.779 0.148438 moveto
/C glyphshow
165.454 0.148438 moveto
/slash glyphshow
172.4 0.148438 moveto
/c glyphshow
183.496 0.148438 moveto
/m glyphshow
/TimesNewRomanPSMT 17.5 selectfont
203.318 15.1766 moveto
/two glyphshow
/TimesNewRomanPSMT 25.0 selectfont
213.141 0.148438 moveto
/parenright glyphshow
grestore
0.050 setlinewidth
[0.185 0.08] 0 setdash
0.722 0.224 0.055 setrgbcolor
gsave
388.174 313.357 598.576 473.97 clipbox
981.741304 491.26253 m
976.732609 497.396602 l
971.723913 488.237174 l
966.715217 489.396412 l
961.706522 499.047371 l
956.697826 501.960303 l
951.68913 496.297282 l
946.680435 509.884291 l
941.671739 493.520044 l
936.663043 503.180225 l
931.654348 501.624949 l
926.645652 494.200001 l
921.636957 533.469414 l
916.628261 534.879987 l
911.619565 490.73453 l
906.61087 500.043432 l
901.602174 679.086089 l
896.593478 687.336295 l
891.584783 694.733942 l
886.576087 698.774173 l
881.567391 702.339327 l
876.558696 704.108682 l
871.55 708.111155 l
866.541304 711.525686 l
861.532609 713.057528 l
856.523913 715.581265 l
851.515217 719.050816 l
846.506522 720.424364 l
841.497826 722.112131 l
836.48913 724.587177 l
831.480435 726.301742 l
826.471739 727.813579 l
821.463043 729.584989 l
816.454348 730.894365 l
811.445652 732.753312 l
806.436957 733.345055 l
801.428261 735.235486 l
796.419565 736.5516 l
791.41087 737.666384 l
786.402174 739.270268 l
781.393478 740.414144 l
776.384783 741.179864 l
771.376087 742.603469 l
766.367391 744.10365 l
761.358696 745.271577 l
756.35 746.391276 l
751.341304 747.447987 l
746.332609 748.484832 l
741.323913 749.498505 l
736.315217 750.460607 l
731.306522 751.194419 l
726.297826 752.432468 l
721.28913 753.210023 l
716.280435 754.09476 l
711.271739 754.955928 l
706.263043 755.995095 l
701.254348 756.751411 l
696.245652 757.597744 l
691.236957 758.586825 l
686.228261 759.39884 l
681.219565 760.11087 l
676.21087 760.938708 l
671.202174 761.747914 l
666.193478 762.611462 l
661.184783 763.479938 l
656.176087 764.050923 l
651.167391 764.731062 l
646.158696 765.64835 l
641.15 766.37577 l
636.141304 766.973219 l
631.132609 767.650028 l
626.123913 768.406423 l
621.115217 769.057995 l
616.106522 769.731143 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
388.174 313.357 598.576 473.97 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

0 2 m
-2 -2 l
2 -2 l
cl

gsave
0.722 0.224 0.055 setrgbcolor
fill
grestore
stroke
grestore
} bind def
981.741 491.263 o
976.733 497.397 o
971.724 488.237 o
966.715 489.396 o
961.707 499.047 o
956.698 501.96 o
951.689 496.297 o
946.68 509.884 o
941.672 493.52 o
936.663 503.18 o
931.654 501.625 o
926.646 494.2 o
921.637 533.469 o
916.628 534.88 o
911.62 490.735 o
906.611 500.043 o
901.602 679.086 o
896.593 687.336 o
891.585 694.734 o
886.576 698.774 o
881.567 702.339 o
876.559 704.109 o
871.55 708.111 o
866.541 711.526 o
861.533 713.058 o
856.524 715.581 o
851.515 719.051 o
846.507 720.424 o
841.498 722.112 o
836.489 724.587 o
831.48 726.302 o
826.472 727.814 o
821.463 729.585 o
816.454 730.894 o
811.446 732.753 o
806.437 733.345 o
801.428 735.235 o
796.42 736.552 o
791.411 737.666 o
786.402 739.27 o
781.393 740.414 o
776.385 741.18 o
771.376 742.603 o
766.367 744.104 o
761.359 745.272 o
756.35 746.391 o
751.341 747.448 o
746.333 748.485 o
741.324 749.499 o
736.315 750.461 o
731.307 751.194 o
726.298 752.432 o
721.289 753.21 o
716.28 754.095 o
711.272 754.956 o
706.263 755.995 o
701.254 756.751 o
696.246 757.598 o
691.237 758.587 o
686.228 759.399 o
681.22 760.111 o
676.211 760.939 o
671.202 761.748 o
666.193 762.611 o
661.185 763.48 o
656.176 764.051 o
651.167 764.731 o
646.159 765.648 o
641.15 766.376 o
636.141 766.973 o
631.133 767.65 o
626.124 768.406 o
621.115 769.058 o
616.107 769.731 o
grestore
0.050 setlinewidth
1 setlinejoin
[0.185 0.08] 0 setdash
1.000 0.271 0.000 setrgbcolor
gsave
388.174 313.357 598.576 473.97 clipbox
981.741304 489.574619 m
976.732609 490.24467 l
971.723913 491.227312 l
966.715217 498.907761 l
961.706522 488.756571 l
956.697826 489.756506 l
951.68913 489.405699 l
946.680435 489.718667 l
941.671739 499.963232 l
936.663043 488.864572 l
931.654348 494.785887 l
926.645652 516.3606 l
921.636957 505.828541 l
916.628261 510.791375 l
911.619565 513.797566 l
906.61087 538.276473 l
901.602174 680.35702 l
896.593478 688.330293 l
891.584783 694.378037 l
886.576087 698.380505 l
881.567391 702.42187 l
876.558696 705.467416 l
871.55 708.222748 l
866.541304 710.450341 l
861.532609 713.86406 l
856.523913 716.165513 l
851.515217 718.206318 l
846.506522 720.764657 l
841.497826 722.440188 l
836.48913 723.978252 l
831.480435 726.295979 l
826.471739 728.099403 l
821.463043 728.925151 l
816.454348 730.40744 l
811.445652 732.482739 l
806.436957 733.862173 l
801.428261 735.139822 l
796.419565 736.917952 l
791.41087 737.908817 l
786.402174 739.192975 l
781.393478 740.549284 l
776.384783 741.686963 l
771.376087 743.162438 l
766.367391 744.025218 l
761.358696 745.068093 l
756.35 746.302654 l
751.341304 747.570626 l
746.332609 748.221897 l
741.323913 749.629922 l
736.315217 750.324774 l
731.306522 751.538172 l
726.297826 752.352315 l
721.28913 753.171701 l
716.280435 754.249139 l
711.271739 755.18548 l
706.263043 755.978891 l
701.254348 756.812784 l
696.245652 757.749648 l
691.236957 758.527199 l
686.228261 759.400151 l
681.219565 760.188462 l
676.21087 760.920711 l
671.202174 761.823297 l
666.193478 762.574237 l
661.184783 763.44756 l
656.176087 764.137962 l
651.167391 764.941889 l
646.158696 765.517069 l
641.15 766.231166 l
636.141304 766.942873 l
631.132609 767.703601 l
626.123913 768.408013 l
621.115217 769.074348 l
616.106522 769.769688 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
388.174 313.357 598.576 473.97 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-4 -4 m
4 -4 l
4 4 l
-4 4 l
cl

gsave
grestore
stroke
grestore
} bind def
981.741 489.575 o
976.733 490.245 o
971.724 491.227 o
966.715 498.908 o
961.707 488.757 o
956.698 489.757 o
951.689 489.406 o
946.68 489.719 o
941.672 499.963 o
936.663 488.865 o
931.654 494.786 o
926.646 516.361 o
921.637 505.829 o
916.628 510.791 o
911.62 513.798 o
906.611 538.276 o
901.602 680.357 o
896.593 688.33 o
891.585 694.378 o
886.576 698.381 o
881.567 702.422 o
876.559 705.467 o
871.55 708.223 o
866.541 710.45 o
861.533 713.864 o
856.524 716.166 o
851.515 718.206 o
846.507 720.765 o
841.498 722.44 o
836.489 723.978 o
831.48 726.296 o
826.472 728.099 o
821.463 728.925 o
816.454 730.407 o
811.446 732.483 o
806.437 733.862 o
801.428 735.14 o
796.42 736.918 o
791.411 737.909 o
786.402 739.193 o
781.393 740.549 o
776.385 741.687 o
771.376 743.162 o
766.367 744.025 o
761.359 745.068 o
756.35 746.303 o
751.341 747.571 o
746.333 748.222 o
741.324 749.63 o
736.315 750.325 o
731.307 751.538 o
726.298 752.352 o
721.289 753.172 o
716.28 754.249 o
711.272 755.185 o
706.263 755.979 o
701.254 756.813 o
696.246 757.75 o
691.237 758.527 o
686.228 759.4 o
681.22 760.188 o
676.211 760.921 o
671.202 761.823 o
666.193 762.574 o
661.185 763.448 o
656.176 764.138 o
651.167 764.942 o
646.159 765.517 o
641.15 766.231 o
636.141 766.943 o
631.133 767.704 o
626.124 768.408 o
621.115 769.074 o
616.107 769.77 o
grestore
0.050 setlinewidth
1 setlinejoin
[0.185 0.08] 0 setdash
1.000 0.514 0.000 setrgbcolor
gsave
388.174 313.357 598.576 473.97 clipbox
981.741304 488.055441 m
976.732609 494.100562 l
971.723913 491.454035 l
966.715217 497.503993 l
961.706522 489.965936 l
956.697826 488.165455 l
951.68913 493.329056 l
946.680435 501.286627 l
941.671739 489.552675 l
936.663043 501.853142 l
931.654348 490.393597 l
926.645652 519.824851 l
921.636957 489.798191 l
916.628261 578.416513 l
911.619565 590.869664 l
906.61087 580.08559 l
901.602174 678.79483 l
896.593478 688.172096 l
891.584783 693.318866 l
886.576087 698.388576 l
881.567391 702.146628 l
876.558696 705.661587 l
871.55 708.766585 l
866.541304 711.319447 l
861.532609 713.985152 l
856.523913 715.541969 l
851.515217 718.130952 l
846.506522 720.470768 l
841.497826 722.355044 l
836.48913 724.130281 l
831.480435 725.907133 l
826.471739 727.759617 l
821.463043 729.292917 l
816.454348 730.615604 l
811.445652 732.466177 l
806.436957 734.098027 l
801.428261 735.036228 l
796.419565 736.441504 l
791.41087 737.862489 l
786.402174 739.303205 l
781.393478 740.307798 l
776.384783 741.503079 l
771.376087 743.023799 l
766.367391 743.986546 l
761.358696 745.085962 l
756.35 746.186037 l
751.341304 747.406496 l
746.332609 748.222069 l
741.323913 749.285938 l
736.315217 750.42179 l
731.306522 751.326605 l
726.297826 752.214288 l
721.28913 753.300545 l
716.280435 754.085454 l
711.271739 755.072188 l
706.263043 755.994867 l
701.254348 756.940036 l
696.245652 757.669876 l
691.236957 758.596402 l
686.228261 759.564387 l
681.219565 760.232271 l
676.21087 761.118279 l
671.202174 761.793292 l
666.193478 762.601046 l
661.184783 763.388437 l
656.176087 764.11771 l
651.167391 764.872675 l
646.158696 765.661882 l
641.15 766.353033 l
636.141304 766.994188 l
631.132609 767.742914 l
626.123913 768.451859 l
621.115217 769.069919 l
616.106522 769.765658 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
388.174 313.357 598.576 473.97 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
981.741 488.055 o
976.733 494.101 o
971.724 491.454 o
966.715 497.504 o
961.707 489.966 o
956.698 488.165 o
951.689 493.329 o
946.68 501.287 o
941.672 489.553 o
936.663 501.853 o
931.654 490.394 o
926.646 519.825 o
921.637 489.798 o
916.628 578.417 o
911.62 590.87 o
906.611 580.086 o
901.602 678.795 o
896.593 688.172 o
891.585 693.319 o
886.576 698.389 o
881.567 702.147 o
876.559 705.662 o
871.55 708.767 o
866.541 711.319 o
861.533 713.985 o
856.524 715.542 o
851.515 718.131 o
846.507 720.471 o
841.498 722.355 o
836.489 724.13 o
831.48 725.907 o
826.472 727.76 o
821.463 729.293 o
816.454 730.616 o
811.446 732.466 o
806.437 734.098 o
801.428 735.036 o
796.42 736.442 o
791.411 737.862 o
786.402 739.303 o
781.393 740.308 o
776.385 741.503 o
771.376 743.024 o
766.367 743.987 o
761.359 745.086 o
756.35 746.186 o
751.341 747.406 o
746.333 748.222 o
741.324 749.286 o
736.315 750.422 o
731.307 751.327 o
726.298 752.214 o
721.289 753.301 o
716.28 754.085 o
711.272 755.072 o
706.263 755.995 o
701.254 756.94 o
696.246 757.67 o
691.237 758.596 o
686.228 759.564 o
681.22 760.232 o
676.211 761.118 o
671.202 761.793 o
666.193 762.601 o
661.185 763.388 o
656.176 764.118 o
651.167 764.873 o
646.159 765.662 o
641.15 766.353 o
636.141 766.994 o
631.133 767.743 o
626.124 768.452 o
621.115 769.07 o
616.107 769.766 o
grestore
2.500 setlinewidth
0 setlinejoin
2 setlinecap
0.000 setgray
gsave
598.576087 473.969728 m
598.576087 787.32625 l
stroke
grestore
gsave
986.75 473.969728 m
986.75 787.32625 l
stroke
grestore
gsave
598.576087 473.969728 m
986.75 473.969728 l
stroke
grestore
gsave
598.576087 787.32625 m
986.75 787.32625 l
stroke
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

598.576 793.326 translate
0 rotate
0 0 m /parenleft glyphshow
8.3252 0 m /b glyphshow
20.8252 0 m /parenright glyphshow
grestore
0.050 setlinewidth
1 setlinejoin
0 setlinecap
[0.185 0.08] 0 setdash
0.722 0.224 0.055 setrgbcolor
gsave
689.520856 557.669728 m
707.520856 557.669728 l
725.520856 557.669728 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

0 2 m
-2 -2 l
2 -2 l
cl

gsave
0.722 0.224 0.055 setrgbcolor
fill
grestore
stroke
grestore
} bind def
707.521 557.67 o
grestore
0.000 setgray
gsave
739.921 551.37 translate
0 rotate
/TimesNewRomanPSMT 18.0 selectfont
0 0.078125 moveto
/P glyphshow
/TimesNewRomanPSMT 12.6 selectfont
10.2812 -4.55913 moveto
/x glyphshow
grestore
0.050 setlinewidth
1 setlinejoin
[0.185 0.08] 0 setdash
1.000 0.271 0.000 setrgbcolor
gsave
689.520856 531.069728 m
707.520856 531.069728 l
725.520856 531.069728 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-4 -4 m
4 -4 l
4 4 l
-4 4 l
cl

gsave
grestore
stroke
grestore
} bind def
707.521 531.07 o
grestore
0.000 setgray
gsave
739.921 524.77 translate
0 rotate
/TimesNewRomanPSMT 18.0 selectfont
0 0.078125 moveto
/P glyphshow
/TimesNewRomanPSMT 12.6 selectfont
10.2812 -4.55913 moveto
/y glyphshow
grestore
0.050 setlinewidth
1 setlinejoin
[0.185 0.08] 0 setdash
1.000 0.514 0.000 setrgbcolor
gsave
689.520856 501.469728 m
707.520856 501.469728 l
725.520856 501.469728 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
707.521 501.47 o
grestore
0.000 setgray
gsave
739.921 495.17 translate
0 rotate
/TimesNewRomanPSMT 18.0 selectfont
0 0.078125 moveto
/P glyphshow
/TimesNewRomanPSMT 12.6 selectfont
10.2812 -4.55913 moveto
/z glyphshow
grestore
0.031 0.659 0.659 setrgbcolor
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

-0 -12.247449 m
7.348469 0 l
0 12.247449 l
-7.348469 0 l
cl

gsave
0.031 0.659 0.659 setrgbcolor
fill
grestore
stroke
grestore
} bind def
811.921 556.095 o
grestore
0.000 setgray
/TimesNewRomanPSMT 18.000 selectfont
gsave

844.321 551.37 translate
0 rotate
0 0 m /R glyphshow
12.0059 0 m /e glyphshow
19.9951 0 m /f glyphshow
25.9893 0 m /period glyphshow
30.4893 0 m /bracketleft glyphshow
36.4834 0 m /a glyphshow
44.4727 0 m /bracketright glyphshow
grestore
0.031 0.416 0.416 setrgbcolor
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

8.660254 -0 m
-8.660254 8.660254 l
-8.660254 -8.660254 l
cl

gsave
0.031 0.416 0.416 setrgbcolor
fill
grestore
stroke
grestore
} bind def
811.921 530.651 o
grestore
0.000 setgray
/TimesNewRomanPSMT 18.000 selectfont
gsave

844.321 525.926 translate
0 rotate
0 0 m /R glyphshow
12.0059 0 m /e glyphshow
19.9951 0 m /f glyphshow
25.9893 0 m /period glyphshow
30.4893 0 m /bracketleft glyphshow
36.4834 0 m /b glyphshow
45.4834 0 m /bracketright glyphshow
grestore
0.031 0.235 0.235 setrgbcolor
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 8.660254 m
-1.944348 2.676166 l
-8.236391 2.676166 l
-3.146021 -1.022204 l
-5.09037 -7.006293 l
-0 -3.307923 l
5.09037 -7.006293 l
3.146021 -1.022204 l
8.236391 2.676166 l
1.944348 2.676166 l
cl

gsave
0.031 0.235 0.235 setrgbcolor
fill
grestore
stroke
grestore
} bind def
811.921 505.207 o
grestore
0.000 setgray
/TimesNewRomanPSMT 18.000 selectfont
gsave

844.321 500.482 translate
0 rotate
0 0 m /R glyphshow
12.0059 0 m /e glyphshow
19.9951 0 m /f glyphshow
25.9893 0 m /period glyphshow
30.4893 0 m /bracketleft glyphshow
36.4834 0 m /c glyphshow
44.4727 0 m /bracketright glyphshow
grestore
gsave
93.95 66.60625 m
482.123913 66.60625 l
482.123913 379.962772 l
93.95 379.962772 l
cl
1.000 setgray
fill
grestore
2.500 setlinewidth
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
106.472 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
106.472 379.963 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

100.222 39.2469 translate
0 rotate
0 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
181.602 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
181.602 379.963 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

162.852 39.2469 translate
0 rotate
0 0 m /three glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
256.733 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
256.733 379.963 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

237.983 39.2469 translate
0 rotate
0 0 m /six glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
331.863 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
331.863 379.963 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

313.113 39.2469 translate
0 rotate
0 0 m /nine glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
406.993 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
406.993 379.963 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

381.993 39.2469 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /two glyphshow
25 0 m /zero glyphshow
37.5 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 379.963 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

457.124 39.2469 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /five glyphshow
25 0 m /zero glyphshow
37.5 0 m /zero glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
125.254 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
125.254 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
144.037 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
144.037 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
162.82 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
162.82 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
200.385 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
200.385 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
219.167 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
219.167 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
237.95 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
237.95 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
275.515 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
275.515 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
294.298 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
294.298 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
313.08 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
313.08 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
350.646 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
350.646 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
369.428 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
369.428 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
388.211 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
388.211 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
425.776 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
425.776 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
444.559 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
444.559 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
463.341 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
463.341 379.963 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

204.576 12.5437 translate
0 rotate
0 0 m /T glyphshow
13.521 0 m /e glyphshow
24.6172 0 m /m glyphshow
44.063 0 m /p glyphshow
56.563 0 m /e glyphshow
67.6592 0 m /r glyphshow
75.9844 0 m /a glyphshow
87.0806 0 m /t glyphshow
94.0264 0 m /u glyphshow
106.526 0 m /r glyphshow
114.852 0 m /e glyphshow
125.948 0 m /space glyphshow
132.198 0 m /parenleft glyphshow
140.523 0 m /K glyphshow
158.577 0 m /parenright glyphshow
grestore
2.500 setlinewidth
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 80.8437 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 80.8437 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

40.2 72.164 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 175.966 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 175.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

40.2 167.286 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /two glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 271.088 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 271.088 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

40.2 262.409 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /four glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 366.211 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 366.211 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

40.2 357.531 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /six glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 104.624 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 104.624 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 128.405 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 128.405 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 152.185 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 152.185 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 199.747 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 199.747 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 223.527 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 223.527 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 247.308 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 247.308 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 294.869 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 294.869 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 318.65 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 318.65 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 342.43 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 342.43 o
grestore
gsave
25.2 180.285 translate
90 rotate
/TimesNewRomanPSMT 25.0 selectfont
0 0.6875 moveto
/less glyphshow
14.0991 0.6875 moveto
/omega glyphshow
/TimesNewRomanPSMT 17.5 selectfont
30.9299 -5.75313 moveto
/x glyphshow
39.6799 -5.75313 moveto
/comma glyphshow
46.7009 -5.75313 moveto
/y glyphshow
55.4509 -5.75313 moveto
/comma glyphshow
62.4719 -5.75313 moveto
/z glyphshow
/TimesNewRomanPSMT 25.0 selectfont
71.3127 0.6875 moveto
/greater glyphshow
grestore
0.050 setlinewidth
[0.185 0.08] 0 setdash
0.722 0.224 0.055 setrgbcolor
gsave
388.174 313.357 93.95 66.606 clipbox
477.115217 81.128342 m
472.106522 81.579269 l
467.097826 80.917979 l
462.08913 82.275184 l
457.080435 81.621028 l
452.071739 81.184607 l
447.063043 82.050696 l
442.054348 82.154807 l
437.045652 81.280537 l
432.036957 81.112646 l
427.028261 81.078783 l
422.019565 80.861143 l
417.01087 80.899002 l
412.002174 80.905042 l
406.993478 81.365244 l
401.984783 82.005465 l
396.976087 220.627363 l
391.967391 236.242125 l
386.958696 246.084815 l
381.95 253.682523 l
376.941304 260.604814 l
371.932609 265.336866 l
366.923913 271.097618 l
361.915217 276.139293 l
356.906522 279.26782 l
351.897826 283.815286 l
346.88913 287.858842 l
341.880435 290.630232 l
336.871739 293.417793 l
331.863043 297.110538 l
326.854348 299.549047 l
321.845652 302.340555 l
316.836957 305.113657 l
311.828261 307.406486 l
306.819565 309.702121 l
301.81087 311.617314 l
296.802174 314.273749 l
291.793478 316.135579 l
286.784783 318.001499 l
281.776087 320.159777 l
276.767391 321.978754 l
271.758696 323.657759 l
266.75 325.646006 l
261.741304 327.561818 l
256.732609 329.255139 l
251.723913 330.826845 l
246.715217 332.314797 l
241.706522 333.965217 l
236.697826 335.735729 l
231.68913 336.950917 l
226.680435 338.352308 l
221.671739 339.97343 l
216.663043 341.416246 l
211.654348 342.541972 l
206.645652 344.039578 l
201.636957 345.466889 l
196.628261 346.74286 l
191.619565 347.880713 l
186.61087 349.130098 l
181.602174 350.25863 l
176.593478 351.633861 l
171.584783 352.824555 l
166.576087 353.977866 l
161.567391 355.118335 l
156.558696 356.253002 l
151.55 357.28546 l
146.541304 358.376561 l
141.532609 359.581429 l
136.523913 360.645848 l
131.515217 361.71664 l
126.506522 362.640659 l
121.497826 363.709739 l
116.48913 364.703862 l
111.480435 365.681149 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
388.174 313.357 93.95 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

0 2 m
-2 -2 l
2 -2 l
cl

gsave
0.722 0.224 0.055 setrgbcolor
fill
grestore
stroke
grestore
} bind def
477.115 81.1283 o
472.107 81.5793 o
467.098 80.918 o
462.089 82.2752 o
457.08 81.621 o
452.072 81.1846 o
447.063 82.0507 o
442.054 82.1548 o
437.046 81.2805 o
432.037 81.1126 o
427.028 81.0788 o
422.02 80.8611 o
417.011 80.899 o
412.002 80.905 o
406.993 81.3652 o
401.985 82.0055 o
396.976 220.627 o
391.967 236.242 o
386.959 246.085 o
381.95 253.683 o
376.941 260.605 o
371.933 265.337 o
366.924 271.098 o
361.915 276.139 o
356.907 279.268 o
351.898 283.815 o
346.889 287.859 o
341.88 290.63 o
336.872 293.418 o
331.863 297.111 o
326.854 299.549 o
321.846 302.341 o
316.837 305.114 o
311.828 307.406 o
306.82 309.702 o
301.811 311.617 o
296.802 314.274 o
291.793 316.136 o
286.785 318.001 o
281.776 320.16 o
276.767 321.979 o
271.759 323.658 o
266.75 325.646 o
261.741 327.562 o
256.733 329.255 o
251.724 330.827 o
246.715 332.315 o
241.707 333.965 o
236.698 335.736 o
231.689 336.951 o
226.68 338.352 o
221.672 339.973 o
216.663 341.416 o
211.654 342.542 o
206.646 344.04 o
201.637 345.467 o
196.628 346.743 o
191.62 347.881 o
186.611 349.13 o
181.602 350.259 o
176.593 351.634 o
171.585 352.825 o
166.576 353.978 o
161.567 355.118 o
156.559 356.253 o
151.55 357.285 o
146.541 358.377 o
141.533 359.581 o
136.524 360.646 o
131.515 361.717 o
126.507 362.641 o
121.498 363.71 o
116.489 364.704 o
111.48 365.681 o
grestore
0.050 setlinewidth
1 setlinejoin
[0.185 0.08] 0 setdash
1.000 0.271 0.000 setrgbcolor
gsave
388.174 313.357 93.95 66.606 clipbox
477.115217 81.376468 m
472.106522 80.861999 l
467.097826 81.605761 l
462.08913 81.259563 l
457.080435 82.825324 l
452.071739 81.561196 l
447.063043 81.064229 l
442.054348 81.882424 l
437.045652 81.094526 l
432.036957 81.854648 l
427.028261 81.332617 l
422.019565 83.236538 l
417.01087 83.127909 l
412.002174 84.511178 l
406.993478 81.003779 l
401.984783 85.08857 l
396.976087 220.79026 l
391.967391 235.597624 l
386.958696 246.264549 l
381.95 253.442767 l
376.941304 260.252434 l
371.932609 266.221361 l
366.923913 271.187319 l
361.915217 275.501593 l
356.906522 280.176286 l
351.897826 284.275108 l
346.88913 287.28321 l
341.880435 291.271214 l
336.871739 293.818068 l
331.863043 296.521588 l
326.854348 299.498251 l
321.845652 302.329378 l
316.836957 304.781489 l
311.828261 307.209202 l
306.819565 309.57656 l
301.81087 311.891457 l
296.802174 314.061245 l
291.793478 316.378759 l
286.784783 318.197403 l
281.776087 320.53142 l
276.767391 322.295464 l
271.758696 324.196675 l
266.75 325.934465 l
261.741304 327.46427 l
256.732609 329.095856 l
251.723913 330.983607 l
246.715217 332.45377 l
241.706522 333.860059 l
236.697826 335.465106 l
231.68913 337.009513 l
226.680435 338.651134 l
221.671739 339.948746 l
216.663043 341.214729 l
211.654348 342.840846 l
206.645652 344.139837 l
201.636957 345.393597 l
196.628261 346.594707 l
191.619565 347.937169 l
186.61087 349.168195 l
181.602174 350.406688 l
176.593478 351.730648 l
171.584783 352.877538 l
166.576087 353.983098 l
161.567391 355.044473 l
156.558696 356.27079 l
151.55 357.422817 l
146.541304 358.43996 l
141.532609 359.424049 l
136.523913 360.529037 l
131.515217 361.641541 l
126.506522 362.658589 l
121.497826 363.664175 l
116.48913 364.724219 l
111.480435 365.69518 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
388.174 313.357 93.95 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-4 -4 m
4 -4 l
4 4 l
-4 4 l
cl

gsave
grestore
stroke
grestore
} bind def
477.115 81.3765 o
472.107 80.862 o
467.098 81.6058 o
462.089 81.2596 o
457.08 82.8253 o
452.072 81.5612 o
447.063 81.0642 o
442.054 81.8824 o
437.046 81.0945 o
432.037 81.8546 o
427.028 81.3326 o
422.02 83.2365 o
417.011 83.1279 o
412.002 84.5112 o
406.993 81.0038 o
401.985 85.0886 o
396.976 220.79 o
391.967 235.598 o
386.959 246.265 o
381.95 253.443 o
376.941 260.252 o
371.933 266.221 o
366.924 271.187 o
361.915 275.502 o
356.907 280.176 o
351.898 284.275 o
346.889 287.283 o
341.88 291.271 o
336.872 293.818 o
331.863 296.522 o
326.854 299.498 o
321.846 302.329 o
316.837 304.781 o
311.828 307.209 o
306.82 309.577 o
301.811 311.891 o
296.802 314.061 o
291.793 316.379 o
286.785 318.197 o
281.776 320.531 o
276.767 322.295 o
271.759 324.197 o
266.75 325.934 o
261.741 327.464 o
256.733 329.096 o
251.724 330.984 o
246.715 332.454 o
241.707 333.86 o
236.698 335.465 o
231.689 337.01 o
226.68 338.651 o
221.672 339.949 o
216.663 341.215 o
211.654 342.841 o
206.646 344.14 o
201.637 345.394 o
196.628 346.595 o
191.62 347.937 o
186.611 349.168 o
181.602 350.407 o
176.593 351.731 o
171.585 352.878 o
166.576 353.983 o
161.567 355.044 o
156.559 356.271 o
151.55 357.423 o
146.541 358.44 o
141.533 359.424 o
136.524 360.529 o
131.515 361.642 o
126.507 362.659 o
121.498 363.664 o
116.489 364.724 o
111.48 365.695 o
grestore
0.050 setlinewidth
1 setlinejoin
[0.185 0.08] 0 setdash
1.000 0.514 0.000 setrgbcolor
gsave
388.174 313.357 93.95 66.606 clipbox
477.115217 81.232738 m
472.106522 81.984348 l
467.097826 80.887492 l
462.08913 81.855172 l
457.080435 81.535465 l
452.071739 81.597961 l
447.063043 81.745591 l
442.054348 80.92478 l
437.045652 80.878883 l
432.036957 80.849728 l
427.028261 81.487238 l
422.019565 81.712868 l
417.01087 82.576817 l
412.002174 81.452614 l
406.993478 83.009148 l
401.984783 81.9687 l
396.976087 220.468794 l
391.967391 236.301624 l
386.958696 245.523261 l
381.95 254.040801 l
376.941304 260.475163 l
371.932609 266.031116 l
366.923913 271.651516 l
361.915217 275.921463 l
356.906522 280.176286 l
351.897826 283.736335 l
346.88913 286.971684 l
341.880435 290.48831 l
336.871739 294.035898 l
331.863043 296.832542 l
326.854348 299.114195 l
321.845652 302.400006 l
316.836957 304.956086 l
311.828261 307.525151 l
306.819565 309.807231 l
301.81087 312.202127 l
296.802174 314.156701 l
291.793478 316.227657 l
286.784783 318.424508 l
281.776087 320.173427 l
276.767391 321.948743 l
271.758696 323.914161 l
266.75 325.813469 l
261.741304 327.354119 l
256.732609 329.092908 l
251.723913 330.628563 l
246.715217 332.315415 l
241.706522 333.970354 l
236.697826 335.348772 l
231.68913 337.06183 l
226.680435 338.480152 l
221.671739 339.892624 l
216.663043 341.340196 l
211.654348 342.719612 l
206.645652 344.065784 l
201.636957 345.378663 l
196.628261 346.795177 l
191.619565 348.084513 l
186.61087 349.307834 l
181.602174 350.464427 l
176.593478 351.585586 l
171.584783 352.904743 l
166.576087 354.076032 l
161.567391 355.178167 l
156.558696 356.188985 l
151.55 357.427098 l
146.541304 358.480435 l
141.532609 359.556935 l
136.523913 360.6107 l
131.515217 361.65657 l
126.506522 362.71181 l
121.497826 363.725386 l
116.48913 364.700676 l
111.480435 365.719293 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
388.174 313.357 93.95 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
477.115 81.2327 o
472.107 81.9843 o
467.098 80.8875 o
462.089 81.8552 o
457.08 81.5355 o
452.072 81.598 o
447.063 81.7456 o
442.054 80.9248 o
437.046 80.8789 o
432.037 80.8497 o
427.028 81.4872 o
422.02 81.7129 o
417.011 82.5768 o
412.002 81.4526 o
406.993 83.0091 o
401.985 81.9687 o
396.976 220.469 o
391.967 236.302 o
386.959 245.523 o
381.95 254.041 o
376.941 260.475 o
371.933 266.031 o
366.924 271.652 o
361.915 275.921 o
356.907 280.176 o
351.898 283.736 o
346.889 286.972 o
341.88 290.488 o
336.872 294.036 o
331.863 296.833 o
326.854 299.114 o
321.846 302.4 o
316.837 304.956 o
311.828 307.525 o
306.82 309.807 o
301.811 312.202 o
296.802 314.157 o
291.793 316.228 o
286.785 318.425 o
281.776 320.173 o
276.767 321.949 o
271.759 323.914 o
266.75 325.813 o
261.741 327.354 o
256.733 329.093 o
251.724 330.629 o
246.715 332.315 o
241.707 333.97 o
236.698 335.349 o
231.689 337.062 o
226.68 338.48 o
221.672 339.893 o
216.663 341.34 o
211.654 342.72 o
206.646 344.066 o
201.637 345.379 o
196.628 346.795 o
191.62 348.085 o
186.611 349.308 o
181.602 350.464 o
176.593 351.586 o
171.585 352.905 o
166.576 354.076 o
161.567 355.178 o
156.559 356.189 o
151.55 357.427 o
146.541 358.48 o
141.533 359.557 o
136.524 360.611 o
131.515 361.657 o
126.507 362.712 o
121.498 363.725 o
116.489 364.701 o
111.48 365.719 o
grestore
2.500 setlinewidth
0 setlinejoin
2 setlinecap
0.000 setgray
gsave
93.95 66.60625 m
93.95 379.962772 l
stroke
grestore
gsave
482.123913 66.60625 m
482.123913 379.962772 l
stroke
grestore
gsave
93.95 66.60625 m
482.123913 66.60625 l
stroke
grestore
gsave
93.95 379.962772 m
482.123913 379.962772 l
stroke
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

93.95 385.963 translate
0 rotate
0 0 m /parenleft glyphshow
8.3252 0 m /c glyphshow
19.4214 0 m /parenright glyphshow
grestore
0.050 setlinewidth
1 setlinejoin
0 setlinecap
[0.185 0.08] 0 setdash
0.722 0.224 0.055 setrgbcolor
gsave
352.123913 354.962772 m
372.123913 354.962772 l
392.123913 354.962772 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

0 2 m
-2 -2 l
2 -2 l
cl

gsave
0.722 0.224 0.055 setrgbcolor
fill
grestore
stroke
grestore
} bind def
372.124 354.963 o
grestore
0.000 setgray
gsave
408.124 347.963 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
3.024 0.546875 moveto
/less glyphshow
17.3273 0.546875 moveto
/omega glyphshow
/TimesNewRomanPSMT 14.0 selectfont
30.7919 -4.60562 moveto
/x glyphshow
/TimesNewRomanPSMT 20.0 selectfont
41.6747 0.546875 moveto
/greater glyphshow
grestore
0.050 setlinewidth
1 setlinejoin
[0.185 0.08] 0 setdash
1.000 0.271 0.000 setrgbcolor
gsave
352.123913 324.962772 m
372.123913 324.962772 l
392.123913 324.962772 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-4 -4 m
4 -4 l
4 4 l
-4 4 l
cl

gsave
grestore
stroke
grestore
} bind def
372.124 324.963 o
grestore
0.000 setgray
gsave
408.124 317.963 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
3.024 0.546875 moveto
/less glyphshow
17.3273 0.546875 moveto
/omega glyphshow
/TimesNewRomanPSMT 14.0 selectfont
30.7919 -4.60562 moveto
/y glyphshow
/TimesNewRomanPSMT 20.0 selectfont
38.6507 0.546875 moveto
/greater glyphshow
grestore
0.050 setlinewidth
1 setlinejoin
[0.185 0.08] 0 setdash
1.000 0.514 0.000 setrgbcolor
gsave
352.123913 291.962772 m
372.123913 291.962772 l
392.123913 291.962772 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
372.124 291.963 o
grestore
0.000 setgray
gsave
408.124 284.963 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
3.024 0.546875 moveto
/less glyphshow
17.3273 0.546875 moveto
/omega glyphshow
/TimesNewRomanPSMT 14.0 selectfont
30.7919 -4.60562 moveto
/z glyphshow
/TimesNewRomanPSMT 20.0 selectfont
40.8885 0.546875 moveto
/greater glyphshow
grestore
gsave
598.576087 66.60625 m
986.75 66.60625 l
986.75 379.962772 l
598.576087 379.962772 l
cl
1.000 setgray
fill
grestore
0 setlinejoin
0.902 0.902 0.980 setrgbcolor
gsave
388.174 313.357 598.576 66.606 clipbox
598.576087 66.60625 m
598.576087 379.962772 l
906.61087 379.962772 l
906.61087 66.60625 l
cl
gsave
fill
grestore
stroke
grestore
0.800 setgray
gsave
388.174 313.357 598.576 66.606 clipbox
906.61087 66.60625 m
906.61087 379.962772 l
986.75 379.962772 l
986.75 66.60625 l
cl
gsave
fill
grestore
stroke
grestore
2.500 setlinewidth
1 setlinejoin
0.000 setgray
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
611.098 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
611.098 379.963 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

604.848 39.2469 translate
0 rotate
0 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
686.228 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
686.228 379.963 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

667.478 39.2469 translate
0 rotate
0 0 m /three glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
761.359 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
761.359 379.963 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

742.609 39.2469 translate
0 rotate
0 0 m /six glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
836.489 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
836.489 379.963 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

817.739 39.2469 translate
0 rotate
0 0 m /nine glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
911.62 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
911.62 379.963 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

886.62 39.2469 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /two glyphshow
25 0 m /zero glyphshow
37.5 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 379.963 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

961.75 39.2469 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /five glyphshow
25 0 m /zero glyphshow
37.5 0 m /zero glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
629.88 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
629.88 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
648.663 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
648.663 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
667.446 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
667.446 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
705.011 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
705.011 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
723.793 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
723.793 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
742.576 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
742.576 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
780.141 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
780.141 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
798.924 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
798.924 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
817.707 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
817.707 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
855.272 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
855.272 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
874.054 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
874.054 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
892.837 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
892.837 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
930.402 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
930.402 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
949.185 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
949.185 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
967.967 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
967.967 379.963 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

709.202 12.5437 translate
0 rotate
0 0 m /T glyphshow
13.521 0 m /e glyphshow
24.6172 0 m /m glyphshow
44.063 0 m /p glyphshow
56.563 0 m /e glyphshow
67.6592 0 m /r glyphshow
75.9844 0 m /a glyphshow
87.0806 0 m /t glyphshow
94.0264 0 m /u glyphshow
106.526 0 m /r glyphshow
114.852 0 m /e glyphshow
125.948 0 m /space glyphshow
132.198 0 m /parenleft glyphshow
140.523 0 m /K glyphshow
158.577 0 m /parenright glyphshow
grestore
2.500 setlinewidth
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 66.6063 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

544.826 57.9266 translate
0 rotate
0 0 m /seven glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /eight glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 129.278 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 129.278 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

544.826 120.598 translate
0 rotate
0 0 m /seven glyphshow
12.5 0 m /period glyphshow
18.75 0 m /one glyphshow
31.25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 191.949 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 191.949 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

544.826 183.269 translate
0 rotate
0 0 m /seven glyphshow
12.5 0 m /period glyphshow
18.75 0 m /one glyphshow
31.25 0 m /two glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 254.62 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 254.62 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

544.826 245.94 translate
0 rotate
0 0 m /seven glyphshow
12.5 0 m /period glyphshow
18.75 0 m /one glyphshow
31.25 0 m /four glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 317.291 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 317.291 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

544.826 308.612 translate
0 rotate
0 0 m /seven glyphshow
12.5 0 m /period glyphshow
18.75 0 m /one glyphshow
31.25 0 m /six glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 379.963 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

544.826 371.283 translate
0 rotate
0 0 m /seven glyphshow
12.5 0 m /period glyphshow
18.75 0 m /one glyphshow
31.25 0 m /eight glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 82.2741 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 82.2741 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 97.9419 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 97.9419 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 113.61 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 113.61 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 144.945 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 144.945 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 160.613 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 160.613 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 176.281 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 176.281 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 207.617 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 207.617 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 223.285 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 223.285 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 238.952 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 238.952 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 270.288 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 270.288 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 285.956 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 285.956 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 301.624 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 301.624 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 332.959 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 332.959 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 348.627 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 348.627 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 364.295 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 364.295 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

535.482 100.417 translate
90 rotate
0 0 m /L glyphshow
15.271 0 m /a glyphshow
26.3672 0 m /t glyphshow
33.313 0 m /t glyphshow
40.2588 0 m /i glyphshow
47.2046 0 m /c glyphshow
58.3008 0 m /e glyphshow
69.397 0 m /space glyphshow
75.647 0 m /P glyphshow
89.5508 0 m /a glyphshow
100.647 0 m /r glyphshow
108.972 0 m /a glyphshow
120.068 0 m /m glyphshow
139.514 0 m /e glyphshow
150.61 0 m /t glyphshow
157.556 0 m /e glyphshow
168.652 0 m /r glyphshow
176.978 0 m /s glyphshow
186.707 0 m /space glyphshow
192.957 0 m /parenleft glyphshow
201.282 0 m /a glyphshow
212.378 0 m /period glyphshow
218.628 0 m /u glyphshow
231.128 0 m /period glyphshow
237.378 0 m /parenright glyphshow
grestore
0.050 setlinewidth
[0.185 0.08] 0 setdash
0.722 0.224 0.055 setrgbcolor
gsave
388.174 313.357 598.576 66.606 clipbox
981.741304 105.224308 m
976.732609 104.494187 l
971.723913 103.833005 l
966.715217 103.588587 l
961.706522 102.889802 l
956.697826 102.51064 l
951.68913 102.184749 l
946.680435 101.714715 l
941.671739 101.467163 l
936.663043 101.131872 l
931.654348 100.92819 l
926.645652 101.623841 l
921.636957 102.28189 l
916.628261 102.592113 l
911.619565 104.011618 l
906.61087 106.399395 l
901.602174 163.637097 l
896.593478 177.264972 l
891.584783 187.163905 l
886.576087 195.217167 l
881.567391 202.424367 l
876.558696 208.237131 l
871.55 214.808217 l
866.541304 220.373429 l
861.532609 225.368332 l
856.523913 230.422772 l
851.515217 235.289199 l
846.506522 239.867338 l
841.497826 243.821897 l
836.48913 248.061611 l
831.480435 251.762352 l
826.471739 256.039668 l
821.463043 259.718474 l
816.454348 263.181063 l
811.445652 266.8348 l
806.436957 270.009102 l
801.428261 273.512428 l
796.419565 276.774469 l
791.41087 279.804627 l
786.402174 283.192011 l
781.393478 285.97775 l
776.384783 288.854363 l
771.376087 292.204144 l
766.367391 294.892743 l
761.358696 297.622079 l
756.35 300.43602 l
751.341304 303.256229 l
746.332609 305.662807 l
741.323913 308.483016 l
736.315217 310.949131 l
731.306522 313.515521 l
726.297826 316.016106 l
721.28913 318.479089 l
716.280435 320.929537 l
711.271739 323.36745 l
706.263043 325.821032 l
701.254348 328.096 l
696.245652 330.361568 l
691.236957 332.617735 l
686.228261 334.814364 l
681.219565 337.136336 l
676.21087 339.342366 l
671.202174 341.573464 l
666.193478 343.588347 l
661.184783 345.788109 l
656.176087 347.815526 l
651.167391 349.827275 l
646.158696 351.980034 l
641.15 353.972982 l
636.141304 355.950261 l
631.132609 357.955743 l
626.123913 359.948691 l
621.115217 361.863299 l
616.106522 363.812377 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
388.174 313.357 598.576 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

0 2 m
-2 -2 l
2 -2 l
cl

gsave
0.722 0.224 0.055 setrgbcolor
fill
grestore
stroke
grestore
} bind def
981.741 105.224 o
976.733 104.494 o
971.724 103.833 o
966.715 103.589 o
961.707 102.89 o
956.698 102.511 o
951.689 102.185 o
946.68 101.715 o
941.672 101.467 o
936.663 101.132 o
931.654 100.928 o
926.646 101.624 o
921.637 102.282 o
916.628 102.592 o
911.62 104.012 o
906.611 106.399 o
901.602 163.637 o
896.593 177.265 o
891.585 187.164 o
886.576 195.217 o
881.567 202.424 o
876.559 208.237 o
871.55 214.808 o
866.541 220.373 o
861.533 225.368 o
856.524 230.423 o
851.515 235.289 o
846.507 239.867 o
841.498 243.822 o
836.489 248.062 o
831.48 251.762 o
826.472 256.04 o
821.463 259.718 o
816.454 263.181 o
811.446 266.835 o
806.437 270.009 o
801.428 273.512 o
796.42 276.774 o
791.411 279.805 o
786.402 283.192 o
781.393 285.978 o
776.385 288.854 o
771.376 292.204 o
766.367 294.893 o
761.359 297.622 o
756.35 300.436 o
751.341 303.256 o
746.333 305.663 o
741.324 308.483 o
736.315 310.949 o
731.307 313.516 o
726.298 316.016 o
721.289 318.479 o
716.28 320.93 o
711.272 323.367 o
706.263 325.821 o
701.254 328.096 o
696.246 330.362 o
691.237 332.618 o
686.228 334.814 o
681.22 337.136 o
676.211 339.342 o
671.202 341.573 o
666.193 343.588 o
661.185 345.788 o
656.176 347.816 o
651.167 349.827 o
646.159 351.98 o
641.15 353.973 o
636.141 355.95 o
631.133 357.956 o
626.124 359.949 o
621.115 361.863 o
616.107 363.812 o
grestore
0.050 setlinewidth
1 setlinejoin
[0.185 0.08] 0 setdash
1.000 0.271 0.000 setrgbcolor
gsave
388.174 313.357 598.576 66.606 clipbox
981.741304 105.224308 m
976.732609 104.494187 l
971.723913 103.833005 l
966.715217 103.588587 l
961.706522 102.889802 l
956.697826 102.51064 l
951.68913 102.184749 l
946.680435 101.714715 l
941.671739 101.467163 l
936.663043 101.131872 l
931.654348 100.92819 l
926.645652 101.623841 l
921.636957 102.28189 l
916.628261 102.592113 l
911.619565 104.011618 l
906.61087 106.399395 l
901.602174 163.637097 l
896.593478 177.264972 l
891.584783 187.163905 l
886.576087 195.217167 l
881.567391 202.424367 l
876.558696 208.237131 l
871.55 214.808217 l
866.541304 220.373429 l
861.532609 225.368332 l
856.523913 230.422772 l
851.515217 235.289199 l
846.506522 239.867338 l
841.497826 243.821897 l
836.48913 248.061611 l
831.480435 251.762352 l
826.471739 256.039668 l
821.463043 259.718474 l
816.454348 263.181063 l
811.445652 266.8348 l
806.436957 270.009102 l
801.428261 273.512428 l
796.419565 276.774469 l
791.41087 279.804627 l
786.402174 283.192011 l
781.393478 285.97775 l
776.384783 288.854363 l
771.376087 292.204144 l
766.367391 294.892743 l
761.358696 297.622079 l
756.35 300.43602 l
751.341304 303.256229 l
746.332609 305.662807 l
741.323913 308.483016 l
736.315217 310.949131 l
731.306522 313.515521 l
726.297826 316.016106 l
721.28913 318.479089 l
716.280435 320.929537 l
711.271739 323.36745 l
706.263043 325.821032 l
701.254348 328.096 l
696.245652 330.361568 l
691.236957 332.617735 l
686.228261 334.814364 l
681.219565 337.136336 l
676.21087 339.342366 l
671.202174 341.573464 l
666.193478 343.588347 l
661.184783 345.788109 l
656.176087 347.815526 l
651.167391 349.827275 l
646.158696 351.980034 l
641.15 353.972982 l
636.141304 355.950261 l
631.132609 357.955743 l
626.123913 359.948691 l
621.115217 361.863299 l
616.106522 363.812377 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
388.174 313.357 598.576 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-4 -4 m
4 -4 l
4 4 l
-4 4 l
cl

gsave
grestore
stroke
grestore
} bind def
981.741 105.224 o
976.733 104.494 o
971.724 103.833 o
966.715 103.589 o
961.707 102.89 o
956.698 102.511 o
951.689 102.185 o
946.68 101.715 o
941.672 101.467 o
936.663 101.132 o
931.654 100.928 o
926.646 101.624 o
921.637 102.282 o
916.628 102.592 o
911.62 104.012 o
906.611 106.399 o
901.602 163.637 o
896.593 177.265 o
891.585 187.164 o
886.576 195.217 o
881.567 202.424 o
876.559 208.237 o
871.55 214.808 o
866.541 220.373 o
861.533 225.368 o
856.524 230.423 o
851.515 235.289 o
846.507 239.867 o
841.498 243.822 o
836.489 248.062 o
831.48 251.762 o
826.472 256.04 o
821.463 259.718 o
816.454 263.181 o
811.446 266.835 o
806.437 270.009 o
801.428 273.512 o
796.42 276.774 o
791.411 279.805 o
786.402 283.192 o
781.393 285.978 o
776.385 288.854 o
771.376 292.204 o
766.367 294.893 o
761.359 297.622 o
756.35 300.436 o
751.341 303.256 o
746.333 305.663 o
741.324 308.483 o
736.315 310.949 o
731.307 313.516 o
726.298 316.016 o
721.289 318.479 o
716.28 320.93 o
711.272 323.367 o
706.263 325.821 o
701.254 328.096 o
696.246 330.362 o
691.237 332.618 o
686.228 334.814 o
681.22 337.136 o
676.211 339.342 o
671.202 341.573 o
666.193 343.588 o
661.185 345.788 o
656.176 347.816 o
651.167 349.827 o
646.159 351.98 o
641.15 353.973 o
636.141 355.95 o
631.133 357.956 o
626.124 359.949 o
621.115 361.863 o
616.107 363.812 o
grestore
0.050 setlinewidth
1 setlinejoin
[0.185 0.08] 0 setdash
1.000 0.514 0.000 setrgbcolor
gsave
388.174 313.357 598.576 66.606 clipbox
981.741304 105.224308 m
976.732609 104.494187 l
971.723913 103.833005 l
966.715217 103.588587 l
961.706522 102.889802 l
956.697826 102.51064 l
951.68913 102.184749 l
946.680435 101.714715 l
941.671739 101.467163 l
936.663043 101.131872 l
931.654348 100.92819 l
926.645652 101.623841 l
921.636957 102.28189 l
916.628261 102.592113 l
911.619565 104.011618 l
906.61087 106.399395 l
901.602174 163.637097 l
896.593478 177.264972 l
891.584783 187.163905 l
886.576087 195.217167 l
881.567391 202.424367 l
876.558696 208.237131 l
871.55 214.808217 l
866.541304 220.373429 l
861.532609 225.368332 l
856.523913 230.422772 l
851.515217 235.289199 l
846.506522 239.867338 l
841.497826 243.821897 l
836.48913 248.061611 l
831.480435 251.762352 l
826.471739 256.039668 l
821.463043 259.718474 l
816.454348 263.181063 l
811.445652 266.8348 l
806.436957 270.009102 l
801.428261 273.512428 l
796.419565 276.774469 l
791.41087 279.804627 l
786.402174 283.192011 l
781.393478 285.97775 l
776.384783 288.854363 l
771.376087 292.204144 l
766.367391 294.892743 l
761.358696 297.622079 l
756.35 300.43602 l
751.341304 303.256229 l
746.332609 305.662807 l
741.323913 308.483016 l
736.315217 310.949131 l
731.306522 313.515521 l
726.297826 316.016106 l
721.28913 318.479089 l
716.280435 320.929537 l
711.271739 323.36745 l
706.263043 325.821032 l
701.254348 328.096 l
696.245652 330.361568 l
691.236957 332.617735 l
686.228261 334.814364 l
681.219565 337.136336 l
676.21087 339.342366 l
671.202174 341.573464 l
666.193478 343.588347 l
661.184783 345.788109 l
656.176087 347.815526 l
651.167391 349.827275 l
646.158696 351.980034 l
641.15 353.972982 l
636.141304 355.950261 l
631.132609 357.955743 l
626.123913 359.948691 l
621.115217 361.863299 l
616.106522 363.812377 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
388.174 313.357 598.576 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
981.741 105.224 o
976.733 104.494 o
971.724 103.833 o
966.715 103.589 o
961.707 102.89 o
956.698 102.511 o
951.689 102.185 o
946.68 101.715 o
941.672 101.467 o
936.663 101.132 o
931.654 100.928 o
926.646 101.624 o
921.637 102.282 o
916.628 102.592 o
911.62 104.012 o
906.611 106.399 o
901.602 163.637 o
896.593 177.265 o
891.585 187.164 o
886.576 195.217 o
881.567 202.424 o
876.559 208.237 o
871.55 214.808 o
866.541 220.373 o
861.533 225.368 o
856.524 230.423 o
851.515 235.289 o
846.507 239.867 o
841.498 243.822 o
836.489 248.062 o
831.48 251.762 o
826.472 256.04 o
821.463 259.718 o
816.454 263.181 o
811.446 266.835 o
806.437 270.009 o
801.428 273.512 o
796.42 276.774 o
791.411 279.805 o
786.402 283.192 o
781.393 285.978 o
776.385 288.854 o
771.376 292.204 o
766.367 294.893 o
761.359 297.622 o
756.35 300.436 o
751.341 303.256 o
746.333 305.663 o
741.324 308.483 o
736.315 310.949 o
731.307 313.516 o
726.298 316.016 o
721.289 318.479 o
716.28 320.93 o
711.272 323.367 o
706.263 325.821 o
701.254 328.096 o
696.246 330.362 o
691.237 332.618 o
686.228 334.814 o
681.22 337.136 o
676.211 339.342 o
671.202 341.573 o
666.193 343.588 o
661.185 345.788 o
656.176 347.816 o
651.167 349.827 o
646.159 351.98 o
641.15 353.973 o
636.141 355.95 o
631.133 357.956 o
626.124 359.949 o
621.115 361.863 o
616.107 363.812 o
grestore
2.500 setlinewidth
0 setlinejoin
2 setlinecap
0.000 setgray
gsave
598.576087 66.60625 m
598.576087 379.962772 l
stroke
grestore
gsave
986.75 66.60625 m
986.75 379.962772 l
stroke
grestore
gsave
598.576087 66.60625 m
986.75 66.60625 l
stroke
grestore
gsave
598.576087 379.962772 m
986.75 379.962772 l
stroke
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

713.393 223.285 translate
0 rotate
0 0 m /R glyphshow
16.6748 0 m /three glyphshow
29.1748 0 m /C glyphshow
grestore
gsave
908.672 223.285 translate
0 rotate
/TimesNewRomanPSMT 25.0 selectfont
0 0.921875 moveto
/P glyphshow
13.9038 0.921875 moveto
/m glyphshow
33.3496 0.546875 moveto
/three glyphshow
45.8496 0.921875 moveto
/m glyphshow
33.349609375 20.875 12.5 1.5625 rectfill
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

598.576 385.963 translate
0 rotate
0 0 m /parenleft glyphshow
8.3252 0 m /d glyphshow
20.8252 0 m /parenright glyphshow
grestore
0.050 setlinewidth
1 setlinejoin
0 setlinecap
[0.185 0.08] 0 setdash
0.722 0.224 0.055 setrgbcolor
gsave
902.75 354.962772 m
922.75 354.962772 l
942.75 354.962772 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

0 2 m
-2 -2 l
2 -2 l
cl

gsave
0.722 0.224 0.055 setrgbcolor
fill
grestore
stroke
grestore
} bind def
922.75 354.963 o
grestore
0.000 setgray
gsave
958.75 347.963 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.796875 moveto
/a glyphshow
grestore
0.050 setlinewidth
1 setlinejoin
[0.185 0.08] 0 setdash
1.000 0.271 0.000 setrgbcolor
gsave
902.75 326.681522 m
922.75 326.681522 l
942.75 326.681522 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-4 -4 m
4 -4 l
4 4 l
-4 4 l
cl

gsave
grestore
stroke
grestore
} bind def
922.75 326.682 o
grestore
0.000 setgray
gsave
958.75 319.682 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.109375 moveto
/b glyphshow
grestore
0.050 setlinewidth
1 setlinejoin
[0.185 0.08] 0 setdash
1.000 0.514 0.000 setrgbcolor
gsave
902.75 298.400272 m
922.75 298.400272 l
942.75 298.400272 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
922.75 298.4 o
grestore
0.000 setgray
gsave
958.75 291.4 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.796875 moveto
/c glyphshow
grestore

end
showpage
