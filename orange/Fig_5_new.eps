%!PS-Adobe-3.0 EPSF-3.0
%%Title: Fig_5_new.eps
%%Creator: Matplotlib v3.7.1, https://matplotlib.org/
%%CreationDate: Mon Dec 23 15:05:21 2024
%%Orientation: portrait
%%BoundingBox: -363 7 976 785
%%HiResBoundingBox: -363.500000 7.976875 975.500000 784.023125
%%EndComments
%%BeginProlog
/mpldict 11 dict def
mpldict begin
/_d { bind def } bind def
/m { moveto } _d
/l { lineto } _d
/r { rlineto } _d
/c { curveto } _d
/cl { closepath } _d
/ce { closepath eofill } _d
/box {
      m
      1 index 0 r
      0 exch r
      neg 0 r
      cl
    } _d
/clipbox {
      box
      clip
      newpath
    } _d
/sc { setcachedevice } _d
%!PS-TrueTypeFont-1.0-2.53740
%%Title: unknown
%%Creator: Converted from TrueType to type 42 by PPR
15 dict begin
/FontName /TimesNewRomanPSMT def
/PaintType 0 def
/FontMatrix[1 0 0 1 0 0]def
/FontBBox[-568 -307 2028 1007]def
/FontType 42 def
/Encoding StandardEncoding def
/FontInfo 10 dict dup begin
/FamilyName (unknown) def
/FullName (unknown) def
/Weight (unknown) def
/Version (unknown) def
/ItalicAngle 0.0 def
/isFixedPitch false def
/UnderlinePosition -223 def
/UnderlineThickness 100 def
end readonly def
/sfnts[<00010000000900800003001063767420F34DDA810000009C000007C46670676D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>]def
/CharStrings 48 dict dup begin
/.notdef 0 def
/minus 48 def
/space 3 def
/parenleft 4 def
/parenright 5 def
/hyphen 6 def
/period 7 def
/slash 8 def
/zero 9 def
/one 10 def
/two 11 def
/three 12 def
/four 13 def
/five 14 def
/six 15 def
/seven 16 def
/eight 17 def
/nine 18 def
/mu 49 def
/B 19 def
/C 20 def
/G 21 def
/H 22 def
/K 23 def
/P 24 def
/R 25 def
/S 26 def
/T 27 def
/U 28 def
/a 29 def
/b 30 def
/c 31 def
/d 32 def
/e 33 def
/f 34 def
/i 35 def
/l 36 def
/m 37 def
/n 38 def
/o 39 def
/p 40 def
/r 41 def
/s 42 def
/t 43 def
/u 44 def
/x 45 def
/y 46 def
/z 47 def
end readonly def

systemdict/resourcestatus known
 {42 /FontType resourcestatus
   {pop pop false}{true}ifelse}
 {true}ifelse
{/TrueDict where{pop}{(%%[ Error: no TrueType rasterizer ]%%)= flush}ifelse
/FontType 3 def
 /TrueState 271 string def
 TrueDict begin sfnts save
 72 0 matrix defaultmatrix dtransform dup
 mul exch dup mul add sqrt cvi 0 72 matrix
 defaultmatrix dtransform dup mul exch dup
 mul add sqrt cvi 3 -1 roll restore
 TrueState initer end
 /BuildGlyph{exch begin
  CharStrings dup 2 index known
    {exch}{exch pop /.notdef}ifelse
  get dup xcheck
    {currentdict systemdict begin begin exec end end}
    {TrueDict begin /bander load cvlit exch TrueState render end}
    ifelse
 end}bind def
 /BuildChar{
  1 index /Encoding get exch get
  1 index /BuildGlyph get exec
 }bind def
}if

FontName currentdict end definefont pop
end
%%EndProlog
mpldict begin
-363.5 7.977 translate
1339 776.046 0 0 clipbox
gsave
0 -0 m
1339 -0 l
1339 776.04625 l
0 776.04625 l
cl
1.000 setgray
fill
grestore
gsave
79.2 454.68625 m
393.969231 454.68625 l
393.969231 731.88625 l
79.2 731.88625 l
cl
1.000 setgray
fill
grestore
2.500 setlinewidth
1 setlinejoin
0 setlinecap
[] 0 setdash
0.000 setgray
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
96.6872 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
96.6872 731.886 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

90.4372 427.327 translate
0 rotate
0 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
166.636 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
166.636 731.886 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

147.886 427.327 translate
0 rotate
0 0 m /four glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
236.585 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
236.585 731.886 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

217.835 427.327 translate
0 rotate
0 0 m /eight glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
306.533 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
306.533 731.886 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

281.533 427.327 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /two glyphshow
25 0 m /zero glyphshow
37.5 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
376.482 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
376.482 731.886 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

351.482 427.327 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /six glyphshow
25 0 m /zero glyphshow
37.5 0 m /zero glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
114.174 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
114.174 731.886 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
131.662 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
131.662 731.886 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
149.149 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
149.149 731.886 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
184.123 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
184.123 731.886 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
201.61 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
201.61 731.886 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
219.097 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
219.097 731.886 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
254.072 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
254.072 731.886 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
271.559 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
271.559 731.886 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
289.046 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
289.046 731.886 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
324.021 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
324.021 731.886 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
341.508 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
341.508 731.886 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
358.995 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
358.995 731.886 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 731.886 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

153.124 400.624 translate
0 rotate
0 0 m /T glyphshow
13.521 0 m /e glyphshow
24.6172 0 m /m glyphshow
44.063 0 m /p glyphshow
56.563 0 m /e glyphshow
67.6592 0 m /r glyphshow
75.9844 0 m /a glyphshow
87.0806 0 m /t glyphshow
94.0264 0 m /u glyphshow
106.526 0 m /r glyphshow
114.852 0 m /e glyphshow
125.948 0 m /space glyphshow
132.198 0 m /parenleft glyphshow
140.523 0 m /K glyphshow
158.577 0 m /parenright glyphshow
grestore
2.500 setlinewidth
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 454.686 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

56.7 446.007 translate
0 rotate
0 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 500.886 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 500.886 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

44.2 492.207 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /five glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 547.086 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 547.086 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

44.2 538.407 translate
0 rotate
0 0 m /three glyphshow
12.5 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 593.286 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 593.286 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

44.2 584.607 translate
0 rotate
0 0 m /four glyphshow
12.5 0 m /five glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 639.486 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 639.486 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

44.2 630.807 translate
0 rotate
0 0 m /six glyphshow
12.5 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 685.686 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 685.686 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

44.2 677.007 translate
0 rotate
0 0 m /seven glyphshow
12.5 0 m /five glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 731.886 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 731.886 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

44.2 723.207 translate
0 rotate
0 0 m /nine glyphshow
12.5 0 m /zero glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 466.236 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 466.236 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 477.786 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 477.786 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 489.336 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 489.336 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 512.436 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 512.436 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 523.986 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 523.986 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 535.536 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 535.536 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 558.636 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 558.636 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 570.186 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 570.186 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 581.736 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 581.736 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 604.836 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 604.836 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 616.386 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 616.386 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 627.936 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 627.936 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 651.036 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 651.036 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 662.586 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 662.586 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 674.136 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 674.136 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 697.236 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 697.236 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 708.786 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 708.786 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 720.336 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 720.336 o
grestore
gsave
34.2 482.286 translate
90 rotate
/TimesNewRomanPSMT 25.0 selectfont
0 0.148438 moveto
/P glyphshow
13.9038 0.148438 moveto
/o glyphshow
26.4038 0.148438 moveto
/l glyphshow
33.3496 0.148438 moveto
/a glyphshow
44.4458 0.148438 moveto
/r glyphshow
52.771 0.148438 moveto
/i glyphshow
59.7168 0.148438 moveto
/z glyphshow
70.813 0.148438 moveto
/a glyphshow
81.9092 0.148438 moveto
/t glyphshow
88.855 0.148438 moveto
/i glyphshow
95.8008 0.148438 moveto
/o glyphshow
108.301 0.148438 moveto
/n glyphshow
120.801 0.148438 moveto
/space glyphshow
127.051 0.148438 moveto
/parenleft glyphshow
135.376 0.148438 moveto
/mu glyphshow
148.779 0.148438 moveto
/C glyphshow
165.454 0.148438 moveto
/slash glyphshow
172.4 0.148438 moveto
/c glyphshow
183.496 0.148438 moveto
/m glyphshow
/TimesNewRomanPSMT 17.5 selectfont
203.318 15.1766 moveto
/two glyphshow
/TimesNewRomanPSMT 25.0 selectfont
213.141 0.148438 moveto
/parenright glyphshow
grestore
2 setlinecap
0.749 0.212 0.047 setrgbcolor
gsave
314.769 277.2 79.2 454.686 clipbox
390.471795 455.936428 m
386.974359 457.554356 l
383.476923 457.676706 l
379.979487 457.871905 l
376.482051 459.680908 l
372.984615 461.483016 l
369.487179 460.187099 l
365.989744 457.07856 l
362.492308 457.967927 l
358.994872 463.649153 l
355.497436 460.509825 l
352 461.170778 l
348.502564 466.926794 l
345.005128 455.696838 l
341.507692 455.219662 l
338.010256 456.13675 l
334.512821 456.391354 l
331.015385 457.82904 l
327.517949 459.156894 l
324.020513 505.788844 l
320.523077 632.885651 l
317.025641 638.299402 l
313.528205 642.557576 l
310.030769 646.408441 l
306.533333 649.362135 l
303.035897 652.471156 l
299.538462 654.280747 l
296.041026 656.507458 l
292.54359 658.762573 l
289.046154 660.673788 l
285.548718 662.555149 l
282.051282 664.457945 l
278.553846 666.181222 l
275.05641 667.550936 l
271.558974 669.020513 l
268.061538 670.468291 l
264.564103 671.90871 l
261.066667 673.288458 l
257.569231 674.616445 l
254.071795 675.970366 l
250.574359 677.050488 l
247.076923 678.192533 l
243.579487 679.331036 l
240.082051 680.473773 l
236.584615 681.497637 l
233.087179 682.64271 l
229.589744 683.670426 l
226.092308 684.342707 l
222.594872 685.599895 l
219.097436 686.366735 l
215.6 687.471348 l
212.102564 688.306804 l
208.605128 689.192625 l
205.107692 690.006711 l
201.610256 690.943604 l
198.112821 691.722868 l
194.615385 692.492242 l
191.117949 693.388691 l
187.620513 694.080302 l
184.123077 694.823932 l
180.625641 695.603935 l
177.128205 696.281742 l
173.630769 697.07377 l
170.133333 697.81607 l
166.635897 698.434401 l
163.138462 699.135622 l
159.641026 699.872713 l
156.14359 700.52686 l
152.646154 701.186217 l
149.148718 701.95166 l
145.651282 702.465171 l
142.153846 703.060579 l
138.65641 703.687046 l
135.158974 704.345974 l
131.661538 704.937265 l
128.164103 705.477543 l
124.666667 706.052239 l
121.169231 706.732932 l
117.671795 707.309286 l
114.174359 707.863077 l
110.676923 708.425023 l
107.179487 708.955668 l
103.682051 709.513294 l
100.184615 710.055623 l
stroke
grestore
0.937 0.424 0.000 setrgbcolor
gsave
314.769 277.2 79.2 454.686 clipbox
390.471795 456.312002 m
386.974359 455.573037 l
383.476923 458.036929 l
379.979487 459.164911 l
376.482051 457.113263 l
372.984615 457.186483 l
369.487179 455.796762 l
365.989744 458.274993 l
362.492308 459.244741 l
358.994872 458.510375 l
355.497436 457.75294 l
352 462.250024 l
348.502564 459.547613 l
345.005128 461.066678 l
341.507692 469.910928 l
338.010256 455.215349 l
334.512821 455.643756 l
331.015385 455.368178 l
327.517949 458.118215 l
324.020513 456.48775 l
320.523077 461.087851 l
317.025641 463.321959 l
313.528205 630.530477 l
310.030769 636.393558 l
306.533333 640.937314 l
303.035897 644.946349 l
299.538462 647.881478 l
296.041026 650.783548 l
292.54359 653.409991 l
289.046154 655.336079 l
285.548718 657.670114 l
282.051282 659.908349 l
278.553846 661.593421 l
275.05641 663.387378 l
271.558974 665.547809 l
268.061538 666.677313 l
264.564103 668.360282 l
261.066667 669.804153 l
257.569231 671.085873 l
254.071795 672.726629 l
250.574359 673.877835 l
247.076923 675.129907 l
243.579487 676.298314 l
240.082051 677.638424 l
236.584615 678.62658 l
233.087179 679.809438 l
229.589744 680.969893 l
226.092308 681.870449 l
222.594872 682.917558 l
219.097436 684.073419 l
215.6 684.963779 l
212.102564 685.889851 l
208.605128 686.957165 l
205.107692 687.742617 l
201.610256 688.557667 l
198.112821 689.682061 l
194.615385 690.337259 l
191.117949 691.223797 l
187.620513 692.168306 l
184.123077 692.804163 l
180.625641 693.554045 l
177.128205 694.392402 l
173.630769 695.168306 l
170.133333 695.88404 l
166.635897 696.615718 l
163.138462 697.43023 l
159.641026 698.114314 l
156.14359 698.79855 l
152.646154 699.416927 l
149.148718 700.194592 l
145.651282 700.803914 l
142.153846 701.42329 l
138.65641 702.190064 l
135.158974 702.780014 l
131.661538 703.372836 l
128.164103 703.938964 l
124.666667 704.577079 l
121.169231 705.216722 l
117.671795 705.819762 l
114.174359 706.387176 l
110.676923 706.992636 l
107.179487 707.545814 l
103.682051 708.135708 l
100.184615 708.666468 l
stroke
grestore
1.000 0.702 0.000 setrgbcolor
gsave
314.769 277.2 79.2 454.686 clipbox
390.471795 455.363491 m
386.974359 459.637552 l
383.476923 457.040945 l
379.979487 457.111415 l
376.482051 456.767032 l
372.984615 459.003316 l
369.487179 458.842191 l
365.989744 457.309449 l
362.492308 457.678635 l
358.994872 459.641282 l
355.497436 459.279796 l
352 458.684494 l
348.502564 460.461097 l
345.005128 458.898675 l
341.507692 463.072808 l
338.010256 468.536765 l
334.512821 460.8815 l
331.015385 454.966059 l
327.517949 456.320968 l
324.020513 456.234046 l
320.523077 456.452554 l
317.025641 456.179391 l
313.528205 459.92368 l
310.030769 458.924556 l
306.533333 628.202955 l
303.035897 634.686388 l
299.538462 639.448649 l
296.041026 643.655305 l
292.54359 646.446922 l
289.046154 649.700201 l
285.548718 652.525754 l
282.051282 654.75966 l
278.553846 656.510348 l
275.05641 658.593418 l
271.558974 660.94547 l
268.061538 662.428546 l
264.564103 664.348257 l
261.066667 665.942506 l
257.569231 667.700208 l
254.071795 668.871261 l
250.574359 670.377203 l
247.076923 672.019208 l
243.579487 673.256635 l
240.082051 674.420511 l
236.584615 675.689475 l
233.087179 676.947312 l
229.589744 678.04123 l
226.092308 679.304055 l
222.594872 680.443476 l
219.097436 681.390836 l
215.6 682.496044 l
212.102564 683.431442 l
208.605128 684.480265 l
205.107692 685.368797 l
201.610256 686.275152 l
198.112821 687.224505 l
194.615385 688.241336 l
191.117949 688.966671 l
187.620513 689.865488 l
184.123077 690.732234 l
180.625641 691.524743 l
177.128205 692.387954 l
173.630769 693.185034 l
170.133333 693.968194 l
166.635897 694.819192 l
163.138462 695.5614 l
159.641026 696.222324 l
156.14359 696.97766 l
152.646154 697.580991 l
149.148718 698.34416 l
145.651282 699.093006 l
142.153846 699.783604 l
138.65641 700.405237 l
135.158974 701.091292 l
131.661538 701.804232 l
128.164103 702.400298 l
124.666667 703.013535 l
121.169231 703.665129 l
117.671795 704.274813 l
114.174359 704.882541 l
110.676923 705.506106 l
107.179487 706.091063 l
103.682051 706.680591 l
100.184615 707.223769 l
stroke
grestore
0.031 0.659 0.255 setrgbcolor
gsave
314.769 277.2 79.2 454.686 clipbox
390.471795 455.537234 m
386.974359 456.280056 l
383.476923 455.209068 l
379.979487 457.46574 l
376.482051 456.115351 l
372.984615 457.462559 l
369.487179 457.461539 l
365.989744 455.482909 l
362.492308 458.049515 l
358.994872 455.915952 l
355.497436 458.065441 l
352 456.181224 l
348.502564 457.491248 l
345.005128 458.552744 l
341.507692 456.73699 l
338.010256 457.706876 l
334.512821 459.92271 l
331.015385 461.917507 l
327.517949 459.929165 l
324.020513 467.288408 l
320.523077 470.275694 l
317.025641 455.558747 l
313.528205 455.469621 l
310.030769 455.573755 l
306.533333 455.939352 l
303.035897 457.022394 l
299.538462 460.606714 l
296.041026 468.276655 l
292.54359 624.149489 l
289.046154 631.693787 l
285.548718 636.86163 l
282.051282 640.704818 l
278.553846 644.866561 l
275.05641 647.680584 l
271.558974 650.284455 l
268.061538 652.809485 l
264.564103 655.247459 l
261.066667 657.285786 l
257.569231 659.398667 l
254.071795 660.942198 l
250.574359 663.082105 l
247.076923 664.588515 l
243.579487 666.078645 l
240.082051 667.457563 l
236.584615 669.251156 l
233.087179 670.482433 l
229.589744 671.852978 l
226.092308 673.253207 l
222.594872 674.400655 l
219.097436 675.597169 l
215.6 676.919725 l
212.102564 678.030321 l
208.605128 679.138028 l
205.107692 680.318648 l
201.610256 681.36624 l
198.112821 682.431815 l
194.615385 683.222818 l
191.117949 684.414954 l
187.620513 685.466421 l
184.123077 686.329224 l
180.625641 687.14836 l
177.128205 688.087192 l
173.630769 688.956418 l
170.133333 689.787006 l
166.635897 690.617467 l
163.138462 691.517785 l
159.641026 692.297032 l
156.14359 693.043392 l
152.646154 693.816656 l
149.148718 694.556695 l
145.651282 695.396283 l
142.153846 696.234662 l
138.65641 696.866323 l
135.158974 697.668903 l
131.661538 698.276802 l
128.164103 699.007484 l
124.666667 699.723919 l
121.169231 700.355429 l
117.671795 701.059389 l
114.174359 701.666143 l
110.676923 702.363506 l
107.179487 702.976171 l
103.682051 703.61195 l
100.184615 704.237349 l
stroke
grestore
0.031 0.416 0.416 setrgbcolor
gsave
314.769 277.2 79.2 454.686 clipbox
390.471795 455.801222 m
386.974359 456.786987 l
383.476923 456.281898 l
379.979487 458.363299 l
376.482051 456.311477 l
372.984615 457.512955 l
369.487179 455.687811 l
365.989744 456.19454 l
362.492308 457.746911 l
358.994872 457.121232 l
355.497436 458.336214 l
352 457.377423 l
348.502564 456.695078 l
345.005128 458.261541 l
341.507692 461.61228 l
338.010256 457.839167 l
334.512821 460.213356 l
331.015385 462.80987 l
327.517949 456.607173 l
324.020513 460.076757 l
320.523077 456.687537 l
317.025641 457.471876 l
313.528205 462.306612 l
310.030769 455.881701 l
306.533333 455.956135 l
303.035897 456.966018 l
299.538462 459.544122 l
296.041026 456.217807 l
292.54359 464.204109 l
289.046154 456.69382 l
285.548718 622.474062 l
282.051282 630.483334 l
278.553846 635.858834 l
275.05641 640.041822 l
271.558974 643.390358 l
268.061538 646.797865 l
264.564103 649.513823 l
261.066667 651.965037 l
257.569231 654.272201 l
254.071795 656.43652 l
250.574359 658.632046 l
247.076923 660.343489 l
243.579487 662.058534 l
240.082051 663.677619 l
236.584615 665.444399 l
233.087179 667.086952 l
229.589744 668.278293 l
226.092308 669.966144 l
222.594872 671.341334 l
219.097436 672.821237 l
215.6 673.781138 l
212.102564 675.065827 l
208.605128 676.385308 l
205.107692 677.412538 l
201.610256 678.702395 l
198.112821 679.681228 l
194.615385 680.700691 l
191.117949 681.839003 l
187.620513 682.726419 l
184.123077 683.875106 l
180.625641 684.775115 l
177.128205 685.803294 l
173.630769 686.6807 l
170.133333 687.464845 l
166.635897 688.441126 l
163.138462 689.310948 l
159.641026 690.254007 l
156.14359 691.128577 l
152.646154 691.841854 l
149.148718 692.629747 l
145.651282 693.508831 l
142.153846 694.230784 l
138.65641 694.965732 l
135.158974 695.734673 l
131.661538 696.488464 l
128.164103 697.286049 l
124.666667 697.937821 l
121.169231 698.642949 l
117.671795 699.314207 l
114.174359 700.017148 l
110.676923 700.704232 l
107.179487 701.384114 l
103.682051 702.018732 l
100.184615 702.652784 l
stroke
grestore
0.031 0.659 0.659 setrgbcolor
gsave
314.769 277.2 79.2 454.686 clipbox
390.471795 456.391152 m
386.974359 455.68403 l
383.476923 456.888072 l
379.979487 456.823211 l
376.482051 456.305306 l
372.984615 456.28067 l
369.487179 455.495612 l
365.989744 456.130402 l
362.492308 456.208061 l
358.994872 457.731889 l
355.497436 456.003378 l
352 457.474483 l
348.502564 456.401573 l
345.005128 458.714017 l
341.507692 458.403853 l
338.010256 457.150158 l
334.512821 459.658357 l
331.015385 457.454378 l
327.517949 456.745175 l
324.020513 458.296337 l
320.523077 456.244408 l
317.025641 459.841199 l
313.528205 462.94141 l
310.030769 461.089138 l
306.533333 460.14403 l
303.035897 455.463267 l
299.538462 455.57799 l
296.041026 456.369889 l
292.54359 456.149126 l
289.046154 460.631954 l
285.548718 470.065004 l
282.051282 466.775773 l
278.553846 621.273235 l
275.05641 629.480637 l
271.558974 634.977373 l
268.061538 638.907241 l
264.564103 642.808006 l
261.066667 645.856266 l
257.569231 648.993612 l
254.071795 651.271792 l
250.574359 653.686777 l
247.076923 655.65057 l
243.579487 657.50183 l
240.082051 659.763177 l
236.584615 661.456561 l
233.087179 663.130908 l
229.589744 664.600681 l
226.092308 666.351928 l
222.594872 668.13932 l
219.097436 669.342064 l
215.6 670.727116 l
212.102564 672.213332 l
208.605128 673.406199 l
205.107692 674.644383 l
201.610256 675.641757 l
198.112821 676.978624 l
194.615385 678.054446 l
191.117949 679.240258 l
187.620513 680.389537 l
184.123077 681.40874 l
180.625641 682.240522 l
177.128205 683.40704 l
173.630769 684.393525 l
170.133333 685.189565 l
166.635897 686.232041 l
163.138462 687.143459 l
159.641026 688.056928 l
156.14359 688.863456 l
152.646154 689.820078 l
149.148718 690.614148 l
145.651282 691.503647 l
142.153846 692.351681 l
138.65641 693.083727 l
135.158974 693.8717 l
131.661538 694.579874 l
128.164103 695.342131 l
124.666667 696.118778 l
121.169231 696.821092 l
117.671795 697.604438 l
114.174359 698.30421 l
110.676923 698.996344 l
107.179487 699.667047 l
103.682051 700.347878 l
100.184615 701.044789 l
stroke
grestore
2.500 setlinewidth
0 setlinejoin
0.000 setgray
gsave
79.2 454.68625 m
79.2 731.88625 l
stroke
grestore
gsave
393.969231 454.68625 m
393.969231 731.88625 l
stroke
grestore
gsave
79.2 454.68625 m
393.969231 454.68625 l
stroke
grestore
gsave
79.2 731.88625 m
393.969231 731.88625 l
stroke
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

131.662 751.487 translate
0 rotate
0 0 m /parenleft glyphshow
8.3252 0 m /a glyphshow
19.4214 0 m /parenright glyphshow
27.7466 0 m /space glyphshow
33.9966 0 m /H glyphshow
52.0508 0 m /y glyphshow
64.5508 0 m /d glyphshow
77.0508 0 m /r glyphshow
85.376 0 m /o glyphshow
97.876 0 m /s glyphshow
107.605 0 m /t glyphshow
114.551 0 m /a glyphshow
125.647 0 m /t glyphshow
132.593 0 m /i glyphshow
139.539 0 m /c glyphshow
150.635 0 m /space glyphshow
156.885 0 m /P glyphshow
170.789 0 m /r glyphshow
179.114 0 m /e glyphshow
190.21 0 m /s glyphshow
199.939 0 m /s glyphshow
209.668 0 m /u glyphshow
222.168 0 m /r glyphshow
230.493 0 m /e glyphshow
grestore
1.500 setlinewidth
1 setlinejoin
0.749 0.212 0.047 setrgbcolor
gsave
101.7 654.13875 m
126.7 654.13875 l
151.7 654.13875 l
stroke
grestore
0.000 setgray
/TimesNewRomanPSMT 25.000 selectfont
gsave

171.7 645.389 translate
0 rotate
0 0 m /space glyphshow
6.25 0 m /hyphen glyphshow
14.5752 0 m /three glyphshow
27.0752 0 m /G glyphshow
45.1294 0 m /P glyphshow
59.0332 0 m /a glyphshow
grestore
0.937 0.424 0.000 setrgbcolor
gsave
101.7 618.795 m
126.7 618.795 l
151.7 618.795 l
stroke
grestore
0.000 setgray
/TimesNewRomanPSMT 25.000 selectfont
gsave

171.7 610.045 translate
0 rotate
0 0 m /space glyphshow
6.25 0 m /hyphen glyphshow
14.5752 0 m /two glyphshow
27.0752 0 m /G glyphshow
45.1294 0 m /P glyphshow
59.0332 0 m /a glyphshow
grestore
1.000 0.702 0.000 setrgbcolor
gsave
101.7 583.45125 m
126.7 583.45125 l
151.7 583.45125 l
stroke
grestore
0.000 setgray
/TimesNewRomanPSMT 25.000 selectfont
gsave

171.7 574.701 translate
0 rotate
0 0 m /space glyphshow
6.25 0 m /hyphen glyphshow
14.5752 0 m /one glyphshow
27.0752 0 m /G glyphshow
45.1294 0 m /P glyphshow
59.0332 0 m /a glyphshow
grestore
0.031 0.659 0.255 setrgbcolor
gsave
101.7 548.1075 m
126.7 548.1075 l
151.7 548.1075 l
stroke
grestore
0.000 setgray
/TimesNewRomanPSMT 25.000 selectfont
gsave

171.7 539.357 translate
0 rotate
0 0 m /space glyphshow
6.25 0 m /one glyphshow
18.75 0 m /G glyphshow
36.8042 0 m /P glyphshow
50.708 0 m /a glyphshow
grestore
0.031 0.416 0.416 setrgbcolor
gsave
101.7 512.76375 m
126.7 512.76375 l
151.7 512.76375 l
stroke
grestore
0.000 setgray
/TimesNewRomanPSMT 25.000 selectfont
gsave

171.7 504.014 translate
0 rotate
0 0 m /space glyphshow
6.25 0 m /two glyphshow
18.75 0 m /G glyphshow
36.8042 0 m /P glyphshow
50.708 0 m /a glyphshow
grestore
0.031 0.659 0.659 setrgbcolor
gsave
101.7 477.42 m
126.7 477.42 l
151.7 477.42 l
stroke
grestore
0.000 setgray
/TimesNewRomanPSMT 25.000 selectfont
gsave

171.7 468.67 translate
0 rotate
0 0 m /space glyphshow
6.25 0 m /three glyphshow
18.75 0 m /G glyphshow
36.8042 0 m /P glyphshow
50.708 0 m /a glyphshow
grestore
gsave
535.615385 454.68625 m
850.384615 454.68625 l
850.384615 731.88625 l
535.615385 731.88625 l
cl
1.000 setgray
fill
grestore
2.500 setlinewidth
0 setlinecap
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
553.103 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
553.103 731.886 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

546.853 427.327 translate
0 rotate
0 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
623.051 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
623.051 731.886 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

604.301 427.327 translate
0 rotate
0 0 m /four glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
693 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
693 731.886 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

674.25 427.327 translate
0 rotate
0 0 m /eight glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
762.949 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
762.949 731.886 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

737.949 427.327 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /two glyphshow
25 0 m /zero glyphshow
37.5 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
832.897 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
832.897 731.886 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

807.897 427.327 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /six glyphshow
25 0 m /zero glyphshow
37.5 0 m /zero glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
570.59 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
570.59 731.886 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
588.077 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
588.077 731.886 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
605.564 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
605.564 731.886 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
640.538 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
640.538 731.886 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
658.026 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
658.026 731.886 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
675.513 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
675.513 731.886 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
710.487 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
710.487 731.886 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
727.974 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
727.974 731.886 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
745.462 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
745.462 731.886 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
780.436 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
780.436 731.886 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
797.923 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
797.923 731.886 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
815.41 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
815.41 731.886 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.385 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.385 731.886 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

609.539 400.624 translate
0 rotate
0 0 m /T glyphshow
13.521 0 m /e glyphshow
24.6172 0 m /m glyphshow
44.063 0 m /p glyphshow
56.563 0 m /e glyphshow
67.6592 0 m /r glyphshow
75.9844 0 m /a glyphshow
87.0806 0 m /t glyphshow
94.0264 0 m /u glyphshow
106.526 0 m /r glyphshow
114.852 0 m /e glyphshow
125.948 0 m /space glyphshow
132.198 0 m /parenleft glyphshow
140.523 0 m /K glyphshow
158.577 0 m /parenright glyphshow
grestore
2.500 setlinewidth
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
535.615 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.385 454.686 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

513.115 446.007 translate
0 rotate
0 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
535.615 500.886 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.385 500.886 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

500.615 492.207 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /five glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
535.615 547.086 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.385 547.086 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

500.615 538.407 translate
0 rotate
0 0 m /three glyphshow
12.5 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
535.615 593.286 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.385 593.286 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

500.615 584.607 translate
0 rotate
0 0 m /four glyphshow
12.5 0 m /five glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
535.615 639.486 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.385 639.486 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

500.615 630.807 translate
0 rotate
0 0 m /six glyphshow
12.5 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
535.615 685.686 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.385 685.686 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

500.615 677.007 translate
0 rotate
0 0 m /seven glyphshow
12.5 0 m /five glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
535.615 731.886 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.385 731.886 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

500.615 723.207 translate
0 rotate
0 0 m /nine glyphshow
12.5 0 m /zero glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
535.615 466.236 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.385 466.236 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
535.615 477.786 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.385 477.786 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
535.615 489.336 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.385 489.336 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
535.615 512.436 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.385 512.436 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
535.615 523.986 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.385 523.986 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
535.615 535.536 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.385 535.536 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
535.615 558.636 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.385 558.636 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
535.615 570.186 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.385 570.186 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
535.615 581.736 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.385 581.736 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
535.615 604.836 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.385 604.836 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
535.615 616.386 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.385 616.386 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
535.615 627.936 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.385 627.936 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
535.615 651.036 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.385 651.036 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
535.615 662.586 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.385 662.586 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
535.615 674.136 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.385 674.136 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
535.615 697.236 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.385 697.236 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
535.615 708.786 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.385 708.786 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
535.615 720.336 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.385 720.336 o
grestore
gsave
490.615 482.286 translate
90 rotate
/TimesNewRomanPSMT 25.0 selectfont
0 0.148438 moveto
/P glyphshow
13.9038 0.148438 moveto
/o glyphshow
26.4038 0.148438 moveto
/l glyphshow
33.3496 0.148438 moveto
/a glyphshow
44.4458 0.148438 moveto
/r glyphshow
52.771 0.148438 moveto
/i glyphshow
59.7168 0.148438 moveto
/z glyphshow
70.813 0.148438 moveto
/a glyphshow
81.9092 0.148438 moveto
/t glyphshow
88.855 0.148438 moveto
/i glyphshow
95.8008 0.148438 moveto
/o glyphshow
108.301 0.148438 moveto
/n glyphshow
120.801 0.148438 moveto
/space glyphshow
127.051 0.148438 moveto
/parenleft glyphshow
135.376 0.148438 moveto
/mu glyphshow
148.779 0.148438 moveto
/C glyphshow
165.454 0.148438 moveto
/slash glyphshow
172.4 0.148438 moveto
/c glyphshow
183.496 0.148438 moveto
/m glyphshow
/TimesNewRomanPSMT 17.5 selectfont
203.318 15.1766 moveto
/two glyphshow
/TimesNewRomanPSMT 25.0 selectfont
213.141 0.148438 moveto
/parenright glyphshow
grestore
2 setlinecap
0.749 0.212 0.047 setrgbcolor
gsave
314.769 277.2 535.615 454.686 clipbox
899.348718 455.587882 m
895.851282 456.182463 l
892.353846 455.863615 l
888.85641 455.262855 l
885.358974 455.414135 l
881.861538 455.93052 l
878.364103 455.660586 l
874.866667 455.624473 l
871.369231 456.683478 l
867.871795 456.310694 l
864.374359 455.570248 l
860.876923 455.951543 l
857.379487 457.106917 l
853.882051 455.865094 l
850.384615 455.743523 l
846.887179 456.945509 l
843.389744 455.973668 l
839.892308 456.471973 l
836.394872 457.645091 l
832.897436 455.823319 l
829.4 456.068737 l
825.902564 455.753065 l
822.405128 457.219863 l
818.907692 458.186407 l
815.410256 457.984368 l
811.912821 458.667532 l
808.415385 458.997101 l
804.917949 461.60433 l
801.420513 458.742992 l
797.923077 457.896399 l
794.425641 457.153987 l
790.928205 458.851103 l
787.430769 482.657409 l
783.933333 470.747249 l
780.435897 458.225656 l
776.938462 458.073763 l
773.441026 471.552506 l
769.94359 460.11494 l
766.446154 461.064455 l
762.948718 469.668925 l
759.451282 474.314201 l
755.953846 483.21017 l
752.45641 545.13824 l
748.958974 576.90637 l
745.461538 612.456354 l
741.964103 617.97086 l
738.466667 638.344791 l
734.969231 641.67484 l
731.471795 644.673685 l
727.974359 648.047156 l
724.476923 650.303611 l
720.979487 652.879228 l
717.482051 655.035738 l
713.984615 657.036848 l
710.487179 658.955603 l
706.989744 660.919331 l
703.492308 662.477271 l
699.994872 664.229485 l
696.497436 665.942073 l
693 667.42099 l
689.502564 668.97478 l
686.005128 670.307474 l
682.507692 671.661515 l
679.010256 673.012047 l
675.512821 674.292901 l
672.015385 675.260758 l
668.517949 676.750513 l
665.020513 677.69941 l
661.523077 679.029682 l
658.025641 679.982912 l
654.528205 680.995214 l
651.030769 682.081345 l
647.533333 683.045097 l
644.035897 684.145804 l
640.538462 685.062933 l
637.041026 685.889076 l
633.54359 686.932842 l
630.046154 687.770647 l
626.548718 688.759455 l
623.051282 689.623268 l
619.553846 690.436683 l
616.05641 691.256001 l
612.558974 692.167418 l
609.061538 692.925539 l
605.564103 693.723984 l
602.066667 694.400004 l
598.569231 695.250712 l
595.071795 695.951988 l
591.574359 696.641264 l
588.076923 697.436365 l
584.579487 698.150525 l
581.082051 698.839016 l
577.584615 699.519474 l
574.087179 700.204655 l
570.589744 700.896002 l
567.092308 701.526894 l
563.594872 702.205919 l
560.097436 702.821395 l
556.6 703.472286 l
stroke
grestore
0.937 0.424 0.000 setrgbcolor
gsave
314.769 277.2 535.615 454.686 clipbox
899.348718 455.209617 m
895.851282 455.909134 l
892.353846 455.676346 l
888.85641 456.786799 l
885.358974 455.673089 l
881.861538 456.029981 l
878.364103 455.484557 l
874.866667 456.62239 l
871.369231 456.206504 l
867.871795 455.744503 l
864.374359 457.26011 l
860.876923 455.923296 l
857.379487 455.679754 l
853.882051 454.906116 l
850.384615 456.439687 l
846.887179 456.528616 l
843.389744 456.942527 l
839.892308 456.465383 l
836.394872 457.246445 l
832.897436 456.818078 l
829.4 457.334028 l
825.902564 458.423228 l
822.405128 460.512073 l
818.907692 456.025519 l
815.410256 458.747783 l
811.912821 456.057891 l
808.415385 457.224627 l
804.917949 457.102131 l
801.420513 460.575209 l
797.923077 462.280315 l
794.425641 461.697338 l
790.928205 464.521671 l
787.430769 462.631657 l
783.933333 469.56829 l
780.435897 456.601697 l
776.938462 458.547288 l
773.441026 457.432568 l
769.94359 459.343947 l
766.446154 461.371711 l
762.948718 471.529883 l
759.451282 467.711734 l
755.953846 484.672447 l
752.45641 520.514547 l
748.958974 629.039422 l
745.461538 634.527951 l
741.964103 638.938191 l
738.466667 642.433478 l
734.969231 645.767907 l
731.471795 648.658618 l
727.974359 651.372446 l
724.476923 653.690509 l
720.979487 655.945266 l
717.482051 658.165419 l
713.984615 659.767292 l
710.487179 661.860358 l
706.989744 663.299681 l
703.492308 664.950178 l
699.994872 666.552565 l
696.497436 668.13539 l
693 669.461842 l
689.502564 671.0761 l
686.005128 672.273771 l
682.507692 673.639265 l
679.010256 674.925312 l
675.512821 676.062535 l
672.015385 677.240248 l
668.517949 678.360712 l
665.020513 679.537179 l
661.523077 680.654467 l
658.025641 681.669842 l
654.528205 682.696001 l
651.030769 683.716021 l
647.533333 684.683685 l
644.035897 685.633002 l
640.538462 686.445306 l
637.041026 687.541366 l
633.54359 688.351188 l
630.046154 689.232697 l
626.548718 690.076416 l
623.051282 690.974356 l
619.553846 691.712718 l
616.05641 692.583442 l
612.558974 693.379271 l
609.061538 694.125164 l
605.564103 694.890217 l
602.066667 695.643626 l
598.569231 696.391953 l
595.071795 697.13095 l
591.574359 697.834592 l
588.076923 698.542881 l
584.579487 699.201943 l
581.082051 699.935189 l
577.584615 700.580553 l
574.087179 701.255707 l
570.589744 701.913344 l
567.092308 702.575165 l
563.594872 703.18985 l
560.097436 703.816471 l
556.6 704.442621 l
stroke
grestore
1.000 0.702 0.000 setrgbcolor
gsave
314.769 277.2 535.615 454.686 clipbox
899.348718 455.533676 m
895.851282 455.790397 l
892.353846 455.838259 l
888.85641 455.085958 l
885.358974 455.108155 l
881.861538 456.334675 l
878.364103 455.420138 l
874.866667 456.200146 l
871.369231 455.163781 l
867.871795 455.809019 l
864.374359 455.54249 l
860.876923 455.786989 l
857.379487 455.264265 l
853.882051 457.492722 l
850.384615 456.626384 l
846.887179 455.9394 l
843.389744 456.712387 l
839.892308 456.673378 l
836.394872 457.696677 l
832.897436 456.075468 l
829.4 458.365803 l
825.902564 456.989575 l
822.405128 455.95905 l
818.907692 457.938756 l
815.410256 458.163154 l
811.912821 456.341266 l
808.415385 458.912964 l
804.917949 457.680782 l
801.420513 460.386112 l
797.923077 460.284956 l
794.425641 460.864962 l
790.928205 457.443681 l
787.430769 460.168728 l
783.933333 473.741762 l
780.435897 456.277968 l
776.938462 455.336079 l
773.441026 455.910686 l
769.94359 459.204642 l
766.446154 461.680158 l
762.948718 463.713061 l
759.451282 466.094196 l
755.953846 469.341129 l
752.45641 628.054257 l
748.958974 634.294128 l
745.461538 638.878973 l
741.964103 642.509635 l
738.466667 646.074469 l
734.969231 648.975644 l
731.471795 651.490173 l
727.974359 653.697551 l
724.476923 656.049101 l
720.979487 657.956807 l
717.482051 660.366368 l
713.984615 662.02039 l
710.487179 663.670774 l
706.989744 665.292771 l
703.492308 666.794071 l
699.994872 668.422804 l
696.497436 669.856745 l
693 671.540279 l
689.502564 672.654777 l
686.005128 673.872147 l
682.507692 675.085062 l
679.010256 676.397339 l
675.512821 677.580768 l
672.015385 678.786777 l
668.517949 679.916515 l
665.020513 680.819039 l
661.523077 681.953654 l
658.025641 682.95277 l
654.528205 683.983421 l
651.030769 684.798683 l
647.533333 685.849789 l
644.035897 686.802447 l
640.538462 687.71537 l
637.041026 688.572014 l
633.54359 689.472248 l
630.046154 690.36361 l
626.548718 691.203774 l
623.051282 691.94397 l
619.553846 692.748182 l
616.05641 693.553589 l
612.558974 694.369644 l
609.061538 695.070248 l
605.564103 695.850622 l
602.066667 696.530089 l
598.569231 697.328648 l
595.071795 698.022061 l
591.574359 698.748558 l
588.076923 699.405864 l
584.579487 700.110404 l
581.082051 700.761197 l
577.584615 701.455714 l
574.087179 702.074597 l
570.589744 702.71444 l
567.092308 703.341544 l
563.594872 703.97061 l
560.097436 704.580461 l
556.6 705.206671 l
stroke
grestore
0.031 0.659 0.255 setrgbcolor
gsave
314.769 277.2 535.615 454.686 clipbox
899.348718 456.815266 m
895.851282 455.953613 l
892.353846 455.253148 l
888.85641 456.124608 l
885.358974 456.734313 l
881.861538 455.783739 l
878.364103 456.324441 l
874.866667 456.217817 l
871.369231 455.594886 l
867.871795 455.851198 l
864.374359 456.317325 l
860.876923 456.899377 l
857.379487 455.706388 l
853.882051 455.314496 l
850.384615 457.454943 l
846.887179 456.155841 l
843.389744 455.536404 l
839.892308 458.183242 l
836.394872 456.283314 l
832.897436 456.423243 l
829.4 457.654792 l
825.902564 455.58865 l
822.405128 455.630858 l
818.907692 456.759842 l
815.410256 458.244673 l
811.912821 459.838193 l
808.415385 457.820477 l
804.917949 460.719312 l
801.420513 460.675894 l
797.923077 455.105753 l
794.425641 455.334858 l
790.928205 456.079561 l
787.430769 458.167348 l
783.933333 455.384932 l
780.435897 457.972892 l
776.938462 455.491234 l
773.441026 457.79772 l
769.94359 558.542117 l
766.446154 567.704175 l
762.948718 574.305859 l
759.451282 583.362391 l
755.953846 586.556368 l
752.45641 591.697559 l
748.958974 640.74602 l
745.461538 644.542046 l
741.964103 647.228237 l
738.466667 650.470136 l
734.969231 653.09931 l
731.471795 654.885829 l
727.974359 657.171859 l
724.476923 659.358829 l
720.979487 661.13989 l
717.482051 663.197641 l
713.984615 664.769093 l
710.487179 666.47079 l
706.989744 667.737433 l
703.492308 669.433402 l
699.994872 670.59572 l
696.497436 672.149961 l
693 673.435364 l
689.502564 674.831351 l
686.005128 675.978534 l
682.507692 677.221121 l
679.010256 678.194503 l
675.512821 679.400204 l
672.015385 680.439142 l
668.517949 681.705969 l
665.020513 682.483098 l
661.523077 683.573834 l
658.025641 684.508461 l
654.528205 685.500267 l
651.030769 686.380627 l
647.533333 687.331596 l
644.035897 688.293875 l
640.538462 689.268727 l
637.041026 690.008813 l
633.54359 690.892435 l
630.046154 691.624911 l
626.548718 692.385912 l
623.051282 693.297525 l
619.553846 694.124945 l
616.05641 694.778633 l
612.558974 695.554185 l
609.061538 696.295515 l
605.564103 697.067898 l
602.066667 697.79297 l
598.569231 698.491515 l
595.071795 699.158238 l
591.574359 699.809385 l
588.076923 700.517997 l
584.579487 701.246407 l
581.082051 701.891936 l
577.584615 702.494754 l
574.087179 703.114437 l
570.589744 703.727133 l
567.092308 704.363754 l
563.594872 704.943127 l
560.097436 705.565413 l
556.6 706.185679 l
stroke
grestore
0.031 0.416 0.416 setrgbcolor
gsave
314.769 277.2 535.615 454.686 clipbox
899.348718 455.113739 m
895.851282 455.759644 l
892.353846 455.571187 l
888.85641 455.285053 l
885.358974 455.562341 l
881.861538 455.453926 l
878.364103 455.946063 l
874.866667 455.438413 l
871.369231 456.473622 l
867.871795 456.55781 l
864.374359 457.087229 l
860.876923 456.76242 l
857.379487 456.145594 l
853.882051 456.618525 l
850.384615 457.885832 l
846.887179 457.760991 l
843.389744 456.418855 l
839.892308 456.023896 l
836.394872 457.211122 l
832.897436 455.182946 l
829.4 457.539777 l
825.902564 458.251226 l
822.405128 460.249198 l
818.907692 457.632619 l
815.410256 455.402812 l
811.912821 454.932367 l
808.415385 454.87891 l
804.917949 455.531554 l
801.420513 456.258408 l
797.923077 459.047594 l
794.425641 457.433137 l
790.928205 468.860065 l
787.430769 541.964636 l
783.933333 560.101993 l
780.435897 570.771254 l
776.938462 577.563182 l
773.441026 585.71138 l
769.94359 589.740471 l
766.446154 595.905133 l
762.948718 602.029433 l
759.451282 605.971136 l
755.953846 611.879759 l
752.45641 615.651477 l
748.958974 619.007039 l
745.461538 622.946354 l
741.964103 626.568513 l
738.466667 629.85881 l
734.969231 637.32893 l
731.471795 656.183295 l
727.974359 658.762147 l
724.476923 660.296409 l
720.979487 662.189876 l
717.482051 663.77788 l
713.984615 665.556192 l
710.487179 666.955869 l
706.989744 668.679181 l
703.492308 669.993802 l
699.994872 671.501097 l
696.497436 672.671994 l
693 674.030048 l
689.502564 675.24992 l
686.005128 676.451977 l
682.507692 677.678178 l
679.010256 678.629423 l
675.512821 679.898965 l
672.015385 680.991345 l
668.517949 682.05702 l
665.020513 682.915326 l
661.523077 683.920468 l
658.025641 685.103674 l
654.528205 685.94792 l
651.030769 686.89293 l
647.533333 687.804323 l
644.035897 688.585273 l
640.538462 689.503776 l
637.041026 690.389914 l
633.54359 691.329933 l
630.046154 691.953136 l
626.548718 692.856883 l
623.051282 693.62874 l
619.553846 694.400194 l
616.05641 695.172215 l
612.558974 695.862068 l
609.061538 696.64134 l
605.564103 697.305841 l
602.066667 698.062981 l
598.569231 698.812118 l
595.071795 699.475916 l
591.574359 700.120763 l
588.076923 700.799499 l
584.579487 701.431125 l
581.082051 702.128111 l
577.584615 702.699423 l
574.087179 703.374494 l
570.589744 703.994864 l
567.092308 704.63663 l
563.594872 705.235761 l
560.097436 705.822669 l
556.6 706.424619 l
stroke
grestore
0.031 0.659 0.659 setrgbcolor
gsave
314.769 277.2 535.615 454.686 clipbox
899.348718 455.067533 m
895.851282 455.395898 l
892.353846 456.328221 l
888.85641 456.561109 l
885.358974 455.366481 l
881.861538 455.318005 l
878.364103 455.231815 l
874.866667 456.11302 l
871.369231 456.593902 l
867.871795 455.563182 l
864.374359 456.544462 l
860.876923 459.054151 l
857.379487 455.791495 l
853.882051 455.371154 l
850.384615 456.166889 l
846.887179 455.78541 l
843.389744 456.870439 l
839.892308 455.359158 l
836.394872 456.52679 l
832.897436 455.048603 l
829.4 455.019709 l
825.902564 454.869862 l
822.405128 454.960644 l
818.907692 455.231322 l
815.410256 455.55806 l
811.912821 455.162622 l
808.415385 460.068121 l
804.917949 465.205742 l
801.420513 463.10951 l
797.923077 557.657174 l
794.425641 568.98584 l
790.928205 576.555392 l
787.430769 583.980367 l
783.933333 590.385615 l
780.435897 596.245179 l
776.938462 601.934041 l
773.441026 607.413185 l
769.94359 612.117666 l
766.446154 615.56473 l
762.948718 619.696202 l
759.451282 623.233352 l
755.953846 627.405953 l
752.45641 631.262214 l
748.958974 634.682544 l
745.461538 637.367886 l
741.964103 641.044319 l
738.466667 644.169976 l
734.969231 646.825185 l
731.471795 649.200837 l
727.974359 651.777755 l
724.476923 654.732009 l
720.979487 656.7861 l
717.482051 658.948808 l
713.984615 645.533577 l
710.487179 667.753813 l
706.989744 669.182101 l
703.492308 670.417895 l
699.994872 671.843579 l
696.497436 673.089076 l
693 674.347717 l
689.502564 675.530719 l
686.005128 676.704547 l
682.507692 678.012066 l
679.010256 679.144548 l
675.512821 680.059845 l
672.015385 681.281746 l
668.517949 682.351678 l
665.020513 683.286125 l
661.523077 684.341013 l
658.025641 685.068126 l
654.528205 686.152599 l
651.030769 687.088129 l
647.533333 687.924906 l
644.035897 688.774856 l
640.538462 689.751144 l
637.041026 690.599975 l
633.54359 691.488573 l
630.046154 692.178429 l
626.548718 692.996387 l
623.051282 693.860534 l
619.553846 694.578404 l
616.05641 695.320653 l
612.558974 696.054021 l
609.061538 696.769189 l
605.564103 697.523414 l
602.066667 698.221985 l
598.569231 698.94381 l
595.071795 699.575813 l
591.574359 700.246225 l
588.076923 700.942284 l
584.579487 701.615045 l
581.082051 702.252421 l
577.584615 702.840478 l
574.087179 703.512584 l
570.589744 704.117885 l
567.092308 704.765332 l
563.594872 705.357138 l
560.097436 705.956149 l
556.6 706.536316 l
stroke
grestore
2.500 setlinewidth
0 setlinejoin
0.000 setgray
gsave
535.615385 454.68625 m
535.615385 731.88625 l
stroke
grestore
gsave
850.384615 454.68625 m
850.384615 731.88625 l
stroke
grestore
gsave
535.615385 454.68625 m
850.384615 454.68625 l
stroke
grestore
gsave
535.615385 731.88625 m
850.384615 731.88625 l
stroke
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

605.564 751.487 translate
0 rotate
0 0 m /parenleft glyphshow
8.3252 0 m /b glyphshow
20.8252 0 m /parenright glyphshow
29.1504 0 m /space glyphshow
35.4004 0 m /U glyphshow
53.4546 0 m /n glyphshow
65.9546 0 m /i glyphshow
72.9004 0 m /a glyphshow
83.9966 0 m /x glyphshow
96.4966 0 m /i glyphshow
103.442 0 m /a glyphshow
114.539 0 m /l glyphshow
121.484 0 m /space glyphshow
127.734 0 m /S glyphshow
141.638 0 m /t glyphshow
148.584 0 m /r glyphshow
156.909 0 m /e glyphshow
168.005 0 m /s glyphshow
177.734 0 m /s glyphshow
grestore
1.500 setlinewidth
1 setlinejoin
0.749 0.212 0.047 setrgbcolor
gsave
558.115385 654.13875 m
583.115385 654.13875 l
608.115385 654.13875 l
stroke
grestore
0.000 setgray
/TimesNewRomanPSMT 25.000 selectfont
gsave

628.115 645.389 translate
0 rotate
0 0 m /space glyphshow
6.25 0 m /hyphen glyphshow
14.5752 0 m /three glyphshow
27.0752 0 m /G glyphshow
45.1294 0 m /P glyphshow
59.0332 0 m /a glyphshow
grestore
0.937 0.424 0.000 setrgbcolor
gsave
558.115385 618.795 m
583.115385 618.795 l
608.115385 618.795 l
stroke
grestore
0.000 setgray
/TimesNewRomanPSMT 25.000 selectfont
gsave

628.115 610.045 translate
0 rotate
0 0 m /space glyphshow
6.25 0 m /hyphen glyphshow
14.5752 0 m /two glyphshow
27.0752 0 m /G glyphshow
45.1294 0 m /P glyphshow
59.0332 0 m /a glyphshow
grestore
1.000 0.702 0.000 setrgbcolor
gsave
558.115385 583.45125 m
583.115385 583.45125 l
608.115385 583.45125 l
stroke
grestore
0.000 setgray
/TimesNewRomanPSMT 25.000 selectfont
gsave

628.115 574.701 translate
0 rotate
0 0 m /space glyphshow
6.25 0 m /hyphen glyphshow
14.5752 0 m /one glyphshow
27.0752 0 m /G glyphshow
45.1294 0 m /P glyphshow
59.0332 0 m /a glyphshow
grestore
0.031 0.659 0.255 setrgbcolor
gsave
558.115385 548.1075 m
583.115385 548.1075 l
608.115385 548.1075 l
stroke
grestore
0.000 setgray
/TimesNewRomanPSMT 25.000 selectfont
gsave

628.115 539.357 translate
0 rotate
0 0 m /space glyphshow
6.25 0 m /one glyphshow
18.75 0 m /G glyphshow
36.8042 0 m /P glyphshow
50.708 0 m /a glyphshow
grestore
0.031 0.416 0.416 setrgbcolor
gsave
558.115385 512.76375 m
583.115385 512.76375 l
608.115385 512.76375 l
stroke
grestore
0.000 setgray
/TimesNewRomanPSMT 25.000 selectfont
gsave

628.115 504.014 translate
0 rotate
0 0 m /space glyphshow
6.25 0 m /two glyphshow
18.75 0 m /G glyphshow
36.8042 0 m /P glyphshow
50.708 0 m /a glyphshow
grestore
0.031 0.659 0.659 setrgbcolor
gsave
558.115385 477.42 m
583.115385 477.42 l
608.115385 477.42 l
stroke
grestore
0.000 setgray
/TimesNewRomanPSMT 25.000 selectfont
gsave

628.115 468.67 translate
0 rotate
0 0 m /space glyphshow
6.25 0 m /three glyphshow
18.75 0 m /G glyphshow
36.8042 0 m /P glyphshow
50.708 0 m /a glyphshow
grestore
gsave
992.030769 454.68625 m
1306.8 454.68625 l
1306.8 731.88625 l
992.030769 731.88625 l
cl
1.000 setgray
fill
grestore
2.500 setlinewidth
0 setlinecap
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1010.55 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1010.55 731.886 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

1004.3 427.327 translate
0 rotate
0 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1084.61 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1084.61 731.886 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

1065.86 427.327 translate
0 rotate
0 0 m /four glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1158.67 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1158.67 731.886 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

1139.92 427.327 translate
0 rotate
0 0 m /eight glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1232.74 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1232.74 731.886 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

1207.74 427.327 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /two glyphshow
25 0 m /zero glyphshow
37.5 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1306.8 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1306.8 731.886 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

1281.8 427.327 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /six glyphshow
25 0 m /zero glyphshow
37.5 0 m /zero glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1029.06 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1029.06 731.886 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1047.58 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1047.58 731.886 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1066.09 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1066.09 731.886 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1103.13 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1103.13 731.886 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1121.64 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1121.64 731.886 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1140.16 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1140.16 731.886 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1177.19 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1177.19 731.886 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1195.7 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1195.7 731.886 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1214.22 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1214.22 731.886 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1251.25 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1251.25 731.886 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1269.77 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1269.77 731.886 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1288.28 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1288.28 731.886 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

1065.95 400.624 translate
0 rotate
0 0 m /T glyphshow
13.521 0 m /e glyphshow
24.6172 0 m /m glyphshow
44.063 0 m /p glyphshow
56.563 0 m /e glyphshow
67.6592 0 m /r glyphshow
75.9844 0 m /a glyphshow
87.0806 0 m /t glyphshow
94.0264 0 m /u glyphshow
106.526 0 m /r glyphshow
114.852 0 m /e glyphshow
125.948 0 m /space glyphshow
132.198 0 m /parenleft glyphshow
140.523 0 m /K glyphshow
158.577 0 m /parenright glyphshow
grestore
2.500 setlinewidth
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
992.031 454.686 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1306.8 454.686 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

969.531 446.007 translate
0 rotate
0 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
992.031 500.886 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1306.8 500.886 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

957.031 492.207 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /five glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
992.031 547.086 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1306.8 547.086 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

957.031 538.407 translate
0 rotate
0 0 m /three glyphshow
12.5 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
992.031 593.286 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1306.8 593.286 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

957.031 584.607 translate
0 rotate
0 0 m /four glyphshow
12.5 0 m /five glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
992.031 639.486 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1306.8 639.486 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

957.031 630.807 translate
0 rotate
0 0 m /six glyphshow
12.5 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
992.031 685.686 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1306.8 685.686 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

957.031 677.007 translate
0 rotate
0 0 m /seven glyphshow
12.5 0 m /five glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
992.031 731.886 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1306.8 731.886 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

957.031 723.207 translate
0 rotate
0 0 m /nine glyphshow
12.5 0 m /zero glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
992.031 466.236 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1306.8 466.236 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
992.031 477.786 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1306.8 477.786 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
992.031 489.336 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1306.8 489.336 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
992.031 512.436 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1306.8 512.436 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
992.031 523.986 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1306.8 523.986 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
992.031 535.536 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1306.8 535.536 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
992.031 558.636 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1306.8 558.636 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
992.031 570.186 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1306.8 570.186 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
992.031 581.736 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1306.8 581.736 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
992.031 604.836 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1306.8 604.836 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
992.031 616.386 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1306.8 616.386 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
992.031 627.936 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1306.8 627.936 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
992.031 651.036 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1306.8 651.036 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
992.031 662.586 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1306.8 662.586 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
992.031 674.136 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1306.8 674.136 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
992.031 697.236 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1306.8 697.236 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
992.031 708.786 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1306.8 708.786 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
992.031 720.336 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1306.8 720.336 o
grestore
gsave
947.031 482.286 translate
90 rotate
/TimesNewRomanPSMT 25.0 selectfont
0 0.148438 moveto
/P glyphshow
13.9038 0.148438 moveto
/o glyphshow
26.4038 0.148438 moveto
/l glyphshow
33.3496 0.148438 moveto
/a glyphshow
44.4458 0.148438 moveto
/r glyphshow
52.771 0.148438 moveto
/i glyphshow
59.7168 0.148438 moveto
/z glyphshow
70.813 0.148438 moveto
/a glyphshow
81.9092 0.148438 moveto
/t glyphshow
88.855 0.148438 moveto
/i glyphshow
95.8008 0.148438 moveto
/o glyphshow
108.301 0.148438 moveto
/n glyphshow
120.801 0.148438 moveto
/space glyphshow
127.051 0.148438 moveto
/parenleft glyphshow
135.376 0.148438 moveto
/mu glyphshow
148.779 0.148438 moveto
/C glyphshow
165.454 0.148438 moveto
/slash glyphshow
172.4 0.148438 moveto
/c glyphshow
183.496 0.148438 moveto
/m glyphshow
/TimesNewRomanPSMT 17.5 selectfont
203.318 15.1766 moveto
/two glyphshow
/TimesNewRomanPSMT 25.0 selectfont
213.141 0.148438 moveto
/parenright glyphshow
grestore
2 setlinecap
0.749 0.212 0.047 setrgbcolor
gsave
314.769 277.2 992.031 454.686 clipbox
1340 455.481223 m
1336.425339 456.250985 l
1332.722172 455.095648 l
1329.019005 455.967383 l
1325.315837 456.693272 l
1321.61267 455.413117 l
1317.909502 456.347411 l
1314.206335 455.733249 l
1310.503167 457.043461 l
1306.8 456.837803 l
1303.096833 456.250195 l
1299.393665 457.278979 l
1295.690498 456.918902 l
1291.98733 456.118939 l
1288.284163 455.856511 l
1284.580995 459.359943 l
1280.877828 464.118818 l
1277.174661 454.884714 l
1273.471493 456.044484 l
1269.768326 456.221395 l
1266.065158 456.221102 l
1262.361991 456.181864 l
1258.658824 460.392291 l
1254.955656 458.26961 l
1251.252489 524.052357 l
1247.549321 565.586501 l
1243.846154 574.252025 l
1240.142986 581.803361 l
1236.439819 588.931929 l
1232.736652 593.677528 l
1229.033484 600.061918 l
1225.330317 605.522057 l
1221.627149 610.336403 l
1217.923982 615.548456 l
1214.220814 618.789637 l
1210.517647 622.726976 l
1206.81448 627.102879 l
1203.111312 630.619829 l
1199.408145 633.912821 l
1195.704977 637.198923 l
1192.00181 640.775722 l
1188.298643 643.137862 l
1184.595475 646.22934 l
1180.892308 649.000195 l
1177.18914 652.220433 l
1173.485973 654.82239 l
1169.782805 656.939466 l
1166.079638 659.801973 l
1162.376471 661.989689 l
1158.673303 664.41464 l
1154.970136 665.757928 l
1151.266968 667.306165 l
1147.563801 668.760637 l
1143.860633 670.277438 l
1140.157466 671.463281 l
1136.454299 672.836925 l
1132.751131 674.008801 l
1129.047964 675.295236 l
1125.344796 676.415512 l
1121.641629 677.537218 l
1117.938462 678.78045 l
1114.235294 679.806928 l
1110.532127 680.855765 l
1106.828959 681.958877 l
1103.125792 683.00336 l
1099.422624 683.818679 l
1095.719457 684.73798 l
1092.01629 685.736996 l
1088.313122 686.59615 l
1084.609955 687.657911 l
1080.906787 688.594598 l
1077.20362 689.399882 l
1073.500452 690.278125 l
1069.797285 691.075167 l
1066.094118 691.904089 l
1062.39095 692.676455 l
1058.687783 693.472925 l
1054.984615 694.30735 l
1051.281448 695.031175 l
1047.578281 695.751788 l
1043.875113 696.449898 l
1040.171946 697.252259 l
1036.468778 697.95738 l
1032.765611 698.663411 l
1029.062443 699.334282 l
1025.359276 700.054375 l
1021.656109 700.726194 l
1017.952941 701.390207 l
1014.249774 702.03339 l
stroke
grestore
0.937 0.424 0.000 setrgbcolor
gsave
314.769 277.2 992.031 454.686 clipbox
1340 455.968779 m
1336.425339 455.792729 l
1332.722172 456.262626 l
1329.019005 455.779658 l
1325.315837 456.542249 l
1321.61267 455.596805 l
1317.909502 457.323572 l
1314.206335 456.224139 l
1310.503167 455.604521 l
1306.8 455.720917 l
1303.096833 456.690124 l
1299.393665 456.358498 l
1295.690498 456.809277 l
1291.98733 457.297094 l
1288.284163 463.454557 l
1284.580995 457.162789 l
1280.877828 458.46596 l
1277.174661 460.120998 l
1273.471493 458.774419 l
1269.768326 454.985535 l
1266.065158 454.973669 l
1262.361991 455.027411 l
1258.658824 457.597883 l
1254.955656 455.471327 l
1251.252489 467.406434 l
1247.549321 475.157993 l
1243.846154 513.703903 l
1240.142986 565.090287 l
1236.439819 573.872526 l
1232.736652 581.755485 l
1229.033484 588.784826 l
1225.330317 594.752034 l
1221.627149 599.547241 l
1217.923982 605.22807 l
1214.220814 609.341717 l
1210.517647 613.80828 l
1206.81448 618.081875 l
1203.111312 622.046729 l
1199.408145 625.756552 l
1195.704977 629.723185 l
1192.00181 632.438365 l
1188.298643 635.207991 l
1184.595475 638.919132 l
1180.892308 656.911598 l
1177.18914 658.843517 l
1173.485973 660.943205 l
1169.782805 662.659727 l
1166.079638 664.429397 l
1162.376471 665.944687 l
1158.673303 667.516437 l
1154.970136 668.793469 l
1151.266968 670.10567 l
1147.563801 671.824708 l
1143.860633 672.985478 l
1140.157466 674.298853 l
1136.454299 675.42603 l
1132.751131 676.563705 l
1129.047964 677.793099 l
1125.344796 678.881094 l
1121.641629 680.060774 l
1117.938462 681.080743 l
1114.235294 682.153324 l
1110.532127 683.076855 l
1106.828959 684.121739 l
1103.125792 685.119332 l
1099.422624 685.997123 l
1095.719457 687.013211 l
1092.01629 687.877244 l
1088.313122 688.714773 l
1084.609955 689.571707 l
1080.906787 690.4571 l
1077.20362 691.260063 l
1073.500452 692.075596 l
1069.797285 692.819803 l
1066.094118 693.711889 l
1062.39095 694.424811 l
1058.687783 695.210983 l
1054.984615 696.008842 l
1051.281448 696.706178 l
1047.578281 697.405698 l
1043.875113 698.13377 l
1040.171946 698.848376 l
1036.468778 699.542133 l
1032.765611 700.205692 l
1029.062443 700.895954 l
1025.359276 701.557069 l
1021.656109 702.208302 l
1017.952941 702.841033 l
1014.249774 703.475227 l
stroke
grestore
1.000 0.702 0.000 setrgbcolor
gsave
314.769 277.2 992.031 454.686 clipbox
1340 457.063901 m
1336.425339 455.370421 l
1332.722172 454.944645 l
1329.019005 456.714101 l
1325.315837 456.293696 l
1321.61267 456.694818 l
1317.909502 455.05904 l
1314.206335 455.571787 l
1310.503167 456.598587 l
1306.8 456.173138 l
1303.096833 456.608437 l
1299.393665 456.895403 l
1295.690498 455.528308 l
1291.98733 457.218396 l
1288.284163 459.618741 l
1284.580995 457.872438 l
1280.877828 456.551168 l
1277.174661 457.694878 l
1273.471493 457.082969 l
1269.768326 455.05851 l
1266.065158 454.842373 l
1262.361991 454.797217 l
1258.658824 454.919619 l
1254.955656 455.544441 l
1251.252489 455.383162 l
1247.549321 456.543031 l
1243.846154 459.323528 l
1240.142986 476.445022 l
1236.439819 464.655944 l
1232.736652 563.833792 l
1229.033484 570.125967 l
1225.330317 578.78806 l
1221.627149 585.342567 l
1217.923982 592.138361 l
1214.220814 594.728788 l
1210.517647 606.540802 l
1206.81448 643.561616 l
1203.111312 646.547836 l
1199.408145 649.534719 l
1195.704977 652.2724 l
1192.00181 654.557821 l
1188.298643 656.548574 l
1184.595475 658.65312 l
1180.892308 660.279919 l
1177.18914 662.227138 l
1173.485973 664.086664 l
1169.782805 665.566575 l
1166.079638 667.259827 l
1162.376471 668.772606 l
1158.673303 670.164689 l
1154.970136 671.562538 l
1151.266968 673.007323 l
1147.563801 674.129046 l
1143.860633 675.505523 l
1140.157466 676.710964 l
1136.454299 677.840632 l
1132.751131 678.774156 l
1129.047964 680.01111 l
1125.344796 681.026239 l
1121.641629 682.178417 l
1117.938462 683.038165 l
1114.235294 684.09346 l
1110.532127 685.040251 l
1106.828959 685.960838 l
1103.125792 686.856471 l
1099.422624 687.812307 l
1095.719457 688.677849 l
1092.01629 689.506738 l
1088.313122 690.484569 l
1084.609955 691.332032 l
1080.906787 692.105905 l
1077.20362 692.831833 l
1073.500452 693.688259 l
1069.797285 694.339857 l
1066.094118 695.220665 l
1062.39095 695.958091 l
1058.687783 696.74113 l
1054.984615 697.39899 l
1051.281448 698.114718 l
1047.578281 698.833897 l
1043.875113 699.545007 l
1040.171946 700.257334 l
1036.468778 700.9055 l
1032.765611 701.550412 l
1029.062443 702.179559 l
1025.359276 702.822261 l
1021.656109 703.448405 l
1017.952941 704.08379 l
1014.249774 704.707152 l
stroke
grestore
0.031 0.659 0.255 setrgbcolor
gsave
314.769 277.2 992.031 454.686 clipbox
1340 456.727432 m
1336.425339 456.410452 l
1332.722172 455.74092 l
1329.019005 456.213538 l
1325.315837 456.620528 l
1321.61267 455.908826 l
1317.909502 456.984472 l
1314.206335 458.021046 l
1310.503167 457.968524 l
1306.8 457.13904 l
1303.096833 455.28334 l
1299.393665 457.21698 l
1295.690498 457.93399 l
1291.98733 459.50573 l
1288.284163 457.634189 l
1284.580995 457.124106 l
1280.877828 459.737244 l
1277.174661 459.491587 l
1273.471493 457.828333 l
1269.768326 455.28587 l
1266.065158 455.055356 l
1262.361991 455.136418 l
1258.658824 456.278107 l
1254.955656 456.431701 l
1251.252489 455.526124 l
1247.549321 460.549442 l
1243.846154 462.339253 l
1240.142986 460.114904 l
1236.439819 470.206644 l
1232.736652 552.458164 l
1229.033484 626.359909 l
1225.330317 635.486258 l
1221.627149 640.052063 l
1217.923982 643.620762 l
1214.220814 646.747791 l
1210.517647 649.815251 l
1206.81448 652.325736 l
1203.111312 654.555756 l
1199.408145 657.043834 l
1195.704977 658.939428 l
1192.00181 660.85068 l
1188.298643 662.512326 l
1184.595475 664.21162 l
1180.892308 665.90433 l
1177.18914 667.69153 l
1173.485973 668.943056 l
1169.782805 670.316619 l
1166.079638 671.888467 l
1162.376471 673.240477 l
1158.673303 674.477272 l
1154.970136 675.648088 l
1151.266968 676.929076 l
1147.563801 678.002805 l
1143.860633 679.127928 l
1140.157466 680.06578 l
1136.454299 681.457015 l
1132.751131 682.369748 l
1129.047964 683.46999 l
1125.344796 684.330582 l
1121.641629 685.334916 l
1117.938462 686.187431 l
1114.235294 687.345066 l
1110.532127 688.056681 l
1106.828959 688.954801 l
1103.125792 689.896645 l
1099.422624 690.706932 l
1095.719457 691.572067 l
1092.01629 692.321802 l
1088.313122 693.119578 l
1084.609955 693.925242 l
1080.906787 694.639728 l
1077.20362 695.476088 l
1073.500452 696.10336 l
1069.797285 697.008383 l
1066.094118 697.622083 l
1062.39095 698.376443 l
1058.687783 699.000054 l
1054.984615 699.780306 l
1051.281448 700.4217 l
1047.578281 701.042447 l
1043.875113 701.698185 l
1040.171946 702.326283 l
1036.468778 702.963518 l
1032.765611 703.630951 l
1029.062443 704.293035 l
1025.359276 704.864522 l
1021.656109 705.427142 l
1017.952941 706.04609 l
1014.249774 706.635187 l
stroke
grestore
0.031 0.416 0.416 setrgbcolor
gsave
314.769 277.2 992.031 454.686 clipbox
1340 456.571753 m
1336.425339 455.240855 l
1332.722172 456.056981 l
1329.019005 456.206503 l
1325.315837 456.794399 l
1321.61267 456.34649 l
1317.909502 454.842557 l
1314.206335 457.270525 l
1310.503167 457.910758 l
1306.8 458.084008 l
1303.096833 457.042734 l
1299.393665 457.976119 l
1295.690498 456.802923 l
1291.98733 458.536595 l
1288.284163 459.156474 l
1284.580995 460.73996 l
1280.877828 463.638282 l
1277.174661 458.334867 l
1273.471493 466.425701 l
1269.768326 456.125324 l
1266.065158 455.435109 l
1262.361991 456.893167 l
1258.658824 456.777295 l
1254.955656 458.787356 l
1251.252489 468.186745 l
1247.549321 468.801698 l
1243.846154 462.91405 l
1240.142986 460.880145 l
1236.439819 511.664243 l
1232.736652 601.050513 l
1229.033484 636.935804 l
1225.330317 640.776854 l
1221.627149 644.509628 l
1217.923982 647.742317 l
1214.220814 650.289727 l
1210.517647 652.965661 l
1206.81448 655.292144 l
1203.111312 657.167609 l
1199.408145 659.343568 l
1195.704977 661.380797 l
1192.00181 663.0975 l
1188.298643 664.635916 l
1184.595475 666.36024 l
1180.892308 667.992996 l
1177.18914 669.303536 l
1173.485973 670.722839 l
1169.782805 672.207169 l
1166.079638 673.561368 l
1162.376471 674.698745 l
1158.673303 675.775663 l
1154.970136 676.974012 l
1151.266968 678.220886 l
1147.563801 679.376325 l
1143.860633 680.597491 l
1140.157466 681.59762 l
1136.454299 682.647233 l
1132.751131 683.645766 l
1129.047964 684.587534 l
1125.344796 685.472347 l
1121.641629 686.378397 l
1117.938462 687.387446 l
1114.235294 688.245657 l
1110.532127 689.101483 l
1106.828959 690.085293 l
1103.125792 690.984641 l
1099.422624 691.709312 l
1095.719457 692.564533 l
1092.01629 693.278834 l
1088.313122 694.102732 l
1084.609955 694.860835 l
1080.906787 695.582079 l
1077.20362 696.356787 l
1073.500452 697.060615 l
1069.797285 697.744558 l
1066.094118 698.452569 l
1062.39095 699.118931 l
1058.687783 699.791891 l
1054.984615 700.518653 l
1051.281448 701.228344 l
1047.578281 701.846793 l
1043.875113 702.476376 l
1040.171946 703.076859 l
1036.468778 703.723283 l
1032.765611 704.352262 l
1029.062443 704.975202 l
1025.359276 705.560504 l
1021.656109 706.153177 l
1017.952941 706.722366 l
1014.249774 707.311628 l
stroke
grestore
0.031 0.659 0.659 setrgbcolor
gsave
314.769 277.2 992.031 454.686 clipbox
1340 457.558238 m
1336.425339 456.978319 l
1332.722172 455.36804 l
1329.019005 456.4853 l
1325.315837 458.174649 l
1321.61267 456.778246 l
1317.909502 455.808531 l
1314.206335 458.554659 l
1310.503167 455.821048 l
1306.8 456.984934 l
1303.096833 460.954535 l
1299.393665 457.926548 l
1295.690498 458.535846 l
1291.98733 456.215551 l
1288.284163 456.95344 l
1284.580995 464.209172 l
1280.877828 468.621536 l
1277.174661 470.165094 l
1273.471493 461.063559 l
1269.768326 456.719658 l
1266.065158 458.299857 l
1262.361991 458.700949 l
1258.658824 464.251099 l
1254.955656 460.63751 l
1251.252489 473.24052 l
1247.549321 493.182377 l
1243.846154 462.113672 l
1240.142986 473.08442 l
1236.439819 632.357086 l
1232.736652 637.572355 l
1229.033484 641.022568 l
1225.330317 644.622039 l
1221.627149 647.271424 l
1217.923982 650.167395 l
1214.220814 652.895305 l
1210.517647 655.009253 l
1206.81448 657.348065 l
1203.111312 658.822225 l
1199.408145 661.101271 l
1195.704977 662.865941 l
1192.00181 664.585433 l
1188.298643 666.055586 l
1184.595475 667.644906 l
1180.892308 669.205329 l
1177.18914 670.637401 l
1173.485973 671.793455 l
1169.782805 673.320988 l
1166.079638 674.591977 l
1162.376471 675.624139 l
1158.673303 677.128478 l
1154.970136 677.990239 l
1151.266968 679.428131 l
1147.563801 680.341425 l
1143.860633 681.410967 l
1140.157466 682.527308 l
1136.454299 683.397811 l
1132.751131 684.551443 l
1129.047964 685.423238 l
1125.344796 686.348968 l
1121.641629 687.352037 l
1117.938462 688.158307 l
1114.235294 689.170193 l
1110.532127 689.947506 l
1106.828959 690.703067 l
1103.125792 691.574355 l
1099.422624 692.341735 l
1095.719457 693.225467 l
1092.01629 693.964065 l
1088.313122 694.761021 l
1084.609955 695.4634 l
1080.906787 696.253888 l
1077.20362 696.962747 l
1073.500452 697.710887 l
1069.797285 698.34646 l
1066.094118 699.025071 l
1062.39095 699.818938 l
1058.687783 700.435799 l
1054.984615 701.119337 l
1051.281448 701.784527 l
1047.578281 702.455799 l
1043.875113 703.045858 l
1040.171946 703.645305 l
1036.468778 704.262899 l
1032.765611 704.829703 l
1029.062443 705.475169 l
1025.359276 706.070582 l
1021.656109 706.663241 l
1017.952941 707.224684 l
1014.249774 707.803164 l
stroke
grestore
2.500 setlinewidth
0 setlinejoin
0.000 setgray
gsave
992.030769 454.68625 m
992.030769 731.88625 l
stroke
grestore
gsave
1306.8 454.68625 m
1306.8 731.88625 l
stroke
grestore
gsave
992.030769 454.68625 m
1306.8 454.68625 l
stroke
grestore
gsave
992.030769 731.88625 m
1306.8 731.88625 l
stroke
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

1075.35 751.487 translate
0 rotate
0 0 m /parenleft glyphshow
8.3252 0 m /c glyphshow
19.4214 0 m /parenright glyphshow
27.7466 0 m /space glyphshow
33.9966 0 m /B glyphshow
50.6714 0 m /i glyphshow
57.6172 0 m /a glyphshow
68.7134 0 m /x glyphshow
81.2134 0 m /i glyphshow
88.1592 0 m /a glyphshow
99.2554 0 m /l glyphshow
106.201 0 m /space glyphshow
112.451 0 m /S glyphshow
126.355 0 m /t glyphshow
133.301 0 m /r glyphshow
141.626 0 m /e glyphshow
152.722 0 m /s glyphshow
162.451 0 m /s glyphshow
grestore
1.500 setlinewidth
1 setlinejoin
0.749 0.212 0.047 setrgbcolor
gsave
1014.530769 654.13875 m
1039.530769 654.13875 l
1064.530769 654.13875 l
stroke
grestore
0.000 setgray
/TimesNewRomanPSMT 25.000 selectfont
gsave

1084.53 645.389 translate
0 rotate
0 0 m /space glyphshow
6.25 0 m /hyphen glyphshow
14.5752 0 m /three glyphshow
27.0752 0 m /G glyphshow
45.1294 0 m /P glyphshow
59.0332 0 m /a glyphshow
grestore
0.937 0.424 0.000 setrgbcolor
gsave
1014.530769 618.795 m
1039.530769 618.795 l
1064.530769 618.795 l
stroke
grestore
0.000 setgray
/TimesNewRomanPSMT 25.000 selectfont
gsave

1084.53 610.045 translate
0 rotate
0 0 m /space glyphshow
6.25 0 m /hyphen glyphshow
14.5752 0 m /two glyphshow
27.0752 0 m /G glyphshow
45.1294 0 m /P glyphshow
59.0332 0 m /a glyphshow
grestore
1.000 0.702 0.000 setrgbcolor
gsave
1014.530769 583.45125 m
1039.530769 583.45125 l
1064.530769 583.45125 l
stroke
grestore
0.000 setgray
/TimesNewRomanPSMT 25.000 selectfont
gsave

1084.53 574.701 translate
0 rotate
0 0 m /space glyphshow
6.25 0 m /hyphen glyphshow
14.5752 0 m /one glyphshow
27.0752 0 m /G glyphshow
45.1294 0 m /P glyphshow
59.0332 0 m /a glyphshow
grestore
0.031 0.659 0.255 setrgbcolor
gsave
1014.530769 548.1075 m
1039.530769 548.1075 l
1064.530769 548.1075 l
stroke
grestore
0.000 setgray
/TimesNewRomanPSMT 25.000 selectfont
gsave

1084.53 539.357 translate
0 rotate
0 0 m /space glyphshow
6.25 0 m /one glyphshow
18.75 0 m /G glyphshow
36.8042 0 m /P glyphshow
50.708 0 m /a glyphshow
grestore
0.031 0.416 0.416 setrgbcolor
gsave
1014.530769 512.76375 m
1039.530769 512.76375 l
1064.530769 512.76375 l
stroke
grestore
0.000 setgray
/TimesNewRomanPSMT 25.000 selectfont
gsave

1084.53 504.014 translate
0 rotate
0 0 m /space glyphshow
6.25 0 m /two glyphshow
18.75 0 m /G glyphshow
36.8042 0 m /P glyphshow
50.708 0 m /a glyphshow
grestore
0.031 0.659 0.659 setrgbcolor
gsave
1014.530769 477.42 m
1039.530769 477.42 l
1064.530769 477.42 l
stroke
grestore
0.000 setgray
/TimesNewRomanPSMT 25.000 selectfont
gsave

1084.53 468.67 translate
0 rotate
0 0 m /space glyphshow
6.25 0 m /three glyphshow
18.75 0 m /G glyphshow
36.8042 0 m /P glyphshow
50.708 0 m /a glyphshow
grestore
gsave
79.2 66.60625 m
393.969231 66.60625 l
393.969231 343.80625 l
79.2 343.80625 l
cl
1.000 setgray
fill
grestore
1.000 setlinewidth
0 setlinecap
0.502 0.741 0.867 setrgbcolor
gsave
314.769 277.2 79.2 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

367.738462 93.743943 m
367.738462 93.743943 l
341.507692 93.743943 l
315.276923 93.743943 l
289.046154 93.743943 l
262.815385 93.743943 l
236.584615 93.743943 l
210.353846 93.743943 l
184.123077 93.743943 l
157.892308 93.743943 l
131.661538 93.743943 l
105.430769 93.743943 l
105.430769 324.46927 l
105.430769 324.46927 l
131.661538 302.49543 l
157.892308 269.534669 l
184.123077 247.560828 l
210.353846 225.586987 l
236.584615 203.613147 l
262.815385 181.639306 l
289.046154 159.665465 l
315.276923 137.691625 l
341.507692 115.717784 l
367.738462 93.743943 l
cl

gsave
0.502 0.741 0.867 setrgbcolor
fill
grestore
stroke
grestore
} bind def
0 0 o
grestore
0.800 0.800 0.600 setrgbcolor
gsave
314.769 277.2 79.2 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

367.738462 93.743943 m
367.738462 324.46927 l
341.507692 324.46927 l
315.276923 324.46927 l
289.046154 324.46927 l
262.815385 324.46927 l
236.584615 324.46927 l
210.353846 324.46927 l
184.123077 324.46927 l
157.892308 324.46927 l
131.661538 324.46927 l
105.430769 324.46927 l
105.430769 324.46927 l
105.430769 324.46927 l
131.661538 302.49543 l
157.892308 269.534669 l
184.123077 247.560828 l
210.353846 225.586987 l
236.584615 203.613147 l
262.815385 181.639306 l
289.046154 159.665465 l
315.276923 137.691625 l
341.507692 115.717784 l
367.738462 93.743943 l
cl

gsave
0.800 0.800 0.600 setrgbcolor
fill
grestore
stroke
grestore
} bind def
0 0 o
grestore
2.500 setlinewidth
0.000 setgray
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
105.431 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
105.431 343.806 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

92.1339 39.2469 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /five glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
157.892 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
157.892 343.806 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

144.595 39.2469 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /three glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
210.354 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
210.354 343.806 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

197.057 39.2469 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /one glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
262.815 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
262.815 343.806 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

256.565 39.2469 translate
0 rotate
0 0 m /one glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
315.277 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
315.277 343.806 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

309.027 39.2469 translate
0 rotate
0 0 m /three glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
367.738 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
367.738 343.806 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

361.488 39.2469 translate
0 rotate
0 0 m /five glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
92.3154 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
92.3154 343.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
118.546 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
118.546 343.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
131.662 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
131.662 343.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
144.777 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
144.777 343.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
171.008 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
171.008 343.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
184.123 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
184.123 343.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
197.238 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
197.238 343.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
223.469 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
223.469 343.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
236.585 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
236.585 343.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
249.7 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
249.7 343.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
275.931 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
275.931 343.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
289.046 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
289.046 343.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
302.162 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
302.162 343.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
328.392 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
328.392 343.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
341.508 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
341.508 343.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
354.623 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
354.623 343.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
380.854 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
380.854 343.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 343.806 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

161.241 12.5438 translate
0 rotate
0 0 m /P glyphshow
13.9038 0 m /r glyphshow
22.229 0 m /e glyphshow
33.3252 0 m /s glyphshow
43.0542 0 m /s glyphshow
52.7832 0 m /u glyphshow
65.2832 0 m /r glyphshow
73.6084 0 m /e glyphshow
84.7046 0 m /space glyphshow
90.9546 0 m /parenleft glyphshow
99.2798 0 m /G glyphshow
117.334 0 m /P glyphshow
131.238 0 m /a glyphshow
142.334 0 m /parenright glyphshow
grestore
2.500 setlinewidth
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 76.1649 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 76.1649 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

37.95 67.4852 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /eight glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 139.889 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 139.889 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

37.95 131.209 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /nine glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 203.613 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 203.613 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

37.95 194.933 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 267.337 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 267.337 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

37.95 258.658 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /period glyphshow
18.75 0 m /one glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 331.061 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 331.061 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

37.95 322.382 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /period glyphshow
18.75 0 m /two glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 88.9097 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 88.9097 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 101.655 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 101.655 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 114.399 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 114.399 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 127.144 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 127.144 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 152.634 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 152.634 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 165.379 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 165.379 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 178.123 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 178.123 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 190.868 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 190.868 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 216.358 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 216.358 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 229.103 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 229.103 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 241.848 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 241.848 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 254.592 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 254.592 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 280.082 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 280.082 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 292.827 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 292.827 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 305.572 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 305.572 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 318.317 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
393.969 318.317 o
grestore
gsave
26.95 158.706 translate
90 rotate
/TimesNewRomanPSMT 25.0 selectfont
0 0.640625 moveto
/T glyphshow
/TimesNewRomanPSMT 17.5 selectfont
15.6467 -5.8 moveto
/C glyphshow
/TimesNewRomanPSMT 25.0 selectfont
28.3925 0.640625 moveto
/slash glyphshow
35.3383 0.640625 moveto
/T glyphshow
/TimesNewRomanPSMT 17.5 selectfont
50.985 -5.8 moveto
/C glyphshow
/TimesNewRomanPSMT 25.0 selectfont
63.7308 0.640625 moveto
/parenleft glyphshow
72.056 0.640625 moveto
/zero glyphshow
84.556 0.640625 moveto
/parenright glyphshow
grestore
[9.6 2.4 1.5 2.4] 0 setdash
0.749 0.212 0.047 setrgbcolor
gsave
314.769 277.2 79.2 66.606 clipbox
367.738462 93.743943 m
341.507692 115.717784 l
315.276923 137.691625 l
289.046154 159.665465 l
262.815385 181.639306 l
236.584615 203.613147 l
210.353846 225.586987 l
184.123077 247.560828 l
157.892308 269.534669 l
131.661538 302.49543 l
105.430769 324.46927 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
314.769 277.2 79.2 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
367.738 93.7439 o
341.508 115.718 o
315.277 137.692 o
289.046 159.665 o
262.815 181.639 o
236.585 203.613 o
210.354 225.587 o
184.123 247.561 o
157.892 269.535 o
131.662 302.495 o
105.431 324.469 o
grestore
2.500 setlinewidth
0 setlinejoin
2 setlinecap
0.000 setgray
gsave
79.2 66.60625 m
79.2 343.80625 l
stroke
grestore
gsave
393.969231 66.60625 m
393.969231 343.80625 l
stroke
grestore
gsave
79.2 66.60625 m
393.969231 66.60625 l
stroke
grestore
gsave
79.2 343.80625 m
393.969231 343.80625 l
stroke
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

175.787 154.392 translate
0 rotate
0 0 m /R glyphshow
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

306.941 249.978 translate
0 rotate
0 0 m /C glyphshow
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

223.469 358.309 translate
0 rotate
0 0 m /parenleft glyphshow
8.3252 0 m /d glyphshow
20.8252 0 m /parenright glyphshow
grestore
gsave
535.615385 66.60625 m
850.384615 66.60625 l
850.384615 343.80625 l
535.615385 343.80625 l
cl
1.000 setgray
fill
grestore
1.000 setlinewidth
1 setlinejoin
0 setlinecap
0.502 0.741 0.867 setrgbcolor
gsave
314.769 277.2 535.615 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

821.769231 94.622897 m
821.769231 94.622897 l
793.153846 94.622897 l
764.538462 94.622897 l
735.923077 94.622897 l
707.307692 94.622897 l
678.692308 94.622897 l
650.076923 94.622897 l
621.461538 94.622897 l
592.846154 94.622897 l
564.230769 94.622897 l
564.230769 182.51826 l
564.230769 182.51826 l
592.846154 188.01172 l
621.461538 198.99864 l
650.076923 204.4921 l
678.692308 209.98556 l
707.307692 198.99864 l
735.923077 177.024799 l
764.538462 138.570578 l
793.153846 127.583658 l
821.769231 94.622897 l
cl

gsave
0.502 0.741 0.867 setrgbcolor
fill
grestore
stroke
grestore
} bind def
0 0 o
grestore
0.969 0.733 0.600 setrgbcolor
gsave
314.769 277.2 535.615 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

821.769231 314.361304 m
821.769231 94.622897 l
793.153846 127.583658 l
764.538462 138.570578 l
735.923077 177.024799 l
707.307692 198.99864 l
678.692308 209.98556 l
678.692308 209.98556 l
678.692308 209.98556 l
707.307692 231.959401 l
735.923077 259.426702 l
764.538462 275.907082 l
793.153846 297.880923 l
821.769231 314.361304 l
cl

gsave
0.969 0.733 0.600 setrgbcolor
fill
grestore
stroke
grestore
} bind def
0 0 o
grestore
0.800 0.800 0.600 setrgbcolor
gsave
314.769 277.2 535.615 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

821.769231 314.361304 m
821.769231 314.361304 l
793.153846 314.361304 l
764.538462 314.361304 l
735.923077 314.361304 l
707.307692 314.361304 l
678.692308 314.361304 l
650.076923 314.361304 l
621.461538 314.361304 l
592.846154 314.361304 l
564.230769 314.361304 l
564.230769 182.51826 l
564.230769 182.51826 l
592.846154 188.01172 l
621.461538 198.99864 l
650.076923 204.4921 l
678.692308 209.98556 l
707.307692 231.959401 l
735.923077 259.426702 l
764.538462 275.907082 l
793.153846 297.880923 l
821.769231 314.361304 l
cl

gsave
0.800 0.800 0.600 setrgbcolor
fill
grestore
stroke
grestore
} bind def
0 0 o
grestore
2.500 setlinewidth
0.000 setgray
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
535.615 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
535.615 343.806 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

522.319 39.2469 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /five glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
592.846 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
592.846 343.806 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

579.549 39.2469 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /three glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
650.077 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
650.077 343.806 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

636.78 39.2469 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /one glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
707.308 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
707.308 343.806 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

701.058 39.2469 translate
0 rotate
0 0 m /one glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
764.538 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
764.538 343.806 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

758.288 39.2469 translate
0 rotate
0 0 m /three glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
821.769 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
821.769 343.806 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

815.519 39.2469 translate
0 rotate
0 0 m /five glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
549.923 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
549.923 343.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
564.231 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
564.231 343.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
578.538 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
578.538 343.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
607.154 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
607.154 343.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
621.462 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
621.462 343.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
635.769 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
635.769 343.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
664.385 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
664.385 343.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
678.692 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
678.692 343.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
693 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
693 343.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
721.615 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
721.615 343.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
735.923 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
735.923 343.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
750.231 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
750.231 343.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
778.846 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
778.846 343.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
793.154 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
793.154 343.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
807.462 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
807.462 343.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
836.077 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
836.077 343.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.385 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.385 343.806 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

583.961 12.5438 translate
0 rotate
0 0 m /U glyphshow
18.0542 0 m /n glyphshow
30.5542 0 m /i glyphshow
37.5 0 m /a glyphshow
48.5962 0 m /x glyphshow
61.0962 0 m /i glyphshow
68.042 0 m /a glyphshow
79.1382 0 m /l glyphshow
86.084 0 m /space glyphshow
92.334 0 m /S glyphshow
106.238 0 m /t glyphshow
113.184 0 m /r glyphshow
121.509 0 m /e glyphshow
132.605 0 m /s glyphshow
142.334 0 m /s glyphshow
152.063 0 m /space glyphshow
158.313 0 m /parenleft glyphshow
166.638 0 m /G glyphshow
184.692 0 m /P glyphshow
198.596 0 m /a glyphshow
209.692 0 m /parenright glyphshow
grestore
2.500 setlinewidth
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
535.615 82.5373 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.385 82.5373 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

494.365 73.8576 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /six glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
535.615 146.261 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.385 146.261 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

494.365 137.582 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /eight glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
535.615 209.986 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.385 209.986 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

494.365 201.306 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
535.615 273.71 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.385 273.71 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

494.365 265.03 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /period glyphshow
18.75 0 m /two glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
535.615 337.434 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.385 337.434 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

494.365 328.754 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /period glyphshow
18.75 0 m /four glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
535.615 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.385 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
535.615 98.4683 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.385 98.4683 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
535.615 114.399 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.385 114.399 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
535.615 130.33 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.385 130.33 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
535.615 162.192 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.385 162.192 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
535.615 178.123 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.385 178.123 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
535.615 194.055 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.385 194.055 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
535.615 225.917 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.385 225.917 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
535.615 241.848 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.385 241.848 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
535.615 257.779 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.385 257.779 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
535.615 289.641 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.385 289.641 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
535.615 305.572 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.385 305.572 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
535.615 321.503 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.385 321.503 o
grestore
gsave
483.365 158.706 translate
90 rotate
/TimesNewRomanPSMT 25.0 selectfont
0 0.640625 moveto
/T glyphshow
/TimesNewRomanPSMT 17.5 selectfont
15.6467 -5.8 moveto
/C glyphshow
/TimesNewRomanPSMT 25.0 selectfont
28.3925 0.640625 moveto
/slash glyphshow
35.3383 0.640625 moveto
/T glyphshow
/TimesNewRomanPSMT 17.5 selectfont
50.985 -5.8 moveto
/C glyphshow
/TimesNewRomanPSMT 25.0 selectfont
63.7308 0.640625 moveto
/parenleft glyphshow
72.056 0.640625 moveto
/zero glyphshow
84.556 0.640625 moveto
/parenright glyphshow
grestore
[9.6 2.4 1.5 2.4] 0 setdash
0.749 0.212 0.047 setrgbcolor
gsave
314.769 277.2 535.615 66.606 clipbox
821.769231 94.622897 m
793.153846 127.583658 l
764.538462 138.570578 l
735.923077 177.024799 l
707.307692 198.99864 l
678.692308 209.98556 l
650.076923 204.4921 l
621.461538 198.99864 l
592.846154 188.01172 l
564.230769 182.51826 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
314.769 277.2 535.615 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
821.769 94.6229 o
793.154 127.584 o
764.538 138.571 o
735.923 177.025 o
707.308 198.999 o
678.692 209.986 o
650.077 204.492 o
621.462 198.999 o
592.846 188.012 o
564.231 182.518 o
grestore
1.500 setlinewidth
[5.55 2.4] 0 setdash
0.937 0.424 0.000 setrgbcolor
gsave
314.769 277.2 535.615 66.606 clipbox
821.769231 314.361304 m
793.153846 297.880923 l
764.538462 275.907082 l
735.923077 259.426702 l
707.307692 231.959401 l
678.692308 209.98556 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
314.769 277.2 535.615 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-8 -8 m
8 -8 l
8 8 l
-8 8 l
cl

gsave
grestore
stroke
grestore
} bind def
821.769 314.361 o
793.154 297.881 o
764.538 275.907 o
735.923 259.427 o
707.308 231.959 o
678.692 209.986 o
grestore
2.500 setlinewidth
2 setlinecap
0.000 setgray
gsave
535.615385 66.60625 m
535.615385 343.80625 l
stroke
grestore
gsave
850.384615 66.60625 m
850.384615 343.80625 l
stroke
grestore
gsave
535.615385 66.60625 m
850.384615 66.60625 l
stroke
grestore
gsave
535.615385 343.80625 m
850.384615 343.80625 l
stroke
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

670.356 128.902 translate
0 rotate
0 0 m /R glyphshow
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

670.356 256.35 translate
0 rotate
0 0 m /C glyphshow
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

756.906 224.488 translate
0 rotate
0 0 m /T glyphshow
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

664.385 361.495 translate
0 rotate
0 0 m /parenleft glyphshow
8.3252 0 m /e glyphshow
19.4214 0 m /parenright glyphshow
grestore
1.500 setlinewidth
1 setlinejoin
0 setlinecap
[9.6 2.4 1.5 2.4] 0 setdash
0.749 0.212 0.047 setrgbcolor
gsave
558.115385 326.41625 m
583.115385 326.41625 l
608.115385 326.41625 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
583.115 326.416 o
grestore
0.000 setgray
gsave
628.115 317.666 translate
0 rotate
/TimesNewRomanPSMT 25.0 selectfont
0 0.453125 moveto
/T glyphshow
/TimesNewRomanPSMT 17.5 selectfont
15.6467 -5.9875 moveto
/C glyphshow
/TimesNewRomanPSMT 12.25 selectfont
27.5821 -10.4959 moveto
/two glyphshow
grestore
1.500 setlinewidth
[5.55 2.4] 0 setdash
0.937 0.424 0.000 setrgbcolor
gsave
714.115385 326.41625 m
739.115385 326.41625 l
764.115385 326.41625 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-8 -8 m
8 -8 l
8 8 l
-8 8 l
cl

gsave
grestore
stroke
grestore
} bind def
739.115 326.416 o
grestore
0.000 setgray
gsave
784.115 317.666 translate
0 rotate
/TimesNewRomanPSMT 25.0 selectfont
0 0.453125 moveto
/T glyphshow
/TimesNewRomanPSMT 17.5 selectfont
15.6467 -5.9875 moveto
/C glyphshow
/TimesNewRomanPSMT 12.25 selectfont
27.5821 -10.4959 moveto
/one glyphshow
grestore
gsave
992.030769 66.60625 m
1306.8 66.60625 l
1306.8 343.80625 l
992.030769 343.80625 l
cl
1.000 setgray
fill
grestore
1 setlinejoin
0.502 0.741 0.867 setrgbcolor
gsave
314.769 277.2 992.031 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

1278.184615 284.542802 m
1278.184615 98.149698 l
1249.569231 98.149698 l
1220.953846 98.149698 l
1192.338462 98.149698 l
1163.723077 98.149698 l
1135.107692 98.149698 l
1106.492308 98.149698 l
1077.876923 98.149698 l
1049.261538 98.149698 l
1020.646154 98.149698 l
1020.646154 98.149698 l
1020.646154 98.149698 l
1049.261538 145.942802 l
1077.876923 169.839353 l
1106.492308 203.294526 l
1135.107692 236.749698 l
1163.723077 260.64625 l
1192.338462 265.42556 l
1220.953846 270.204871 l
1249.569231 274.984181 l
1278.184615 284.542802 l
cl

gsave
0.502 0.741 0.867 setrgbcolor
fill
grestore
stroke
grestore
} bind def
0 0 o
grestore
0.969 0.733 0.600 setrgbcolor
gsave
314.769 277.2 992.031 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

1163.723077 260.64625 m
1163.723077 260.64625 l
1135.107692 236.749698 l
1106.492308 203.294526 l
1077.876923 169.839353 l
1049.261538 145.942802 l
1020.646154 98.149698 l
1020.646154 308.439353 l
1020.646154 308.439353 l
1049.261538 303.660043 l
1077.876923 294.101422 l
1106.492308 279.763491 l
1135.107692 270.204871 l
1163.723077 260.64625 l
cl

gsave
0.969 0.733 0.600 setrgbcolor
fill
grestore
stroke
grestore
} bind def
0 0 o
grestore
0.800 0.800 0.600 setrgbcolor
gsave
314.769 277.2 992.031 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

1278.184615 284.542802 m
1278.184615 308.439353 l
1249.569231 308.439353 l
1220.953846 308.439353 l
1192.338462 308.439353 l
1163.723077 308.439353 l
1135.107692 308.439353 l
1106.492308 308.439353 l
1077.876923 308.439353 l
1049.261538 308.439353 l
1020.646154 308.439353 l
1020.646154 308.439353 l
1020.646154 308.439353 l
1049.261538 303.660043 l
1077.876923 294.101422 l
1106.492308 279.763491 l
1135.107692 270.204871 l
1163.723077 260.64625 l
1192.338462 265.42556 l
1220.953846 270.204871 l
1249.569231 274.984181 l
1278.184615 284.542802 l
cl

gsave
0.800 0.800 0.600 setrgbcolor
fill
grestore
stroke
grestore
} bind def
0 0 o
grestore
2.500 setlinewidth
0.000 setgray
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1020.65 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1020.65 343.806 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

1007.35 39.2469 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /five glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1077.88 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1077.88 343.806 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

1064.58 39.2469 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /three glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1135.11 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1135.11 343.806 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

1121.81 39.2469 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /one glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1192.34 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1192.34 343.806 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

1186.09 39.2469 translate
0 rotate
0 0 m /one glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1249.57 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1249.57 343.806 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

1243.32 39.2469 translate
0 rotate
0 0 m /three glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1306.8 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1306.8 343.806 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

1300.55 39.2469 translate
0 rotate
0 0 m /five glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1006.34 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1006.34 343.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1034.95 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1034.95 343.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1049.26 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1049.26 343.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1063.57 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1063.57 343.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1092.18 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1092.18 343.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1106.49 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1106.49 343.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1120.8 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1120.8 343.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1149.42 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1149.42 343.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1163.72 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1163.72 343.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1178.03 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1178.03 343.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1206.65 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1206.65 343.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1220.95 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1220.95 343.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1235.26 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1235.26 343.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1263.88 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1263.88 343.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1278.18 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1278.18 343.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1292.49 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1292.49 343.806 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

1047.32 12.5438 translate
0 rotate
0 0 m /B glyphshow
16.6748 0 m /i glyphshow
23.6206 0 m /a glyphshow
34.7168 0 m /x glyphshow
47.2168 0 m /i glyphshow
54.1626 0 m /a glyphshow
65.2588 0 m /l glyphshow
72.2046 0 m /space glyphshow
78.4546 0 m /S glyphshow
92.3584 0 m /t glyphshow
99.3042 0 m /r glyphshow
107.629 0 m /e glyphshow
118.726 0 m /s glyphshow
128.455 0 m /s glyphshow
138.184 0 m /space glyphshow
144.434 0 m /parenleft glyphshow
152.759 0 m /G glyphshow
170.813 0 m /P glyphshow
184.717 0 m /a glyphshow
195.813 0 m /parenright glyphshow
grestore
2.500 setlinewidth
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
992.031 94.3263 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1306.8 94.3263 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

950.781 85.6466 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /four glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
992.031 149.766 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1306.8 149.766 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

950.781 141.087 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /six glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
992.031 205.206 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1306.8 205.206 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

950.781 196.527 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /eight glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
992.031 260.646 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1306.8 260.646 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

950.781 251.967 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
992.031 316.086 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1306.8 316.086 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

950.781 307.407 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /period glyphshow
18.75 0 m /two glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
992.031 80.4663 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1306.8 80.4663 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
992.031 108.186 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1306.8 108.186 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
992.031 122.046 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1306.8 122.046 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
992.031 135.906 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1306.8 135.906 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
992.031 163.626 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1306.8 163.626 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
992.031 177.486 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1306.8 177.486 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
992.031 191.346 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1306.8 191.346 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
992.031 219.066 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1306.8 219.066 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
992.031 232.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1306.8 232.926 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
992.031 246.786 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1306.8 246.786 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
992.031 274.506 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1306.8 274.506 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
992.031 288.366 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1306.8 288.366 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
992.031 302.226 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1306.8 302.226 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
992.031 329.946 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
1306.8 329.946 o
grestore
gsave
939.781 158.706 translate
90 rotate
/TimesNewRomanPSMT 25.0 selectfont
0 0.640625 moveto
/T glyphshow
/TimesNewRomanPSMT 17.5 selectfont
15.6467 -5.8 moveto
/C glyphshow
/TimesNewRomanPSMT 25.0 selectfont
28.3925 0.640625 moveto
/slash glyphshow
35.3383 0.640625 moveto
/T glyphshow
/TimesNewRomanPSMT 17.5 selectfont
50.985 -5.8 moveto
/C glyphshow
/TimesNewRomanPSMT 25.0 selectfont
63.7308 0.640625 moveto
/parenleft glyphshow
72.056 0.640625 moveto
/zero glyphshow
84.556 0.640625 moveto
/parenright glyphshow
grestore
[9.6 2.4 1.5 2.4] 0 setdash
0.749 0.212 0.047 setrgbcolor
gsave
314.769 277.2 992.031 66.606 clipbox
1278.184615 284.542802 m
1249.569231 274.984181 l
1220.953846 270.204871 l
1192.338462 265.42556 l
1163.723077 260.64625 l
1135.107692 236.749698 l
1106.492308 203.294526 l
1077.876923 169.839353 l
1049.261538 145.942802 l
1020.646154 98.149698 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
314.769 277.2 992.031 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
1278.18 284.543 o
1249.57 274.984 o
1220.95 270.205 o
1192.34 265.426 o
1163.72 260.646 o
1135.11 236.75 o
1106.49 203.295 o
1077.88 169.839 o
1049.26 145.943 o
1020.65 98.1497 o
grestore
1.500 setlinewidth
[5.55 2.4] 0 setdash
0.937 0.424 0.000 setrgbcolor
gsave
314.769 277.2 992.031 66.606 clipbox
1163.723077 260.64625 m
1135.107692 270.204871 l
1106.492308 279.763491 l
1077.876923 294.101422 l
1049.261538 303.660043 l
1020.646154 308.439353 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
314.769 277.2 992.031 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-8 -8 m
8 -8 l
8 8 l
-8 8 l
cl

gsave
grestore
stroke
grestore
} bind def
1163.72 260.646 o
1135.11 270.205 o
1106.49 279.763 o
1077.88 294.101 o
1049.26 303.66 o
1020.65 308.439 o
grestore
2.500 setlinewidth
2 setlinecap
0.000 setgray
gsave
992.030769 66.60625 m
992.030769 343.80625 l
stroke
grestore
gsave
1306.8 66.60625 m
1306.8 343.80625 l
stroke
grestore
gsave
992.030769 66.60625 m
1306.8 66.60625 l
stroke
grestore
gsave
992.030769 343.80625 m
1306.8 343.80625 l
stroke
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

1155.39 160.127 translate
0 rotate
0 0 m /R glyphshow
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

1155.39 279.323 translate
0 rotate
0 0 m /C glyphshow
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

1041.63 243.287 translate
0 rotate
0 0 m /T glyphshow
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

1149.42 359.711 translate
0 rotate
0 0 m /parenleft glyphshow
8.3252 0 m /f glyphshow
16.6504 0 m /parenright glyphshow
grestore
1.500 setlinewidth
1 setlinejoin
0 setlinecap
[9.6 2.4 1.5 2.4] 0 setdash
0.749 0.212 0.047 setrgbcolor
gsave
1014.530769 326.41625 m
1039.530769 326.41625 l
1064.530769 326.41625 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
1039.53 326.416 o
grestore
0.000 setgray
gsave
1084.53 317.666 translate
0 rotate
/TimesNewRomanPSMT 25.0 selectfont
0 0.453125 moveto
/T glyphshow
/TimesNewRomanPSMT 17.5 selectfont
15.6467 -5.9875 moveto
/C glyphshow
/TimesNewRomanPSMT 12.25 selectfont
27.5821 -10.4959 moveto
/two glyphshow
grestore
1.500 setlinewidth
[5.55 2.4] 0 setdash
0.937 0.424 0.000 setrgbcolor
gsave
1170.530769 326.41625 m
1195.530769 326.41625 l
1220.530769 326.41625 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-8 -8 m
8 -8 l
8 8 l
-8 8 l
cl

gsave
grestore
stroke
grestore
} bind def
1195.53 326.416 o
grestore
0.000 setgray
gsave
1240.53 317.666 translate
0 rotate
/TimesNewRomanPSMT 25.0 selectfont
0 0.453125 moveto
/T glyphshow
/TimesNewRomanPSMT 17.5 selectfont
15.6467 -5.9875 moveto
/C glyphshow
/TimesNewRomanPSMT 12.25 selectfont
27.5821 -10.4959 moveto
/one glyphshow
grestore

end
showpage
