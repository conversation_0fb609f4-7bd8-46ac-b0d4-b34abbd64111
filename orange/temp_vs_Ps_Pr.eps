%!PS-Adobe-3.0 EPSF-3.0
%%Title: temp_vs_Ps_Pr.eps
%%Creator: Mat<PERSON>lotlib v3.7.1, https://matplotlib.org/
%%CreationDate: Wed Aug 28 19:00:46 2024
%%Orientation: portrait
%%BoundingBox: 39 178 573 614
%%HiResBoundingBox: 39.600000 178.916875 572.400000 613.083125
%%EndComments
%%BeginProlog
/mpldict 11 dict def
mpldict begin
/_d { bind def } bind def
/m { moveto } _d
/l { lineto } _d
/r { rlineto } _d
/c { curveto } _d
/cl { closepath } _d
/ce { closepath eofill } _d
/box {
      m
      1 index 0 r
      0 exch r
      neg 0 r
      cl
    } _d
/clipbox {
      box
      clip
      newpath
    } _d
/sc { setcachedevice } _d
%!PS-TrueTypeFont-1.0-2.53740
%%Title: unknown
%%Creator: Converted from TrueType to type 42 by PPR
15 dict begin
/FontName /TimesNewRomanPSMT def
/PaintType 0 def
/FontMatrix[1 0 0 1 0 0]def
/FontBBox[-568 -307 2028 1007]def
/FontType 42 def
/Encoding StandardEncoding def
/FontInfo 10 dict dup begin
/FamilyName (unknown) def
/FullName (unknown) def
/Weight (unknown) def
/Version (unknown) def
/ItalicAngle 0.0 def
/isFixedPitch false def
/UnderlinePosition -223 def
/UnderlineThickness 100 def
end readonly def
/sfnts[<00010000000900800003001063767420F34DDA810000009C000007C46670676D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>]def
/CharStrings 38 dict dup begin
/.notdef 0 def
/space 3 def
/parenleft 4 def
/parenright 5 def
/period 6 def
/slash 7 def
/zero 8 def
/one 9 def
/two 10 def
/three 11 def
/four 12 def
/five 13 def
/six 14 def
/nine 15 def
/mu 39 def
/C 16 def
/K 17 def
/P 18 def
/R 19 def
/T 20 def
/bracketleft 21 def
/bracketright 22 def
/a 23 def
/b 24 def
/c 25 def
/e 26 def
/f 27 def
/i 28 def
/l 29 def
/m 30 def
/n 31 def
/o 32 def
/p 33 def
/r 34 def
/s 35 def
/t 36 def
/u 37 def
/z 38 def
end readonly def

systemdict/resourcestatus known
 {42 /FontType resourcestatus
   {pop pop false}{true}ifelse}
 {true}ifelse
{/TrueDict where{pop}{(%%[ Error: no TrueType rasterizer ]%%)= flush}ifelse
/FontType 3 def
 /TrueState 271 string def
 TrueDict begin sfnts save
 72 0 matrix defaultmatrix dtransform dup
 mul exch dup mul add sqrt cvi 0 72 matrix
 defaultmatrix dtransform dup mul exch dup
 mul add sqrt cvi 3 -1 roll restore
 TrueState initer end
 /BuildGlyph{exch begin
  CharStrings dup 2 index known
    {exch}{exch pop /.notdef}ifelse
  get dup xcheck
    {currentdict systemdict begin begin exec end end}
    {TrueDict begin /bander load cvlit exch TrueState render end}
    ifelse
 end}bind def
 /BuildChar{
  1 index /Encoding get exch get
  1 index /BuildGlyph get exec
 }bind def
}if

FontName currentdict end definefont pop
end
%%EndProlog
mpldict begin
39.6 178.917 translate
532.8 434.166 0 0 clipbox
gsave
0 -0 m
532.8 -0 l
532.8 434.16625 l
0 434.16625 l
cl
1.000 setgray
fill
grestore
gsave
79.2 66.60625 m
525.6 66.60625 l
525.6 426.96625 l
79.2 426.96625 l
cl
1.000 setgray
fill
grestore
1.000 setlinewidth
1 setlinejoin
0 setlinecap
[] 0 setdash
0.031 0.659 0.255 setrgbcolor
gsave
446.4 360.36 79.2 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

-8.660254 -8.660254 m
8.660254 -8.660254 l
8.660254 8.660254 l
-8.660254 8.660254 l
cl

gsave
0.031 0.659 0.255 setrgbcolor
fill
grestore
stroke
grestore
} bind def
152.925 82.9863 o
206.195 147.52 o
248.811 212.055 o
291.426 241.388 o
grestore
2.500 setlinewidth
0.031 0.416 0.416 setrgbcolor
gsave
446.4 360.36 79.2 66.606 clipbox
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-11.18034 0 m
11.18034 0 l
0 -11.18034 m
0 11.18034 l

gsave
0.031 0.416 0.416 setrgbcolor
fill
grestore
stroke
grestore
} bind def
207.047 258.988 o
grestore
1.000 setlinewidth
0.031 0.659 0.659 setrgbcolor
gsave
446.4 360.36 79.2 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 13.228757 m
-2.970041 4.08791 l
-12.581295 4.087911 l
-4.805627 -1.561443 l
-7.775668 -10.702289 l
-0 -5.052935 l
7.775668 -10.702289 l
4.805627 -1.561443 l
12.581295 4.087911 l
2.970041 4.08791 l
cl

gsave
0.031 0.659 0.659 setrgbcolor
fill
grestore
stroke
grestore
} bind def
207.047 159.254 o
grestore
2.500 setlinewidth
0.000 setgray
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

72.95 39.2469 translate
0 rotate
0 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
207.047 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
207.047 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

188.297 39.2469 translate
0 rotate
0 0 m /three glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
334.895 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
334.895 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

316.145 39.2469 translate
0 rotate
0 0 m /six glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
462.742 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
462.742 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

443.992 39.2469 translate
0 rotate
0 0 m /nine glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
111.162 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
111.162 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
143.124 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
143.124 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
175.085 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
175.085 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
239.009 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
239.009 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
270.971 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
270.971 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
302.933 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
302.933 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
366.856 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
366.856 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
398.818 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
398.818 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
430.78 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
430.78 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
494.704 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
494.704 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

218.939 12.5438 translate
0 rotate
0 0 m /T glyphshow
13.521 0 m /e glyphshow
24.6172 0 m /m glyphshow
44.063 0 m /p glyphshow
56.563 0 m /e glyphshow
67.6592 0 m /r glyphshow
75.9844 0 m /a glyphshow
87.0806 0 m /t glyphshow
94.0264 0 m /u glyphshow
106.526 0 m /r glyphshow
114.852 0 m /e glyphshow
125.948 0 m /space glyphshow
132.198 0 m /parenleft glyphshow
140.523 0 m /K glyphshow
158.577 0 m /parenright glyphshow
grestore
2.500 setlinewidth
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 124.053 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.6 124.053 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

44.2 115.374 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 182.721 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.6 182.721 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

44.2 174.041 translate
0 rotate
0 0 m /two glyphshow
12.5 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 241.388 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.6 241.388 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

44.2 232.709 translate
0 rotate
0 0 m /three glyphshow
12.5 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 300.056 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.6 300.056 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

44.2 291.376 translate
0 rotate
0 0 m /four glyphshow
12.5 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 358.723 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.6 358.723 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

44.2 350.043 translate
0 rotate
0 0 m /five glyphshow
12.5 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 417.39 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.6 417.39 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

44.2 408.711 translate
0 rotate
0 0 m /six glyphshow
12.5 0 m /zero glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 77.1195 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.6 77.1195 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 88.853 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.6 88.853 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 100.586 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.6 100.586 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 112.32 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.6 112.32 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 135.787 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.6 135.787 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 147.52 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.6 147.52 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 159.254 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.6 159.254 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 170.987 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.6 170.987 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 194.454 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.6 194.454 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 206.188 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.6 206.188 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 217.921 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.6 217.921 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 229.655 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.6 229.655 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 253.122 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.6 253.122 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 264.855 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.6 264.855 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 276.589 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.6 276.589 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 288.322 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.6 288.322 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 311.789 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.6 311.789 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 323.523 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.6 323.523 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 335.256 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.6 335.256 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 346.99 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.6 346.99 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 370.457 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.6 370.457 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 382.19 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.6 382.19 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 393.924 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.6 393.924 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 405.657 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.6 405.657 o
grestore
gsave
34.2 135.786 translate
90 rotate
/TimesNewRomanPSMT 25.0 selectfont
0 0.148438 moveto
/P glyphshow
13.9038 0.148438 moveto
/o glyphshow
26.4038 0.148438 moveto
/l glyphshow
33.3496 0.148438 moveto
/a glyphshow
44.4458 0.148438 moveto
/r glyphshow
52.771 0.148438 moveto
/i glyphshow
59.7168 0.148438 moveto
/z glyphshow
70.813 0.148438 moveto
/a glyphshow
81.9092 0.148438 moveto
/t glyphshow
88.855 0.148438 moveto
/i glyphshow
95.8008 0.148438 moveto
/o glyphshow
108.301 0.148438 moveto
/n glyphshow
120.801 0.148438 moveto
/space glyphshow
127.051 0.148438 moveto
/parenleft glyphshow
135.376 0.148438 moveto
/mu glyphshow
148.779 0.148438 moveto
/C glyphshow
165.454 0.148438 moveto
/slash glyphshow
172.4 0.148438 moveto
/c glyphshow
183.496 0.148438 moveto
/m glyphshow
/TimesNewRomanPSMT 17.5 selectfont
203.318 15.1766 moveto
/two glyphshow
/TimesNewRomanPSMT 25.0 selectfont
213.141 0.148438 moveto
/parenright glyphshow
grestore
[9.6 2.4 1.5 2.4] 0 setdash
0.937 0.424 0.000 setrgbcolor
gsave
446.4 360.36 79.2 66.606 clipbox
100.507876 410.58625 m
121.815752 409.493713 l
164.431504 407.143283 l
207.047255 404.710175 l
249.663007 402.117615 l
292.278759 399.613639 l
334.894511 396.967929 l
377.510263 393.932449 l
420.126014 390.660745 l
505.357518 384.001705 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
446.4 360.36 79.2 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
100.508 410.586 o
121.816 409.494 o
164.432 407.143 o
207.047 404.71 o
249.663 402.118 o
292.279 399.614 o
334.895 396.968 o
377.51 393.932 o
420.126 390.661 o
505.358 384.002 o
grestore
2.500 setlinewidth
0 setlinejoin
2 setlinecap
0.000 setgray
gsave
79.2 66.60625 m
79.2 426.96625 l
stroke
grestore
gsave
525.6 66.60625 m
525.6 426.96625 l
stroke
grestore
gsave
79.2 66.60625 m
525.6 66.60625 l
stroke
grestore
gsave
79.2 426.96625 m
525.6 426.96625 l
stroke
grestore
1.500 setlinewidth
1 setlinejoin
0 setlinecap
[9.6 2.4 1.5 2.4] 0 setdash
0.937 0.424 0.000 setrgbcolor
gsave
394.4125 182.45 m
414.4125 182.45 l
434.4125 182.45 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
414.413 182.45 o
grestore
0.000 setgray
gsave
450.413 175.45 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.75 moveto
/P glyphshow
/TimesNewRomanPSMT 14.0 selectfont
11.4236 -4.4025 moveto
/s glyphshow
grestore
0.031 0.659 0.255 setrgbcolor
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

-8.660254 -8.660254 m
8.660254 -8.660254 l
8.660254 8.660254 l
-8.660254 8.660254 l
cl

gsave
0.031 0.659 0.255 setrgbcolor
fill
grestore
stroke
grestore
} bind def
414.413 150.7 o
grestore
0.000 setgray
/TimesNewRomanPSMT 20.000 selectfont
gsave

450.413 145.45 translate
0 rotate
0 0 m /R glyphshow
13.3398 0 m /e glyphshow
22.2168 0 m /f glyphshow
28.877 0 m /period glyphshow
33.877 0 m /bracketleft glyphshow
40.5371 0 m /a glyphshow
49.4141 0 m /bracketright glyphshow
grestore
2.500 setlinewidth
0.031 0.416 0.416 setrgbcolor
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-11.18034 0 m
11.18034 0 l
0 -11.18034 m
0 11.18034 l

gsave
0.031 0.416 0.416 setrgbcolor
fill
grestore
stroke
grestore
} bind def
414.413 122.419 o
grestore
0.000 setgray
/TimesNewRomanPSMT 20.000 selectfont
gsave

450.413 117.169 translate
0 rotate
0 0 m /R glyphshow
13.3398 0 m /e glyphshow
22.2168 0 m /f glyphshow
28.877 0 m /period glyphshow
33.877 0 m /bracketleft glyphshow
40.5371 0 m /b glyphshow
50.5371 0 m /bracketright glyphshow
grestore
1.000 setlinewidth
0.031 0.659 0.659 setrgbcolor
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 13.228757 m
-2.970041 4.08791 l
-12.581295 4.087911 l
-4.805627 -1.561443 l
-7.775668 -10.702289 l
-0 -5.052935 l
7.775668 -10.702289 l
4.805627 -1.561443 l
12.581295 4.087911 l
2.970041 4.08791 l
cl

gsave
0.031 0.659 0.659 setrgbcolor
fill
grestore
stroke
grestore
} bind def
414.413 94.1375 o
grestore
0.000 setgray
/TimesNewRomanPSMT 20.000 selectfont
gsave

450.413 88.8875 translate
0 rotate
0 0 m /R glyphshow
13.3398 0 m /e glyphshow
22.2168 0 m /f glyphshow
28.877 0 m /period glyphshow
33.877 0 m /bracketleft glyphshow
40.5371 0 m /c glyphshow
49.4141 0 m /bracketright glyphshow
grestore

end
showpage
