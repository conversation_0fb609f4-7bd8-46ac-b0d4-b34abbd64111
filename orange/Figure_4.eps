%!PS-Adobe-3.0 EPSF-3.0
%%Title: Figure_4.eps
%%Creator: Matplotlib v3.7.1, https://matplotlib.org/
%%CreationDate: Wed Dec 25 12:48:02 2024
%%Orientation: portrait
%%BoundingBox: -194 -21 807 814
%%HiResBoundingBox: -194.712507 -21.709670 806.712507 813.709670
%%EndComments
%%BeginProlog
/mpldict 11 dict def
mpldict begin
/_d { bind def } bind def
/m { moveto } _d
/l { lineto } _d
/r { rlineto } _d
/c { curveto } _d
/cl { closepath } _d
/ce { closepath eofill } _d
/box {
      m
      1 index 0 r
      0 exch r
      neg 0 r
      cl
    } _d
/clipbox {
      box
      clip
      newpath
    } _d
/sc { setcachedevice } _d
%!PS-TrueTypeFont-1.0-2.53740
%%Title: unknown
%%Creator: Converted from TrueType to type 42 by PPR
15 dict begin
/FontName /TimesNewRomanPSMT def
/PaintType 0 def
/FontMatrix[1 0 0 1 0 0]def
/FontBBox[-568 -307 2028 1007]def
/FontType 42 def
/Encoding StandardEncoding def
/FontInfo 10 dict dup begin
/FamilyName (unknown) def
/FullName (unknown) def
/Weight (unknown) def
/Version (unknown) def
/ItalicAngle 0.0 def
/isFixedPitch false def
/UnderlinePosition -223 def
/UnderlineThickness 100 def
end readonly def
/sfnts[<00010000000900800003001063767420F34DDA810000009C000007C46670676D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>]def
/CharStrings 46 dict dup begin
/.notdef 0 def
/minus 46 def
/space 3 def
/parenleft 4 def
/parenright 5 def
/period 6 def
/slash 7 def
/zero 8 def
/one 9 def
/two 10 def
/three 11 def
/four 12 def
/five 13 def
/six 14 def
/eight 15 def
/mu 47 def
/C 16 def
/E 17 def
/F 18 def
/H 19 def
/K 20 def
/M 21 def
/P 22 def
/R 23 def
/S 24 def
/T 25 def
/V 26 def
/bracketleft 27 def
/bracketright 28 def
/a 29 def
/b 30 def
/c 31 def
/d 32 def
/e 33 def
/f 34 def
/i 35 def
/l 36 def
/m 37 def
/n 38 def
/o 39 def
/p 40 def
/r 41 def
/t 42 def
/u 43 def
/x 44 def
/z 45 def
end readonly def

systemdict/resourcestatus known
 {42 /FontType resourcestatus
   {pop pop false}{true}ifelse}
 {true}ifelse
{/TrueDict where{pop}{(%%[ Error: no TrueType rasterizer ]%%)= flush}ifelse
/FontType 3 def
 /TrueState 271 string def
 TrueDict begin sfnts save
 72 0 matrix defaultmatrix dtransform dup
 mul exch dup mul add sqrt cvi 0 72 matrix
 defaultmatrix dtransform dup mul exch dup
 mul add sqrt cvi 3 -1 roll restore
 TrueState initer end
 /BuildGlyph{exch begin
  CharStrings dup 2 index known
    {exch}{exch pop /.notdef}ifelse
  get dup xcheck
    {currentdict systemdict begin begin exec end end}
    {TrueDict begin /bander load cvlit exch TrueState render end}
    ifelse
 end}bind def
 /BuildChar{
  1 index /Encoding get exch get
  1 index /BuildGlyph get exec
 }bind def
}if

FontName currentdict end definefont pop
end
%%EndProlog
mpldict begin
-194.713 -21.71 translate
1001.425 835.419 0 0 clipbox
gsave
0 0 m
1001.425015 0 l
1001.425015 835.419339 l
0 835.419339 l
cl
1.000 setgray
fill
grestore
gsave
93.29375 493.154821 m
465.29375 493.154821 l
465.29375 787.32625 l
93.29375 787.32625 l
cl
1.000 setgray
fill
grestore
2.500 setlinewidth
1 setlinejoin
0 setlinecap
[] 0 setdash
0.000 setgray
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
173.612 493.155 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
173.612 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

150.94 465.795 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /two glyphshow
26.5991 0 m /period glyphshow
32.8491 0 m /five glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
279.294 493.155 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
279.294 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

263.669 465.795 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
384.976 493.155 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
384.976 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

369.351 465.795 translate
0 rotate
0 0 m /two glyphshow
12.5 0 m /period glyphshow
18.75 0 m /five glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
110.203 493.155 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
110.203 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
131.339 493.155 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
131.339 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
152.476 493.155 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
152.476 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
194.748 493.155 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
194.748 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
215.885 493.155 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
215.885 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
237.021 493.155 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
237.021 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
258.157 493.155 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
258.157 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
300.43 493.155 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
300.43 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
321.566 493.155 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
321.566 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
342.703 493.155 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
342.703 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
363.839 493.155 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
363.839 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
406.112 493.155 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
406.112 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
427.248 493.155 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
427.248 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
448.385 493.155 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
448.385 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

161.247 439.092 translate
0 rotate
0 0 m /E glyphshow
15.271 0 m /l glyphshow
22.2168 0 m /e glyphshow
33.313 0 m /c glyphshow
44.4092 0 m /t glyphshow
51.355 0 m /r glyphshow
59.6802 0 m /i glyphshow
66.626 0 m /c glyphshow
77.7222 0 m /space glyphshow
83.9722 0 m /F glyphshow
97.876 0 m /i glyphshow
104.822 0 m /e glyphshow
115.918 0 m /l glyphshow
122.864 0 m /d glyphshow
135.364 0 m /space glyphshow
141.614 0 m /parenleft glyphshow
149.939 0 m /M glyphshow
172.168 0 m /V glyphshow
190.222 0 m /slash glyphshow
197.168 0 m /c glyphshow
208.264 0 m /m glyphshow
227.71 0 m /parenright glyphshow
grestore
2.500 setlinewidth
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 503.457 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.294 503.457 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

44.2 494.778 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /six glyphshow
26.5991 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 549.057 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.294 549.057 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

44.2 540.377 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /four glyphshow
26.5991 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 594.657 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.294 594.657 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

44.2 585.977 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /two glyphshow
26.5991 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 640.257 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.294 640.257 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

70.7938 631.577 translate
0 rotate
0 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 685.856 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.294 685.856 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

58.2938 677.177 translate
0 rotate
0 0 m /two glyphshow
12.5 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 731.456 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.294 731.456 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

58.2938 722.776 translate
0 rotate
0 0 m /four glyphshow
12.5 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 777.056 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.294 777.056 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

58.2938 768.376 translate
0 rotate
0 0 m /six glyphshow
12.5 0 m /zero glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 514.857 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.294 514.857 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 526.257 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.294 526.257 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 537.657 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.294 537.657 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 560.457 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.294 560.457 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 571.857 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.294 571.857 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 583.257 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.294 583.257 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 606.057 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.294 606.057 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 617.457 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.294 617.457 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 628.857 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.294 628.857 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 651.657 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.294 651.657 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 663.056 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.294 663.056 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 674.456 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.294 674.456 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 697.256 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.294 697.256 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 708.656 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.294 708.656 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 720.056 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.294 720.056 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 742.856 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.294 742.856 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 754.256 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.294 754.256 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 765.656 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.294 765.656 o
grestore
gsave
34.2 529.241 translate
90 rotate
/TimesNewRomanPSMT 25.0 selectfont
0 0.148438 moveto
/P glyphshow
13.9038 0.148438 moveto
/o glyphshow
26.4038 0.148438 moveto
/l glyphshow
33.3496 0.148438 moveto
/a glyphshow
44.4458 0.148438 moveto
/r glyphshow
52.771 0.148438 moveto
/i glyphshow
59.7168 0.148438 moveto
/z glyphshow
70.813 0.148438 moveto
/a glyphshow
81.9092 0.148438 moveto
/t glyphshow
88.855 0.148438 moveto
/i glyphshow
95.8008 0.148438 moveto
/o glyphshow
108.301 0.148438 moveto
/n glyphshow
120.801 0.148438 moveto
/space glyphshow
127.051 0.148438 moveto
/parenleft glyphshow
135.376 0.148438 moveto
/mu glyphshow
148.779 0.148438 moveto
/C glyphshow
165.454 0.148438 moveto
/slash glyphshow
172.4 0.148438 moveto
/c glyphshow
183.496 0.148438 moveto
/m glyphshow
/TimesNewRomanPSMT 17.5 selectfont
203.318 15.1766 moveto
/two glyphshow
/TimesNewRomanPSMT 25.0 selectfont
213.141 0.148438 moveto
/parenright glyphshow
grestore
2 setlinecap
0.749 0.212 0.047 setrgbcolor
gsave
372 294.171 93.294 493.155 clipbox
448.384659 773.950231 m
439.930114 773.105638 l
431.475568 772.256454 l
423.021023 771.386615 l
414.566477 770.475464 l
406.111932 769.555132 l
397.657386 768.623325 l
389.202841 767.686928 l
380.748295 766.683974 l
372.29375 765.726921 l
363.839205 764.714786 l
355.384659 763.698061 l
346.930114 762.617073 l
338.475568 761.53838 l
330.021023 760.404605 l
321.566477 759.286896 l
313.111932 758.11181 l
304.657386 756.888526 l
296.202841 755.646882 l
287.748295 754.308844 l
279.29375 752.982282 l
270.839205 751.614407 l
262.384659 750.161615 l
253.930114 748.603248 l
245.475568 747.056357 l
237.021023 745.399302 l
228.566477 743.664213 l
220.111932 741.768468 l
211.657386 739.831411 l
203.202841 737.685501 l
194.748295 735.319263 l
186.29375 732.73499 l
177.839205 729.866127 l
169.384659 726.398245 l
160.930114 722.205114 l
152.475568 715.822466 l
144.021023 510.023968 l
135.566477 509.103637 l
127.111932 508.281994 l
118.657386 507.391499 l
110.202841 506.52625 l
118.657386 507.407565 l
127.111932 508.238388 l
135.566477 509.1679 l
144.021023 510.030854 l
152.475568 510.960365 l
160.930114 511.899058 l
169.384659 512.787258 l
177.839205 513.774147 l
186.29375 514.767921 l
194.748295 515.7594 l
203.202841 516.799076 l
211.657386 517.907605 l
220.111932 518.951872 l
228.566477 520.090237 l
237.021023 521.221716 l
245.475568 522.403688 l
253.930114 523.624677 l
262.384659 524.836484 l
270.839205 526.163047 l
279.29375 527.526331 l
287.748295 528.875844 l
296.202841 530.321752 l
304.657386 531.861757 l
313.111932 533.44996 l
321.566477 535.113901 l
330.021023 536.821449 l
338.475568 538.721784 l
346.930114 540.716218 l
355.384659 542.885079 l
363.839205 545.19853 l
372.29375 547.773622 l
380.748295 550.62642 l
389.202841 554.128729 l
397.657386 558.33563 l
406.111932 564.697622 l
414.566477 770.503005 l
423.021023 771.370549 l
431.475568 772.258749 l
439.930114 773.110228 l
448.384659 773.954821 l
448.384659 773.954821 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
0 setlinecap
gsave
372 294.171 93.294 493.155 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

0 5 m
-5 -5 l
5 -5 l
cl

gsave
0.749 0.212 0.047 setrgbcolor
fill
grestore
stroke
grestore
} bind def
448.385 773.95 o
439.93 773.106 o
431.476 772.256 o
423.021 771.387 o
414.566 770.475 o
406.112 769.555 o
397.657 768.623 o
389.203 767.687 o
380.748 766.684 o
372.294 765.727 o
363.839 764.715 o
355.385 763.698 o
346.93 762.617 o
338.476 761.538 o
330.021 760.405 o
321.566 759.287 o
313.112 758.112 o
304.657 756.889 o
296.203 755.647 o
287.748 754.309 o
279.294 752.982 o
270.839 751.614 o
262.385 750.162 o
253.93 748.603 o
245.476 747.056 o
237.021 745.399 o
228.566 743.664 o
220.112 741.768 o
211.657 739.831 o
203.203 737.686 o
194.748 735.319 o
186.294 732.735 o
177.839 729.866 o
169.385 726.398 o
160.93 722.205 o
152.476 715.822 o
144.021 510.024 o
135.566 509.104 o
127.112 508.282 o
118.657 507.391 o
110.203 506.526 o
118.657 507.408 o
127.112 508.238 o
135.566 509.168 o
144.021 510.031 o
152.476 510.96 o
160.93 511.899 o
169.385 512.787 o
177.839 513.774 o
186.294 514.768 o
194.748 515.759 o
203.203 516.799 o
211.657 517.908 o
220.112 518.952 o
228.566 520.09 o
237.021 521.222 o
245.476 522.404 o
253.93 523.625 o
262.385 524.836 o
270.839 526.163 o
279.294 527.526 o
287.748 528.876 o
296.203 530.322 o
304.657 531.862 o
313.112 533.45 o
321.566 535.114 o
330.021 536.821 o
338.476 538.722 o
346.93 540.716 o
355.385 542.885 o
363.839 545.199 o
372.294 547.774 o
380.748 550.626 o
389.203 554.129 o
397.657 558.336 o
406.112 564.698 o
414.566 770.503 o
423.021 771.371 o
431.476 772.259 o
439.93 773.11 o
448.385 773.955 o
448.385 773.955 o
grestore
1.500 setlinewidth
1 setlinejoin
[5.55 2.4] 0 setdash
1.000 0.702 0.000 setrgbcolor
gsave
372 294.171 93.294 493.155 clipbox
448.384659 771.173171 m
439.930114 770.179397 l
431.475568 769.295787 l
423.021023 768.290537 l
414.566477 767.347255 l
406.111932 766.420038 l
397.657386 765.32987 l
389.202841 764.329211 l
380.748295 763.259698 l
372.29375 762.128218 l
363.839205 760.999034 l
355.384659 760.002964 l
346.930114 758.715418 l
338.475568 757.560988 l
330.021023 756.22295 l
321.566477 755.013437 l
313.111932 753.611136 l
304.657386 752.236377 l
296.202841 750.765224 l
287.748295 749.378989 l
279.29375 747.687507 l
270.839205 746.083239 l
262.384659 744.288478 l
253.930114 742.544209 l
245.475568 740.522234 l
237.021023 738.217962 l
228.566477 736.035331 l
220.111932 733.519912 l
211.657386 730.437605 l
203.202841 727.098247 l
194.748295 722.755936 l
186.29375 716.446731 l
177.839205 517.200717 l
169.384659 516.13809 l
160.930114 515.171857 l
152.475568 514.159722 l
144.021023 513.182013 l
135.566477 512.208895 l
127.111932 511.155448 l
118.657386 510.324625 l
110.202841 509.392818 l
118.657386 510.244297 l
127.111932 511.254137 l
135.566477 512.188239 l
144.021023 513.124636 l
152.475568 514.129886 l
160.930114 515.151201 l
169.384659 516.195468 l
177.839205 517.271865 l
186.29375 518.366623 l
194.748295 519.445316 l
203.202841 520.636468 l
211.657386 521.800079 l
220.111932 522.943035 l
228.566477 524.274187 l
237.021023 525.449274 l
245.475568 526.801082 l
253.930114 528.297481 l
262.384659 529.617159 l
270.839205 531.198476 l
279.29375 532.80504 l
287.748295 534.475866 l
296.202841 536.210955 l
304.657386 538.026371 l
313.111932 540.098838 l
321.566477 542.120813 l
330.021023 544.461806 l
338.475568 547.080505 l
346.930114 550.00445 l
355.384659 553.419545 l
363.839205 557.598906 l
372.29375 563.825487 l
380.748295 763.229862 l
389.202841 764.352161 l
397.657386 765.3161 l
406.111932 766.378727 l
414.566477 767.308238 l
423.021023 768.345619 l
431.475568 769.286607 l
439.930114 770.236774 l
448.384659 771.090548 l
448.384659 771.090548 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
372 294.171 93.294 493.155 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-5 -5 m
5 -5 l
5 5 l
-5 5 l
cl

gsave
grestore
stroke
grestore
} bind def
448.385 771.173 o
439.93 770.179 o
431.476 769.296 o
423.021 768.291 o
414.566 767.347 o
406.112 766.42 o
397.657 765.33 o
389.203 764.329 o
380.748 763.26 o
372.294 762.128 o
363.839 760.999 o
355.385 760.003 o
346.93 758.715 o
338.476 757.561 o
330.021 756.223 o
321.566 755.013 o
313.112 753.611 o
304.657 752.236 o
296.203 750.765 o
287.748 749.379 o
279.294 747.688 o
270.839 746.083 o
262.385 744.288 o
253.93 742.544 o
245.476 740.522 o
237.021 738.218 o
228.566 736.035 o
220.112 733.52 o
211.657 730.438 o
203.203 727.098 o
194.748 722.756 o
186.294 716.447 o
177.839 517.201 o
169.385 516.138 o
160.93 515.172 o
152.476 514.16 o
144.021 513.182 o
135.566 512.209 o
127.112 511.155 o
118.657 510.325 o
110.203 509.393 o
118.657 510.244 o
127.112 511.254 o
135.566 512.188 o
144.021 513.125 o
152.476 514.13 o
160.93 515.151 o
169.385 516.195 o
177.839 517.272 o
186.294 518.367 o
194.748 519.445 o
203.203 520.636 o
211.657 521.8 o
220.112 522.943 o
228.566 524.274 o
237.021 525.449 o
245.476 526.801 o
253.93 528.297 o
262.385 529.617 o
270.839 531.198 o
279.294 532.805 o
287.748 534.476 o
296.203 536.211 o
304.657 538.026 o
313.112 540.099 o
321.566 542.121 o
330.021 544.462 o
338.476 547.081 o
346.93 550.004 o
355.385 553.42 o
363.839 557.599 o
372.294 563.825 o
380.748 763.23 o
389.203 764.352 o
397.657 765.316 o
406.112 766.379 o
414.566 767.308 o
423.021 768.346 o
431.476 769.287 o
439.93 770.237 o
448.385 771.091 o
448.385 771.091 o
grestore
1.500 setlinewidth
1 setlinejoin
[1.5 2.475] 0 setdash
0.031 0.659 0.255 setrgbcolor
gsave
372 294.171 93.294 493.155 clipbox
448.384659 769.082344 m
439.930114 768.152832 l
431.475568 767.090205 l
423.021023 766.052824 l
414.566477 765.017738 l
406.111932 764.014783 l
397.657386 762.924615 l
389.202841 761.834447 l
380.748295 760.647885 l
372.29375 759.456733 l
363.839205 758.224269 l
355.384659 756.966559 l
346.930114 755.669833 l
338.475568 754.405238 l
330.021023 753.046544 l
321.566477 751.54326 l
313.111932 750.193746 l
304.657386 748.543576 l
296.202841 746.932422 l
287.748295 745.259301 l
279.29375 743.386507 l
270.839205 741.32781 l
262.384659 739.42977 l
253.930114 737.040581 l
245.475568 734.564178 l
237.021023 731.84679 l
228.566477 728.411039 l
220.111932 724.704468 l
211.657386 719.400513 l
203.202841 523.45484 l
194.748295 522.20631 l
186.29375 520.987617 l
177.839205 519.881383 l
169.384659 518.722363 l
160.930114 517.684982 l
152.475568 516.482354 l
144.021023 515.392186 l
135.566477 514.469559 l
127.111932 513.436768 l
118.657386 512.516437 l
110.202841 511.394138 l
118.657386 512.422338 l
127.111932 513.445949 l
135.566477 514.499395 l
144.021023 515.566613 l
152.475568 516.558092 l
160.930114 517.600063 l
169.384659 518.78892 l
177.839205 519.863023 l
186.29375 520.902699 l
194.748295 522.277458 l
203.202841 523.553529 l
211.657386 524.689599 l
220.111932 526.126326 l
228.566477 527.402396 l
237.021023 528.917156 l
245.475568 530.353883 l
253.930114 531.912249 l
262.384659 533.537174 l
270.839205 535.331935 l
279.29375 537.170303 l
287.748295 539.077523 l
296.202841 541.172941 l
304.657386 543.507048 l
313.111932 545.875582 l
321.566477 548.762806 l
330.021023 551.840523 l
338.475568 555.918899 l
346.930114 561.37892 l
355.384659 757.11115 l
363.839205 758.254105 l
372.29375 759.548536 l
380.748295 760.65477 l
389.202841 761.724282 l
397.657386 762.887894 l
406.111932 764.044619 l
414.566477 765.026918 l
423.021023 766.174464 l
431.475568 767.051188 l
439.930114 768.001356 l
448.384659 769.057098 l
448.384659 769.057098 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
372 294.171 93.294 493.155 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -5 m
1.326016 -5 2.597899 -4.473168 3.535534 -3.535534 c
4.473168 -2.597899 5 -1.326016 5 0 c
5 1.326016 4.473168 2.597899 3.535534 3.535534 c
2.597899 4.473168 1.326016 5 0 5 c
-1.326016 5 -2.597899 4.473168 -3.535534 3.535534 c
-4.473168 2.597899 -5 1.326016 -5 0 c
-5 -1.326016 -4.473168 -2.597899 -3.535534 -3.535534 c
-2.597899 -4.473168 -1.326016 -5 0 -5 c
cl

gsave
grestore
stroke
grestore
} bind def
448.385 769.082 o
439.93 768.153 o
431.476 767.09 o
423.021 766.053 o
414.566 765.018 o
406.112 764.015 o
397.657 762.925 o
389.203 761.834 o
380.748 760.648 o
372.294 759.457 o
363.839 758.224 o
355.385 756.967 o
346.93 755.67 o
338.476 754.405 o
330.021 753.047 o
321.566 751.543 o
313.112 750.194 o
304.657 748.544 o
296.203 746.932 o
287.748 745.259 o
279.294 743.387 o
270.839 741.328 o
262.385 739.43 o
253.93 737.041 o
245.476 734.564 o
237.021 731.847 o
228.566 728.411 o
220.112 724.704 o
211.657 719.401 o
203.203 523.455 o
194.748 522.206 o
186.294 520.988 o
177.839 519.881 o
169.385 518.722 o
160.93 517.685 o
152.476 516.482 o
144.021 515.392 o
135.566 514.47 o
127.112 513.437 o
118.657 512.516 o
110.203 511.394 o
118.657 512.422 o
127.112 513.446 o
135.566 514.499 o
144.021 515.567 o
152.476 516.558 o
160.93 517.6 o
169.385 518.789 o
177.839 519.863 o
186.294 520.903 o
194.748 522.277 o
203.203 523.554 o
211.657 524.69 o
220.112 526.126 o
228.566 527.402 o
237.021 528.917 o
245.476 530.354 o
253.93 531.912 o
262.385 533.537 o
270.839 535.332 o
279.294 537.17 o
287.748 539.078 o
296.203 541.173 o
304.657 543.507 o
313.112 545.876 o
321.566 548.763 o
330.021 551.841 o
338.476 555.919 o
346.93 561.379 o
355.385 757.111 o
363.839 758.254 o
372.294 759.549 o
380.748 760.655 o
389.203 761.724 o
397.657 762.888 o
406.112 764.045 o
414.566 765.027 o
423.021 766.174 o
431.476 767.051 o
439.93 768.001 o
448.385 769.057 o
448.385 769.057 o
grestore
1.500 setlinewidth
[9.6 2.4 1.5 2.4] 0 setdash
0.031 0.235 0.235 setrgbcolor
gsave
372 294.171 93.294 493.155 clipbox
448.384659 766.71381 m
439.930114 765.671839 l
431.475568 764.554129 l
423.021023 763.633798 l
414.566477 762.42199 l
406.111932 761.230838 l
397.657386 760.080997 l
389.202841 759.002305 l
380.748295 757.726234 l
372.29375 756.498361 l
363.839205 755.130487 l
355.384659 753.748842 l
346.930114 752.229492 l
338.475568 750.760634 l
330.021023 749.206857 l
321.566477 747.623245 l
313.111932 745.85373 l
304.657386 744.116346 l
296.202841 742.158633 l
287.748295 740.028789 l
279.29375 737.850748 l
270.839205 735.326148 l
262.384659 732.640892 l
253.930114 729.43006 l
245.475568 725.966768 l
237.021023 721.275602 l
228.566477 714.431641 l
220.111932 529.743389 l
211.657386 528.171251 l
203.202841 526.782721 l
194.748295 525.465339 l
186.29375 524.164023 l
177.839205 522.83746 l
169.384659 521.604997 l
160.930114 520.367943 l
152.475568 519.165315 l
144.021023 518.01318 l
135.566477 517.037766 l
127.111932 515.862679 l
118.657386 514.719724 l
110.202841 513.845295 l
118.657386 514.809233 l
127.111932 515.757105 l
135.566477 516.984979 l
144.021023 517.997114 l
152.475568 519.169905 l
160.930114 520.280729 l
169.384659 521.490242 l
177.839205 522.8191 l
186.29375 524.09517 l
194.748295 525.46993 l
203.202841 526.840099 l
211.657386 528.207973 l
220.111932 529.713552 l
228.566477 531.276509 l
237.021023 532.928975 l
245.475568 534.521768 l
253.930114 536.389972 l
262.384659 538.407356 l
270.839205 540.27097 l
279.29375 542.630324 l
287.748295 545.07689 l
296.202841 547.72772 l
304.657386 550.849044 l
313.111932 554.429385 l
321.566477 559.145797 l
330.021023 565.920905 l
338.475568 750.93047 l
346.930114 752.259328 l
355.384659 753.746547 l
363.839205 755.08229 l
372.29375 756.459344 l
380.748295 757.834104 l
389.202841 758.944928 l
397.657386 760.048866 l
406.111932 761.341002 l
414.566477 762.360023 l
423.021023 763.54888 l
431.475568 764.634458 l
439.930114 765.724626 l
448.384659 766.686269 l
448.384659 766.686269 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
372 294.171 93.294 493.155 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

5 -0 m
-5 5 l
-5 -5 l
cl

gsave
grestore
stroke
grestore
} bind def
448.385 766.714 o
439.93 765.672 o
431.476 764.554 o
423.021 763.634 o
414.566 762.422 o
406.112 761.231 o
397.657 760.081 o
389.203 759.002 o
380.748 757.726 o
372.294 756.498 o
363.839 755.13 o
355.385 753.749 o
346.93 752.229 o
338.476 750.761 o
330.021 749.207 o
321.566 747.623 o
313.112 745.854 o
304.657 744.116 o
296.203 742.159 o
287.748 740.029 o
279.294 737.851 o
270.839 735.326 o
262.385 732.641 o
253.93 729.43 o
245.476 725.967 o
237.021 721.276 o
228.566 714.432 o
220.112 529.743 o
211.657 528.171 o
203.203 526.783 o
194.748 525.465 o
186.294 524.164 o
177.839 522.837 o
169.385 521.605 o
160.93 520.368 o
152.476 519.165 o
144.021 518.013 o
135.566 517.038 o
127.112 515.863 o
118.657 514.72 o
110.203 513.845 o
118.657 514.809 o
127.112 515.757 o
135.566 516.985 o
144.021 517.997 o
152.476 519.17 o
160.93 520.281 o
169.385 521.49 o
177.839 522.819 o
186.294 524.095 o
194.748 525.47 o
203.203 526.84 o
211.657 528.208 o
220.112 529.714 o
228.566 531.277 o
237.021 532.929 o
245.476 534.522 o
253.93 536.39 o
262.385 538.407 o
270.839 540.271 o
279.294 542.63 o
287.748 545.077 o
296.203 547.728 o
304.657 550.849 o
313.112 554.429 o
321.566 559.146 o
330.021 565.921 o
338.476 750.93 o
346.93 752.259 o
355.385 753.747 o
363.839 755.082 o
372.294 756.459 o
380.748 757.834 o
389.203 758.945 o
397.657 760.049 o
406.112 761.341 o
414.566 762.36 o
423.021 763.549 o
431.476 764.634 o
439.93 765.725 o
448.385 766.686 o
448.385 766.686 o
grestore
0.500 setlinewidth
1 setlinejoin
[1.85 0.8] 0 setdash
0.000 0.000 1.000 setrgbcolor
gsave
372 294.171 93.294 493.155 clipbox
279.29375 493.154821 m
279.29375 787.32625 l
stroke
grestore
gsave
372 294.171 93.294 493.155 clipbox
93.29375 640.256601 m
465.29375 640.256601 l
stroke
grestore
2.500 setlinewidth
0 setlinejoin
2 setlinecap
[] 0 setdash
0.000 setgray
gsave
93.29375 493.154821 m
93.29375 787.32625 l
stroke
grestore
gsave
465.29375 493.154821 m
465.29375 787.32625 l
stroke
grestore
gsave
93.29375 493.154821 m
465.29375 493.154821 l
stroke
grestore
gsave
93.29375 787.32625 m
465.29375 787.32625 l
stroke
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

266.274 810.86 translate
0 rotate
0 0 m /parenleft glyphshow
8.3252 0 m /a glyphshow
19.4214 0 m /parenright glyphshow
grestore
1.500 setlinewidth
1 setlinejoin
0.749 0.212 0.047 setrgbcolor
gsave
222.7625 695.928036 m
247.7625 695.928036 l
272.7625 695.928036 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
0 setlinecap
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

0 5 m
-5 -5 l
5 -5 l
cl

gsave
0.749 0.212 0.047 setrgbcolor
fill
grestore
stroke
grestore
} bind def
247.763 695.928 o
grestore
0.000 setgray
/TimesNewRomanPSMT 25.000 selectfont
gsave

274.013 687.178 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
37.5 0 m /space glyphshow
43.75 0 m /K glyphshow
grestore
1.500 setlinewidth
1 setlinejoin
[5.55 2.4] 0 setdash
1.000 0.702 0.000 setrgbcolor
gsave
222.7625 660.584286 m
247.7625 660.584286 l
272.7625 660.584286 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-5 -5 m
5 -5 l
5 5 l
-5 5 l
cl

gsave
grestore
stroke
grestore
} bind def
247.763 660.584 o
grestore
0.000 setgray
/TimesNewRomanPSMT 25.000 selectfont
gsave

274.013 651.834 translate
0 rotate
0 0 m /four glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
37.5 0 m /space glyphshow
43.75 0 m /K glyphshow
grestore
1.500 setlinewidth
1 setlinejoin
[1.5 2.475] 0 setdash
0.031 0.659 0.255 setrgbcolor
gsave
222.7625 625.240536 m
247.7625 625.240536 l
272.7625 625.240536 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -5 m
1.326016 -5 2.597899 -4.473168 3.535534 -3.535534 c
4.473168 -2.597899 5 -1.326016 5 0 c
5 1.326016 4.473168 2.597899 3.535534 3.535534 c
2.597899 4.473168 1.326016 5 0 5 c
-1.326016 5 -2.597899 4.473168 -3.535534 3.535534 c
-4.473168 2.597899 -5 1.326016 -5 0 c
-5 -1.326016 -4.473168 -2.597899 -3.535534 -3.535534 c
-2.597899 -4.473168 -1.326016 -5 0 -5 c
cl

gsave
grestore
stroke
grestore
} bind def
247.763 625.241 o
grestore
0.000 setgray
/TimesNewRomanPSMT 25.000 selectfont
gsave

274.013 616.491 translate
0 rotate
0 0 m /six glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
37.5 0 m /space glyphshow
43.75 0 m /K glyphshow
grestore
1.500 setlinewidth
[9.6 2.4 1.5 2.4] 0 setdash
0.031 0.235 0.235 setrgbcolor
gsave
222.7625 589.896786 m
247.7625 589.896786 l
272.7625 589.896786 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

5 -0 m
-5 5 l
-5 -5 l
cl

gsave
grestore
stroke
grestore
} bind def
247.763 589.897 o
grestore
0.000 setgray
/TimesNewRomanPSMT 25.000 selectfont
gsave

274.013 581.147 translate
0 rotate
0 0 m /eight glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
37.5 0 m /space glyphshow
43.75 0 m /K glyphshow
grestore
gsave
614.09375 493.154821 m
986.09375 493.154821 l
986.09375 787.32625 l
614.09375 787.32625 l
cl
1.000 setgray
fill
grestore
1 setlinejoin
0.031 0.659 0.255 setrgbcolor
gsave
372 294.171 614.094 493.155 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

-8.660254 -8.660254 m
8.660254 -8.660254 l
8.660254 8.660254 l
-8.660254 8.660254 l
cl

gsave
0.031 0.659 0.255 setrgbcolor
fill
grestore
stroke
grestore
} bind def
675.531 507.474 o
719.923 559.977 o
755.436 612.48 o
790.949 636.345 o
grestore
2.500 setlinewidth
0.031 0.416 0.416 setrgbcolor
gsave
372 294.171 614.094 493.155 clipbox
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-11.18034 0 m
11.18034 0 l
0 -11.18034 m
0 11.18034 l

gsave
0.031 0.416 0.416 setrgbcolor
fill
grestore
stroke
grestore
} bind def
720.633 650.664 o
grestore
1.000 setlinewidth
0.031 0.659 0.659 setrgbcolor
gsave
372 294.171 614.094 493.155 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 13.228757 m
-2.970041 4.08791 l
-12.581295 4.087911 l
-4.805627 -1.561443 l
-7.775668 -10.702289 l
-0 -5.052935 l
7.775668 -10.702289 l
4.805627 -1.561443 l
12.581295 4.087911 l
2.970041 4.08791 l
cl

gsave
0.031 0.659 0.659 setrgbcolor
fill
grestore
stroke
grestore
} bind def
720.633 569.523 o
grestore
2.500 setlinewidth
0.000 setgray
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.094 493.155 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.094 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

607.844 465.795 translate
0 rotate
0 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
685.12 493.155 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
685.12 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

666.37 465.795 translate
0 rotate
0 0 m /two glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
756.146 493.155 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
756.146 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

737.396 465.795 translate
0 rotate
0 0 m /four glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
827.173 493.155 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
827.173 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

808.423 465.795 translate
0 rotate
0 0 m /six glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
898.199 493.155 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
898.199 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

879.449 465.795 translate
0 rotate
0 0 m /eight glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
969.225 493.155 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
969.225 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

944.225 465.795 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
37.5 0 m /zero glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
631.85 493.155 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
631.85 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
649.607 493.155 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
649.607 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
667.363 493.155 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
667.363 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
702.877 493.155 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
702.877 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
720.633 493.155 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
720.633 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
738.39 493.155 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
738.39 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
773.903 493.155 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
773.903 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
791.659 493.155 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
791.659 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
809.416 493.155 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
809.416 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
844.929 493.155 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
844.929 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
862.686 493.155 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
862.686 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
880.442 493.155 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
880.442 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
915.955 493.155 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
915.955 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
933.712 493.155 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
933.712 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
951.468 493.155 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
951.468 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

716.633 439.092 translate
0 rotate
0 0 m /T glyphshow
13.521 0 m /e glyphshow
24.6172 0 m /m glyphshow
44.063 0 m /p glyphshow
56.563 0 m /e glyphshow
67.6592 0 m /r glyphshow
75.9844 0 m /a glyphshow
87.0806 0 m /t glyphshow
94.0264 0 m /u glyphshow
106.526 0 m /r glyphshow
114.852 0 m /e glyphshow
125.948 0 m /space glyphshow
132.198 0 m /parenleft glyphshow
140.523 0 m /K glyphshow
158.577 0 m /parenright glyphshow
grestore
2.500 setlinewidth
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.094 493.155 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.094 493.155 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

591.594 484.475 translate
0 rotate
0 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.094 540.885 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.094 540.885 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

579.094 532.205 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.094 588.615 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.094 588.615 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

579.094 579.935 translate
0 rotate
0 0 m /two glyphshow
12.5 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.094 636.345 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.094 636.345 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

579.094 627.666 translate
0 rotate
0 0 m /three glyphshow
12.5 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.094 684.075 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.094 684.075 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

579.094 675.396 translate
0 rotate
0 0 m /four glyphshow
12.5 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.094 731.806 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.094 731.806 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

579.094 723.126 translate
0 rotate
0 0 m /five glyphshow
12.5 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.094 779.536 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.094 779.536 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

579.094 770.856 translate
0 rotate
0 0 m /six glyphshow
12.5 0 m /zero glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.094 502.701 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.094 502.701 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.094 512.247 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.094 512.247 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.094 521.793 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.094 521.793 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.094 531.339 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.094 531.339 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.094 550.431 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.094 550.431 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.094 559.977 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.094 559.977 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.094 569.523 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.094 569.523 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.094 579.069 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.094 579.069 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.094 598.161 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.094 598.161 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.094 607.707 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.094 607.707 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.094 617.253 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.094 617.253 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.094 626.799 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.094 626.799 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.094 645.891 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.094 645.891 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.094 655.437 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.094 655.437 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.094 664.983 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.094 664.983 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.094 674.529 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.094 674.529 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.094 693.621 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.094 693.621 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.094 703.167 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.094 703.167 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.094 712.713 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.094 712.713 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.094 722.26 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.094 722.26 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.094 741.352 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.094 741.352 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.094 750.898 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.094 750.898 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.094 760.444 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.094 760.444 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.094 769.99 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.094 769.99 o
grestore
gsave
568.094 570.741 translate
90 rotate
/TimesNewRomanPSMT 25.0 selectfont
0 0.148438 moveto
/P glyphshow
/TimesNewRomanPSMT 17.5 selectfont
14.2795 -6.29219 moveto
/S glyphshow
24.0122 -6.29219 moveto
/a glyphshow
31.7795 -6.29219 moveto
/t glyphshow
/TimesNewRomanPSMT 25.0 selectfont
37.715 0.148438 moveto
/space glyphshow
43.965 0.148438 moveto
/parenleft glyphshow
52.2902 0.148438 moveto
/mu glyphshow
65.6935 0.148438 moveto
/C glyphshow
82.3683 0.148438 moveto
/slash glyphshow
89.3141 0.148438 moveto
/c glyphshow
100.41 0.148438 moveto
/m glyphshow
/TimesNewRomanPSMT 17.5 selectfont
120.232 15.1766 moveto
/two glyphshow
/TimesNewRomanPSMT 25.0 selectfont
130.055 0.148438 moveto
/parenright glyphshow
grestore
[9.6 2.4 1.5 2.4] 0 setdash
0.937 0.424 0.000 setrgbcolor
gsave
372 294.171 614.094 493.155 clipbox
631.850313 773.999946 m
649.606876 773.111089 l
685.120003 771.198846 l
720.633129 769.219338 l
756.146256 767.110104 l
791.659382 765.072941 l
827.172509 762.920466 l
862.685635 760.450885 l
898.198762 757.789119 l
969.225015 752.371513 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
372 294.171 614.094 493.155 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
631.85 774 o
649.607 773.111 o
685.12 771.199 o
720.633 769.219 o
756.146 767.11 o
791.659 765.073 o
827.173 762.92 o
862.686 760.451 o
898.199 757.789 o
969.225 752.372 o
grestore
2.500 setlinewidth
0 setlinejoin
2 setlinecap
0.000 setgray
gsave
614.09375 493.154821 m
614.09375 787.32625 l
stroke
grestore
gsave
986.09375 493.154821 m
986.09375 787.32625 l
stroke
grestore
gsave
614.09375 493.154821 m
986.09375 493.154821 l
stroke
grestore
gsave
614.09375 787.32625 m
986.09375 787.32625 l
stroke
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

787.074 810.86 translate
0 rotate
0 0 m /parenleft glyphshow
8.3252 0 m /b glyphshow
20.8252 0 m /parenright glyphshow
grestore
1.500 setlinewidth
1 setlinejoin
0 setlinecap
[9.6 2.4 1.5 2.4] 0 setdash
0.937 0.424 0.000 setrgbcolor
gsave
815.84375 637.436071 m
840.84375 637.436071 l
865.84375 637.436071 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
840.844 637.436 o
grestore
0.000 setgray
gsave
885.844 628.686 translate
0 rotate
/TimesNewRomanPSMT 25.0 selectfont
0 0.453125 moveto
/H glyphshow
/TimesNewRomanPSMT 17.5 selectfont
18.4299 -5.9875 moveto
/e glyphshow
26.1972 -5.9875 moveto
/f glyphshow
32.0249 -5.9875 moveto
/f glyphshow
37.8525 -5.9875 moveto
/period glyphshow
grestore
0.031 0.659 0.255 setrgbcolor
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

-8.660254 -8.660254 m
8.660254 -8.660254 l
8.660254 8.660254 l
-8.660254 8.660254 l
cl

gsave
0.031 0.659 0.255 setrgbcolor
fill
grestore
stroke
grestore
} bind def
840.844 598.249 o
grestore
0.000 setgray
/TimesNewRomanPSMT 25.000 selectfont
gsave

885.844 591.686 translate
0 rotate
0 0 m /R glyphshow
16.6748 0 m /e glyphshow
27.771 0 m /f glyphshow
36.0962 0 m /period glyphshow
42.3462 0 m /space glyphshow
48.5962 0 m /bracketleft glyphshow
56.9214 0 m /a glyphshow
68.0176 0 m /bracketright glyphshow
grestore
2.500 setlinewidth
0.031 0.416 0.416 setrgbcolor
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-11.18034 0 m
11.18034 0 l
0 -11.18034 m
0 11.18034 l

gsave
0.031 0.416 0.416 setrgbcolor
fill
grestore
stroke
grestore
} bind def
840.844 562.905 o
grestore
0.000 setgray
/TimesNewRomanPSMT 25.000 selectfont
gsave

885.844 556.342 translate
0 rotate
0 0 m /R glyphshow
16.6748 0 m /e glyphshow
27.771 0 m /f glyphshow
36.0962 0 m /period glyphshow
42.3462 0 m /space glyphshow
48.5962 0 m /bracketleft glyphshow
56.9214 0 m /b glyphshow
69.4214 0 m /bracketright glyphshow
grestore
1.000 setlinewidth
0.031 0.659 0.659 setrgbcolor
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 13.228757 m
-2.970041 4.08791 l
-12.581295 4.087911 l
-4.805627 -1.561443 l
-7.775668 -10.702289 l
-0 -5.052935 l
7.775668 -10.702289 l
4.805627 -1.561443 l
12.581295 4.087911 l
2.970041 4.08791 l
cl

gsave
0.031 0.659 0.659 setrgbcolor
fill
grestore
stroke
grestore
} bind def
840.844 527.561 o
grestore
0.000 setgray
/TimesNewRomanPSMT 25.000 selectfont
gsave

885.844 520.999 translate
0 rotate
0 0 m /R glyphshow
16.6748 0 m /e glyphshow
27.771 0 m /f glyphshow
36.0962 0 m /period glyphshow
42.3462 0 m /space glyphshow
48.5962 0 m /bracketleft glyphshow
56.9214 0 m /c glyphshow
68.0176 0 m /bracketright glyphshow
grestore
gsave
93.29375 66.60625 m
465.29375 66.60625 l
465.29375 360.777679 l
93.29375 360.777679 l
cl
1.000 setgray
fill
grestore
2.500 setlinewidth
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
106.787 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
106.787 360.778 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

100.537 39.2469 translate
0 rotate
0 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
175.106 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
175.106 360.778 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

156.356 39.2469 translate
0 rotate
0 0 m /two glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
243.426 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
243.426 360.778 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

224.676 39.2469 translate
0 rotate
0 0 m /four glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
311.746 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
311.746 360.778 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

292.996 39.2469 translate
0 rotate
0 0 m /six glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
380.065 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
380.065 360.778 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

361.315 39.2469 translate
0 rotate
0 0 m /eight glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
448.385 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
448.385 360.778 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

423.385 39.2469 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
37.5 0 m /zero glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
123.867 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
123.867 360.778 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
140.947 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
140.947 360.778 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
158.027 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
158.027 360.778 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
192.186 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
192.186 360.778 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
209.266 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
209.266 360.778 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
226.346 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
226.346 360.778 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
260.506 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
260.506 360.778 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
277.586 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
277.586 360.778 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
294.666 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
294.666 360.778 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
328.825 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
328.825 360.778 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
345.905 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
345.905 360.778 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
362.985 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
362.985 360.778 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
397.145 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
397.145 360.778 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
414.225 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
414.225 360.778 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
431.305 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
431.305 360.778 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

195.833 12.5437 translate
0 rotate
0 0 m /T glyphshow
13.521 0 m /e glyphshow
24.6172 0 m /m glyphshow
44.063 0 m /p glyphshow
56.563 0 m /e glyphshow
67.6592 0 m /r glyphshow
75.9844 0 m /a glyphshow
87.0806 0 m /t glyphshow
94.0264 0 m /u glyphshow
106.526 0 m /r glyphshow
114.852 0 m /e glyphshow
125.948 0 m /space glyphshow
132.198 0 m /parenleft glyphshow
140.523 0 m /K glyphshow
158.577 0 m /parenright glyphshow
grestore
2.500 setlinewidth
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 103.749 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.294 103.749 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

70.7938 95.0694 translate
0 rotate
0 0 m /one glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 163.178 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.294 163.178 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

70.7938 154.498 translate
0 rotate
0 0 m /two glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 222.606 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.294 222.606 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

70.7938 213.927 translate
0 rotate
0 0 m /three glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 282.035 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.294 282.035 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

70.7938 273.355 translate
0 rotate
0 0 m /four glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 341.463 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.294 341.463 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

70.7938 332.784 translate
0 rotate
0 0 m /five glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 68.092 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.294 68.092 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 79.9777 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.294 79.9777 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 91.8634 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.294 91.8634 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 115.635 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.294 115.635 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 127.521 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.294 127.521 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 139.406 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.294 139.406 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 151.292 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.294 151.292 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 175.063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.294 175.063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 186.949 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.294 186.949 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 198.835 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.294 198.835 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 210.721 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.294 210.721 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 234.492 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.294 234.492 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 246.378 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.294 246.378 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 258.263 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.294 258.263 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 270.149 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.294 270.149 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 293.921 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.294 293.921 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 305.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.294 305.806 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 317.692 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.294 317.692 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 329.578 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.294 329.578 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.2938 353.349 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.294 353.349 o
grestore
gsave
59.7938 150.692 translate
90 rotate
/TimesNewRomanPSMT 25.0 selectfont
0 0.640625 moveto
/E glyphshow
/TimesNewRomanPSMT 17.5 selectfont
15.6467 -5.8 moveto
/c glyphshow
/TimesNewRomanPSMT 25.0 selectfont
24.4875 0.640625 moveto
/space glyphshow
30.7375 0.640625 moveto
/parenleft glyphshow
39.0627 0.640625 moveto
/M glyphshow
61.2917 0.640625 moveto
/V glyphshow
79.3459 0.640625 moveto
/slash glyphshow
86.2917 0.640625 moveto
/c glyphshow
97.3879 0.640625 moveto
/m glyphshow
116.834 0.640625 moveto
/parenright glyphshow
grestore
[9.6 2.4 1.5 2.4] 0 setdash
0.749 0.212 0.047 setrgbcolor
gsave
372 294.171 93.294 66.606 clipbox
110.202841 240.434821 m
123.866753 234.491964 l
140.946643 222.60625 l
175.106422 210.720536 l
209.266202 186.949107 l
243.425981 175.063393 l
277.585761 151.291964 l
311.745541 139.40625 l
345.90532 127.520536 l
380.0651 115.634821 l
448.384659 79.977679 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
372 294.171 93.294 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
110.203 240.435 o
123.867 234.492 o
140.947 222.606 o
175.106 210.721 o
209.266 186.949 o
243.426 175.063 o
277.586 151.292 o
311.746 139.406 o
345.905 127.521 o
380.065 115.635 o
448.385 79.9777 o
grestore
1.500 setlinewidth
[9.6 2.4 1.5 2.4] 0 setdash
0.000 0.467 0.733 setrgbcolor
gsave
372 294.171 93.294 66.606 clipbox
110.202841 329.577679 m
123.866753 311.749107 l
140.946643 311.749107 l
175.106422 276.091964 l
209.266202 258.263393 l
243.425981 258.263393 l
277.585761 186.949107 l
311.745541 186.949107 l
345.90532 163.177679 l
380.0651 139.40625 l
448.384659 91.863393 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
372 294.171 93.294 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-8 -8 m
8 -8 l
8 8 l
-8 8 l
cl

gsave
grestore
stroke
grestore
} bind def
110.203 329.578 o
123.867 311.749 o
140.947 311.749 o
175.106 276.092 o
209.266 258.263 o
243.426 258.263 o
277.586 186.949 o
311.746 186.949 o
345.905 163.178 o
380.065 139.406 o
448.385 91.8634 o
grestore
1.500 setlinewidth
1 setlinejoin
[9.6 2.4 1.5 2.4] 0 setdash
0.031 0.659 0.255 setrgbcolor
gsave
372 294.171 93.294 66.606 clipbox
110.202841 347.40625 m
123.866753 347.40625 l
140.946643 347.40625 l
175.106422 311.749107 l
209.266202 270.149107 l
243.425981 270.149107 l
277.585761 258.263393 l
311.745541 222.60625 l
345.90532 163.177679 l
380.0651 163.177679 l
448.384659 115.634821 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
372 294.171 93.294 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-8 0 m
8 -8 l
8 8 l
cl

gsave
grestore
stroke
grestore
} bind def
110.203 347.406 o
123.867 347.406 o
140.947 347.406 o
175.106 311.749 o
209.266 270.149 o
243.426 270.149 o
277.586 258.263 o
311.746 222.606 o
345.905 163.178 o
380.065 163.178 o
448.385 115.635 o
grestore
2.500 setlinewidth
2 setlinecap
0.000 setgray
gsave
93.29375 66.60625 m
93.29375 360.777679 l
stroke
grestore
gsave
465.29375 66.60625 m
465.29375 360.777679 l
stroke
grestore
gsave
93.29375 66.60625 m
465.29375 66.60625 l
stroke
grestore
gsave
93.29375 360.777679 m
465.29375 360.777679 l
stroke
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

266.274 384.311 translate
0 rotate
0 0 m /parenleft glyphshow
8.3252 0 m /c glyphshow
19.4214 0 m /parenright glyphshow
grestore
1.500 setlinewidth
1 setlinejoin
0 setlinecap
[9.6 2.4 1.5 2.4] 0 setdash
0.749 0.212 0.047 setrgbcolor
gsave
293.79375 329.027679 m
318.79375 329.027679 l
343.79375 329.027679 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
318.794 329.028 o
grestore
0.000 setgray
gsave
363.794 320.278 translate
0 rotate
/TimesNewRomanPSMT 25.0 selectfont
0 0.640625 moveto
/E glyphshow
/TimesNewRomanPSMT 17.5 selectfont
15.6467 -5.8 moveto
/c glyphshow
/TimesNewRomanPSMT 25.0 selectfont
24.4875 0.640625 moveto
/parenleft glyphshow
32.8127 0.640625 moveto
/one glyphshow
45.3127 0.640625 moveto
/one glyphshow
57.8127 0.640625 moveto
/one glyphshow
70.3127 0.640625 moveto
/parenright glyphshow
grestore
1.500 setlinewidth
[9.6 2.4 1.5 2.4] 0 setdash
0.000 0.467 0.733 setrgbcolor
gsave
293.79375 291.527679 m
318.79375 291.527679 l
343.79375 291.527679 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-8 -8 m
8 -8 l
8 8 l
-8 8 l
cl

gsave
grestore
stroke
grestore
} bind def
318.794 291.528 o
grestore
0.000 setgray
gsave
363.794 282.778 translate
0 rotate
/TimesNewRomanPSMT 25.0 selectfont
0 0.640625 moveto
/E glyphshow
/TimesNewRomanPSMT 17.5 selectfont
15.6467 -5.8 moveto
/c glyphshow
/TimesNewRomanPSMT 25.0 selectfont
24.4875 0.640625 moveto
/parenleft glyphshow
32.8127 0.640625 moveto
/one glyphshow
45.3127 0.640625 moveto
/one glyphshow
57.8127 0.640625 moveto
/zero glyphshow
70.3127 0.640625 moveto
/parenright glyphshow
grestore
1.500 setlinewidth
1 setlinejoin
[9.6 2.4 1.5 2.4] 0 setdash
0.031 0.659 0.255 setrgbcolor
gsave
293.79375 254.027679 m
318.79375 254.027679 l
343.79375 254.027679 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-8 0 m
8 -8 l
8 8 l
cl

gsave
grestore
stroke
grestore
} bind def
318.794 254.028 o
grestore
0.000 setgray
gsave
363.794 245.278 translate
0 rotate
/TimesNewRomanPSMT 25.0 selectfont
0 0.640625 moveto
/E glyphshow
/TimesNewRomanPSMT 17.5 selectfont
15.6467 -5.8 moveto
/c glyphshow
/TimesNewRomanPSMT 25.0 selectfont
24.4875 0.640625 moveto
/parenleft glyphshow
32.8127 0.640625 moveto
/zero glyphshow
45.3127 0.640625 moveto
/zero glyphshow
57.8127 0.640625 moveto
/one glyphshow
70.3127 0.640625 moveto
/parenright glyphshow
grestore
gsave
614.09375 66.60625 m
986.09375 66.60625 l
986.09375 360.777679 l
614.09375 360.777679 l
cl
1.000 setgray
fill
grestore
2.500 setlinewidth
1 setlinejoin
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.094 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.094 360.778 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

607.844 39.2469 translate
0 rotate
0 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
685.12 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
685.12 360.778 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

666.37 39.2469 translate
0 rotate
0 0 m /two glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
756.146 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
756.146 360.778 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

737.396 39.2469 translate
0 rotate
0 0 m /four glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
827.173 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
827.173 360.778 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

808.423 39.2469 translate
0 rotate
0 0 m /six glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
898.199 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
898.199 360.778 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

879.449 39.2469 translate
0 rotate
0 0 m /eight glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
969.225 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
969.225 360.778 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

944.225 39.2469 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
37.5 0 m /zero glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
631.85 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
631.85 360.778 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
649.607 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
649.607 360.778 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
667.363 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
667.363 360.778 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
702.877 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
702.877 360.778 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
720.633 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
720.633 360.778 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
738.39 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
738.39 360.778 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
773.903 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
773.903 360.778 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
791.659 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
791.659 360.778 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
809.416 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
809.416 360.778 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
844.929 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
844.929 360.778 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
862.686 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
862.686 360.778 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
880.442 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
880.442 360.778 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
915.955 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
915.955 360.778 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
933.712 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
933.712 360.778 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
951.468 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
951.468 360.778 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

716.633 12.5437 translate
0 rotate
0 0 m /T glyphshow
13.521 0 m /e glyphshow
24.6172 0 m /m glyphshow
44.063 0 m /p glyphshow
56.563 0 m /e glyphshow
67.6592 0 m /r glyphshow
75.9844 0 m /a glyphshow
87.0806 0 m /t glyphshow
94.0264 0 m /u glyphshow
106.526 0 m /r glyphshow
114.852 0 m /e glyphshow
125.948 0 m /space glyphshow
132.198 0 m /parenleft glyphshow
140.523 0 m /K glyphshow
158.577 0 m /parenright glyphshow
grestore
2.500 setlinewidth
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.094 81.5785 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.094 81.5785 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

547.844 72.8988 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /one glyphshow
43.75 0 m /four glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.094 147.784 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.094 147.784 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

547.844 139.104 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /one glyphshow
43.75 0 m /six glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.094 213.989 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.094 213.989 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

547.844 205.31 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /one glyphshow
43.75 0 m /eight glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.094 280.195 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.094 280.195 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

547.844 271.515 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /two glyphshow
43.75 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.094 346.4 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.094 346.4 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

547.844 337.721 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /two glyphshow
43.75 0 m /two glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.094 98.1299 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.094 98.1299 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.094 114.681 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.094 114.681 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.094 131.233 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.094 131.233 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.094 164.335 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.094 164.335 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.094 180.887 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.094 180.887 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.094 197.438 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.094 197.438 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.094 230.541 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.094 230.541 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.094 247.092 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.094 247.092 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.094 263.643 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.094 263.643 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.094 296.746 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.094 296.746 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.094 313.298 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.094 313.298 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.094 329.849 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.094 329.849 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

538.5 183.825 translate
90 rotate
0 0 m /S glyphshow
13.9038 0 m /t glyphshow
20.8496 0 m /r glyphshow
29.1748 0 m /a glyphshow
40.271 0 m /i glyphshow
47.2168 0 m /n glyphshow
grestore
[9.6 2.4 1.5 2.4] 0 setdash
0.749 0.212 0.047 setrgbcolor
gsave
372 294.171 614.094 66.606 clipbox
631.850313 284.966251 m
649.606876 277.829967 l
685.120003 261.989655 l
720.633129 245.91928 l
756.146256 228.311283 l
791.659382 210.033618 l
827.172509 190.535124 l
862.685635 168.537045 l
898.198762 143.752048 l
969.225015 79.977679 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
372 294.171 614.094 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
631.85 284.966 o
649.607 277.83 o
685.12 261.99 o
720.633 245.919 o
756.146 228.311 o
791.659 210.034 o
827.173 190.535 o
862.686 168.537 o
898.199 143.752 o
969.225 79.9777 o
grestore
1.500 setlinewidth
[5.55 2.4] 0 setdash
0.031 0.659 0.255 setrgbcolor
gsave
372 294.171 614.094 66.606 clipbox
631.850313 347.40625 m
649.606876 340.398074 l
685.120003 326.521746 l
720.633129 312.031032 l
756.146256 296.028186 l
791.659382 280.394766 l
827.172509 262.676868 l
862.685635 243.7345 l
898.198762 222.878134 l
969.225015 174.293277 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
372 294.171 614.094 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-8 -8 m
8 -8 l
8 8 l
-8 8 l
cl

gsave
grestore
stroke
grestore
} bind def
631.85 347.406 o
649.607 340.398 o
685.12 326.522 o
720.633 312.031 o
756.146 296.028 o
791.659 280.395 o
827.173 262.677 o
862.686 243.735 o
898.199 222.878 o
969.225 174.293 o
grestore
2.500 setlinewidth
2 setlinecap
0.000 setgray
gsave
614.09375 66.60625 m
614.09375 360.777679 l
stroke
grestore
gsave
986.09375 66.60625 m
986.09375 360.777679 l
stroke
grestore
gsave
614.09375 66.60625 m
986.09375 66.60625 l
stroke
grestore
gsave
614.09375 360.777679 m
986.09375 360.777679 l
stroke
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

787.074 384.311 translate
0 rotate
0 0 m /parenleft glyphshow
8.3252 0 m /d glyphshow
20.8252 0 m /parenright glyphshow
grestore
1.500 setlinewidth
1 setlinejoin
0 setlinecap
[9.6 2.4 1.5 2.4] 0 setdash
0.749 0.212 0.047 setrgbcolor
gsave
847.59375 329.527679 m
872.59375 329.527679 l
897.59375 329.527679 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
872.594 329.528 o
grestore
0.000 setgray
gsave
917.594 320.778 translate
0 rotate
/TimesNewRomanPSMT 25.0 selectfont
0 0.0625 moveto
/S glyphshow
/TimesNewRomanPSMT 17.5 selectfont
14.2795 -6.37813 moveto
/r glyphshow
20.1071 -6.37813 moveto
/e glyphshow
27.8745 -6.37813 moveto
/m glyphshow
grestore
1.500 setlinewidth
[5.55 2.4] 0 setdash
0.031 0.659 0.255 setrgbcolor
gsave
847.59375 292.527679 m
872.59375 292.527679 l
897.59375 292.527679 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-8 -8 m
8 -8 l
8 8 l
-8 8 l
cl

gsave
grestore
stroke
grestore
} bind def
872.594 292.528 o
grestore
0.000 setgray
gsave
917.594 283.778 translate
0 rotate
/TimesNewRomanPSMT 25.0 selectfont
0 0.0625 moveto
/S glyphshow
/TimesNewRomanPSMT 17.5 selectfont
14.2795 -6.37813 moveto
/m glyphshow
27.8916 -6.37813 moveto
/a glyphshow
35.6589 -6.37813 moveto
/x glyphshow
grestore

end
showpage
