%!PS-Adobe-3.0 EPSF-3.0
%%Title: Fig2.eps
%%Creator: Matplotlib v3.7.1, https://matplotlib.org/
%%CreationDate: Wed Aug  7 16:34:02 2024
%%Orientation: portrait
%%BoundingBox: -203 -12 816 805
%%HiResBoundingBox: -203.475000 -12.942812 815.475000 804.942812
%%EndComments
%%BeginProlog
/mpldict 11 dict def
mpldict begin
/_d { bind def } bind def
/m { moveto } _d
/l { lineto } _d
/r { rlineto } _d
/c { curveto } _d
/cl { closepath } _d
/ce { closepath eofill } _d
/box {
      m
      1 index 0 r
      0 exch r
      neg 0 r
      cl
    } _d
/clipbox {
      box
      clip
      newpath
    } _d
/sc { setcachedevice } _d
%!PS-TrueTypeFont-1.0-2.53740
%%Title: unknown
%%Creator: Converted from TrueType to type 42 by PPR
15 dict begin
/FontName /TimesNewRomanPSMT def
/PaintType 0 def
/FontMatrix[1 0 0 1 0 0]def
/FontBBox[-568 -307 2028 1007]def
/FontType 42 def
/Encoding StandardEncoding def
/FontInfo 10 dict dup begin
/FamilyName (unknown) def
/FullName (unknown) def
/Weight (unknown) def
/Version (unknown) def
/ItalicAngle 0.0 def
/isFixedPitch false def
/UnderlinePosition -223 def
/UnderlineThickness 100 def
end readonly def
/sfnts[<00010000000900800003001063767420F34DDA810000009C000007C46670676D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>]def
/CharStrings 46 dict dup begin
/.notdef 0 def
/Gamma 45 def
/space 3 def
/parenleft 4 def
/parenright 5 def
/comma 6 def
/period 7 def
/slash 8 def
/zero 9 def
/one 10 def
/two 11 def
/three 12 def
/four 13 def
/five 14 def
/six 15 def
/seven 16 def
/eight 17 def
/nine 18 def
/less 19 def
/mu 46 def
/greater 20 def
/C 21 def
/omega 47 def
/K 22 def
/L 23 def
/P 24 def
/R 25 def
/T 26 def
/a 27 def
/b 28 def
/c 29 def
/d 30 def
/e 31 def
/i 32 def
/l 33 def
/m 34 def
/n 35 def
/o 36 def
/p 37 def
/r 38 def
/s 39 def
/t 40 def
/u 41 def
/x 42 def
/y 43 def
/z 44 def
end readonly def

systemdict/resourcestatus known
 {42 /FontType resourcestatus
   {pop pop false}{true}ifelse}
 {true}ifelse
{/TrueDict where{pop}{(%%[ Error: no TrueType rasterizer ]%%)= flush}ifelse
/FontType 3 def
 /TrueState 271 string def
 TrueDict begin sfnts save
 72 0 matrix defaultmatrix dtransform dup
 mul exch dup mul add sqrt cvi 0 72 matrix
 defaultmatrix dtransform dup mul exch dup
 mul add sqrt cvi 3 -1 roll restore
 TrueState initer end
 /BuildGlyph{exch begin
  CharStrings dup 2 index known
    {exch}{exch pop /.notdef}ifelse
  get dup xcheck
    {currentdict systemdict begin begin exec end end}
    {TrueDict begin /bander load cvlit exch TrueState render end}
    ifelse
 end}bind def
 /BuildChar{
  1 index /Encoding get exch get
  1 index /BuildGlyph get exec
 }bind def
}if

FontName currentdict end definefont pop
end
%%EndProlog
mpldict begin
-203.475 -12.943 translate
1018.95 817.886 0 0 clipbox
gsave
0 0 m
1018.95 0 l
1018.95 817.885625 l
0 817.885625 l
cl
1.000 setgray
fill
grestore
gsave
93.95 473.969728 m
482.123913 473.969728 l
482.123913 787.32625 l
93.95 787.32625 l
cl
1.000 setgray
fill
grestore
2.500 setlinewidth
1 setlinejoin
0 setlinecap
[] 0 setdash
0.000 setgray
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

87.7 446.61 translate
0 rotate
0 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
171.585 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
171.585 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

152.835 446.61 translate
0 rotate
0 0 m /three glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
249.22 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
249.22 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

230.47 446.61 translate
0 rotate
0 0 m /six glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
326.854 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
326.854 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

308.104 446.61 translate
0 rotate
0 0 m /nine glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
404.489 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
404.489 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

379.489 446.61 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /two glyphshow
25 0 m /zero glyphshow
37.5 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

457.124 446.61 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /five glyphshow
25 0 m /zero glyphshow
37.5 0 m /zero glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
113.359 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
113.359 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
132.767 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
132.767 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
152.176 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
152.176 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
190.993 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
190.993 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
210.402 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
210.402 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
229.811 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
229.811 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
268.628 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
268.628 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
288.037 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
288.037 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
307.446 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
307.446 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
346.263 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
346.263 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
365.672 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
365.672 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
385.08 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
385.08 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
423.898 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
423.898 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
443.307 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
443.307 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
462.715 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
462.715 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

204.576 419.907 translate
0 rotate
0 0 m /T glyphshow
13.521 0 m /e glyphshow
24.6172 0 m /m glyphshow
44.063 0 m /p glyphshow
56.563 0 m /e glyphshow
67.6592 0 m /r glyphshow
75.9844 0 m /a glyphshow
87.0806 0 m /t glyphshow
94.0264 0 m /u glyphshow
106.526 0 m /r glyphshow
114.852 0 m /e glyphshow
125.948 0 m /space glyphshow
132.198 0 m /parenleft glyphshow
140.523 0 m /K glyphshow
158.577 0 m /parenright glyphshow
grestore
2.500 setlinewidth
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 488.02 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 488.02 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

40.2 479.341 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 545.421 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 545.421 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

40.2 536.742 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /one glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 602.823 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 602.823 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

40.2 594.143 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /two glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 660.224 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 660.224 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

40.2 651.544 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /three glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 717.625 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 717.625 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

40.2 708.945 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /four glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 775.026 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 775.026 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

40.2 766.346 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /five glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 476.54 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 476.54 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 499.501 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 499.501 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 510.981 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 510.981 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 522.461 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 522.461 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 533.941 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 533.941 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 556.902 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 556.902 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 568.382 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 568.382 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 579.862 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 579.862 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 591.342 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 591.342 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 614.303 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 614.303 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 625.783 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 625.783 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 637.263 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 637.263 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 648.743 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 648.743 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 671.704 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 671.704 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 683.184 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 683.184 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 694.664 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 694.664 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 706.144 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 706.144 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 729.105 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 729.105 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 740.585 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 740.585 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 752.065 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 752.065 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 763.545 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 763.545 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 786.506 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 786.506 o
grestore
gsave
25.2 574.148 translate
90 rotate
/TimesNewRomanPSMT 25.0 selectfont
0 0.640625 moveto
/less glyphshow
14.0991 0.640625 moveto
/u glyphshow
/TimesNewRomanPSMT 17.5 selectfont
26.9748 -5.8 moveto
/x glyphshow
35.7248 -5.8 moveto
/comma glyphshow
42.7458 -5.8 moveto
/y glyphshow
51.4958 -5.8 moveto
/comma glyphshow
58.5168 -5.8 moveto
/z glyphshow
/TimesNewRomanPSMT 25.0 selectfont
67.3576 0.640625 moveto
/parenleft glyphshow
75.6828 0.640625 moveto
/Gamma glyphshow
90.1359 0.640625 moveto
/parenright glyphshow
98.4611 0.640625 moveto
/greater glyphshow
grestore
0.050 setlinewidth
[0.185 0.08] 0 setdash
0.722 0.224 0.055 setrgbcolor
gsave
388.174 313.357 93.95 473.97 clipbox
476.948261 491.345525 m
471.772609 497.33567 l
466.596957 488.390633 l
461.421304 489.522582 l
456.245652 498.945425 l
451.07 501.788844 l
445.894348 496.259515 l
440.718696 509.522947 l
435.543043 493.547602 l
430.367391 502.977103 l
425.191739 501.458558 l
420.016087 494.211502 l
414.840435 532.550702 l
409.664783 533.929762 l
404.48913 490.829375 l
399.313478 499.923596 l
394.137826 676.266647 l
388.962174 684.748744 l
383.786522 692.31627 l
378.61087 696.531803 l
373.435217 700.261781 l
368.259565 702.179895 l
363.083913 706.336306 l
357.908261 709.886619 l
352.732609 711.556013 l
347.556957 714.214543 l
342.381304 717.810834 l
337.205652 719.318071 l
332.03 721.120235 l
326.854348 723.717116 l
321.678696 725.542068 l
316.503043 727.185173 l
311.327391 729.069593 l
306.151739 730.487801 l
300.976087 732.462053 l
295.800435 733.15695 l
290.624783 735.160706 l
285.44913 736.584195 l
280.273478 737.799719 l
275.097826 739.517503 l
269.922174 740.755989 l
264.746522 741.619932 l
259.57087 743.159428 l
254.395217 744.754374 l
249.219565 746.018747 l
244.043913 747.23852 l
238.868261 748.396299 l
233.692609 749.520499 l
228.516957 750.636605 l
223.341304 751.689283 l
218.165652 752.516834 l
212.99 753.849227 l
207.814348 754.718279 l
202.638696 755.69513 l
197.463043 756.648505 l
192.287391 757.782118 l
187.111739 758.625225 l
181.936087 759.55914 l
176.760435 760.637017 l
171.584783 761.534999 l
166.40913 762.337294 l
161.233478 763.252439 l
156.057826 764.150364 l
150.882174 765.095529 l
145.706522 766.053036 l
140.53087 766.704366 l
135.355217 767.465734 l
130.179565 768.472147 l
125.003913 769.281329 l
119.828261 769.959064 l
114.652609 770.718365 l
109.476957 771.558027 l
104.301304 772.289202 l
99.125652 773.043796 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
388.174 313.357 93.95 473.97 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

0 2 m
-2 -2 l
2 -2 l
cl

gsave
0.722 0.224 0.055 setrgbcolor
fill
grestore
stroke
grestore
} bind def
476.948 491.346 o
471.773 497.336 o
466.597 488.391 o
461.421 489.523 o
456.246 498.945 o
451.07 501.789 o
445.894 496.26 o
440.719 509.523 o
435.543 493.548 o
430.367 502.977 o
425.192 501.459 o
420.016 494.212 o
414.84 532.551 o
409.665 533.93 o
404.489 490.829 o
399.313 499.924 o
394.138 676.267 o
388.962 684.749 o
383.787 692.316 o
378.611 696.532 o
373.435 700.262 o
368.26 702.18 o
363.084 706.336 o
357.908 709.887 o
352.733 711.556 o
347.557 714.215 o
342.381 717.811 o
337.206 719.318 o
332.03 721.12 o
326.854 723.717 o
321.679 725.542 o
316.503 727.185 o
311.327 729.07 o
306.152 730.488 o
300.976 732.462 o
295.8 733.157 o
290.625 735.161 o
285.449 736.584 o
280.273 737.8 o
275.098 739.518 o
269.922 740.756 o
264.747 741.62 o
259.571 743.159 o
254.395 744.754 o
249.22 746.019 o
244.044 747.239 o
238.868 748.396 o
233.693 749.52 o
228.517 750.637 o
223.341 751.689 o
218.166 752.517 o
212.99 753.849 o
207.814 754.718 o
202.639 755.695 o
197.463 756.649 o
192.287 757.782 o
187.112 758.625 o
181.936 759.559 o
176.76 760.637 o
171.585 761.535 o
166.409 762.337 o
161.233 763.252 o
156.058 764.15 o
150.882 765.096 o
145.707 766.053 o
140.531 766.704 o
135.355 767.466 o
130.18 768.472 o
125.004 769.281 o
119.828 769.959 o
114.653 770.718 o
109.477 771.558 o
104.301 772.289 o
99.1257 773.044 o
grestore
0.050 setlinewidth
1 setlinejoin
[0.185 0.08] 0 setdash
1.000 0.271 0.000 setrgbcolor
gsave
388.174 313.357 93.95 473.97 clipbox
476.948261 489.696966 m
471.772609 490.351166 l
466.596957 491.31051 l
461.421304 498.810131 l
456.245652 488.897714 l
451.07 489.873934 l
445.894348 489.531364 l
440.718696 489.836795 l
435.543043 499.83738 l
430.367391 489.00293 l
425.191739 494.782815 l
420.016087 515.844927 l
414.840435 505.564972 l
409.664783 510.411114 l
404.48913 513.351024 l
399.313478 537.271192 l
394.137826 677.517761 l
388.962174 685.729039 l
383.786522 691.964803 l
378.61087 696.142624 l
373.435217 700.343463 l
368.259565 703.525491 l
363.083913 706.446918 l
357.908261 708.819935 l
352.732609 712.356586 l
347.556957 714.794868 l
342.381304 716.971459 l
337.205652 719.656508 l
332.03 721.446675 l
326.854348 723.110846 l
321.678696 725.536328 l
316.503043 727.470055 l
311.327391 728.411604 l
306.151739 730.002015 l
300.976087 732.191981 l
295.800435 733.67333 l
290.624783 735.065133 l
285.44913 736.950356 l
280.273478 738.042124 l
275.097826 739.440184 l
269.922174 740.891226 l
264.746522 742.127587 l
259.57087 743.719261 l
254.395217 744.675792 l
249.219565 745.814801 l
244.043913 747.149663 l
238.868261 748.51931 l
233.692609 749.256683 l
228.516957 750.768513 l
223.341304 751.552898 l
218.165652 752.862101 l
212.99 753.768694 l
207.814348 754.679763 l
202.638696 755.850343 l
197.463043 756.879372 l
192.287391 757.765816 l
187.111739 758.686988 l
181.936087 759.712056 l
176.760435 760.576976 l
171.584783 761.536319 l
166.40913 762.415474 l
161.233478 763.2343 l
156.057826 764.226363 l
150.882174 765.057989 l
145.706522 766.020375 l
140.53087 766.79219 l
135.355217 767.67852 l
130.179565 768.339608 l
125.003913 769.135301 l
119.828261 769.928411 l
114.652609 770.772494 l
109.476957 771.559635 l
104.301304 772.305734 l
99.125652 773.082772 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
388.174 313.357 93.95 473.97 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-4 -4 m
4 -4 l
4 4 l
-4 4 l
cl

gsave
grestore
stroke
grestore
} bind def
476.948 489.697 o
471.773 490.351 o
466.597 491.311 o
461.421 498.81 o
456.246 488.898 o
451.07 489.874 o
445.894 489.531 o
440.719 489.837 o
435.543 499.837 o
430.367 489.003 o
425.192 494.783 o
420.016 515.845 o
414.84 505.565 o
409.665 510.411 o
404.489 513.351 o
399.313 537.271 o
394.138 677.518 o
388.962 685.729 o
383.787 691.965 o
378.611 696.143 o
373.435 700.343 o
368.26 703.525 o
363.084 706.447 o
357.908 708.82 o
352.733 712.357 o
347.557 714.795 o
342.381 716.971 o
337.206 719.657 o
332.03 721.447 o
326.854 723.111 o
321.679 725.536 o
316.503 727.47 o
311.327 728.412 o
306.152 730.002 o
300.976 732.192 o
295.8 733.673 o
290.625 735.065 o
285.449 736.95 o
280.273 738.042 o
275.098 739.44 o
269.922 740.891 o
264.747 742.128 o
259.571 743.719 o
254.395 744.676 o
249.22 745.815 o
244.044 747.15 o
238.868 748.519 o
233.693 749.257 o
228.517 750.769 o
223.341 751.553 o
218.166 752.862 o
212.99 753.769 o
207.814 754.68 o
202.639 755.85 o
197.463 756.879 o
192.287 757.766 o
187.112 758.687 o
181.936 759.712 o
176.76 760.577 o
171.585 761.536 o
166.409 762.415 o
161.233 763.234 o
156.058 764.226 o
150.882 765.058 o
145.707 766.02 o
140.531 766.792 o
135.355 767.679 o
130.18 768.34 o
125.004 769.135 o
119.828 769.928 o
114.653 770.772 o
109.477 771.56 o
104.301 772.306 o
99.1257 773.083 o
grestore
0.050 setlinewidth
1 setlinejoin
[0.185 0.08] 0 setdash
1.000 0.514 0.000 setrgbcolor
gsave
388.174 313.357 93.95 473.97 clipbox
476.948261 488.213207 m
471.772609 494.11679 l
466.596957 491.531906 l
461.421304 497.439393 l
456.245652 490.078511 l
451.07 488.320546 l
445.894348 493.36168 l
440.718696 501.129707 l
435.543043 489.674695 l
430.367391 501.681676 l
425.191739 490.495415 l
420.016087 519.226768 l
414.840435 489.914574 l
409.664783 576.436163 l
404.48913 588.613912 l
399.313478 578.112045 l
394.137826 675.979929 l
388.962174 685.573023 l
383.786522 690.918841 l
378.61087 696.150603 l
373.435217 700.071095 l
368.259565 703.717784 l
363.083913 706.985971 l
357.908261 709.682041 l
352.732609 712.476784 l
347.556957 714.175511 l
342.381304 716.89655 l
337.205652 719.364222 l
332.03 721.361951 l
326.854348 723.262213 l
321.678696 725.148986 l
316.503043 727.131389 l
311.327391 728.77834 l
306.151739 730.209692 l
300.976087 732.175449 l
295.800435 733.908846 l
290.624783 734.961639 l
285.44913 736.474157 l
280.273478 737.995801 l
275.097826 739.550452 l
269.922174 740.649567 l
264.746522 741.943502 l
259.57087 743.580408 l
254.395217 744.637046 l
249.219565 745.83271 l
244.043913 747.032737 l
238.868261 748.354683 l
233.692609 749.256856 l
228.516957 750.423245 l
223.341304 751.650308 l
218.165652 752.649603 l
212.99 753.630013 l
207.814348 754.80926 l
202.638696 755.685774 l
197.463043 756.765431 l
192.287391 757.781889 l
187.111739 758.81505 l
181.936087 759.631752 l
176.760435 760.64666 l
171.584783 761.701749 l
166.40913 762.459615 l
161.233478 763.433424 l
156.057826 764.196112 l
150.882174 765.085025 l
145.706522 765.960736 l
140.53087 766.771755 l
135.355217 767.608663 l
130.179565 768.485808 l
125.003913 769.258369 l
119.828261 769.980245 l
114.652609 770.812216 l
109.476957 771.603948 l
104.301304 772.301256 l
99.125652 773.078696 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
388.174 313.357 93.95 473.97 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
476.948 488.213 o
471.773 494.117 o
466.597 491.532 o
461.421 497.439 o
456.246 490.079 o
451.07 488.321 o
445.894 493.362 o
440.719 501.13 o
435.543 489.675 o
430.367 501.682 o
425.192 490.495 o
420.016 519.227 o
414.84 489.915 o
409.665 576.436 o
404.489 588.614 o
399.313 578.112 o
394.138 675.98 o
388.962 685.573 o
383.787 690.919 o
378.611 696.151 o
373.435 700.071 o
368.26 703.718 o
363.084 706.986 o
357.908 709.682 o
352.733 712.477 o
347.557 714.176 o
342.381 716.897 o
337.206 719.364 o
332.03 721.362 o
326.854 723.262 o
321.679 725.149 o
316.503 727.131 o
311.327 728.778 o
306.152 730.21 o
300.976 732.175 o
295.8 733.909 o
290.625 734.962 o
285.449 736.474 o
280.273 737.996 o
275.098 739.55 o
269.922 740.65 o
264.747 741.944 o
259.571 743.58 o
254.395 744.637 o
249.22 745.833 o
244.044 747.033 o
238.868 748.355 o
233.693 749.257 o
228.517 750.423 o
223.341 751.65 o
218.166 752.65 o
212.99 753.63 o
207.814 754.809 o
202.639 755.686 o
197.463 756.765 o
192.287 757.782 o
187.112 758.815 o
181.936 759.632 o
176.76 760.647 o
171.585 761.702 o
166.409 762.46 o
161.233 763.433 o
156.058 764.196 o
150.882 765.085 o
145.707 765.961 o
140.531 766.772 o
135.355 767.609 o
130.18 768.486 o
125.004 769.258 o
119.828 769.98 o
114.653 770.812 o
109.477 771.604 o
104.301 772.301 o
99.1257 773.079 o
grestore
2.500 setlinewidth
0 setlinejoin
2 setlinecap
0.000 setgray
gsave
93.95 473.969728 m
93.95 787.32625 l
stroke
grestore
gsave
482.123913 473.969728 m
482.123913 787.32625 l
stroke
grestore
gsave
93.95 473.969728 m
482.123913 473.969728 l
stroke
grestore
gsave
93.95 787.32625 m
482.123913 787.32625 l
stroke
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

93.95 793.326 translate
0 rotate
0 0 m /parenleft glyphshow
8.3252 0 m /a glyphshow
19.4214 0 m /parenright glyphshow
grestore
0.050 setlinewidth
1 setlinejoin
0 setlinecap
[0.185 0.08] 0 setdash
0.722 0.224 0.055 setrgbcolor
gsave
367.123913 762.32625 m
387.123913 762.32625 l
407.123913 762.32625 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

0 2 m
-2 -2 l
2 -2 l
cl

gsave
0.722 0.224 0.055 setrgbcolor
fill
grestore
stroke
grestore
} bind def
387.124 762.326 o
grestore
0.000 setgray
gsave
423.124 755.326 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.546875 moveto
/less glyphshow
11.2793 0.546875 moveto
/u glyphshow
/TimesNewRomanPSMT 14.0 selectfont
21.5799 -4.60562 moveto
/x glyphshow
/TimesNewRomanPSMT 20.0 selectfont
29.4386 0.546875 moveto
/greater glyphshow
grestore
0.050 setlinewidth
1 setlinejoin
[0.185 0.08] 0 setdash
1.000 0.271 0.000 setrgbcolor
gsave
367.123913 732.32625 m
387.123913 732.32625 l
407.123913 732.32625 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-4 -4 m
4 -4 l
4 4 l
-4 4 l
cl

gsave
grestore
stroke
grestore
} bind def
387.124 732.326 o
grestore
0.000 setgray
gsave
423.124 725.326 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.546875 moveto
/less glyphshow
11.2793 0.546875 moveto
/u glyphshow
/TimesNewRomanPSMT 14.0 selectfont
21.5799 -4.60562 moveto
/y glyphshow
/TimesNewRomanPSMT 20.0 selectfont
29.4386 0.546875 moveto
/greater glyphshow
grestore
0.050 setlinewidth
1 setlinejoin
[0.185 0.08] 0 setdash
1.000 0.514 0.000 setrgbcolor
gsave
367.123913 699.32625 m
387.123913 699.32625 l
407.123913 699.32625 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
387.124 699.326 o
grestore
0.000 setgray
gsave
423.124 692.326 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.546875 moveto
/less glyphshow
11.2793 0.546875 moveto
/u glyphshow
/TimesNewRomanPSMT 14.0 selectfont
21.5799 -4.60562 moveto
/z glyphshow
/TimesNewRomanPSMT 20.0 selectfont
28.6525 0.546875 moveto
/greater glyphshow
grestore
gsave
598.576087 473.969728 m
986.75 473.969728 l
986.75 787.32625 l
598.576087 787.32625 l
cl
1.000 setgray
fill
grestore
2.500 setlinewidth
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

592.326 446.61 translate
0 rotate
0 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
676.211 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
676.211 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

657.461 446.61 translate
0 rotate
0 0 m /three glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
753.846 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
753.846 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

735.096 446.61 translate
0 rotate
0 0 m /six glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
831.48 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
831.48 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

812.73 446.61 translate
0 rotate
0 0 m /nine glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
909.115 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
909.115 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

884.115 446.61 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /two glyphshow
25 0 m /zero glyphshow
37.5 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

961.75 446.61 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /five glyphshow
25 0 m /zero glyphshow
37.5 0 m /zero glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
617.985 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
617.985 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
637.393 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
637.393 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
656.802 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
656.802 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
695.62 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
695.62 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
715.028 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
715.028 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
734.437 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
734.437 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
773.254 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
773.254 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
792.663 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
792.663 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
812.072 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
812.072 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.889 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.889 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
870.298 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
870.298 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
889.707 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
889.707 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
928.524 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
928.524 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
947.933 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
947.933 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
967.341 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
967.341 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

709.202 419.907 translate
0 rotate
0 0 m /T glyphshow
13.521 0 m /e glyphshow
24.6172 0 m /m glyphshow
44.063 0 m /p glyphshow
56.563 0 m /e glyphshow
67.6592 0 m /r glyphshow
75.9844 0 m /a glyphshow
87.0806 0 m /t glyphshow
94.0264 0 m /u glyphshow
106.526 0 m /r glyphshow
114.852 0 m /e glyphshow
125.948 0 m /space glyphshow
132.198 0 m /parenleft glyphshow
140.523 0 m /K glyphshow
158.577 0 m /parenright glyphshow
grestore
2.500 setlinewidth
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 488.207 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 488.207 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

544.826 479.527 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 583.33 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 583.33 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

544.826 574.65 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /two glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 678.452 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 678.452 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

544.826 669.772 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /four glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 773.574 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 773.574 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

544.826 764.895 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /six glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 511.988 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 511.988 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 535.768 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 535.768 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 559.549 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 559.549 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 607.11 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 607.11 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 630.891 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 630.891 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 654.671 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 654.671 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 702.232 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 702.232 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 726.013 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 726.013 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 749.794 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 749.794 o
grestore
gsave
529.826 587.648 translate
90 rotate
/TimesNewRomanPSMT 25.0 selectfont
0 0.6875 moveto
/less glyphshow
14.0991 0.6875 moveto
/omega glyphshow
/TimesNewRomanPSMT 17.5 selectfont
30.9299 -5.75313 moveto
/x glyphshow
39.6799 -5.75313 moveto
/comma glyphshow
46.7009 -5.75313 moveto
/y glyphshow
55.4509 -5.75313 moveto
/comma glyphshow
62.4719 -5.75313 moveto
/z glyphshow
/TimesNewRomanPSMT 25.0 selectfont
71.3127 0.6875 moveto
/greater glyphshow
grestore
0.050 setlinewidth
[0.185 0.08] 0 setdash
0.722 0.224 0.055 setrgbcolor
gsave
388.174 313.357 598.576 473.97 clipbox
981.574348 488.49182 m
976.398696 488.942747 l
971.223043 488.281457 l
966.047391 489.638663 l
960.871739 488.984506 l
955.696087 488.548085 l
950.520435 489.414174 l
945.344783 489.518285 l
940.16913 488.644016 l
934.993478 488.476125 l
929.817826 488.442261 l
924.642174 488.224621 l
919.466522 488.26248 l
914.29087 488.26852 l
909.115217 488.728722 l
903.939565 489.368943 l
898.763913 627.990841 l
893.588261 643.605603 l
888.412609 653.448294 l
883.236957 661.046001 l
878.061304 667.968293 l
872.885652 672.700344 l
867.71 678.461097 l
862.534348 683.502771 l
857.358696 686.631298 l
852.183043 691.178765 l
847.007391 695.222321 l
841.831739 697.99371 l
836.656087 700.781271 l
831.480435 704.474016 l
826.304783 706.912525 l
821.12913 709.704033 l
815.953478 712.477135 l
810.777826 714.769964 l
805.602174 717.065599 l
800.426522 718.980793 l
795.25087 721.637227 l
790.075217 723.499057 l
784.899565 725.364977 l
779.723913 727.523256 l
774.548261 729.342233 l
769.372609 731.021237 l
764.196957 733.009485 l
759.021304 734.925296 l
753.845652 736.618617 l
748.67 738.190324 l
743.494348 739.678275 l
738.318696 741.328695 l
733.143043 743.099208 l
727.967391 744.314396 l
722.791739 745.715786 l
717.616087 747.336908 l
712.440435 748.779724 l
707.264783 749.90545 l
702.08913 751.403056 l
696.913478 752.830367 l
691.737826 754.106338 l
686.562174 755.244192 l
681.386522 756.493576 l
676.21087 757.622108 l
671.035217 758.997339 l
665.859565 760.188033 l
660.683913 761.341344 l
655.508261 762.481814 l
650.332609 763.616481 l
645.156957 764.648939 l
639.981304 765.74004 l
634.805652 766.944907 l
629.63 768.009326 l
624.454348 769.080118 l
619.278696 770.004137 l
614.103043 771.073217 l
608.927391 772.067341 l
603.751739 773.044628 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
388.174 313.357 598.576 473.97 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

0 2 m
-2 -2 l
2 -2 l
cl

gsave
0.722 0.224 0.055 setrgbcolor
fill
grestore
stroke
grestore
} bind def
981.574 488.492 o
976.399 488.943 o
971.223 488.281 o
966.047 489.639 o
960.872 488.985 o
955.696 488.548 o
950.52 489.414 o
945.345 489.518 o
940.169 488.644 o
934.993 488.476 o
929.818 488.442 o
924.642 488.225 o
919.467 488.262 o
914.291 488.269 o
909.115 488.729 o
903.94 489.369 o
898.764 627.991 o
893.588 643.606 o
888.413 653.448 o
883.237 661.046 o
878.061 667.968 o
872.886 672.7 o
867.71 678.461 o
862.534 683.503 o
857.359 686.631 o
852.183 691.179 o
847.007 695.222 o
841.832 697.994 o
836.656 700.781 o
831.48 704.474 o
826.305 706.913 o
821.129 709.704 o
815.953 712.477 o
810.778 714.77 o
805.602 717.066 o
800.427 718.981 o
795.251 721.637 o
790.075 723.499 o
784.9 725.365 o
779.724 727.523 o
774.548 729.342 o
769.373 731.021 o
764.197 733.009 o
759.021 734.925 o
753.846 736.619 o
748.67 738.19 o
743.494 739.678 o
738.319 741.329 o
733.143 743.099 o
727.967 744.314 o
722.792 745.716 o
717.616 747.337 o
712.44 748.78 o
707.265 749.905 o
702.089 751.403 o
696.913 752.83 o
691.738 754.106 o
686.562 755.244 o
681.387 756.494 o
676.211 757.622 o
671.035 758.997 o
665.86 760.188 o
660.684 761.341 o
655.508 762.482 o
650.333 763.616 o
645.157 764.649 o
639.981 765.74 o
634.806 766.945 o
629.63 768.009 o
624.454 769.08 o
619.279 770.004 o
614.103 771.073 o
608.927 772.067 o
603.752 773.045 o
grestore
0.050 setlinewidth
1 setlinejoin
[0.185 0.08] 0 setdash
1.000 0.271 0.000 setrgbcolor
gsave
388.174 313.357 598.576 473.97 clipbox
981.574348 488.739947 m
976.398696 488.225477 l
971.223043 488.969239 l
966.047391 488.623041 l
960.871739 490.188803 l
955.696087 488.924674 l
950.520435 488.427707 l
945.344783 489.245902 l
940.16913 488.458004 l
934.993478 489.218127 l
929.817826 488.696095 l
924.642174 490.600017 l
919.466522 490.491387 l
914.29087 491.874656 l
909.115217 488.367257 l
903.939565 492.452049 l
898.763913 628.153738 l
893.588261 642.961102 l
888.412609 653.628027 l
883.236957 660.806245 l
878.061304 667.615912 l
872.885652 673.584839 l
867.71 678.550797 l
862.534348 682.865071 l
857.358696 687.539764 l
852.183043 691.638586 l
847.007391 694.646688 l
841.831739 698.634692 l
836.656087 701.181546 l
831.480435 703.885066 l
826.304783 706.86173 l
821.12913 709.692856 l
815.953478 712.144968 l
810.777826 714.57268 l
805.602174 716.940038 l
800.426522 719.254935 l
795.25087 721.424724 l
790.075217 723.742237 l
784.899565 725.560881 l
779.723913 727.894899 l
774.548261 729.658943 l
769.372609 731.560153 l
764.196957 733.297943 l
759.021304 734.827748 l
753.845652 736.459335 l
748.67 738.347085 l
743.494348 739.817249 l
738.318696 741.223538 l
733.143043 742.828585 l
727.967391 744.372991 l
722.791739 746.014613 l
717.616087 747.312224 l
712.440435 748.578208 l
707.264783 750.204324 l
702.08913 751.503315 l
696.913478 752.757075 l
691.737826 753.958185 l
686.562174 755.300647 l
681.386522 756.531673 l
676.21087 757.770166 l
671.035217 759.094126 l
665.859565 760.241016 l
660.683913 761.346576 l
655.508261 762.407951 l
650.332609 763.634269 l
645.156957 764.786295 l
639.981304 765.803439 l
634.805652 766.787527 l
629.63 767.892516 l
624.454348 769.005019 l
619.278696 770.022067 l
614.103043 771.027653 l
608.927391 772.087697 l
603.751739 773.058658 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
388.174 313.357 598.576 473.97 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-4 -4 m
4 -4 l
4 4 l
-4 4 l
cl

gsave
grestore
stroke
grestore
} bind def
981.574 488.74 o
976.399 488.225 o
971.223 488.969 o
966.047 488.623 o
960.872 490.189 o
955.696 488.925 o
950.52 488.428 o
945.345 489.246 o
940.169 488.458 o
934.993 489.218 o
929.818 488.696 o
924.642 490.6 o
919.467 490.491 o
914.291 491.875 o
909.115 488.367 o
903.94 492.452 o
898.764 628.154 o
893.588 642.961 o
888.413 653.628 o
883.237 660.806 o
878.061 667.616 o
872.886 673.585 o
867.71 678.551 o
862.534 682.865 o
857.359 687.54 o
852.183 691.639 o
847.007 694.647 o
841.832 698.635 o
836.656 701.182 o
831.48 703.885 o
826.305 706.862 o
821.129 709.693 o
815.953 712.145 o
810.778 714.573 o
805.602 716.94 o
800.427 719.255 o
795.251 721.425 o
790.075 723.742 o
784.9 725.561 o
779.724 727.895 o
774.548 729.659 o
769.373 731.56 o
764.197 733.298 o
759.021 734.828 o
753.846 736.459 o
748.67 738.347 o
743.494 739.817 o
738.319 741.224 o
733.143 742.829 o
727.967 744.373 o
722.792 746.015 o
717.616 747.312 o
712.44 748.578 o
707.265 750.204 o
702.089 751.503 o
696.913 752.757 o
691.738 753.958 o
686.562 755.301 o
681.387 756.532 o
676.211 757.77 o
671.035 759.094 o
665.86 760.241 o
660.684 761.347 o
655.508 762.408 o
650.333 763.634 o
645.157 764.786 o
639.981 765.803 o
634.806 766.788 o
629.63 767.893 o
624.454 769.005 o
619.279 770.022 o
614.103 771.028 o
608.927 772.088 o
603.752 773.059 o
grestore
0.050 setlinewidth
1 setlinejoin
[0.185 0.08] 0 setdash
1.000 0.514 0.000 setrgbcolor
gsave
388.174 313.357 598.576 473.97 clipbox
981.574348 488.596217 m
976.398696 489.347826 l
971.223043 488.25097 l
966.047391 489.21865 l
960.871739 488.898944 l
955.696087 488.961439 l
950.520435 489.109069 l
945.344783 488.288258 l
940.16913 488.242362 l
934.993478 488.213207 l
929.817826 488.850717 l
924.642174 489.076347 l
919.466522 489.940296 l
914.29087 488.816092 l
909.115217 490.372627 l
903.939565 489.332178 l
898.763913 627.832272 l
893.588261 643.665102 l
888.412609 652.886739 l
883.236957 661.40428 l
878.061304 667.838641 l
872.885652 673.394595 l
867.71 679.014994 l
862.534348 683.284941 l
857.358696 687.539764 l
852.183043 691.099813 l
847.007391 694.335162 l
841.831739 697.851788 l
836.656087 701.399376 l
831.480435 704.196021 l
826.304783 706.477673 l
821.12913 709.763484 l
815.953478 712.319565 l
810.777826 714.888629 l
805.602174 717.17071 l
800.426522 719.565605 l
795.25087 721.520179 l
790.075217 723.591135 l
784.899565 725.787986 l
779.723913 727.536906 l
774.548261 729.312222 l
769.372609 731.27764 l
764.196957 733.176948 l
759.021304 734.717597 l
753.845652 736.456386 l
748.67 737.992041 l
743.494348 739.678893 l
738.318696 741.333832 l
733.143043 742.71225 l
727.967391 744.425308 l
722.791739 745.84363 l
717.616087 747.256102 l
712.440435 748.703674 l
707.264783 750.083091 l
702.08913 751.429262 l
696.913478 752.742141 l
691.737826 754.158655 l
686.562174 755.447991 l
681.386522 756.671312 l
676.21087 757.827905 l
671.035217 758.949065 l
665.859565 760.268221 l
660.683913 761.439511 l
655.508261 762.541646 l
650.332609 763.552463 l
645.156957 764.790576 l
639.981304 765.843913 l
634.805652 766.920413 l
629.63 767.974178 l
624.454348 769.020049 l
619.278696 770.075288 l
614.103043 771.088865 l
608.927391 772.064154 l
603.751739 773.082772 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
388.174 313.357 598.576 473.97 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
981.574 488.596 o
976.399 489.348 o
971.223 488.251 o
966.047 489.219 o
960.872 488.899 o
955.696 488.961 o
950.52 489.109 o
945.345 488.288 o
940.169 488.242 o
934.993 488.213 o
929.818 488.851 o
924.642 489.076 o
919.467 489.94 o
914.291 488.816 o
909.115 490.373 o
903.94 489.332 o
898.764 627.832 o
893.588 643.665 o
888.413 652.887 o
883.237 661.404 o
878.061 667.839 o
872.886 673.395 o
867.71 679.015 o
862.534 683.285 o
857.359 687.54 o
852.183 691.1 o
847.007 694.335 o
841.832 697.852 o
836.656 701.399 o
831.48 704.196 o
826.305 706.478 o
821.129 709.763 o
815.953 712.32 o
810.778 714.889 o
805.602 717.171 o
800.427 719.566 o
795.251 721.52 o
790.075 723.591 o
784.9 725.788 o
779.724 727.537 o
774.548 729.312 o
769.373 731.278 o
764.197 733.177 o
759.021 734.718 o
753.846 736.456 o
748.67 737.992 o
743.494 739.679 o
738.319 741.334 o
733.143 742.712 o
727.967 744.425 o
722.792 745.844 o
717.616 747.256 o
712.44 748.704 o
707.265 750.083 o
702.089 751.429 o
696.913 752.742 o
691.738 754.159 o
686.562 755.448 o
681.387 756.671 o
676.211 757.828 o
671.035 758.949 o
665.86 760.268 o
660.684 761.44 o
655.508 762.542 o
650.333 763.552 o
645.157 764.791 o
639.981 765.844 o
634.806 766.92 o
629.63 767.974 o
624.454 769.02 o
619.279 770.075 o
614.103 771.089 o
608.927 772.064 o
603.752 773.083 o
grestore
2.500 setlinewidth
0 setlinejoin
2 setlinecap
0.000 setgray
gsave
598.576087 473.969728 m
598.576087 787.32625 l
stroke
grestore
gsave
986.75 473.969728 m
986.75 787.32625 l
stroke
grestore
gsave
598.576087 473.969728 m
986.75 473.969728 l
stroke
grestore
gsave
598.576087 787.32625 m
986.75 787.32625 l
stroke
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

598.576 793.326 translate
0 rotate
0 0 m /parenleft glyphshow
8.3252 0 m /b glyphshow
20.8252 0 m /parenright glyphshow
grestore
0.050 setlinewidth
1 setlinejoin
0 setlinecap
[0.185 0.08] 0 setdash
0.722 0.224 0.055 setrgbcolor
gsave
856.75 762.32625 m
876.75 762.32625 l
896.75 762.32625 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

0 2 m
-2 -2 l
2 -2 l
cl

gsave
0.722 0.224 0.055 setrgbcolor
fill
grestore
stroke
grestore
} bind def
876.75 762.326 o
grestore
0.000 setgray
gsave
912.75 755.326 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
3.024 0.546875 moveto
/less glyphshow
17.3273 0.546875 moveto
/omega glyphshow
/TimesNewRomanPSMT 14.0 selectfont
30.7919 -4.60562 moveto
/x glyphshow
/TimesNewRomanPSMT 20.0 selectfont
41.6747 0.546875 moveto
/greater glyphshow
grestore
0.050 setlinewidth
1 setlinejoin
[0.185 0.08] 0 setdash
1.000 0.271 0.000 setrgbcolor
gsave
856.75 732.32625 m
876.75 732.32625 l
896.75 732.32625 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-4 -4 m
4 -4 l
4 4 l
-4 4 l
cl

gsave
grestore
stroke
grestore
} bind def
876.75 732.326 o
grestore
0.000 setgray
gsave
912.75 725.326 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
3.024 0.546875 moveto
/less glyphshow
17.3273 0.546875 moveto
/omega glyphshow
/TimesNewRomanPSMT 14.0 selectfont
30.7919 -4.60562 moveto
/y glyphshow
/TimesNewRomanPSMT 20.0 selectfont
38.6507 0.546875 moveto
/greater glyphshow
grestore
0.050 setlinewidth
1 setlinejoin
[0.185 0.08] 0 setdash
1.000 0.514 0.000 setrgbcolor
gsave
856.75 699.32625 m
876.75 699.32625 l
896.75 699.32625 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
876.75 699.326 o
grestore
0.000 setgray
gsave
912.75 692.326 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
3.024 0.546875 moveto
/less glyphshow
17.3273 0.546875 moveto
/omega glyphshow
/TimesNewRomanPSMT 14.0 selectfont
30.7919 -4.60562 moveto
/z glyphshow
/TimesNewRomanPSMT 20.0 selectfont
40.8885 0.546875 moveto
/greater glyphshow
grestore
gsave
93.95 66.60625 m
482.123913 66.60625 l
482.123913 379.962772 l
93.95 379.962772 l
cl
1.000 setgray
fill
grestore
0 setlinejoin
0.902 0.902 0.980 setrgbcolor
gsave
388.174 313.357 93.95 66.606 clipbox
93.95 66.60625 m
93.95 379.962772 l
399.313478 379.962772 l
399.313478 66.60625 l
cl
gsave
fill
grestore
stroke
grestore
0.800 setgray
gsave
388.174 313.357 93.95 66.606 clipbox
399.313478 66.60625 m
399.313478 379.962772 l
482.123913 379.962772 l
482.123913 66.60625 l
cl
gsave
fill
grestore
stroke
grestore
2.500 setlinewidth
1 setlinejoin
0.000 setgray
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 379.963 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

87.7 39.2469 translate
0 rotate
0 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
171.585 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
171.585 379.963 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

152.835 39.2469 translate
0 rotate
0 0 m /three glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
249.22 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
249.22 379.963 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

230.47 39.2469 translate
0 rotate
0 0 m /six glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
326.854 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
326.854 379.963 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

308.104 39.2469 translate
0 rotate
0 0 m /nine glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
404.489 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
404.489 379.963 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

379.489 39.2469 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /two glyphshow
25 0 m /zero glyphshow
37.5 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 379.963 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

457.124 39.2469 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /five glyphshow
25 0 m /zero glyphshow
37.5 0 m /zero glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
113.359 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
113.359 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
132.767 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
132.767 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
152.176 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
152.176 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
190.993 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
190.993 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
210.402 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
210.402 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
229.811 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
229.811 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
268.628 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
268.628 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
288.037 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
288.037 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
307.446 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
307.446 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
346.263 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
346.263 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
365.672 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
365.672 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
385.08 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
385.08 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
423.898 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
423.898 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
443.307 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
443.307 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
462.715 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
462.715 379.963 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

204.576 12.5437 translate
0 rotate
0 0 m /T glyphshow
13.521 0 m /e glyphshow
24.6172 0 m /m glyphshow
44.063 0 m /p glyphshow
56.563 0 m /e glyphshow
67.6592 0 m /r glyphshow
75.9844 0 m /a glyphshow
87.0806 0 m /t glyphshow
94.0264 0 m /u glyphshow
106.526 0 m /r glyphshow
114.852 0 m /e glyphshow
125.948 0 m /space glyphshow
132.198 0 m /parenleft glyphshow
140.523 0 m /K glyphshow
158.577 0 m /parenright glyphshow
grestore
2.500 setlinewidth
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 66.6062 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

40.2 57.9266 translate
0 rotate
0 0 m /seven glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /eight glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 129.885 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 129.885 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

40.2 121.205 translate
0 rotate
0 0 m /seven glyphshow
12.5 0 m /period glyphshow
18.75 0 m /one glyphshow
31.25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 193.163 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 193.163 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

40.2 184.483 translate
0 rotate
0 0 m /seven glyphshow
12.5 0 m /period glyphshow
18.75 0 m /one glyphshow
31.25 0 m /two glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 256.441 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 256.441 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

40.2 247.762 translate
0 rotate
0 0 m /seven glyphshow
12.5 0 m /period glyphshow
18.75 0 m /one glyphshow
31.25 0 m /four glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 319.72 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 319.72 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

40.2 311.04 translate
0 rotate
0 0 m /seven glyphshow
12.5 0 m /period glyphshow
18.75 0 m /one glyphshow
31.25 0 m /six glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 82.4258 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 82.4258 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 98.2454 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 98.2454 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 114.065 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 114.065 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 145.704 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 145.704 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 161.524 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 161.524 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 177.343 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 177.343 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 208.983 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 208.983 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 224.802 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 224.802 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 240.622 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 240.622 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 272.261 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 272.261 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 288.081 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 288.081 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 303.9 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 303.9 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 335.539 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 335.539 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 351.359 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 351.359 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
93.95 367.178 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
482.124 367.178 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

30.8562 100.417 translate
90 rotate
0 0 m /L glyphshow
15.271 0 m /a glyphshow
26.3672 0 m /t glyphshow
33.313 0 m /t glyphshow
40.2588 0 m /i glyphshow
47.2046 0 m /c glyphshow
58.3008 0 m /e glyphshow
69.397 0 m /space glyphshow
75.647 0 m /P glyphshow
89.5508 0 m /a glyphshow
100.647 0 m /r glyphshow
108.972 0 m /a glyphshow
120.068 0 m /m glyphshow
139.514 0 m /e glyphshow
150.61 0 m /t glyphshow
157.556 0 m /e glyphshow
168.652 0 m /r glyphshow
176.978 0 m /s glyphshow
186.707 0 m /space glyphshow
192.957 0 m /parenleft glyphshow
201.282 0 m /a glyphshow
212.378 0 m /period glyphshow
218.628 0 m /u glyphshow
231.128 0 m /period glyphshow
237.378 0 m /parenright glyphshow
grestore
0.050 setlinewidth
[0.185 0.08] 0 setdash
0.722 0.224 0.055 setrgbcolor
gsave
388.174 313.357 93.95 66.606 clipbox
476.948261 105.598379 m
471.772609 104.861186 l
466.596957 104.193599 l
461.421304 103.946813 l
456.245652 103.24126 l
451.07 102.858426 l
445.894348 102.529378 l
440.718696 102.05479 l
435.543043 101.804841 l
430.367391 101.466301 l
425.191739 101.260647 l
420.016087 101.963037 l
414.840435 102.627459 l
409.664783 102.940687 l
404.48913 104.373942 l
399.313478 106.784848 l
394.137826 164.576979 l
388.962174 178.33686 l
383.786522 188.331677 l
378.61087 196.462947 l
373.435217 203.739959 l
368.259565 209.609028 l
363.083913 216.243764 l
357.908261 221.862883 l
352.732609 226.906169 l
347.556957 232.009569 l
342.381304 236.923134 l
337.205652 241.545619 l
332.03 245.538484 l
326.854348 249.819265 l
321.678696 253.555853 l
316.503043 257.874601 l
311.327391 261.589041 l
306.151739 265.085171 l
300.976087 268.774299 l
295.800435 271.979349 l
290.624783 275.516609 l
285.44913 278.810248 l
280.273478 281.869757 l
275.097826 285.289953 l
269.922174 288.102676 l
264.746522 291.007153 l
259.57087 294.389382 l
254.395217 297.104024 l
249.219565 299.859796 l
244.043913 302.700995 l
238.868261 305.548521 l
233.692609 307.978411 l
228.516957 310.825937 l
223.341304 313.315941 l
218.165652 315.90719 l
212.99 318.431997 l
207.814348 320.918836 l
202.638696 323.39302 l
197.463043 325.854549 l
192.287391 328.331897 l
187.111739 330.628902 l
181.936087 332.916414 l
176.760435 335.194436 l
171.584783 337.412342 l
166.40913 339.756806 l
161.233478 341.984204 l
156.057826 344.236914 l
150.882174 346.271313 l
145.706522 348.492384 l
140.53087 350.539439 l
135.355217 352.570675 l
130.179565 354.744287 l
125.003913 356.756539 l
119.828261 358.752971 l
114.652609 360.777879 l
109.476957 362.790131 l
104.301304 364.723285 l
99.125652 366.691242 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
388.174 313.357 93.95 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

0 2 m
-2 -2 l
2 -2 l
cl

gsave
0.722 0.224 0.055 setrgbcolor
fill
grestore
stroke
grestore
} bind def
476.948 105.598 o
471.773 104.861 o
466.597 104.194 o
461.421 103.947 o
456.246 103.241 o
451.07 102.858 o
445.894 102.529 o
440.719 102.055 o
435.543 101.805 o
430.367 101.466 o
425.192 101.261 o
420.016 101.963 o
414.84 102.627 o
409.665 102.941 o
404.489 104.374 o
399.313 106.785 o
394.138 164.577 o
388.962 178.337 o
383.787 188.332 o
378.611 196.463 o
373.435 203.74 o
368.26 209.609 o
363.084 216.244 o
357.908 221.863 o
352.733 226.906 o
347.557 232.01 o
342.381 236.923 o
337.206 241.546 o
332.03 245.538 o
326.854 249.819 o
321.679 253.556 o
316.503 257.875 o
311.327 261.589 o
306.152 265.085 o
300.976 268.774 o
295.8 271.979 o
290.625 275.517 o
285.449 278.81 o
280.273 281.87 o
275.098 285.29 o
269.922 288.103 o
264.747 291.007 o
259.571 294.389 o
254.395 297.104 o
249.22 299.86 o
244.044 302.701 o
238.868 305.549 o
233.693 307.978 o
228.517 310.826 o
223.341 313.316 o
218.166 315.907 o
212.99 318.432 o
207.814 320.919 o
202.639 323.393 o
197.463 325.855 o
192.287 328.332 o
187.112 330.629 o
181.936 332.916 o
176.76 335.194 o
171.585 337.412 o
166.409 339.757 o
161.233 341.984 o
156.058 344.237 o
150.882 346.271 o
145.707 348.492 o
140.531 350.539 o
135.355 352.571 o
130.18 354.744 o
125.004 356.757 o
119.828 358.753 o
114.653 360.778 o
109.477 362.79 o
104.301 364.723 o
99.1257 366.691 o
grestore
0.050 setlinewidth
1 setlinejoin
[0.185 0.08] 0 setdash
1.000 0.271 0.000 setrgbcolor
gsave
388.174 313.357 93.95 66.606 clipbox
476.948261 105.598379 m
471.772609 104.861186 l
466.596957 104.193599 l
461.421304 103.946813 l
456.245652 103.24126 l
451.07 102.858426 l
445.894348 102.529378 l
440.718696 102.05479 l
435.543043 101.804841 l
430.367391 101.466301 l
425.191739 101.260647 l
420.016087 101.963037 l
414.840435 102.627459 l
409.664783 102.940687 l
404.48913 104.373942 l
399.313478 106.784848 l
394.137826 164.576979 l
388.962174 178.33686 l
383.786522 188.331677 l
378.61087 196.462947 l
373.435217 203.739959 l
368.259565 209.609028 l
363.083913 216.243764 l
357.908261 221.862883 l
352.732609 226.906169 l
347.556957 232.009569 l
342.381304 236.923134 l
337.205652 241.545619 l
332.03 245.538484 l
326.854348 249.819265 l
321.678696 253.555853 l
316.503043 257.874601 l
311.327391 261.589041 l
306.151739 265.085171 l
300.976087 268.774299 l
295.800435 271.979349 l
290.624783 275.516609 l
285.44913 278.810248 l
280.273478 281.869757 l
275.097826 285.289953 l
269.922174 288.102676 l
264.746522 291.007153 l
259.57087 294.389382 l
254.395217 297.104024 l
249.219565 299.859796 l
244.043913 302.700995 l
238.868261 305.548521 l
233.692609 307.978411 l
228.516957 310.825937 l
223.341304 313.315941 l
218.165652 315.90719 l
212.99 318.431997 l
207.814348 320.918836 l
202.638696 323.39302 l
197.463043 325.854549 l
192.287391 328.331897 l
187.111739 330.628902 l
181.936087 332.916414 l
176.760435 335.194436 l
171.584783 337.412342 l
166.40913 339.756806 l
161.233478 341.984204 l
156.057826 344.236914 l
150.882174 346.271313 l
145.706522 348.492384 l
140.53087 350.539439 l
135.355217 352.570675 l
130.179565 354.744287 l
125.003913 356.756539 l
119.828261 358.752971 l
114.652609 360.777879 l
109.476957 362.790131 l
104.301304 364.723285 l
99.125652 366.691242 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
388.174 313.357 93.95 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-4 -4 m
4 -4 l
4 4 l
-4 4 l
cl

gsave
grestore
stroke
grestore
} bind def
476.948 105.598 o
471.773 104.861 o
466.597 104.194 o
461.421 103.947 o
456.246 103.241 o
451.07 102.858 o
445.894 102.529 o
440.719 102.055 o
435.543 101.805 o
430.367 101.466 o
425.192 101.261 o
420.016 101.963 o
414.84 102.627 o
409.665 102.941 o
404.489 104.374 o
399.313 106.785 o
394.138 164.577 o
388.962 178.337 o
383.787 188.332 o
378.611 196.463 o
373.435 203.74 o
368.26 209.609 o
363.084 216.244 o
357.908 221.863 o
352.733 226.906 o
347.557 232.01 o
342.381 236.923 o
337.206 241.546 o
332.03 245.538 o
326.854 249.819 o
321.679 253.556 o
316.503 257.875 o
311.327 261.589 o
306.152 265.085 o
300.976 268.774 o
295.8 271.979 o
290.625 275.517 o
285.449 278.81 o
280.273 281.87 o
275.098 285.29 o
269.922 288.103 o
264.747 291.007 o
259.571 294.389 o
254.395 297.104 o
249.22 299.86 o
244.044 302.701 o
238.868 305.549 o
233.693 307.978 o
228.517 310.826 o
223.341 313.316 o
218.166 315.907 o
212.99 318.432 o
207.814 320.919 o
202.639 323.393 o
197.463 325.855 o
192.287 328.332 o
187.112 330.629 o
181.936 332.916 o
176.76 335.194 o
171.585 337.412 o
166.409 339.757 o
161.233 341.984 o
156.058 344.237 o
150.882 346.271 o
145.707 348.492 o
140.531 350.539 o
135.355 352.571 o
130.18 354.744 o
125.004 356.757 o
119.828 358.753 o
114.653 360.778 o
109.477 362.79 o
104.301 364.723 o
99.1257 366.691 o
grestore
0.050 setlinewidth
1 setlinejoin
[0.185 0.08] 0 setdash
1.000 0.514 0.000 setrgbcolor
gsave
388.174 313.357 93.95 66.606 clipbox
476.948261 105.598379 m
471.772609 104.861186 l
466.596957 104.193599 l
461.421304 103.946813 l
456.245652 103.24126 l
451.07 102.858426 l
445.894348 102.529378 l
440.718696 102.05479 l
435.543043 101.804841 l
430.367391 101.466301 l
425.191739 101.260647 l
420.016087 101.963037 l
414.840435 102.627459 l
409.664783 102.940687 l
404.48913 104.373942 l
399.313478 106.784848 l
394.137826 164.576979 l
388.962174 178.33686 l
383.786522 188.331677 l
378.61087 196.462947 l
373.435217 203.739959 l
368.259565 209.609028 l
363.083913 216.243764 l
357.908261 221.862883 l
352.732609 226.906169 l
347.556957 232.009569 l
342.381304 236.923134 l
337.205652 241.545619 l
332.03 245.538484 l
326.854348 249.819265 l
321.678696 253.555853 l
316.503043 257.874601 l
311.327391 261.589041 l
306.151739 265.085171 l
300.976087 268.774299 l
295.800435 271.979349 l
290.624783 275.516609 l
285.44913 278.810248 l
280.273478 281.869757 l
275.097826 285.289953 l
269.922174 288.102676 l
264.746522 291.007153 l
259.57087 294.389382 l
254.395217 297.104024 l
249.219565 299.859796 l
244.043913 302.700995 l
238.868261 305.548521 l
233.692609 307.978411 l
228.516957 310.825937 l
223.341304 313.315941 l
218.165652 315.90719 l
212.99 318.431997 l
207.814348 320.918836 l
202.638696 323.39302 l
197.463043 325.854549 l
192.287391 328.331897 l
187.111739 330.628902 l
181.936087 332.916414 l
176.760435 335.194436 l
171.584783 337.412342 l
166.40913 339.756806 l
161.233478 341.984204 l
156.057826 344.236914 l
150.882174 346.271313 l
145.706522 348.492384 l
140.53087 350.539439 l
135.355217 352.570675 l
130.179565 354.744287 l
125.003913 356.756539 l
119.828261 358.752971 l
114.652609 360.777879 l
109.476957 362.790131 l
104.301304 364.723285 l
99.125652 366.691242 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
388.174 313.357 93.95 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
476.948 105.598 o
471.773 104.861 o
466.597 104.194 o
461.421 103.947 o
456.246 103.241 o
451.07 102.858 o
445.894 102.529 o
440.719 102.055 o
435.543 101.805 o
430.367 101.466 o
425.192 101.261 o
420.016 101.963 o
414.84 102.627 o
409.665 102.941 o
404.489 104.374 o
399.313 106.785 o
394.138 164.577 o
388.962 178.337 o
383.787 188.332 o
378.611 196.463 o
373.435 203.74 o
368.26 209.609 o
363.084 216.244 o
357.908 221.863 o
352.733 226.906 o
347.557 232.01 o
342.381 236.923 o
337.206 241.546 o
332.03 245.538 o
326.854 249.819 o
321.679 253.556 o
316.503 257.875 o
311.327 261.589 o
306.152 265.085 o
300.976 268.774 o
295.8 271.979 o
290.625 275.517 o
285.449 278.81 o
280.273 281.87 o
275.098 285.29 o
269.922 288.103 o
264.747 291.007 o
259.571 294.389 o
254.395 297.104 o
249.22 299.86 o
244.044 302.701 o
238.868 305.549 o
233.693 307.978 o
228.517 310.826 o
223.341 313.316 o
218.166 315.907 o
212.99 318.432 o
207.814 320.919 o
202.639 323.393 o
197.463 325.855 o
192.287 328.332 o
187.112 330.629 o
181.936 332.916 o
176.76 335.194 o
171.585 337.412 o
166.409 339.757 o
161.233 341.984 o
156.058 344.237 o
150.882 346.271 o
145.707 348.492 o
140.531 350.539 o
135.355 352.571 o
130.18 354.744 o
125.004 356.757 o
119.828 358.753 o
114.653 360.778 o
109.477 362.79 o
104.301 364.723 o
99.1257 366.691 o
grestore
2.500 setlinewidth
0 setlinejoin
2 setlinecap
0.000 setgray
gsave
93.95 66.60625 m
93.95 379.962772 l
stroke
grestore
gsave
482.123913 66.60625 m
482.123913 379.962772 l
stroke
grestore
gsave
93.95 66.60625 m
482.123913 66.60625 l
stroke
grestore
gsave
93.95 379.962772 m
482.123913 379.962772 l
stroke
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

200.419 223.285 translate
0 rotate
0 0 m /R glyphshow
16.6748 0 m /three glyphshow
29.1748 0 m /C glyphshow
grestore
gsave
402.543 223.285 translate
0 rotate
/TimesNewRomanPSMT 25.0 selectfont
0 0.921875 moveto
/P glyphshow
13.9038 0.921875 moveto
/m glyphshow
33.3496 0.546875 moveto
/three glyphshow
45.8496 0.921875 moveto
/m glyphshow
33.349609375 20.875 12.5 1.5625 rectfill
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

93.95 385.963 translate
0 rotate
0 0 m /parenleft glyphshow
8.3252 0 m /c glyphshow
19.4214 0 m /parenright glyphshow
grestore
0.050 setlinewidth
1 setlinejoin
0 setlinecap
[0.185 0.08] 0 setdash
0.722 0.224 0.055 setrgbcolor
gsave
398.123913 354.962772 m
418.123913 354.962772 l
438.123913 354.962772 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

0 2 m
-2 -2 l
2 -2 l
cl

gsave
0.722 0.224 0.055 setrgbcolor
fill
grestore
stroke
grestore
} bind def
418.124 354.963 o
grestore
0.000 setgray
gsave
454.124 347.963 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.796875 moveto
/a glyphshow
grestore
0.050 setlinewidth
1 setlinejoin
[0.185 0.08] 0 setdash
1.000 0.271 0.000 setrgbcolor
gsave
398.123913 326.681522 m
418.123913 326.681522 l
438.123913 326.681522 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-4 -4 m
4 -4 l
4 4 l
-4 4 l
cl

gsave
grestore
stroke
grestore
} bind def
418.124 326.682 o
grestore
0.000 setgray
gsave
454.124 319.682 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.109375 moveto
/b glyphshow
grestore
0.050 setlinewidth
1 setlinejoin
[0.185 0.08] 0 setdash
1.000 0.514 0.000 setrgbcolor
gsave
398.123913 298.400272 m
418.123913 298.400272 l
438.123913 298.400272 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
418.124 298.4 o
grestore
0.000 setgray
gsave
454.124 291.4 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.796875 moveto
/c glyphshow
grestore
gsave
598.576087 66.60625 m
986.75 66.60625 l
986.75 379.962772 l
598.576087 379.962772 l
cl
1.000 setgray
fill
grestore
2.500 setlinewidth
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 379.963 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

592.326 39.2469 translate
0 rotate
0 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
676.211 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
676.211 379.963 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

657.461 39.2469 translate
0 rotate
0 0 m /three glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
753.846 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
753.846 379.963 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

735.096 39.2469 translate
0 rotate
0 0 m /six glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
831.48 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
831.48 379.963 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

812.73 39.2469 translate
0 rotate
0 0 m /nine glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
909.115 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
909.115 379.963 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

884.115 39.2469 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /two glyphshow
25 0 m /zero glyphshow
37.5 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 379.963 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

961.75 39.2469 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /five glyphshow
25 0 m /zero glyphshow
37.5 0 m /zero glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
617.985 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
617.985 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
637.393 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
637.393 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
656.802 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
656.802 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
695.62 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
695.62 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
715.028 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
715.028 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
734.437 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
734.437 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
773.254 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
773.254 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
792.663 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
792.663 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
812.072 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
812.072 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.889 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
850.889 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
870.298 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
870.298 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
889.707 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
889.707 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
928.524 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
928.524 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
947.933 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
947.933 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
967.341 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
967.341 379.963 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

709.202 12.5437 translate
0 rotate
0 0 m /T glyphshow
13.521 0 m /e glyphshow
24.6172 0 m /m glyphshow
44.063 0 m /p glyphshow
56.563 0 m /e glyphshow
67.6592 0 m /r glyphshow
75.9844 0 m /a glyphshow
87.0806 0 m /t glyphshow
94.0264 0 m /u glyphshow
106.526 0 m /r glyphshow
114.852 0 m /e glyphshow
125.948 0 m /space glyphshow
132.198 0 m /parenleft glyphshow
140.523 0 m /K glyphshow
158.577 0 m /parenright glyphshow
grestore
2.500 setlinewidth
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 80.4945 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 80.4945 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

576.076 71.8149 translate
0 rotate
0 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 140.388 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 140.388 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

563.576 131.709 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 200.282 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 200.282 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

563.576 191.602 translate
0 rotate
0 0 m /two glyphshow
12.5 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 260.175 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 260.175 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

563.576 251.496 translate
0 rotate
0 0 m /three glyphshow
12.5 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 320.069 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 320.069 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

563.576 311.389 translate
0 rotate
0 0 m /four glyphshow
12.5 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 379.963 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

563.576 371.283 translate
0 rotate
0 0 m /five glyphshow
12.5 0 m /zero glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 68.5158 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 68.5158 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 92.4733 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 92.4733 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 104.452 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 104.452 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 116.431 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 116.431 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 128.409 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 128.409 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 152.367 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 152.367 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 164.346 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 164.346 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 176.324 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 176.324 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 188.303 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 188.303 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 212.261 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 212.261 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 224.239 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 224.239 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 236.218 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 236.218 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 248.197 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 248.197 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 272.154 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 272.154 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 284.133 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 284.133 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 296.112 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 296.112 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 308.09 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 308.09 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 332.048 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 332.048 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 344.027 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 344.027 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 356.005 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 356.005 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
598.576 367.984 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
986.75 367.984 o
grestore
gsave
553.576 112.285 translate
90 rotate
/TimesNewRomanPSMT 25.0 selectfont
0 0.148438 moveto
/P glyphshow
13.9038 0.148438 moveto
/o glyphshow
26.4038 0.148438 moveto
/l glyphshow
33.3496 0.148438 moveto
/a glyphshow
44.4458 0.148438 moveto
/r glyphshow
52.771 0.148438 moveto
/i glyphshow
59.7168 0.148438 moveto
/z glyphshow
70.813 0.148438 moveto
/a glyphshow
81.9092 0.148438 moveto
/t glyphshow
88.855 0.148438 moveto
/i glyphshow
95.8008 0.148438 moveto
/o glyphshow
108.301 0.148438 moveto
/n glyphshow
120.801 0.148438 moveto
/space glyphshow
127.051 0.148438 moveto
/parenleft glyphshow
135.376 0.148438 moveto
/mu glyphshow
148.779 0.148438 moveto
/C glyphshow
165.454 0.148438 moveto
/slash glyphshow
172.4 0.148438 moveto
/c glyphshow
183.496 0.148438 moveto
/m glyphshow
/TimesNewRomanPSMT 17.5 selectfont
203.318 15.1766 moveto
/two glyphshow
/TimesNewRomanPSMT 25.0 selectfont
213.141 0.148438 moveto
/parenright glyphshow
grestore
0.050 setlinewidth
[0.185 0.08] 0 setdash
0.722 0.224 0.055 setrgbcolor
gsave
388.174 313.357 598.576 66.606 clipbox
981.574348 83.899052 m
976.398696 90.033124 l
971.223043 80.873696 l
966.047391 82.032933 l
960.871739 91.683893 l
955.696087 94.596825 l
950.520435 88.933804 l
945.344783 102.520812 l
940.16913 86.156566 l
934.993478 95.816746 l
929.817826 94.261471 l
924.642174 86.836523 l
919.466522 126.105936 l
914.29087 127.516509 l
909.115217 83.371051 l
903.939565 92.679953 l
898.763913 271.722611 l
893.588261 279.972816 l
888.412609 287.370464 l
883.236957 291.410695 l
878.061304 294.975848 l
872.885652 296.745203 l
867.71 300.747677 l
862.534348 304.162208 l
857.358696 305.69405 l
852.183043 308.217787 l
847.007391 311.687338 l
841.831739 313.060886 l
836.656087 314.748653 l
831.480435 317.223699 l
826.304783 318.938263 l
821.12913 320.450101 l
815.953478 322.221511 l
810.777826 323.530886 l
805.602174 325.389834 l
800.426522 325.981577 l
795.25087 327.872008 l
790.075217 329.188121 l
784.899565 330.302906 l
779.723913 331.90679 l
774.548261 333.050665 l
769.372609 333.816385 l
764.196957 335.23999 l
759.021304 336.740172 l
753.845652 337.908099 l
748.67 339.027798 l
743.494348 340.084508 l
738.318696 341.121353 l
733.143043 342.135026 l
727.967391 343.097129 l
722.791739 343.83094 l
717.616087 345.06899 l
712.440435 345.846545 l
707.264783 346.731282 l
702.08913 347.592449 l
696.913478 348.631617 l
691.737826 349.387933 l
686.562174 350.234266 l
681.386522 351.223347 l
676.21087 352.035362 l
671.035217 352.747392 l
665.859565 353.575229 l
660.683913 354.384436 l
655.508261 355.247984 l
650.332609 356.116459 l
645.156957 356.687445 l
639.981304 357.367584 l
634.805652 358.284872 l
629.63 359.012291 l
624.454348 359.60974 l
619.278696 360.28655 l
614.103043 361.042944 l
608.927391 361.694516 l
603.751739 362.367665 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
388.174 313.357 598.576 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

0 2 m
-2 -2 l
2 -2 l
cl

gsave
0.722 0.224 0.055 setrgbcolor
fill
grestore
stroke
grestore
} bind def
981.574 83.8991 o
976.399 90.0331 o
971.223 80.8737 o
966.047 82.0329 o
960.872 91.6839 o
955.696 94.5968 o
950.52 88.9338 o
945.345 102.521 o
940.169 86.1566 o
934.993 95.8167 o
929.818 94.2615 o
924.642 86.8365 o
919.467 126.106 o
914.291 127.517 o
909.115 83.3711 o
903.94 92.68 o
898.764 271.723 o
893.588 279.973 o
888.413 287.37 o
883.237 291.411 o
878.061 294.976 o
872.886 296.745 o
867.71 300.748 o
862.534 304.162 o
857.359 305.694 o
852.183 308.218 o
847.007 311.687 o
841.832 313.061 o
836.656 314.749 o
831.48 317.224 o
826.305 318.938 o
821.129 320.45 o
815.953 322.222 o
810.778 323.531 o
805.602 325.39 o
800.427 325.982 o
795.251 327.872 o
790.075 329.188 o
784.9 330.303 o
779.724 331.907 o
774.548 333.051 o
769.373 333.816 o
764.197 335.24 o
759.021 336.74 o
753.846 337.908 o
748.67 339.028 o
743.494 340.085 o
738.319 341.121 o
733.143 342.135 o
727.967 343.097 o
722.792 343.831 o
717.616 345.069 o
712.44 345.847 o
707.265 346.731 o
702.089 347.592 o
696.913 348.632 o
691.738 349.388 o
686.562 350.234 o
681.387 351.223 o
676.211 352.035 o
671.035 352.747 o
665.86 353.575 o
660.684 354.384 o
655.508 355.248 o
650.333 356.116 o
645.157 356.687 o
639.981 357.368 o
634.806 358.285 o
629.63 359.012 o
624.454 359.61 o
619.279 360.287 o
614.103 361.043 o
608.927 361.695 o
603.752 362.368 o
grestore
0.050 setlinewidth
1 setlinejoin
[0.185 0.08] 0 setdash
1.000 0.271 0.000 setrgbcolor
gsave
388.174 313.357 598.576 66.606 clipbox
981.574348 82.211141 m
976.398696 82.881192 l
971.223043 83.863834 l
966.047391 91.544283 l
960.871739 81.393093 l
955.696087 82.393028 l
950.520435 82.042221 l
945.344783 82.355189 l
940.16913 92.599754 l
934.993478 81.501093 l
929.817826 87.422408 l
924.642174 108.997121 l
919.466522 98.465063 l
914.29087 103.427896 l
909.115217 106.434088 l
903.939565 130.912995 l
898.763913 272.993542 l
893.588261 280.966815 l
888.412609 287.014559 l
883.236957 291.017027 l
878.061304 295.058392 l
872.885652 298.103938 l
867.71 300.85927 l
862.534348 303.086863 l
857.358696 306.500582 l
852.183043 308.802035 l
847.007391 310.84284 l
841.831739 313.401179 l
836.656087 315.076709 l
831.480435 316.614773 l
826.304783 318.932501 l
821.12913 320.735925 l
815.953478 321.561672 l
810.777826 323.043961 l
805.602174 325.119261 l
800.426522 326.498695 l
795.25087 327.776343 l
790.075217 329.554474 l
784.899565 330.545338 l
779.723913 331.829497 l
774.548261 333.185806 l
769.372609 334.323484 l
764.196957 335.798959 l
759.021304 336.66174 l
753.845652 337.704615 l
748.67 338.939176 l
743.494348 340.207147 l
738.318696 340.858419 l
733.143043 342.266444 l
727.967391 342.961296 l
722.791739 344.174693 l
717.616087 344.988836 l
712.440435 345.808223 l
707.264783 346.885661 l
702.08913 347.822001 l
696.913478 348.615413 l
691.737826 349.449306 l
686.562174 350.386169 l
681.386522 351.163721 l
676.21087 352.036673 l
671.035217 352.824984 l
665.859565 353.557232 l
660.683913 354.459819 l
655.508261 355.210758 l
650.332609 356.084081 l
645.156957 356.774484 l
639.981304 357.578411 l
634.805652 358.15359 l
629.63 358.867687 l
624.454348 359.579395 l
619.278696 360.340123 l
614.103043 361.044535 l
608.927391 361.710869 l
603.751739 362.40621 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
388.174 313.357 598.576 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-4 -4 m
4 -4 l
4 4 l
-4 4 l
cl

gsave
grestore
stroke
grestore
} bind def
981.574 82.2111 o
976.399 82.8812 o
971.223 83.8638 o
966.047 91.5443 o
960.872 81.3931 o
955.696 82.393 o
950.52 82.0422 o
945.345 82.3552 o
940.169 92.5998 o
934.993 81.5011 o
929.818 87.4224 o
924.642 108.997 o
919.467 98.4651 o
914.291 103.428 o
909.115 106.434 o
903.94 130.913 o
898.764 272.994 o
893.588 280.967 o
888.413 287.015 o
883.237 291.017 o
878.061 295.058 o
872.886 298.104 o
867.71 300.859 o
862.534 303.087 o
857.359 306.501 o
852.183 308.802 o
847.007 310.843 o
841.832 313.401 o
836.656 315.077 o
831.48 316.615 o
826.305 318.933 o
821.129 320.736 o
815.953 321.562 o
810.778 323.044 o
805.602 325.119 o
800.427 326.499 o
795.251 327.776 o
790.075 329.554 o
784.9 330.545 o
779.724 331.829 o
774.548 333.186 o
769.373 334.323 o
764.197 335.799 o
759.021 336.662 o
753.846 337.705 o
748.67 338.939 o
743.494 340.207 o
738.319 340.858 o
733.143 342.266 o
727.967 342.961 o
722.792 344.175 o
717.616 344.989 o
712.44 345.808 o
707.265 346.886 o
702.089 347.822 o
696.913 348.615 o
691.738 349.449 o
686.562 350.386 o
681.387 351.164 o
676.211 352.037 o
671.035 352.825 o
665.86 353.557 o
660.684 354.46 o
655.508 355.211 o
650.333 356.084 o
645.157 356.774 o
639.981 357.578 o
634.806 358.154 o
629.63 358.868 o
624.454 359.579 o
619.279 360.34 o
614.103 361.045 o
608.927 361.711 o
603.752 362.406 o
grestore
0.050 setlinewidth
1 setlinejoin
[0.185 0.08] 0 setdash
1.000 0.514 0.000 setrgbcolor
gsave
388.174 313.357 598.576 66.606 clipbox
981.574348 80.691962 m
976.398696 86.737084 l
971.223043 84.090557 l
966.047391 90.140515 l
960.871739 82.602458 l
955.696087 80.801977 l
950.520435 85.965578 l
945.344783 93.923148 l
940.16913 82.189197 l
934.993478 94.489664 l
929.817826 83.030119 l
924.642174 112.461372 l
919.466522 82.434713 l
914.29087 171.053035 l
909.115217 183.506185 l
903.939565 172.722111 l
898.763913 271.431351 l
893.588261 280.808618 l
888.412609 285.955387 l
883.236957 291.025098 l
878.061304 294.78315 l
872.885652 298.298109 l
867.71 301.403107 l
862.534348 303.955969 l
857.358696 306.621674 l
852.183043 308.178491 l
847.007391 310.767474 l
841.831739 313.107289 l
836.656087 314.991566 l
831.480435 316.766803 l
826.304783 318.543655 l
821.12913 320.396139 l
815.953478 321.929439 l
810.777826 323.252126 l
805.602174 325.102699 l
800.426522 326.734548 l
795.25087 327.67275 l
790.075217 329.078026 l
784.899565 330.49901 l
779.723913 331.939727 l
774.548261 332.944319 l
769.372609 334.139601 l
764.196957 335.66032 l
759.021304 336.623068 l
753.845652 337.722484 l
748.67 338.822559 l
743.494348 340.043018 l
738.318696 340.858591 l
733.143043 341.922459 l
727.967391 343.058311 l
722.791739 343.963127 l
717.616087 344.85081 l
712.440435 345.937066 l
707.264783 346.721976 l
702.08913 347.708709 l
696.913478 348.631389 l
691.737826 349.576557 l
686.562174 350.306397 l
681.386522 351.232923 l
676.21087 352.200909 l
671.035217 352.868793 l
665.859565 353.7548 l
660.683913 354.429814 l
655.508261 355.237568 l
650.332609 356.024959 l
645.156957 356.754232 l
639.981304 357.509197 l
634.805652 358.298403 l
629.63 358.989555 l
624.454348 359.630709 l
619.278696 360.379436 l
614.103043 361.088381 l
608.927391 361.706441 l
603.751739 362.402179 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
388.174 313.357 598.576 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
981.574 80.692 o
976.399 86.7371 o
971.223 84.0906 o
966.047 90.1405 o
960.872 82.6025 o
955.696 80.802 o
950.52 85.9656 o
945.345 93.9231 o
940.169 82.1892 o
934.993 94.4897 o
929.818 83.0301 o
924.642 112.461 o
919.467 82.4347 o
914.291 171.053 o
909.115 183.506 o
903.94 172.722 o
898.764 271.431 o
893.588 280.809 o
888.413 285.955 o
883.237 291.025 o
878.061 294.783 o
872.886 298.298 o
867.71 301.403 o
862.534 303.956 o
857.359 306.622 o
852.183 308.178 o
847.007 310.767 o
841.832 313.107 o
836.656 314.992 o
831.48 316.767 o
826.305 318.544 o
821.129 320.396 o
815.953 321.929 o
810.778 323.252 o
805.602 325.103 o
800.427 326.735 o
795.251 327.673 o
790.075 329.078 o
784.9 330.499 o
779.724 331.94 o
774.548 332.944 o
769.373 334.14 o
764.197 335.66 o
759.021 336.623 o
753.846 337.722 o
748.67 338.823 o
743.494 340.043 o
738.319 340.859 o
733.143 341.922 o
727.967 343.058 o
722.792 343.963 o
717.616 344.851 o
712.44 345.937 o
707.265 346.722 o
702.089 347.709 o
696.913 348.631 o
691.738 349.577 o
686.562 350.306 o
681.387 351.233 o
676.211 352.201 o
671.035 352.869 o
665.86 353.755 o
660.684 354.43 o
655.508 355.238 o
650.333 356.025 o
645.157 356.754 o
639.981 357.509 o
634.806 358.298 o
629.63 358.99 o
624.454 359.631 o
619.279 360.379 o
614.103 361.088 o
608.927 361.706 o
603.752 362.402 o
grestore
2.500 setlinewidth
0 setlinejoin
2 setlinecap
0.000 setgray
gsave
598.576087 66.60625 m
598.576087 379.962772 l
stroke
grestore
gsave
986.75 66.60625 m
986.75 379.962772 l
stroke
grestore
gsave
598.576087 66.60625 m
986.75 66.60625 l
stroke
grestore
gsave
598.576087 379.962772 m
986.75 379.962772 l
stroke
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

598.576 385.963 translate
0 rotate
0 0 m /parenleft glyphshow
8.3252 0 m /d glyphshow
20.8252 0 m /parenright glyphshow
grestore
0.050 setlinewidth
1 setlinejoin
0 setlinecap
[0.185 0.08] 0 setdash
0.722 0.224 0.055 setrgbcolor
gsave
892.75 354.962772 m
912.75 354.962772 l
932.75 354.962772 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

0 2 m
-2 -2 l
2 -2 l
cl

gsave
0.722 0.224 0.055 setrgbcolor
fill
grestore
stroke
grestore
} bind def
912.75 354.963 o
grestore
0.000 setgray
gsave
948.75 347.963 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.75 moveto
/P glyphshow
/TimesNewRomanPSMT 14.0 selectfont
11.4236 -4.4025 moveto
/x glyphshow
grestore
0.050 setlinewidth
1 setlinejoin
[0.185 0.08] 0 setdash
1.000 0.271 0.000 setrgbcolor
gsave
892.75 324.962772 m
912.75 324.962772 l
932.75 324.962772 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-4 -4 m
4 -4 l
4 4 l
-4 4 l
cl

gsave
grestore
stroke
grestore
} bind def
912.75 324.963 o
grestore
0.000 setgray
gsave
948.75 317.963 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.75 moveto
/P glyphshow
/TimesNewRomanPSMT 14.0 selectfont
11.4236 -4.4025 moveto
/y glyphshow
grestore
0.050 setlinewidth
1 setlinejoin
[0.185 0.08] 0 setdash
1.000 0.514 0.000 setrgbcolor
gsave
892.75 291.962772 m
912.75 291.962772 l
932.75 291.962772 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
912.75 291.963 o
grestore
0.000 setgray
gsave
948.75 284.963 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.75 moveto
/P glyphshow
/TimesNewRomanPSMT 14.0 selectfont
11.4236 -4.4025 moveto
/z glyphshow
grestore

end
showpage
