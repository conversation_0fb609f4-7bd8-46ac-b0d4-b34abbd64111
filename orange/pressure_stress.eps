%!PS-Adobe-3.0 EPSF-3.0
%%Title: pressure_stress.eps
%%Creator: Matplotlib v3.7.1, https://matplotlib.org/
%%CreationDate: Thu Aug 29 15:21:04 2024
%%Orientation: portrait
%%BoundingBox: -185 -20 798 813
%%HiResBoundingBox: -185.850000 -20.847908 797.850000 812.847908
%%EndComments
%%BeginProlog
/mpldict 11 dict def
mpldict begin
/_d { bind def } bind def
/m { moveto } _d
/l { lineto } _d
/r { rlineto } _d
/c { curveto } _d
/cl { closepath } _d
/ce { closepath eofill } _d
/box {
      m
      1 index 0 r
      0 exch r
      neg 0 r
      cl
    } _d
/clipbox {
      box
      clip
      newpath
    } _d
/sc { setcachedevice } _d
%!PS-TrueTypeFont-1.0-2.53740
%%Title: unknown
%%Creator: Converted from TrueType to type 42 by PPR
15 dict begin
/FontName /TimesNewRomanPSMT def
/PaintType 0 def
/FontMatrix[1 0 0 1 0 0]def
/FontBBox[-568 -307 2028 1007]def
/FontType 42 def
/Encoding StandardEncoding def
/FontInfo 10 dict dup begin
/FamilyName (unknown) def
/FullName (unknown) def
/Weight (unknown) def
/Version (unknown) def
/ItalicAngle 0.0 def
/isFixedPitch false def
/UnderlinePosition -223 def
/UnderlineThickness 100 def
end readonly def
/sfnts[<00010000000900800003001063767420F34DDA810000009C000007C46670676D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>]def
/CharStrings 39 dict dup begin
/.notdef 0 def
/minus 39 def
/space 3 def
/parenleft 4 def
/parenright 5 def
/period 6 def
/slash 7 def
/zero 8 def
/one 9 def
/two 10 def
/three 11 def
/four 12 def
/five 13 def
/six 14 def
/eight 15 def
/nine 16 def
/mu 40 def
/B 17 def
/C 18 def
/G 19 def
/P 20 def
/T 21 def
/U 22 def
/a 23 def
/b 24 def
/c 25 def
/d 26 def
/e 27 def
/i 28 def
/l 29 def
/m 30 def
/n 31 def
/o 32 def
/r 33 def
/s 34 def
/t 35 def
/u 36 def
/x 37 def
/z 38 def
end readonly def

systemdict/resourcestatus known
 {42 /FontType resourcestatus
   {pop pop false}{true}ifelse}
 {true}ifelse
{/TrueDict where{pop}{(%%[ Error: no TrueType rasterizer ]%%)= flush}ifelse
/FontType 3 def
 /TrueState 271 string def
 TrueDict begin sfnts save
 72 0 matrix defaultmatrix dtransform dup
 mul exch dup mul add sqrt cvi 0 72 matrix
 defaultmatrix dtransform dup mul exch dup
 mul add sqrt cvi 3 -1 roll restore
 TrueState initer end
 /BuildGlyph{exch begin
  CharStrings dup 2 index known
    {exch}{exch pop /.notdef}ifelse
  get dup xcheck
    {currentdict systemdict begin begin exec end end}
    {TrueDict begin /bander load cvlit exch TrueState render end}
    ifelse
 end}bind def
 /BuildChar{
  1 index /Encoding get exch get
  1 index /BuildGlyph get exec
 }bind def
}if

FontName currentdict end definefont pop
end
%%EndProlog
mpldict begin
-185.85 -20.848 translate
983.7 833.696 0 0 clipbox
gsave
0 0 m
983.7 0 l
983.7 833.695815 l
0 833.695815 l
cl
1.000 setgray
fill
grestore
gsave
77.45 473.969728 m
465.623913 473.969728 l
465.623913 787.32625 l
77.45 787.32625 l
cl
1.000 setgray
fill
grestore
2.500 setlinewidth
1 setlinejoin
0 setlinecap
[] 0 setdash
0.000 setgray
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
77.45 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
77.45 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

64.1531 446.61 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /six glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
109.798 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
109.798 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

96.501 446.61 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /five glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
142.146 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
142.146 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

128.849 446.61 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /four glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
174.493 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
174.493 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

161.197 446.61 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /three glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
206.841 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
206.841 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

193.544 446.61 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /two glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
239.189 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
239.189 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

225.892 446.61 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /one glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
271.537 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
271.537 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

265.287 446.61 translate
0 rotate
0 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
303.885 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
303.885 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

297.635 446.61 translate
0 rotate
0 0 m /one glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
336.233 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
336.233 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

329.983 446.61 translate
0 rotate
0 0 m /two glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
368.58 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
368.58 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

362.33 446.61 translate
0 rotate
0 0 m /three glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
400.928 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
400.928 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

394.678 446.61 translate
0 rotate
0 0 m /four glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
433.276 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
433.276 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

427.026 446.61 translate
0 rotate
0 0 m /five glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.624 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.624 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

459.374 446.61 translate
0 rotate
0 0 m /six glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
83.9196 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
83.9196 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
90.3891 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
90.3891 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
96.8587 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
96.8587 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.328 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.328 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
116.267 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
116.267 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
122.737 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
122.737 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
129.207 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
129.207 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
135.676 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
135.676 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
148.615 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
148.615 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
155.085 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
155.085 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
161.554 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
161.554 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
168.024 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
168.024 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
180.963 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
180.963 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
187.433 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
187.433 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
193.902 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
193.902 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
200.372 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
200.372 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
213.311 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
213.311 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
219.78 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
219.78 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
226.25 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
226.25 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
232.72 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
232.72 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
245.659 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
245.659 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
252.128 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
252.128 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
258.598 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
258.598 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
265.067 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
265.067 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
278.007 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
278.007 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
284.476 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
284.476 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
290.946 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
290.946 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
297.415 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
297.415 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
310.354 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
310.354 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
316.824 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
316.824 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
323.293 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
323.293 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
329.763 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
329.763 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
342.702 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
342.702 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
349.172 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
349.172 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
355.641 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
355.641 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
362.111 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
362.111 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
375.05 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
375.05 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
381.52 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
381.52 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
387.989 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
387.989 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
394.459 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
394.459 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
407.398 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
407.398 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
413.867 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
413.867 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
420.337 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
420.337 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
426.807 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
426.807 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
439.746 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
439.746 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
446.215 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
446.215 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
452.685 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
452.685 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
459.154 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
459.154 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

196.193 419.907 translate
0 rotate
0 0 m /P glyphshow
13.9038 0 m /r glyphshow
22.229 0 m /e glyphshow
33.3252 0 m /s glyphshow
43.0542 0 m /s glyphshow
52.7832 0 m /u glyphshow
65.2832 0 m /r glyphshow
73.6084 0 m /e glyphshow
84.7046 0 m /space glyphshow
90.9546 0 m /parenleft glyphshow
99.2798 0 m /G glyphshow
117.334 0 m /P glyphshow
131.238 0 m /a glyphshow
142.334 0 m /parenright glyphshow
grestore
2.500 setlinewidth
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
77.45 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.624 473.97 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

36.2 465.29 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /eight glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
77.45 552.309 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.624 552.309 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

36.2 543.629 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /nine glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
77.45 630.648 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.624 630.648 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

36.2 621.968 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
77.45 708.987 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.624 708.987 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

36.2 700.307 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /period glyphshow
18.75 0 m /one glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
77.45 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.624 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

36.2 778.647 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /period glyphshow
18.75 0 m /two glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
77.45 489.638 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.624 489.638 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
77.45 505.305 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.624 505.305 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
77.45 520.973 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.624 520.973 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
77.45 536.641 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.624 536.641 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
77.45 567.977 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.624 567.977 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
77.45 583.645 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.624 583.645 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
77.45 599.312 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.624 599.312 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
77.45 614.98 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.624 614.98 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
77.45 646.316 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.624 646.316 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
77.45 661.984 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.624 661.984 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
77.45 677.651 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.624 677.651 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
77.45 693.319 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.624 693.319 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
77.45 724.655 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.624 724.655 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
77.45 740.323 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.624 740.323 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
77.45 755.991 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.624 755.991 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
77.45 771.658 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.624 771.658 o
grestore
gsave
25.2 584.148 translate
90 rotate
/TimesNewRomanPSMT 25.0 selectfont
0 0.640625 moveto
/T glyphshow
/TimesNewRomanPSMT 17.5 selectfont
15.6467 -5.8 moveto
/C glyphshow
/TimesNewRomanPSMT 25.0 selectfont
28.3925 0.640625 moveto
/slash glyphshow
35.3383 0.640625 moveto
/T glyphshow
/TimesNewRomanPSMT 17.5 selectfont
50.985 -5.8 moveto
/C glyphshow
/TimesNewRomanPSMT 25.0 selectfont
63.7308 0.640625 moveto
/parenleft glyphshow
72.056 0.640625 moveto
/zero glyphshow
84.556 0.640625 moveto
/parenright glyphshow
grestore
[9.6 2.4 1.5 2.4] 0 setdash
1.000 0.514 0.000 setrgbcolor
gsave
388.174 313.357 77.45 473.97 clipbox
433.276087 493.38712 m
400.928261 520.169728 l
368.580435 546.952337 l
336.232609 577.082772 l
303.884783 600.517554 l
271.536957 630.647989 l
239.18913 654.082772 l
206.841304 684.213207 l
174.493478 714.343641 l
142.145652 737.778424 l
109.797826 767.908859 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
388.174 313.357 77.45 473.97 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
433.276 493.387 o
400.928 520.17 o
368.58 546.952 o
336.233 577.083 o
303.885 600.518 o
271.537 630.648 o
239.189 654.083 o
206.841 684.213 o
174.493 714.344 o
142.146 737.778 o
109.798 767.909 o
grestore
2.500 setlinewidth
0 setlinejoin
2 setlinecap
0.000 setgray
gsave
77.45 473.969728 m
77.45 787.32625 l
stroke
grestore
gsave
465.623913 473.969728 m
465.623913 787.32625 l
stroke
grestore
gsave
77.45 473.969728 m
465.623913 473.969728 l
stroke
grestore
gsave
77.45 787.32625 m
465.623913 787.32625 l
stroke
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

67.7457 809.136 translate
0 rotate
0 0 m /parenleft glyphshow
8.3252 0 m /a glyphshow
19.4214 0 m /parenright glyphshow
grestore
gsave
582.076087 473.969728 m
970.25 473.969728 l
970.25 787.32625 l
582.076087 787.32625 l
cl
1.000 setgray
fill
grestore
1 setlinejoin
0 setlinecap
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
582.076 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
582.076 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

568.779 446.61 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /six glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.424 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.424 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

601.127 446.61 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /five glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
646.772 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
646.772 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

633.475 446.61 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /four glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
679.12 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
679.12 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

665.823 446.61 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /three glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
711.467 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
711.467 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

698.171 446.61 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /two glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
743.815 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
743.815 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

730.518 446.61 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /one glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
776.163 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
776.163 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

769.913 446.61 translate
0 rotate
0 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
808.511 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
808.511 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

802.261 446.61 translate
0 rotate
0 0 m /one glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
840.859 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
840.859 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

834.609 446.61 translate
0 rotate
0 0 m /two glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
873.207 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
873.207 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

866.957 446.61 translate
0 rotate
0 0 m /three glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
905.554 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
905.554 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

899.304 446.61 translate
0 rotate
0 0 m /four glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
937.902 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
937.902 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

931.652 446.61 translate
0 rotate
0 0 m /five glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
970.25 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
970.25 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

964 446.61 translate
0 rotate
0 0 m /six glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
588.546 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
588.546 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
595.015 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
595.015 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
601.485 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
601.485 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
607.954 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
607.954 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
620.893 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
620.893 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
627.363 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
627.363 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
633.833 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
633.833 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
640.302 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
640.302 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
653.241 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
653.241 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
659.711 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
659.711 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
666.18 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
666.18 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
672.65 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
672.65 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
685.589 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
685.589 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
692.059 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
692.059 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
698.528 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
698.528 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
704.998 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
704.998 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
717.937 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
717.937 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
724.407 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
724.407 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
730.876 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
730.876 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
737.346 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
737.346 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
750.285 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
750.285 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
756.754 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
756.754 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
763.224 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
763.224 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
769.693 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
769.693 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
782.633 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
782.633 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
789.102 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
789.102 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
795.572 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
795.572 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
802.041 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
802.041 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
814.98 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
814.98 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
821.45 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
821.45 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
827.92 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
827.92 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
834.389 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
834.389 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
847.328 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
847.328 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
853.798 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
853.798 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
860.267 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
860.267 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
866.737 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
866.737 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
879.676 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
879.676 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
886.146 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
886.146 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
892.615 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
892.615 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
899.085 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
899.085 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
912.024 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
912.024 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
918.493 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
918.493 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
924.963 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
924.963 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
931.433 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
931.433 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
944.372 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
944.372 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
950.841 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
950.841 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
957.311 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
957.311 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
963.78 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
963.78 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

700.819 419.907 translate
0 rotate
0 0 m /P glyphshow
13.9038 0 m /r glyphshow
22.229 0 m /e glyphshow
33.3252 0 m /s glyphshow
43.0542 0 m /s glyphshow
52.7832 0 m /u glyphshow
65.2832 0 m /r glyphshow
73.6084 0 m /e glyphshow
84.7046 0 m /space glyphshow
90.9546 0 m /parenleft glyphshow
99.2798 0 m /G glyphshow
117.334 0 m /P glyphshow
131.238 0 m /a glyphshow
142.334 0 m /parenright glyphshow
grestore
2.500 setlinewidth
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
582.076 473.97 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
970.25 473.97 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

547.076 465.29 translate
0 rotate
0 0 m /four glyphshow
12.5 0 m /four glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
582.076 552.309 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
970.25 552.309 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

547.076 543.629 translate
0 rotate
0 0 m /four glyphshow
12.5 0 m /six glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
582.076 630.648 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
970.25 630.648 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

547.076 621.968 translate
0 rotate
0 0 m /four glyphshow
12.5 0 m /eight glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
582.076 708.987 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
970.25 708.987 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

547.076 700.307 translate
0 rotate
0 0 m /five glyphshow
12.5 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
582.076 787.326 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
970.25 787.326 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

547.076 778.647 translate
0 rotate
0 0 m /five glyphshow
12.5 0 m /two glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
582.076 493.555 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
970.25 493.555 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
582.076 513.139 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
970.25 513.139 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
582.076 532.724 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
970.25 532.724 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
582.076 571.894 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
970.25 571.894 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
582.076 591.478 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
970.25 591.478 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
582.076 611.063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
970.25 611.063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
582.076 650.233 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
970.25 650.233 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
582.076 669.818 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
970.25 669.818 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
582.076 689.402 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
970.25 689.402 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
582.076 728.572 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
970.25 728.572 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
582.076 748.157 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
970.25 748.157 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
582.076 767.741 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
970.25 767.741 o
grestore
gsave
537.076 519.648 translate
90 rotate
/TimesNewRomanPSMT 25.0 selectfont
0 0.148438 moveto
/P glyphshow
13.9038 0.148438 moveto
/o glyphshow
26.4038 0.148438 moveto
/l glyphshow
33.3496 0.148438 moveto
/a glyphshow
44.4458 0.148438 moveto
/r glyphshow
52.771 0.148438 moveto
/i glyphshow
59.7168 0.148438 moveto
/z glyphshow
70.813 0.148438 moveto
/a glyphshow
81.9092 0.148438 moveto
/t glyphshow
88.855 0.148438 moveto
/i glyphshow
95.8008 0.148438 moveto
/o glyphshow
108.301 0.148438 moveto
/n glyphshow
120.801 0.148438 moveto
/space glyphshow
127.051 0.148438 moveto
/parenleft glyphshow
135.376 0.148438 moveto
/mu glyphshow
148.779 0.148438 moveto
/C glyphshow
165.454 0.148438 moveto
/slash glyphshow
172.4 0.148438 moveto
/c glyphshow
183.496 0.148438 moveto
/m glyphshow
/TimesNewRomanPSMT 17.5 selectfont
203.318 15.1766 moveto
/two glyphshow
/TimesNewRomanPSMT 25.0 selectfont
213.141 0.148438 moveto
/parenright glyphshow
grestore
[9.6 2.4 1.5 2.4] 0 setdash
1.000 0.514 0.000 setrgbcolor
gsave
388.174 313.357 582.076 473.97 clipbox
614.423913 759.386454 m
646.771739 732.892905 l
679.119565 706.898133 l
711.467391 681.892242 l
743.815217 657.315338 l
776.163043 629.123186 l
808.51087 601.782702 l
840.858696 576.505933 l
873.206522 548.7199 l
905.554348 522.223986 l
937.902174 494.396158 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
388.174 313.357 582.076 473.97 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
614.424 759.386 o
646.772 732.893 o
679.12 706.898 o
711.467 681.892 o
743.815 657.315 o
776.163 629.123 o
808.511 601.783 o
840.859 576.506 o
873.207 548.72 o
905.554 522.224 o
937.902 494.396 o
grestore
2.500 setlinewidth
0 setlinejoin
2 setlinecap
0.000 setgray
gsave
582.076087 473.969728 m
582.076087 787.32625 l
stroke
grestore
gsave
970.25 473.969728 m
970.25 787.32625 l
stroke
grestore
gsave
582.076087 473.969728 m
970.25 473.969728 l
stroke
grestore
gsave
582.076087 787.32625 m
970.25 787.32625 l
stroke
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

572.372 809.136 translate
0 rotate
0 0 m /parenleft glyphshow
8.3252 0 m /b glyphshow
20.8252 0 m /parenright glyphshow
grestore
gsave
77.45 66.60625 m
465.623913 66.60625 l
465.623913 379.962772 l
77.45 379.962772 l
cl
1.000 setgray
fill
grestore
1 setlinejoin
0 setlinecap
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
77.45 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
77.45 379.963 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

64.1531 39.2469 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /six glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
109.798 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
109.798 379.963 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

96.501 39.2469 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /five glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
142.146 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
142.146 379.963 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

128.849 39.2469 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /four glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
174.493 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
174.493 379.963 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

161.197 39.2469 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /three glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
206.841 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
206.841 379.963 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

193.544 39.2469 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /two glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
239.189 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
239.189 379.963 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

225.892 39.2469 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /one glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
271.537 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
271.537 379.963 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

265.287 39.2469 translate
0 rotate
0 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
303.885 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
303.885 379.963 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

297.635 39.2469 translate
0 rotate
0 0 m /one glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
336.233 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
336.233 379.963 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

329.983 39.2469 translate
0 rotate
0 0 m /two glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
368.58 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
368.58 379.963 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

362.33 39.2469 translate
0 rotate
0 0 m /three glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
400.928 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
400.928 379.963 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

394.678 39.2469 translate
0 rotate
0 0 m /four glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
433.276 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
433.276 379.963 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

427.026 39.2469 translate
0 rotate
0 0 m /five glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.624 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.624 379.963 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

459.374 39.2469 translate
0 rotate
0 0 m /six glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
83.9196 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
83.9196 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
90.3891 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
90.3891 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
96.8587 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
96.8587 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.328 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.328 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
116.267 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
116.267 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
122.737 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
122.737 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
129.207 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
129.207 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
135.676 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
135.676 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
148.615 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
148.615 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
155.085 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
155.085 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
161.554 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
161.554 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
168.024 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
168.024 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
180.963 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
180.963 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
187.433 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
187.433 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
193.902 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
193.902 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
200.372 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
200.372 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
213.311 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
213.311 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
219.78 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
219.78 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
226.25 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
226.25 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
232.72 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
232.72 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
245.659 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
245.659 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
252.128 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
252.128 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
258.598 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
258.598 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
265.067 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
265.067 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
278.007 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
278.007 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
284.476 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
284.476 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
290.946 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
290.946 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
297.415 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
297.415 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
310.354 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
310.354 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
316.824 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
316.824 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
323.293 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
323.293 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
329.763 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
329.763 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
342.702 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
342.702 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
349.172 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
349.172 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
355.641 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
355.641 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
362.111 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
362.111 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
375.05 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
375.05 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
381.52 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
381.52 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
387.989 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
387.989 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
394.459 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
394.459 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
407.398 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
407.398 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
413.867 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
413.867 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
420.337 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
420.337 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
426.807 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
426.807 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
439.746 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
439.746 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
446.215 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
446.215 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
452.685 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
452.685 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
459.154 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
459.154 379.963 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

164.584 12.5437 translate
0 rotate
0 0 m /U glyphshow
18.0542 0 m /n glyphshow
30.5542 0 m /i glyphshow
37.5 0 m /a glyphshow
48.5962 0 m /x glyphshow
61.0962 0 m /i glyphshow
68.042 0 m /a glyphshow
79.1382 0 m /l glyphshow
86.084 0 m /space glyphshow
92.334 0 m /s glyphshow
102.063 0 m /t glyphshow
109.009 0 m /r glyphshow
117.334 0 m /e glyphshow
128.43 0 m /s glyphshow
138.159 0 m /s glyphshow
147.888 0 m /space glyphshow
154.138 0 m /parenleft glyphshow
162.463 0 m /G glyphshow
180.518 0 m /P glyphshow
194.421 0 m /a glyphshow
205.518 0 m /parenright glyphshow
grestore
2.500 setlinewidth
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
77.45 74.2631 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.624 74.2631 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

36.2 65.5835 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /six glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
77.45 151.655 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.624 151.655 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

36.2 142.976 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /eight glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
77.45 229.048 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.624 229.048 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

36.2 220.368 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
77.45 306.44 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.624 306.44 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

36.2 297.76 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /period glyphshow
18.75 0 m /two glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
77.45 93.6112 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.624 93.6112 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
77.45 112.959 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.624 112.959 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
77.45 132.307 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.624 132.307 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
77.45 171.004 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.624 171.004 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
77.45 190.352 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.624 190.352 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
77.45 209.7 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.624 209.7 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
77.45 248.396 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.624 248.396 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
77.45 267.744 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.624 267.744 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
77.45 287.092 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.624 287.092 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
77.45 325.788 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.624 325.788 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
77.45 345.136 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.624 345.136 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
77.45 364.484 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
465.624 364.484 o
grestore
gsave
25.2 176.785 translate
90 rotate
/TimesNewRomanPSMT 25.0 selectfont
0 0.640625 moveto
/T glyphshow
/TimesNewRomanPSMT 17.5 selectfont
15.6467 -5.8 moveto
/C glyphshow
/TimesNewRomanPSMT 25.0 selectfont
28.3925 0.640625 moveto
/slash glyphshow
35.3383 0.640625 moveto
/T glyphshow
/TimesNewRomanPSMT 17.5 selectfont
50.985 -5.8 moveto
/C glyphshow
/TimesNewRomanPSMT 25.0 selectfont
63.7308 0.640625 moveto
/parenleft glyphshow
72.056 0.640625 moveto
/zero glyphshow
84.556 0.640625 moveto
/parenright glyphshow
grestore
[9.6 2.4 1.5 2.4] 0 setdash
1.000 0.514 0.000 setrgbcolor
gsave
388.174 313.357 77.45 66.606 clipbox
433.276087 80.849728 m
400.928261 125.30914 l
368.580435 164.828617 l
336.232609 187.881646 l
303.884783 219.167899 l
271.536957 229.047768 l
239.18913 222.461188 l
206.841304 214.227964 l
174.493478 199.40816 l
142.145652 186.235001 l
109.797826 181.295066 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
388.174 313.357 77.45 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
433.276 80.8497 o
400.928 125.309 o
368.58 164.829 o
336.233 187.882 o
303.885 219.168 o
271.537 229.048 o
239.189 222.461 o
206.841 214.228 o
174.493 199.408 o
142.146 186.235 o
109.798 181.295 o
grestore
1.500 setlinewidth
[5.55 2.4] 0 setdash
1.000 0.271 0.000 setrgbcolor
gsave
388.174 313.357 77.45 66.606 clipbox
433.276087 365.719293 m
400.928261 337.72633 l
368.580435 314.673302 l
336.232609 289.973629 l
303.884783 263.627311 l
271.536957 229.047768 l
239.18913 243.867572 l
206.841304 250.454151 l
174.493478 261.980666 l
142.145652 257.040731 l
109.797826 275.153825 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
388.174 313.357 77.45 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-8 -8 m
8 -8 l
8 8 l
-8 8 l
cl

gsave
grestore
stroke
grestore
} bind def
433.276 365.719 o
400.928 337.726 o
368.58 314.673 o
336.233 289.974 o
303.885 263.627 o
271.537 229.048 o
239.189 243.868 o
206.841 250.454 o
174.493 261.981 o
142.146 257.041 o
109.798 275.154 o
grestore
2.500 setlinewidth
2 setlinecap
0.000 setgray
gsave
77.45 66.60625 m
77.45 379.962772 l
stroke
grestore
gsave
465.623913 66.60625 m
465.623913 379.962772 l
stroke
grestore
gsave
77.45 66.60625 m
465.623913 66.60625 l
stroke
grestore
gsave
77.45 379.962772 m
465.623913 379.962772 l
stroke
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

67.7457 385.821 translate
0 rotate
0 0 m /parenleft glyphshow
8.3252 0 m /c glyphshow
19.4214 0 m /parenright glyphshow
grestore
1.500 setlinewidth
1 setlinejoin
0 setlinecap
[9.6 2.4 1.5 2.4] 0 setdash
1.000 0.514 0.000 setrgbcolor
gsave
95.45 354.962772 m
115.45 354.962772 l
135.45 354.962772 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
115.45 354.963 o
grestore
0.000 setgray
gsave
151.45 347.963 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.75 moveto
/T glyphshow
/TimesNewRomanPSMT 14.0 selectfont
12.5174 -4.4025 moveto
/C glyphshow
/TimesNewRomanPSMT 9.799999999999999 selectfont
22.0656 -8.00925 moveto
/two glyphshow
grestore
1.500 setlinewidth
[5.55 2.4] 0 setdash
1.000 0.271 0.000 setrgbcolor
gsave
95.45 321.962772 m
115.45 321.962772 l
135.45 321.962772 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-8 -8 m
8 -8 l
8 8 l
-8 8 l
cl

gsave
grestore
stroke
grestore
} bind def
115.45 321.963 o
grestore
0.000 setgray
gsave
151.45 314.963 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.75 moveto
/T glyphshow
/TimesNewRomanPSMT 14.0 selectfont
12.5174 -4.4025 moveto
/C glyphshow
/TimesNewRomanPSMT 9.799999999999999 selectfont
22.0656 -8.00925 moveto
/one glyphshow
grestore
gsave
582.076087 66.60625 m
970.25 66.60625 l
970.25 379.962772 l
582.076087 379.962772 l
cl
1.000 setgray
fill
grestore
2.500 setlinewidth
1 setlinejoin
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
582.076 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
582.076 379.963 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

568.779 39.2469 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /six glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.424 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
614.424 379.963 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

601.127 39.2469 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /five glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
646.772 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
646.772 379.963 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

633.475 39.2469 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /four glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
679.12 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
679.12 379.963 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

665.823 39.2469 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /three glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
711.467 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
711.467 379.963 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

698.171 39.2469 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /two glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
743.815 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
743.815 379.963 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

730.518 39.2469 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /one glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
776.163 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
776.163 379.963 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

769.913 39.2469 translate
0 rotate
0 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
808.511 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
808.511 379.963 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

802.261 39.2469 translate
0 rotate
0 0 m /one glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
840.859 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
840.859 379.963 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

834.609 39.2469 translate
0 rotate
0 0 m /two glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
873.207 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
873.207 379.963 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

866.957 39.2469 translate
0 rotate
0 0 m /three glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
905.554 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
905.554 379.963 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

899.304 39.2469 translate
0 rotate
0 0 m /four glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
937.902 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
937.902 379.963 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

931.652 39.2469 translate
0 rotate
0 0 m /five glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
970.25 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
970.25 379.963 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

964 39.2469 translate
0 rotate
0 0 m /six glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
588.546 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
588.546 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
595.015 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
595.015 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
601.485 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
601.485 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
607.954 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
607.954 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
620.893 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
620.893 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
627.363 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
627.363 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
633.833 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
633.833 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
640.302 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
640.302 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
653.241 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
653.241 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
659.711 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
659.711 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
666.18 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
666.18 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
672.65 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
672.65 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
685.589 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
685.589 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
692.059 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
692.059 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
698.528 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
698.528 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
704.998 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
704.998 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
717.937 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
717.937 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
724.407 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
724.407 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
730.876 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
730.876 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
737.346 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
737.346 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
750.285 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
750.285 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
756.754 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
756.754 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
763.224 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
763.224 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
769.693 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
769.693 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
782.633 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
782.633 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
789.102 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
789.102 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
795.572 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
795.572 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
802.041 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
802.041 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
814.98 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
814.98 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
821.45 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
821.45 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
827.92 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
827.92 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
834.389 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
834.389 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
847.328 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
847.328 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
853.798 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
853.798 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
860.267 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
860.267 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
866.737 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
866.737 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
879.676 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
879.676 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
886.146 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
886.146 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
892.615 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
892.615 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
899.085 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
899.085 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
912.024 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
912.024 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
918.493 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
918.493 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
924.963 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
924.963 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
931.433 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
931.433 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
944.372 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
944.372 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
950.841 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
950.841 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
957.311 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
957.311 379.963 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
963.78 66.6062 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
963.78 379.963 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

676.155 12.5437 translate
0 rotate
0 0 m /B glyphshow
16.6748 0 m /i glyphshow
23.6206 0 m /a glyphshow
34.7168 0 m /x glyphshow
47.2168 0 m /i glyphshow
54.1626 0 m /a glyphshow
65.2588 0 m /l glyphshow
72.2046 0 m /space glyphshow
78.4546 0 m /s glyphshow
88.1836 0 m /t glyphshow
95.1294 0 m /r glyphshow
103.455 0 m /e glyphshow
114.551 0 m /s glyphshow
124.28 0 m /s glyphshow
134.009 0 m /space glyphshow
140.259 0 m /parenleft glyphshow
148.584 0 m /G glyphshow
166.638 0 m /P glyphshow
180.542 0 m /a glyphshow
191.638 0 m /parenright glyphshow
grestore
2.500 setlinewidth
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
582.076 74.2631 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
970.25 74.2631 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

540.826 65.5835 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /six glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
582.076 151.655 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
970.25 151.655 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

540.826 142.976 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /eight glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
582.076 229.048 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
970.25 229.048 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

540.826 220.368 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
582.076 306.44 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
970.25 306.44 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

540.826 297.76 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /period glyphshow
18.75 0 m /two glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
582.076 93.6112 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
970.25 93.6112 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
582.076 112.959 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
970.25 112.959 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
582.076 132.307 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
970.25 132.307 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
582.076 171.004 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
970.25 171.004 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
582.076 190.352 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
970.25 190.352 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
582.076 209.7 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
970.25 209.7 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
582.076 248.396 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
970.25 248.396 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
582.076 267.744 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
970.25 267.744 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
582.076 287.092 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
970.25 287.092 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
582.076 325.788 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
970.25 325.788 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
582.076 345.136 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
970.25 345.136 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
582.076 364.484 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
970.25 364.484 o
grestore
gsave
529.826 176.785 translate
90 rotate
/TimesNewRomanPSMT 25.0 selectfont
0 0.640625 moveto
/T glyphshow
/TimesNewRomanPSMT 17.5 selectfont
15.6467 -5.8 moveto
/C glyphshow
/TimesNewRomanPSMT 25.0 selectfont
28.3925 0.640625 moveto
/slash glyphshow
35.3383 0.640625 moveto
/T glyphshow
/TimesNewRomanPSMT 17.5 selectfont
50.985 -5.8 moveto
/C glyphshow
/TimesNewRomanPSMT 25.0 selectfont
63.7308 0.640625 moveto
/parenleft glyphshow
72.056 0.640625 moveto
/zero glyphshow
84.556 0.640625 moveto
/parenright glyphshow
grestore
[9.6 2.4 1.5 2.4] 0 setdash
1.000 0.514 0.000 setrgbcolor
gsave
388.174 313.357 582.076 66.606 clipbox
937.902174 80.849728 m
905.554348 125.30914 l
873.206522 164.828617 l
840.858696 187.881646 l
808.51087 219.167899 l
776.163043 229.047768 l
743.815217 222.461188 l
711.467391 214.227964 l
679.119565 199.40816 l
646.771739 186.235001 l
614.423913 181.295066 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
388.174 313.357 582.076 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
937.902 80.8497 o
905.554 125.309 o
873.207 164.829 o
840.859 187.882 o
808.511 219.168 o
776.163 229.048 o
743.815 222.461 o
711.467 214.228 o
679.12 199.408 o
646.772 186.235 o
614.424 181.295 o
grestore
1.500 setlinewidth
[5.55 2.4] 0 setdash
1.000 0.271 0.000 setrgbcolor
gsave
388.174 313.357 582.076 66.606 clipbox
937.902174 365.719293 m
905.554348 337.72633 l
873.206522 314.673302 l
840.858696 289.973629 l
808.51087 263.627311 l
776.163043 229.047768 l
743.815217 243.867572 l
711.467391 250.454151 l
679.119565 261.980666 l
646.771739 257.040731 l
614.423913 275.153825 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
388.174 313.357 582.076 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-8 -8 m
8 -8 l
8 8 l
-8 8 l
cl

gsave
grestore
stroke
grestore
} bind def
937.902 365.719 o
905.554 337.726 o
873.207 314.673 o
840.859 289.974 o
808.511 263.627 o
776.163 229.048 o
743.815 243.868 o
711.467 250.454 o
679.12 261.981 o
646.772 257.041 o
614.424 275.154 o
grestore
2.500 setlinewidth
2 setlinecap
0.000 setgray
gsave
582.076087 66.60625 m
582.076087 379.962772 l
stroke
grestore
gsave
970.25 66.60625 m
970.25 379.962772 l
stroke
grestore
gsave
582.076087 66.60625 m
970.25 66.60625 l
stroke
grestore
gsave
582.076087 379.962772 m
970.25 379.962772 l
stroke
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

572.372 385.821 translate
0 rotate
0 0 m /parenleft glyphshow
8.3252 0 m /d glyphshow
20.8252 0 m /parenright glyphshow
grestore
1.500 setlinewidth
1 setlinejoin
0 setlinecap
[9.6 2.4 1.5 2.4] 0 setdash
1.000 0.514 0.000 setrgbcolor
gsave
600.076087 354.962772 m
620.076087 354.962772 l
640.076087 354.962772 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
620.076 354.963 o
grestore
0.000 setgray
gsave
656.076 347.963 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.75 moveto
/T glyphshow
/TimesNewRomanPSMT 14.0 selectfont
12.5174 -4.4025 moveto
/C glyphshow
/TimesNewRomanPSMT 9.799999999999999 selectfont
22.0656 -8.00925 moveto
/two glyphshow
grestore
1.500 setlinewidth
[5.55 2.4] 0 setdash
1.000 0.271 0.000 setrgbcolor
gsave
600.076087 321.962772 m
620.076087 321.962772 l
640.076087 321.962772 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-8 -8 m
8 -8 l
8 8 l
-8 8 l
cl

gsave
grestore
stroke
grestore
} bind def
620.076 321.963 o
grestore
0.000 setgray
gsave
656.076 314.963 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.75 moveto
/T glyphshow
/TimesNewRomanPSMT 14.0 selectfont
12.5174 -4.4025 moveto
/C glyphshow
/TimesNewRomanPSMT 9.799999999999999 selectfont
22.0656 -8.00925 moveto
/one glyphshow
grestore

end
showpage
