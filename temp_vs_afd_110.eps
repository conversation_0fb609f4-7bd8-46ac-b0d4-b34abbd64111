%!PS-Adobe-3.0 EPSF-3.0
%%Title: temp_vs_afd_110.eps
%%Creator: Mat<PERSON>lotlib v3.7.1, https://matplotlib.org/
%%CreationDate: Wed Apr 24 13:18:14 2024
%%Orientation: portrait
%%BoundingBox: 24 178 588 614
%%HiResBoundingBox: 24.350000 178.916875 587.650000 613.083125
%%EndComments
%%BeginProlog
/mpldict 11 dict def
mpldict begin
/_d { bind def } bind def
/m { moveto } _d
/l { lineto } _d
/r { rlineto } _d
/c { curveto } _d
/cl { closepath } _d
/ce { closepath eofill } _d
/box {
      m
      1 index 0 r
      0 exch r
      neg 0 r
      cl
    } _d
/clipbox {
      box
      clip
      newpath
    } _d
/sc { setcachedevice } _d
%!PS-TrueTypeFont-1.0-2.53740
%%Title: unknown
%%Creator: Converted from TrueType to type 42 by PPR
15 dict begin
/FontName /TimesNewRomanPSMT def
/PaintType 0 def
/FontMatrix[1 0 0 1 0 0]def
/FontBBox[-568 -307 2028 1007]def
/FontType 42 def
/Encoding StandardEncoding def
/FontInfo 10 dict dup begin
/FamilyName (unknown) def
/FullName (unknown) def
/Weight (unknown) def
/Version (unknown) def
/ItalicAngle 0.0 def
/isFixedPitch false def
/UnderlinePosition -223 def
/UnderlineThickness 100 def
end readonly def
/sfnts[<00010000000900800003001063767420F34DDA810000009C000007C46670676D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>]def
/CharStrings 32 dict dup begin
/.notdef 0 def
/minus 32 def
/space 3 def
/parenleft 4 def
/parenright 5 def
/comma 6 def
/period 7 def
/slash 8 def
/zero 9 def
/two 10 def
/four 11 def
/five 12 def
/six 13 def
/eight 14 def
/less 15 def
/greater 16 def
/E 17 def
/F 18 def
/omega 33 def
/M 19 def
/V 20 def
/c 21 def
/d 22 def
/e 23 def
/i 24 def
/l 25 def
/m 26 def
/r 27 def
/t 28 def
/x 29 def
/y 30 def
/z 31 def
end readonly def

systemdict/resourcestatus known
 {42 /FontType resourcestatus
   {pop pop false}{true}ifelse}
 {true}ifelse
{/TrueDict where{pop}{(%%[ Error: no TrueType rasterizer ]%%)= flush}ifelse
/FontType 3 def
 /TrueState 271 string def
 TrueDict begin sfnts save
 72 0 matrix defaultmatrix dtransform dup
 mul exch dup mul add sqrt cvi 0 72 matrix
 defaultmatrix dtransform dup mul exch dup
 mul add sqrt cvi 3 -1 roll restore
 TrueState initer end
 /BuildGlyph{exch begin
  CharStrings dup 2 index known
    {exch}{exch pop /.notdef}ifelse
  get dup xcheck
    {currentdict systemdict begin begin exec end end}
    {TrueDict begin /bander load cvlit exch TrueState render end}
    ifelse
 end}bind def
 /BuildChar{
  1 index /Encoding get exch get
  1 index /BuildGlyph get exec
 }bind def
}if

FontName currentdict end definefont pop
end
%%EndProlog
mpldict begin
24.35 178.917 translate
563.3 434.166 0 0 clipbox
gsave
0 -0 m
563.3 -0 l
563.3 434.16625 l
0 434.16625 l
cl
1.000 setgray
fill
grestore
gsave
103.45 66.60625 m
549.85 66.60625 l
549.85 426.96625 l
103.45 426.96625 l
cl
1.000 setgray
fill
grestore
2.500 setlinewidth
1 setlinejoin
0 setlinecap
[] 0 setdash
0.000 setgray
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

90.1531 39.2469 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /four glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
215.05 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
215.05 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

201.753 39.2469 translate
0 rotate
0 0 m /minus glyphshow
14.0991 0 m /two glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
326.65 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
326.65 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

320.4 39.2469 translate
0 rotate
0 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
438.25 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
438.25 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

432 39.2469 translate
0 rotate
0 0 m /two glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
549.85 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
549.85 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

543.6 39.2469 translate
0 rotate
0 0 m /four glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
131.35 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
131.35 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
159.25 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
159.25 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
187.15 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
187.15 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
242.95 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
242.95 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
270.85 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
270.85 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
298.75 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
298.75 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
354.55 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
354.55 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
382.45 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
382.45 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
410.35 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
410.35 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
466.15 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
466.15 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
494.05 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
494.05 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
521.95 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
521.95 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

208.603 12.5438 translate
0 rotate
0 0 m /E glyphshow
15.271 0 m /l glyphshow
22.2168 0 m /e glyphshow
33.313 0 m /c glyphshow
44.4092 0 m /t glyphshow
51.355 0 m /r glyphshow
59.6802 0 m /i glyphshow
66.626 0 m /c glyphshow
77.7222 0 m /space glyphshow
83.9722 0 m /F glyphshow
97.876 0 m /i glyphshow
104.822 0 m /e glyphshow
115.918 0 m /l glyphshow
122.864 0 m /d glyphshow
135.364 0 m /space glyphshow
141.614 0 m /parenleft glyphshow
149.939 0 m /M glyphshow
172.168 0 m /V glyphshow
190.222 0 m /slash glyphshow
197.168 0 m /c glyphshow
208.264 0 m /m glyphshow
227.71 0 m /parenright glyphshow
grestore
2.500 setlinewidth
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 73.3079 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
549.85 73.3079 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

37.2 64.6282 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /five glyphshow
43.75 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 120.513 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
549.85 120.513 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

37.2 111.833 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /five glyphshow
43.75 0 m /two glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 167.717 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
549.85 167.717 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

37.2 159.038 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /five glyphshow
43.75 0 m /four glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 214.922 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
549.85 214.922 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

37.2 206.243 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /five glyphshow
43.75 0 m /six glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 262.127 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
549.85 262.127 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

37.2 253.447 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /five glyphshow
43.75 0 m /eight glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 309.332 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
549.85 309.332 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

37.2 300.652 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /six glyphshow
43.75 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 356.537 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
549.85 356.537 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

37.2 347.857 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /six glyphshow
43.75 0 m /two glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 403.741 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
549.85 403.741 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

37.2 395.062 translate
0 rotate
0 0 m /zero glyphshow
12.5 0 m /period glyphshow
18.75 0 m /zero glyphshow
31.25 0 m /six glyphshow
43.75 0 m /four glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 85.109 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
549.85 85.109 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 96.9102 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
549.85 96.9102 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 108.711 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
549.85 108.711 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 132.314 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
549.85 132.314 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 144.115 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
549.85 144.115 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 155.916 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
549.85 155.916 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 179.519 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
549.85 179.519 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 191.32 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
549.85 191.32 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 203.121 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
549.85 203.121 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 226.723 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
549.85 226.723 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 238.525 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
549.85 238.525 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 250.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
549.85 250.326 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 273.928 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
549.85 273.928 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 285.729 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
549.85 285.729 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 297.531 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
549.85 297.531 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 321.133 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
549.85 321.133 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 332.934 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
549.85 332.934 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 344.735 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
549.85 344.735 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 368.338 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
549.85 368.338 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 380.139 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
549.85 380.139 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 391.94 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
549.85 391.94 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.45 415.543 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
549.85 415.543 o
grestore
gsave
22.2 203.786 translate
90 rotate
/TimesNewRomanPSMT 25.0 selectfont
0 0.6875 moveto
/less glyphshow
14.0991 0.6875 moveto
/omega glyphshow
/TimesNewRomanPSMT 17.5 selectfont
30.9299 -5.75313 moveto
/x glyphshow
39.6799 -5.75313 moveto
/comma glyphshow
46.7009 -5.75313 moveto
/y glyphshow
55.4509 -5.75313 moveto
/comma glyphshow
62.4719 -5.75313 moveto
/z glyphshow
/TimesNewRomanPSMT 25.0 selectfont
71.3127 0.6875 moveto
/greater glyphshow
grestore
0.500 setlinewidth
[1.85 0.8] 0 setdash
0.031 0.235 0.235 setrgbcolor
gsave
446.4 360.36 103.45 66.606 clipbox
538.69 235.514338 m
527.53 236.067106 l
516.37 236.372757 l
505.21 236.748979 l
494.05 236.685489 l
482.89 236.315876 l
471.73 237.058643 l
460.57 236.126348 l
449.41 236.742371 l
438.25 236.525465 l
427.09 236.140274 l
415.93 235.94107 l
404.77 234.631137 l
393.61 235.191222 l
382.45 234.323598 l
371.29 234.021487 l
360.13 232.995255 l
348.97 231.993098 l
337.81 231.284318 l
326.65 230.817935 l
315.49 229.995155 l
304.33 228.918414 l
293.17 226.938174 l
282.01 225.671433 l
270.85 225.104032 l
259.69 222.661657 l
248.53 222.062864 l
237.37 218.751213 l
226.21 216.817469 l
215.05 215.022035 l
203.89 213.228961 l
192.73 208.849066 l
181.57 207.365655 l
170.41 202.481141 l
159.25 198.121307 l
148.09 194.730116 l
136.93 189.502894 l
125.77 183.035839 l
114.61 235.222849 l
103.45 235.575941 l
114.61 235.929504 l
125.77 236.931426 l
136.93 236.121864 l
148.09 236.417838 l
159.25 236.862507 l
170.41 236.539862 l
181.57 237.157065 l
192.73 236.766209 l
203.89 236.585887 l
215.05 236.364732 l
226.21 236.173317 l
237.37 236.33877 l
248.53 235.060228 l
259.69 235.199955 l
270.85 234.670553 l
282.01 233.689874 l
293.17 233.274471 l
304.33 232.614313 l
315.49 232.203631 l
326.65 231.049474 l
337.81 229.978162 l
348.97 229.013532 l
360.13 228.334255 l
371.29 226.204611 l
382.45 225.115597 l
393.61 222.825457 l
404.77 221.131042 l
415.93 219.12059 l
427.09 217.683676 l
438.25 215.309276 l
449.41 211.646421 l
460.57 208.829948 l
471.73 207.748014 l
482.89 203.124542 l
494.05 200.234901 l
505.21 194.417148 l
516.37 189.698322 l
527.53 182.678971 l
538.69 235.933281 l
549.85 234.840254 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
446.4 360.36 103.45 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

0 2 m
-2 -2 l
2 -2 l
cl

gsave
0.031 0.235 0.235 setrgbcolor
fill
grestore
stroke
grestore
} bind def
538.69 235.514 o
527.53 236.067 o
516.37 236.373 o
505.21 236.749 o
494.05 236.685 o
482.89 236.316 o
471.73 237.059 o
460.57 236.126 o
449.41 236.742 o
438.25 236.525 o
427.09 236.14 o
415.93 235.941 o
404.77 234.631 o
393.61 235.191 o
382.45 234.324 o
371.29 234.021 o
360.13 232.995 o
348.97 231.993 o
337.81 231.284 o
326.65 230.818 o
315.49 229.995 o
304.33 228.918 o
293.17 226.938 o
282.01 225.671 o
270.85 225.104 o
259.69 222.662 o
248.53 222.063 o
237.37 218.751 o
226.21 216.817 o
215.05 215.022 o
203.89 213.229 o
192.73 208.849 o
181.57 207.366 o
170.41 202.481 o
159.25 198.121 o
148.09 194.73 o
136.93 189.503 o
125.77 183.036 o
114.61 235.223 o
103.45 235.576 o
114.61 235.93 o
125.77 236.931 o
136.93 236.122 o
148.09 236.418 o
159.25 236.863 o
170.41 236.54 o
181.57 237.157 o
192.73 236.766 o
203.89 236.586 o
215.05 236.365 o
226.21 236.173 o
237.37 236.339 o
248.53 235.06 o
259.69 235.2 o
270.85 234.671 o
282.01 233.69 o
293.17 233.274 o
304.33 232.614 o
315.49 232.204 o
326.65 231.049 o
337.81 229.978 o
348.97 229.014 o
360.13 228.334 o
371.29 226.205 o
382.45 225.116 o
393.61 222.825 o
404.77 221.131 o
415.93 219.121 o
427.09 217.684 o
438.25 215.309 o
449.41 211.646 o
460.57 208.83 o
471.73 207.748 o
482.89 203.125 o
494.05 200.235 o
505.21 194.417 o
516.37 189.698 o
527.53 182.679 o
538.69 235.933 o
549.85 234.84 o
grestore
0.500 setlinewidth
1 setlinejoin
[1.85 0.8] 0 setdash
0.031 0.416 0.416 setrgbcolor
gsave
446.4 360.36 103.45 66.606 clipbox
538.69 235.731952 m
527.53 235.840759 l
516.37 236.731986 l
505.21 236.540334 l
494.05 237.179015 l
482.89 236.537974 l
471.73 237.079649 l
460.57 236.869588 l
449.41 236.319416 l
438.25 236.645365 l
427.09 236.248373 l
415.93 236.078672 l
404.77 235.462885 l
393.61 234.771571 l
382.45 234.923334 l
371.29 234.169946 l
360.13 233.698842 l
348.97 231.861632 l
337.81 231.680366 l
326.65 231.336479 l
315.49 229.50871 l
304.33 229.111246 l
293.17 227.902803 l
282.01 226.415145 l
270.85 224.706568 l
259.69 222.722079 l
248.53 220.954024 l
237.37 219.573048 l
226.21 217.650161 l
215.05 215.026755 l
203.89 212.276369 l
192.73 209.433697 l
181.57 206.839322 l
170.41 204.067457 l
159.25 200.071809 l
148.09 194.284266 l
136.93 190.241885 l
125.77 183.481924 l
114.61 235.308289 l
103.45 235.297668 l
114.61 236.095901 l
125.77 235.824474 l
136.93 236.172845 l
148.09 236.367565 l
159.25 236.426807 l
170.41 236.800669 l
181.57 236.809638 l
192.73 236.980283 l
203.89 236.85519 l
215.05 236.479676 l
226.21 236.108175 l
237.37 236.252621 l
248.53 235.933045 l
259.69 235.219072 l
270.85 234.298107 l
282.01 233.903711 l
293.17 233.123652 l
304.33 233.923773 l
315.49 231.732527 l
326.65 230.970878 l
337.81 229.269146 l
348.97 228.686167 l
360.13 227.460967 l
371.29 226.051432 l
382.45 224.396904 l
393.61 223.183978 l
404.77 221.203265 l
415.93 218.943336 l
427.09 218.103327 l
438.25 214.333553 l
449.41 212.541188 l
460.57 210.545133 l
471.73 205.836929 l
482.89 203.920887 l
494.05 198.240499 l
505.21 195.179977 l
516.37 189.89729 l
527.53 183.817314 l
538.69 235.56886 l
549.85 235.313718 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
446.4 360.36 103.45 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-5 -5 m
5 -5 l
5 5 l
-5 5 l
cl

gsave
grestore
stroke
grestore
} bind def
538.69 235.732 o
527.53 235.841 o
516.37 236.732 o
505.21 236.54 o
494.05 237.179 o
482.89 236.538 o
471.73 237.08 o
460.57 236.87 o
449.41 236.319 o
438.25 236.645 o
427.09 236.248 o
415.93 236.079 o
404.77 235.463 o
393.61 234.772 o
382.45 234.923 o
371.29 234.17 o
360.13 233.699 o
348.97 231.862 o
337.81 231.68 o
326.65 231.336 o
315.49 229.509 o
304.33 229.111 o
293.17 227.903 o
282.01 226.415 o
270.85 224.707 o
259.69 222.722 o
248.53 220.954 o
237.37 219.573 o
226.21 217.65 o
215.05 215.027 o
203.89 212.276 o
192.73 209.434 o
181.57 206.839 o
170.41 204.067 o
159.25 200.072 o
148.09 194.284 o
136.93 190.242 o
125.77 183.482 o
114.61 235.308 o
103.45 235.298 o
114.61 236.096 o
125.77 235.824 o
136.93 236.173 o
148.09 236.368 o
159.25 236.427 o
170.41 236.801 o
181.57 236.81 o
192.73 236.98 o
203.89 236.855 o
215.05 236.48 o
226.21 236.108 o
237.37 236.253 o
248.53 235.933 o
259.69 235.219 o
270.85 234.298 o
282.01 233.904 o
293.17 233.124 o
304.33 233.924 o
315.49 231.733 o
326.65 230.971 o
337.81 229.269 o
348.97 228.686 o
360.13 227.461 o
371.29 226.051 o
382.45 224.397 o
393.61 223.184 o
404.77 221.203 o
415.93 218.943 o
427.09 218.103 o
438.25 214.334 o
449.41 212.541 o
460.57 210.545 o
471.73 205.837 o
482.89 203.921 o
494.05 198.24 o
505.21 195.18 o
516.37 189.897 o
527.53 183.817 o
538.69 235.569 o
549.85 235.314 o
grestore
0.500 setlinewidth
1 setlinejoin
[1.85 0.8] 0 setdash
0.031 0.659 0.659 setrgbcolor
gsave
446.4 360.36 103.45 66.606 clipbox
538.69 90.708716 m
527.53 97.742937 l
516.37 105.285789 l
505.21 112.356357 l
494.05 119.90983 l
482.89 127.253477 l
471.73 134.569746 l
460.57 141.248751 l
449.41 148.985851 l
438.25 156.702652 l
427.09 163.60824 l
415.93 171.900467 l
404.77 178.71613 l
393.61 185.833903 l
382.45 194.507781 l
371.29 201.986435 l
360.13 208.733886 l
348.97 217.02635 l
337.81 223.392151 l
326.65 230.724705 l
315.49 238.556214 l
304.33 245.698298 l
293.17 253.58362 l
282.01 262.198965 l
270.85 269.446079 l
259.69 278.095175 l
248.53 286.312583 l
237.37 295.401628 l
226.21 302.956045 l
215.05 312.78054 l
203.89 320.843825 l
192.73 331.677794 l
181.57 341.019856 l
170.41 351.964521 l
159.25 363.807256 l
148.09 378.015423 l
136.93 391.031905 l
125.77 410.58625 l
114.61 89.909067 l
103.45 82.98625 l
114.61 90.042421 l
125.77 97.092219 l
136.93 105.757128 l
148.09 112.463275 l
159.25 119.801967 l
170.41 127.156 l
181.57 134.401697 l
192.73 142.021257 l
203.89 149.637513 l
215.05 157.152042 l
226.21 163.849928 l
237.37 171.229924 l
248.53 178.595049 l
259.69 185.718487 l
270.85 193.184631 l
282.01 201.461281 l
293.17 207.951467 l
304.33 215.735299 l
315.49 223.030798 l
326.65 231.100691 l
337.81 239.12574 l
348.97 245.459206 l
360.13 253.423832 l
371.29 262.198965 l
382.45 269.906798 l
393.61 277.967486 l
404.77 286.512968 l
415.93 294.86609 l
427.09 302.871076 l
438.25 312.389212 l
449.41 322.10254 l
460.57 331.113697 l
471.73 341.045111 l
482.89 351.260461 l
494.05 364.093789 l
505.21 376.039667 l
516.37 390.781012 l
527.53 409.463248 l
538.69 89.828347 l
549.85 83.214721 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
446.4 360.36 103.45 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -5 m
1.326016 -5 2.597899 -4.473168 3.535534 -3.535534 c
4.473168 -2.597899 5 -1.326016 5 0 c
5 1.326016 4.473168 2.597899 3.535534 3.535534 c
2.597899 4.473168 1.326016 5 0 5 c
-1.326016 5 -2.597899 4.473168 -3.535534 3.535534 c
-4.473168 2.597899 -5 1.326016 -5 0 c
-5 -1.326016 -4.473168 -2.597899 -3.535534 -3.535534 c
-2.597899 -4.473168 -1.326016 -5 0 -5 c
cl

gsave
grestore
stroke
grestore
} bind def
538.69 90.7087 o
527.53 97.7429 o
516.37 105.286 o
505.21 112.356 o
494.05 119.91 o
482.89 127.253 o
471.73 134.57 o
460.57 141.249 o
449.41 148.986 o
438.25 156.703 o
427.09 163.608 o
415.93 171.9 o
404.77 178.716 o
393.61 185.834 o
382.45 194.508 o
371.29 201.986 o
360.13 208.734 o
348.97 217.026 o
337.81 223.392 o
326.65 230.725 o
315.49 238.556 o
304.33 245.698 o
293.17 253.584 o
282.01 262.199 o
270.85 269.446 o
259.69 278.095 o
248.53 286.313 o
237.37 295.402 o
226.21 302.956 o
215.05 312.781 o
203.89 320.844 o
192.73 331.678 o
181.57 341.02 o
170.41 351.965 o
159.25 363.807 o
148.09 378.015 o
136.93 391.032 o
125.77 410.586 o
114.61 89.9091 o
103.45 82.9863 o
114.61 90.0424 o
125.77 97.0922 o
136.93 105.757 o
148.09 112.463 o
159.25 119.802 o
170.41 127.156 o
181.57 134.402 o
192.73 142.021 o
203.89 149.638 o
215.05 157.152 o
226.21 163.85 o
237.37 171.23 o
248.53 178.595 o
259.69 185.718 o
270.85 193.185 o
282.01 201.461 o
293.17 207.951 o
304.33 215.735 o
315.49 223.031 o
326.65 231.101 o
337.81 239.126 o
348.97 245.459 o
360.13 253.424 o
371.29 262.199 o
382.45 269.907 o
393.61 277.967 o
404.77 286.513 o
415.93 294.866 o
427.09 302.871 o
438.25 312.389 o
449.41 322.103 o
460.57 331.114 o
471.73 341.045 o
482.89 351.26 o
494.05 364.094 o
505.21 376.04 o
516.37 390.781 o
527.53 409.463 o
538.69 89.8283 o
549.85 83.2147 o
grestore
2.500 setlinewidth
0 setlinejoin
2 setlinecap
0.000 setgray
gsave
103.45 66.60625 m
103.45 426.96625 l
stroke
grestore
gsave
549.85 66.60625 m
549.85 426.96625 l
stroke
grestore
gsave
103.45 66.60625 m
549.85 66.60625 l
stroke
grestore
gsave
103.45 426.96625 m
549.85 426.96625 l
stroke
grestore
0.500 setlinewidth
1 setlinejoin
0 setlinecap
[1.85 0.8] 0 setdash
0.031 0.235 0.235 setrgbcolor
gsave
287.65 160.60625 m
307.65 160.60625 l
327.65 160.60625 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

0 2 m
-2 -2 l
2 -2 l
cl

gsave
0.031 0.235 0.235 setrgbcolor
fill
grestore
stroke
grestore
} bind def
307.65 160.606 o
grestore
0.000 setgray
gsave
343.65 153.606 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.796875 moveto
/omega glyphshow
/TimesNewRomanPSMT 14.0 selectfont
13.4646 -4.35562 moveto
/x glyphshow
grestore
0.500 setlinewidth
1 setlinejoin
[1.85 0.8] 0 setdash
0.031 0.416 0.416 setrgbcolor
gsave
287.65 130.60625 m
307.65 130.60625 l
327.65 130.60625 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-5 -5 m
5 -5 l
5 5 l
-5 5 l
cl

gsave
grestore
stroke
grestore
} bind def
307.65 130.606 o
grestore
0.000 setgray
gsave
343.65 123.606 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.796875 moveto
/omega glyphshow
/TimesNewRomanPSMT 14.0 selectfont
13.4646 -4.35562 moveto
/y glyphshow
grestore
0.500 setlinewidth
1 setlinejoin
[1.85 0.8] 0 setdash
0.031 0.659 0.659 setrgbcolor
gsave
287.65 97.60625 m
307.65 97.60625 l
327.65 97.60625 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -5 m
1.326016 -5 2.597899 -4.473168 3.535534 -3.535534 c
4.473168 -2.597899 5 -1.326016 5 0 c
5 1.326016 4.473168 2.597899 3.535534 3.535534 c
2.597899 4.473168 1.326016 5 0 5 c
-1.326016 5 -2.597899 4.473168 -3.535534 3.535534 c
-4.473168 2.597899 -5 1.326016 -5 0 c
-5 -1.326016 -4.473168 -2.597899 -3.535534 -3.535534 c
-2.597899 -4.473168 -1.326016 -5 0 -5 c
cl

gsave
grestore
stroke
grestore
} bind def
307.65 97.6062 o
grestore
0.000 setgray
gsave
343.65 90.6062 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.796875 moveto
/omega glyphshow
/TimesNewRomanPSMT 14.0 selectfont
13.4646 -4.35562 moveto
/z glyphshow
grestore

end
showpage
