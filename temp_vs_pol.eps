%!PS-Adobe-3.0 EPSF-3.0
%%Title: temp_vs_pol.eps
%%Creator: Mat<PERSON>lotlib v3.7.1, https://matplotlib.org/
%%CreationDate: Tue Jul 23 18:19:42 2024
%%Orientation: portrait
%%BoundingBox: 27 174 585 618
%%HiResBoundingBox: 27.100000 174.577031 584.900000 617.422969
%%EndComments
%%BeginProlog
/mpldict 11 dict def
mpldict begin
/_d { bind def } bind def
/m { moveto } _d
/l { lineto } _d
/r { rlineto } _d
/c { curveto } _d
/cl { closepath } _d
/ce { closepath eofill } _d
/box {
      m
      1 index 0 r
      0 exch r
      neg 0 r
      cl
    } _d
/clipbox {
      box
      clip
      newpath
    } _d
/sc { setcachedevice } _d
%!PS-TrueTypeFont-1.0-2.53740
%%Title: unknown
%%Creator: Converted from TrueType to type 42 by PPR
15 dict begin
/FontName /TimesNewRomanPSMT def
/PaintType 0 def
/FontMatrix[1 0 0 1 0 0]def
/FontBBox[-568 -307 2028 1007]def
/FontType 42 def
/Encoding StandardEncoding def
/FontInfo 10 dict dup begin
/FamilyName (unknown) def
/FullName (unknown) def
/Weight (unknown) def
/Version (unknown) def
/ItalicAngle 0.0 def
/isFixedPitch false def
/UnderlinePosition -223 def
/UnderlineThickness 100 def
end readonly def
/sfnts[<00010000000900800003001063767420F34DDA810000009C000007C46670676D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>]def
/CharStrings 33 dict dup begin
/.notdef 0 def
/space 3 def
/parenleft 4 def
/parenright 5 def
/slash 6 def
/zero 7 def
/one 8 def
/two 9 def
/three 10 def
/four 11 def
/five 12 def
/six 13 def
/nine 14 def
/mu 34 def
/C 15 def
/K 16 def
/P 17 def
/T 18 def
/a 19 def
/c 20 def
/e 21 def
/i 22 def
/l 23 def
/m 24 def
/n 25 def
/o 26 def
/p 27 def
/r 28 def
/t 29 def
/u 30 def
/x 31 def
/y 32 def
/z 33 def
end readonly def

systemdict/resourcestatus known
 {42 /FontType resourcestatus
   {pop pop false}{true}ifelse}
 {true}ifelse
{/TrueDict where{pop}{(%%[ Error: no TrueType rasterizer ]%%)= flush}ifelse
/FontType 3 def
 /TrueState 271 string def
 TrueDict begin sfnts save
 72 0 matrix defaultmatrix dtransform dup
 mul exch dup mul add sqrt cvi 0 72 matrix
 defaultmatrix dtransform dup mul exch dup
 mul add sqrt cvi 3 -1 roll restore
 TrueState initer end
 /BuildGlyph{exch begin
  CharStrings dup 2 index known
    {exch}{exch pop /.notdef}ifelse
  get dup xcheck
    {currentdict systemdict begin begin exec end end}
    {TrueDict begin /bander load cvlit exch TrueState render end}
    ifelse
 end}bind def
 /BuildChar{
  1 index /Encoding get exch get
  1 index /BuildGlyph get exec
 }bind def
}if

FontName currentdict end definefont pop
end
%%EndProlog
mpldict begin
27.1 174.577 translate
557.8 442.846 0 0 clipbox
gsave
0 -0 m
557.8 -0 l
557.8 442.845937 l
0 442.845937 l
cl
1.000 setgray
fill
grestore
gsave
79.2 66.60625 m
525.6 66.60625 l
525.6 426.96625 l
79.2 426.96625 l
cl
1.000 setgray
fill
grestore
2.500 setlinewidth
1 setlinejoin
0 setlinecap
[] 0 setdash
0.000 setgray
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

72.95 39.2469 translate
0 rotate
0 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
168.48 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
168.48 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

149.73 39.2469 translate
0 rotate
0 0 m /three glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
257.76 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
257.76 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

239.01 39.2469 translate
0 rotate
0 0 m /six glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
347.04 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
347.04 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

328.29 39.2469 translate
0 rotate
0 0 m /nine glyphshow
12.5 0 m /zero glyphshow
25 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
436.32 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
436.32 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

411.32 39.2469 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /two glyphshow
25 0 m /zero glyphshow
37.5 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.6 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -6 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.6 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

500.6 39.2469 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /five glyphshow
25 0 m /zero glyphshow
37.5 0 m /zero glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
101.52 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
101.52 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
123.84 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
123.84 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
146.16 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
146.16 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
190.8 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
190.8 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
213.12 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
213.12 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
235.44 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
235.44 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
280.08 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
280.08 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
302.4 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
302.4 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
324.72 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
324.72 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
369.36 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
369.36 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
391.68 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
391.68 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
414 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
414 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
458.64 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
458.64 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
480.96 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
480.96 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
503.28 66.6063 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -4 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
503.28 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

218.939 12.5438 translate
0 rotate
0 0 m /T glyphshow
13.521 0 m /e glyphshow
24.6172 0 m /m glyphshow
44.063 0 m /p glyphshow
56.563 0 m /e glyphshow
67.6592 0 m /r glyphshow
75.9844 0 m /a glyphshow
87.0806 0 m /t glyphshow
94.0264 0 m /u glyphshow
106.526 0 m /r glyphshow
114.852 0 m /e glyphshow
125.948 0 m /space glyphshow
132.198 0 m /parenleft glyphshow
140.523 0 m /K glyphshow
158.577 0 m /parenright glyphshow
grestore
2.500 setlinewidth
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 82.5778 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.6 82.5778 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

56.7 73.8981 translate
0 rotate
0 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 151.455 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.6 151.455 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

44.2 142.776 translate
0 rotate
0 0 m /one glyphshow
12.5 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 220.333 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.6 220.333 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

44.2 211.653 translate
0 rotate
0 0 m /two glyphshow
12.5 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 289.211 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.6 289.211 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

44.2 280.531 translate
0 rotate
0 0 m /three glyphshow
12.5 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 358.089 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.6 358.089 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

44.2 349.409 translate
0 rotate
0 0 m /four glyphshow
12.5 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 426.966 o
grestore
gsave
/o {
gsave
newpath
translate
2.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-6 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.6 426.966 o
grestore
/TimesNewRomanPSMT 25.000 selectfont
gsave

44.2 418.287 translate
0 rotate
0 0 m /five glyphshow
12.5 0 m /zero glyphshow
grestore
1.500 setlinewidth
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 68.8023 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.6 68.8023 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 96.3533 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.6 96.3533 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 110.129 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.6 110.129 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 123.904 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.6 123.904 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 137.68 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.6 137.68 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 165.231 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.6 165.231 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 179.007 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.6 179.007 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 192.782 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.6 192.782 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 206.558 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.6 206.558 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 234.109 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.6 234.109 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 247.884 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.6 247.884 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 261.66 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.6 261.66 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 275.435 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.6 275.435 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 302.986 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.6 302.986 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 316.762 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.6 316.762 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 330.537 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.6 330.537 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 344.313 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.6 344.313 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 371.864 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.6 371.864 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 385.64 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.6 385.64 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 399.415 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.6 399.415 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
79.2 413.191 o
grestore
gsave
/o {
gsave
newpath
translate
1.5 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-4 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
525.6 413.191 o
grestore
gsave
34.2 135.786 translate
90 rotate
/TimesNewRomanPSMT 25.0 selectfont
0 0.148438 moveto
/P glyphshow
13.9038 0.148438 moveto
/o glyphshow
26.4038 0.148438 moveto
/l glyphshow
33.3496 0.148438 moveto
/a glyphshow
44.4458 0.148438 moveto
/r glyphshow
52.771 0.148438 moveto
/i glyphshow
59.7168 0.148438 moveto
/z glyphshow
70.813 0.148438 moveto
/a glyphshow
81.9092 0.148438 moveto
/t glyphshow
88.855 0.148438 moveto
/i glyphshow
95.8008 0.148438 moveto
/o glyphshow
108.301 0.148438 moveto
/n glyphshow
120.801 0.148438 moveto
/space glyphshow
127.051 0.148438 moveto
/parenleft glyphshow
135.376 0.148438 moveto
/mu glyphshow
148.779 0.148438 moveto
/C glyphshow
165.454 0.148438 moveto
/slash glyphshow
172.4 0.148438 moveto
/c glyphshow
183.496 0.148438 moveto
/m glyphshow
/TimesNewRomanPSMT 17.5 selectfont
203.318 15.1766 moveto
/two glyphshow
/TimesNewRomanPSMT 25.0 selectfont
213.141 0.148438 moveto
/parenright glyphshow
grestore
0.050 setlinewidth
[0.185 0.08] 0 setdash
0.031 0.235 0.235 setrgbcolor
gsave
446.4 360.36 79.2 66.606 clipbox
519.648 86.492972 m
513.696 93.547155 l
507.744 83.013812 l
501.792 84.346936 l
495.84 95.44554 l
489.888 98.795411 l
483.936 92.282937 l
477.984 107.907997 l
472.032 89.089113 l
466.08 100.198321 l
460.128 98.409754 l
454.176 89.871064 l
448.224 135.030889 l
442.272 136.653048 l
436.32 85.885772 l
430.368 96.591009 l
424.416 302.490065 l
418.464 311.977801 l
412.512 320.485096 l
406.56 325.131361 l
400.608 329.231288 l
394.656 331.266046 l
388.704 335.868891 l
382.752 339.795601 l
376.8 341.55722 l
370.848 344.459518 l
364.896 348.449501 l
358.944 350.029081 l
352.992 351.970014 l
347.04 354.816316 l
341.088 356.788065 l
335.136 358.526679 l
329.184 360.5638 l
323.232 362.069582 l
317.28 364.207371 l
311.328 364.887876 l
305.376 367.061871 l
299.424 368.575402 l
293.472 369.857404 l
287.52 371.701871 l
281.568 373.017328 l
275.616 373.897906 l
269.664 375.535051 l
263.712 377.26026 l
257.76 378.603376 l
251.808 379.89103 l
245.856 381.106247 l
239.904 382.298619 l
233.952 383.464343 l
228 384.570761 l
222.048 385.414644 l
216.096 386.838401 l
210.144 387.732589 l
204.192 388.750037 l
198.24 389.740379 l
192.288 390.935422 l
186.336 391.805185 l
180.384 392.778468 l
174.432 393.915911 l
168.48 394.849729 l
162.528 395.668563 l
156.576 396.620576 l
150.624 397.551164 l
144.672 398.544244 l
138.72 399.542991 l
132.768 400.199624 l
126.816 400.981784 l
120.864 402.036665 l
114.912 402.873198 l
108.96 403.560264 l
103.008 404.338595 l
97.056 405.208449 l
91.104 405.957756 l
85.152 406.731877 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
446.4 360.36 79.2 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

0 2 m
-2 -2 l
2 -2 l
cl

gsave
0.031 0.235 0.235 setrgbcolor
fill
grestore
stroke
grestore
} bind def
519.648 86.493 o
513.696 93.5472 o
507.744 83.0138 o
501.792 84.3469 o
495.84 95.4455 o
489.888 98.7954 o
483.936 92.2829 o
477.984 107.908 o
472.032 89.0891 o
466.08 100.198 o
460.128 98.4098 o
454.176 89.8711 o
448.224 135.031 o
442.272 136.653 o
436.32 85.8858 o
430.368 96.591 o
424.416 302.49 o
418.464 311.978 o
412.512 320.485 o
406.56 325.131 o
400.608 329.231 o
394.656 331.266 o
388.704 335.869 o
382.752 339.796 o
376.8 341.557 o
370.848 344.46 o
364.896 348.45 o
358.944 350.029 o
352.992 351.97 o
347.04 354.816 o
341.088 356.788 o
335.136 358.527 o
329.184 360.564 o
323.232 362.07 o
317.28 364.207 o
311.328 364.888 o
305.376 367.062 o
299.424 368.575 o
293.472 369.857 o
287.52 371.702 o
281.568 373.017 o
275.616 373.898 o
269.664 375.535 o
263.712 377.26 o
257.76 378.603 o
251.808 379.891 o
245.856 381.106 o
239.904 382.299 o
233.952 383.464 o
228 384.571 o
222.048 385.415 o
216.096 386.838 o
210.144 387.733 o
204.192 388.75 o
198.24 389.74 o
192.288 390.935 o
186.336 391.805 o
180.384 392.778 o
174.432 393.916 o
168.48 394.85 o
162.528 395.669 o
156.576 396.621 o
150.624 397.551 o
144.672 398.544 o
138.72 399.543 o
132.768 400.2 o
126.816 400.982 o
120.864 402.037 o
114.912 402.873 o
108.96 403.56 o
103.008 404.339 o
97.056 405.208 o
91.104 405.958 o
85.152 406.732 o
grestore
0.050 setlinewidth
1 setlinejoin
[0.185 0.08] 0 setdash
0.031 0.416 0.416 setrgbcolor
gsave
446.4 360.36 79.2 66.606 clipbox
519.648 84.551875 m
513.696 85.322433 l
507.744 86.452471 l
501.792 95.284988 l
495.84 83.611119 l
489.888 84.761045 l
483.936 84.357616 l
477.984 84.717529 l
472.032 96.49878 l
466.08 83.73532 l
460.128 90.544832 l
454.176 115.355752 l
448.224 103.243884 l
442.272 108.951143 l
436.32 112.408264 l
430.368 140.559007 l
424.416 303.951636 l
418.464 313.120899 l
412.512 320.075805 l
406.56 324.678644 l
400.608 329.326213 l
394.656 332.828591 l
388.704 335.997223 l
382.752 338.558955 l
376.8 342.484731 l
370.848 345.131403 l
364.896 347.478328 l
358.944 350.420418 l
352.992 352.347278 l
347.04 354.116052 l
341.088 356.781439 l
335.136 358.855376 l
329.184 359.804986 l
323.232 361.509618 l
317.28 363.896212 l
311.328 365.482562 l
305.376 366.951857 l
299.424 368.996708 l
293.472 370.136202 l
287.52 371.612984 l
281.568 373.17274 l
275.616 374.48107 l
269.664 376.177866 l
263.712 377.170063 l
257.76 378.36937 l
251.808 379.789115 l
245.856 381.247282 l
239.904 381.996244 l
233.952 383.615473 l
228 384.414553 l
222.048 385.80996 l
216.096 386.746224 l
210.144 387.688519 l
204.192 388.927572 l
198.24 390.004364 l
192.288 390.916788 l
186.336 391.875764 l
180.384 392.953157 l
174.432 393.847342 l
168.48 394.851236 l
162.528 395.757794 l
156.576 396.59988 l
150.624 397.637854 l
144.672 398.501435 l
138.72 399.505756 l
132.768 400.299719 l
126.816 401.224235 l
120.864 401.885691 l
114.912 402.706903 l
108.96 403.525367 l
103.008 404.400204 l
97.056 405.210277 l
91.104 405.976562 l
85.152 406.776204 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
446.4 360.36 79.2 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-4 -4 m
4 -4 l
4 4 l
-4 4 l
cl

gsave
grestore
stroke
grestore
} bind def
519.648 84.5519 o
513.696 85.3224 o
507.744 86.4525 o
501.792 95.285 o
495.84 83.6111 o
489.888 84.761 o
483.936 84.3576 o
477.984 84.7175 o
472.032 96.4988 o
466.08 83.7353 o
460.128 90.5448 o
454.176 115.356 o
448.224 103.244 o
442.272 108.951 o
436.32 112.408 o
430.368 140.559 o
424.416 303.952 o
418.464 313.121 o
412.512 320.076 o
406.56 324.679 o
400.608 329.326 o
394.656 332.829 o
388.704 335.997 o
382.752 338.559 o
376.8 342.485 o
370.848 345.131 o
364.896 347.478 o
358.944 350.42 o
352.992 352.347 o
347.04 354.116 o
341.088 356.781 o
335.136 358.855 o
329.184 359.805 o
323.232 361.51 o
317.28 363.896 o
311.328 365.483 o
305.376 366.952 o
299.424 368.997 o
293.472 370.136 o
287.52 371.613 o
281.568 373.173 o
275.616 374.481 o
269.664 376.178 o
263.712 377.17 o
257.76 378.369 o
251.808 379.789 o
245.856 381.247 o
239.904 381.996 o
233.952 383.615 o
228 384.415 o
222.048 385.81 o
216.096 386.746 o
210.144 387.689 o
204.192 388.928 o
198.24 390.004 o
192.288 390.917 o
186.336 391.876 o
180.384 392.953 o
174.432 393.847 o
168.48 394.851 o
162.528 395.758 o
156.576 396.6 o
150.624 397.638 o
144.672 398.501 o
138.72 399.506 o
132.768 400.3 o
126.816 401.224 o
120.864 401.886 o
114.912 402.707 o
108.96 403.525 o
103.008 404.4 o
97.056 405.21 o
91.104 405.977 o
85.152 406.776 o
grestore
0.050 setlinewidth
1 setlinejoin
[0.185 0.08] 0 setdash
0.031 0.659 0.659 setrgbcolor
gsave
446.4 360.36 79.2 66.606 clipbox
519.648 82.804819 m
513.696 89.756709 l
507.744 86.713203 l
501.792 93.670655 l
495.84 85.001889 l
489.888 82.931336 l
483.936 88.869477 l
477.984 98.020683 l
472.032 84.526639 l
466.08 98.672176 l
460.128 85.493699 l
454.176 119.339641 l
448.224 84.808982 l
442.272 186.720052 l
436.32 201.041176 l
430.368 188.639491 l
424.416 302.155117 l
418.464 312.938973 l
412.512 318.857758 l
406.56 324.687925 l
400.608 329.009685 l
394.656 333.051888 l
388.704 336.622635 l
382.752 339.558427 l
376.8 342.623988 l
370.848 344.414327 l
364.896 347.391658 l
358.944 350.082445 l
352.992 352.249363 l
347.04 354.290886 l
341.088 356.334266 l
335.136 358.464622 l
329.184 360.227917 l
323.232 361.749007 l
317.28 363.877166 l
311.328 365.753793 l
305.376 366.832725 l
299.424 368.448793 l
293.472 370.082924 l
287.52 371.739748 l
281.568 372.89503 l
275.616 374.269604 l
269.664 376.018431 l
263.712 377.12559 l
257.76 378.389919 l
251.808 379.655005 l
245.856 381.058533 l
239.904 381.996442 l
233.952 383.219891 l
228 384.526121 l
222.048 385.566658 l
216.096 386.587494 l
210.144 387.836689 l
204.192 388.739335 l
198.24 389.874078 l
192.288 390.935159 l
186.336 392.022104 l
180.384 392.861419 l
174.432 393.926924 l
168.48 395.040108 l
162.528 395.808174 l
156.576 396.827083 l
150.624 397.603348 l
144.672 398.532265 l
138.72 399.437765 l
132.768 400.276429 l
126.816 401.144639 l
120.864 402.052226 l
114.912 402.847051 l
108.96 403.584378 l
103.008 404.445414 l
97.056 405.260701 l
91.104 405.971469 l
85.152 406.771569 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
446.4 360.36 79.2 66.606 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
519.648 82.8048 o
513.696 89.7567 o
507.744 86.7132 o
501.792 93.6707 o
495.84 85.0019 o
489.888 82.9313 o
483.936 88.8695 o
477.984 98.0207 o
472.032 84.5266 o
466.08 98.6722 o
460.128 85.4937 o
454.176 119.34 o
448.224 84.809 o
442.272 186.72 o
436.32 201.041 o
430.368 188.639 o
424.416 302.155 o
418.464 312.939 o
412.512 318.858 o
406.56 324.688 o
400.608 329.01 o
394.656 333.052 o
388.704 336.623 o
382.752 339.558 o
376.8 342.624 o
370.848 344.414 o
364.896 347.392 o
358.944 350.082 o
352.992 352.249 o
347.04 354.291 o
341.088 356.334 o
335.136 358.465 o
329.184 360.228 o
323.232 361.749 o
317.28 363.877 o
311.328 365.754 o
305.376 366.833 o
299.424 368.449 o
293.472 370.083 o
287.52 371.74 o
281.568 372.895 o
275.616 374.27 o
269.664 376.018 o
263.712 377.126 o
257.76 378.39 o
251.808 379.655 o
245.856 381.059 o
239.904 381.996 o
233.952 383.22 o
228 384.526 o
222.048 385.567 o
216.096 386.587 o
210.144 387.837 o
204.192 388.739 o
198.24 389.874 o
192.288 390.935 o
186.336 392.022 o
180.384 392.861 o
174.432 393.927 o
168.48 395.04 o
162.528 395.808 o
156.576 396.827 o
150.624 397.603 o
144.672 398.532 o
138.72 399.438 o
132.768 400.276 o
126.816 401.145 o
120.864 402.052 o
114.912 402.847 o
108.96 403.584 o
103.008 404.445 o
97.056 405.261 o
91.104 405.971 o
85.152 406.772 o
grestore
2.500 setlinewidth
0 setlinejoin
2 setlinecap
0.000 setgray
gsave
79.2 66.60625 m
79.2 426.96625 l
stroke
grestore
gsave
525.6 66.60625 m
525.6 426.96625 l
stroke
grestore
gsave
79.2 66.60625 m
525.6 66.60625 l
stroke
grestore
gsave
79.2 426.96625 m
525.6 426.96625 l
stroke
grestore
0.050 setlinewidth
1 setlinejoin
0 setlinecap
[0.185 0.08] 0 setdash
0.031 0.235 0.235 setrgbcolor
gsave
431.6 401.96625 m
451.6 401.96625 l
471.6 401.96625 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

0 2 m
-2 -2 l
2 -2 l
cl

gsave
0.031 0.235 0.235 setrgbcolor
fill
grestore
stroke
grestore
} bind def
451.6 401.966 o
grestore
0.000 setgray
gsave
487.6 394.966 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.75 moveto
/P glyphshow
/TimesNewRomanPSMT 14.0 selectfont
11.4236 -4.4025 moveto
/x glyphshow
grestore
0.050 setlinewidth
1 setlinejoin
[0.185 0.08] 0 setdash
0.031 0.416 0.416 setrgbcolor
gsave
431.6 371.96625 m
451.6 371.96625 l
471.6 371.96625 l
stroke
grestore
1.000 setlinewidth
0 setlinejoin
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
0 setlinejoin

0 setlinecap

-4 -4 m
4 -4 l
4 4 l
-4 4 l
cl

gsave
grestore
stroke
grestore
} bind def
451.6 371.966 o
grestore
0.000 setgray
gsave
487.6 364.966 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.75 moveto
/P glyphshow
/TimesNewRomanPSMT 14.0 selectfont
11.4236 -4.4025 moveto
/y glyphshow
grestore
0.050 setlinewidth
1 setlinejoin
[0.185 0.08] 0 setdash
0.031 0.659 0.659 setrgbcolor
gsave
431.6 338.96625 m
451.6 338.96625 l
471.6 338.96625 l
stroke
grestore
1.000 setlinewidth
[] 0 setdash
gsave
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

0 -8 m
2.121625 -8 4.156639 -7.15707 5.656854 -5.656854 c
7.15707 -4.156639 8 -2.121625 8 0 c
8 2.121625 7.15707 4.156639 5.656854 5.656854 c
4.156639 7.15707 2.121625 8 0 8 c
-2.121625 8 -4.156639 7.15707 -5.656854 5.656854 c
-7.15707 4.156639 -8 2.121625 -8 0 c
-8 -2.121625 -7.15707 -4.156639 -5.656854 -5.656854 c
-4.156639 -7.15707 -2.121625 -8 0 -8 c
cl

gsave
grestore
stroke
grestore
} bind def
451.6 338.966 o
grestore
0.000 setgray
gsave
487.6 331.966 translate
0 rotate
/TimesNewRomanPSMT 20.0 selectfont
0 0.75 moveto
/P glyphshow
/TimesNewRomanPSMT 14.0 selectfont
11.4236 -4.4025 moveto
/z glyphshow
grestore

end
showpage
